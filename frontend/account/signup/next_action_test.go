package signup

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	mockDatetime "github.com/epifi/be-common/pkg/datetime/mocks"
	brokerMocks "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/retry/mocks"

	"github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	consentMocks "github.com/epifi/gamma/api/consent/mocks"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	kycMocks "github.com/epifi/gamma/api/kyc/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	authDl "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/auth"
	userMock "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	mocks2 "github.com/epifi/gamma/api/useractions/mocks"
	pkgSourceAndIntent "github.com/epifi/gamma/pkg/acquisition/sourceandintent"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

func TestService_GetNextOnboardingAction(t *testing.T) {
	t.Parallel()
	// Mocking clients required
	type mockedDependencies struct {
		mockConsentClient    *consentMocks.MockConsentClient
		mockKycClient        *kycMocks.MockKycClient
		mockAuthClient       *authMocks.MockAuthClient
		mockUsersClient      *userMock.MockUsersClient
		mockOnboardingClient *onbMocks.MockOnboardingClient
		uaClient             *mocks2.MockUserActionsClient
		timeClient           *mockDatetime.MockTime
		retryStrategy        *mocks.MockRetryStrategy
	}
	type args struct {
		req        *signup.GetNextOnboardingActionRequest
		skipTest   bool
		skipReason string
	}
	sampleActor := &types.Actor{Id: "actorId"}
	firstCallStartedAt := time.Now()
	previousPollingMetrics := &GNOAPollingMetrics{
		FirstCallStartedAt: firstCallStartedAt,
		CurrentCallCounter: 1,
	}
	previousMetricsBytes, _ := json.Marshal(previousPollingMetrics)
	currentPollingMetrics := &GNOAPollingMetrics{
		FirstCallStartedAt: firstCallStartedAt,
		CurrentCallCounter: 2,
	}
	currentPollingMetricsBytes, _ := json.Marshal(currentPollingMetrics)

	tests := []struct {
		name      string
		args      args
		want      *signup.GetNextOnboardingActionResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies)
	}{
		{
			name: "consent-required-success",
			args: args{
				req: &signup.GetNextOnboardingActionRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{ActorId: sampleActor.GetId()},
					},
				},
			},
			want: &signup.GetNextOnboardingActionResponse{
				Status: rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONSENT,
				},
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.timeClient.EXPECT().Now().Return(firstCallStartedAt)
				md.timeClient.EXPECT().Since(time.Time{}).Return(time.Since(time.Time{}))
				md.mockOnboardingClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{ActorId: sampleActor.GetId()}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_CONSENT,
					},
				}, nil)
			},
		},
		{
			name: "consent-not-required-success",
			args: args{
				req: &signup.GetNextOnboardingActionRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{ActorId: sampleActor.GetId()},
					},
				},
			},
			want: &signup.GetNextOnboardingActionResponse{
				Status: rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_REGISTER_CKYC,
				},
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.timeClient.EXPECT().Now().Return(firstCallStartedAt)
				md.timeClient.EXPECT().Since(time.Time{}).Return(time.Since(time.Time{}))
				md.mockOnboardingClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{ActorId: sampleActor.GetId()}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_REGISTER_CKYC,
					},
				}, nil)
			},
		},
		{
			name: "auth-factor-update-action",
			args: args{
				req: &signup.GetNextOnboardingActionRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId:            sampleActor.GetId(),
							AuthFactorUpdateId: "1",
						},
					},
				},
			},
			want: &signup.GetNextOnboardingActionResponse{
				Status: rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_REGISTER_DEVICE,
					ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&authDl.RegisterDeviceScreenOptions{
						Source: auth.DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_AFU.String(),
					}),
				}},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.timeClient.EXPECT().Now().Return(firstCallStartedAt.Add(5 * time.Second))
				md.mockAuthClient.EXPECT().GetAuthFactorUpdateStatus(gomock.Any(), &auth.GetAuthFactorUpdateStatusRequest{
					AuthFactorUpdateId: "1",
				}).Return(&auth.GetAuthFactorUpdateStatusResponse{
					Status: rpc.StatusOk(),
					NextActionDetails: &auth.AuthFactorUpdateNextActionDetails{
						NextAction: auth.AuthFactorUpdateAction_DEVICE_REGISTRATION,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_REGISTER_DEVICE,
							ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&authDl.RegisterDeviceScreenOptions{
								Source: auth.DeviceRegistrationSource_DEVICE_REGISTRATION_SOURCE_AFU.String(),
							}),
						},
					},
				}, nil)
			},
		},
		{
			name: "kyc completed",
			args: args{
				req: &signup.GetNextOnboardingActionRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{ActorId: sampleActor.GetId()},
					},
				},
			},
			want: &signup.GetNextOnboardingActionResponse{
				Status: rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				},
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.timeClient.EXPECT().Now().Return(firstCallStartedAt)
				md.timeClient.EXPECT().Since(time.Time{}).Return(time.Since(time.Time{}))
				md.mockOnboardingClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{ActorId: sampleActor.GetId()}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_CONFIRM_CARD_MAILING_ADDRESS,
					},
				}, nil)
			},
		},
		{
			name: "GNOA polling: (test is skipped due to flakiness)",
			args: args{
				req: &signup.GetNextOnboardingActionRequest{
					PollingRequestInfo: &types.PollingRequestInfo{
						Blob: previousMetricsBytes,
					},
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{ActorId: sampleActor.GetId()},
					},
				},
				skipTest:   true,
				skipReason: "https://epifi.slack.com/archives/C0101A42ZFW/p1731575326771799",
			},
			want: &signup.GetNextOnboardingActionResponse{
				Status: rpc.StatusOk(),
				PollingResponseInfo: &types.PollingResponseInfo{
					Blob:                 currentPollingMetricsBytes,
					RetryIntervalSeconds: 3,
				},
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
					ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
						GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
							Feature: onbPb.Feature_FEATURE_SA.String(),
						},
					},
				},
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.timeClient.EXPECT().Now().Return(firstCallStartedAt)
				md.timeClient.EXPECT().Since(firstCallStartedAt.Round(0)).Return(5 * time.Second).AnyTimes()
				md.mockOnboardingClient.EXPECT().GetOnboardingDetailsMin(gomock.Any(), &onbPb.GetOnboardingDetailsMinRequest{
					ActorId: sampleActor.GetId(),
				}).Return(&onbPb.GetOnboardingDetailsMinResponse{
					Status: rpc.StatusOk(),
					OnboardingDetailsMin: &onbPb.OnboardingDetailsMin{
						CurrentOnboardingStage: onbPb.OnboardingStage_INCOME_ESTIMATE_CHECK,
					},
				}, nil).AnyTimes()
				md.mockOnboardingClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{ActorId: sampleActor.GetId()}).Return(&onbPb.GetNextActionResponse{
					Status: rpc.StatusOk(),
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
						ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
							GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
								Feature: onbPb.Feature_FEATURE_SA.String(),
							},
						},
					},
				}, nil)
			},
		},
		{
			name: "error in get next action",
			args: args{
				req: &signup.GetNextOnboardingActionRequest{
					PollingRequestInfo: &types.PollingRequestInfo{
						Blob: previousMetricsBytes,
					},
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{ActorId: sampleActor.GetId()},
					},
				},
			},
			want: &signup.GetNextOnboardingActionResponse{
				Status: rpc.StatusInternalWithDebugMsg(errors.New("error getDB").Error()),
				PollingResponseInfo: &types.PollingResponseInfo{
					RetryIntervalSeconds: 1,
				},
			},
			wantErr: nil,
			wantMocks: func(args args, md *mockedDependencies) {
				md.timeClient.EXPECT().Now().Return(firstCallStartedAt.Add(5 * time.Second))
				md.mockOnboardingClient.EXPECT().GetOnboardingDetailsMin(gomock.Any(), &onbPb.GetOnboardingDetailsMinRequest{
					ActorId: sampleActor.GetId(),
				}).Return(&onbPb.GetOnboardingDetailsMinResponse{
					Status: rpc.StatusOk(),
					OnboardingDetailsMin: &onbPb.OnboardingDetailsMin{
						CurrentOnboardingStage: onbPb.OnboardingStage_INCOME_ESTIMATE_CHECK,
					},
				}, nil).AnyTimes()
				md.mockOnboardingClient.EXPECT().GetNextAction(gomock.Any(), &onbPb.GetNextActionRequest{ActorId: sampleActor.GetId()}).Return(nil, errors.New("error getDB"))
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if tt.args.skipTest {
				t.Skip(tt.args.skipReason)
			}
			ctr := gomock.NewController(t)
			mockConsentClient := consentMocks.NewMockConsentClient(ctr)
			mockKycClient := kycMocks.NewMockKycClient(ctr)
			mockAuthClient := authMocks.NewMockAuthClient(ctr)
			mockUsersClient := userMock.NewMockUsersClient(ctr)
			mockOnboardingClient := onbMocks.NewMockOnboardingClient(ctr)
			uaClient := mocks2.NewMockUserActionsClient(ctr)
			timeClient := mockDatetime.NewMockTime(ctr)
			brokerMock := brokerMocks.NewMockBroker(ctr)
			acqSourceIntentIdentifier := pkgSourceAndIntent.NewIdentifier(pkgSourceAndIntent.DecryptionKeys{})
			mockRetryStrategy := mocks.NewMockRetryStrategy(ctr)
			md := &mockedDependencies{
				mockConsentClient:    mockConsentClient,
				mockKycClient:        mockKycClient,
				mockAuthClient:       mockAuthClient,
				mockUsersClient:      mockUsersClient,
				mockOnboardingClient: mockOnboardingClient,
				uaClient:             uaClient,
				timeClient:           timeClient,
				retryStrategy:        mockRetryStrategy,
			}
			mockRetryStrategy.EXPECT().GetNextRetryInterval(gomock.Any()).Return(uint64(3)).AnyTimes()
			signupService := NewService(mockAuthClient, mockUsersClient, mockKycClient, nil, nil, mockConsentClient,
				mockOnboardingClient, nil, brokerMock, nil, nil, nil, nil, nil, gconf, uaClient, nil, nil, nil,
				nil, nil, nil, nil, nil, timeClient, acqSourceIntentIdentifier, nil, nil, nil, nil, nil, mockRetryStrategy, nil, nil, nil)
			tt.wantMocks(tt.args, md)
			got, err := signupService.GetNextOnboardingAction(context.Background(), tt.args.req)
			assert.Equal(t, err, tt.wantErr)
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf(" got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}
