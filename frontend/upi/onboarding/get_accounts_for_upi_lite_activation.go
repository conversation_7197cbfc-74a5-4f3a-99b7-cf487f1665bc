package onboarding

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/header"
	feUpiOnbPb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

// GetAccountsForUpiLiteActivation - fetches the lis of all the bank accounts that are eligible for upi lite activation
// Eligibility of the bank account is determined by the response received from list account provider RPC
func (s *Service) GetAccountsForUpiLiteActivation(ctx context.Context, req *feUpiOnbPb.GetAccountsForUpiLiteActivationRequest) (*feUpiOnbPb.GetAccountsForUpiLiteActivationResponse, error) {
	var (
		res = &feUpiOnbPb.GetAccountsForUpiLiteActivationResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	getAccountsRes, err := s.upiOnboardingClient.GetAccountsForUpiLiteActivation(ctx, &upiOnboardingPb.GetAccountsForUpiLiteActivationRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if err = epifigrpc.RPCError(getAccountsRes, err); err != nil {
		logger.Error(ctx, "failed to get upi accounts for the actor",
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		res.RespHeader.ErrorView = defaultErrorView
		res.RespHeader.Status = rpcPb.StatusFromError(err)
		return nil, err
	}

	accountInfos, err := s.getAccountInfos(getAccountsRes.GetAccounts())
	if err != nil {
		logger.Error(ctx, "failed to convert be accountInfos to fe accountInfos",
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		res.RespHeader.ErrorView = defaultErrorView
		res.RespHeader.Status = rpcPb.StatusFromError(err)
		return nil, err
	}

	res.AccountInfos = accountInfos
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}
