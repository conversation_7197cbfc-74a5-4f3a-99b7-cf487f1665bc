//nolint:funlen,dupl
package liquiloans

import (
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
)

type StplProvider struct {
	*FldgProvider
}

var _ provider.IDeeplinkProvider = &StplProvider{}

func NewStplProvider(fldgProvider *FldgProvider) *StplProvider {
	return &StplProvider{
		FldgProvider: fldgProvider,
	}
}

func (ll *StplProvider) GetLoanHeader() *palPbFeEnums.LoanHeader {
	return &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_STPL,
		Vendor:      palPbFeEnums.Vendor_LIQUILOANS,
	}
}
