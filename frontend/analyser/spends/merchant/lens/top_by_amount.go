//nolint:dupl
package lens

import (
	"context"
	"fmt"

	pb "github.com/epifi/gamma/api/frontend/analyser"
	"github.com/epifi/gamma/frontend/analyser/spends/merchant/store"
	"github.com/epifi/gamma/frontend/analyser/visualcomponents"
)

type TopByAmountProcessor struct {
	store     *store.TopMerchantSpendsStore
	processor IEntityLensGenerator
}

func NewTopByAmountLensProcessor(store *store.TopMerchantSpendsStore, processor IEntityLensGenerator) *TopByAmountProcessor {
	return &TopByAmountProcessor{
		store:     store,
		processor: processor,
	}
}

// GenerateLensData generates the lens data which will be used by different components of lens like: Line items, Summery and Visualisation
func (t *TopByAmountProcessor) GenerateLensData(ctx context.Context, actorId string, filterValues []*pb.FilterValue) error {
	entityAggregates, err := t.store.GetEntityAggregates()
	if err != nil {
		return fmt.Errorf("failed to get entity aggregate from store : %w", err)
	}

	err = t.processor.GenerateLensData(ctx, entityAggregates)
	if err != nil {
		return fmt.Errorf("failed to generate top by amount lens: %w", err)
	}

	return nil
}

func (t *TopByAmountProcessor) GetVisualComponentGenerators(ctx context.Context, actorId string) ([]visualcomponents.VisualComponentGenerator, error) {
	generators, err := t.processor.GetVisualComponentGenerators(ctx, actorId, pb.LensName_LENS_NAME_TOP_SPENDS_BY_AMOUNT)
	if err != nil {
		return nil, fmt.Errorf("gailed to get visual component generators: %w", err)
	}
	return generators, nil
}
