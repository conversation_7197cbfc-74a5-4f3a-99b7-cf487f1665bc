package aggregator

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTest "github.com/epifi/gamma/frontend/test"
)

var (
	conf  *config.Config
	gconf *genconf.Config
)

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, gconf, teardown = feTest.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
