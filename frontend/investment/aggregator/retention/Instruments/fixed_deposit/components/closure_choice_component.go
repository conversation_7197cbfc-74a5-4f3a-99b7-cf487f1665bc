package components

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/pkg/errors"

	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/frontend/deeplink"
	retentionScreenfePb "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen"
	retentionScreenOptionPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/aggregator"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/investment/aggregator/retention/navigation"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

var (
	SubtitleForClosureChoice  = "Your effective interest rate for the period of %d %s is %.2f%% p.a"
	FDClosureScreenTitle      = "Close %s Deposit?"
	PenatlyChoiceScreenTitle  = "Close now & get interest rate of"
	MaturityChoiceScreenTitle = "Keep till maturity & get interest rate of"
)

type FDClosureChoiceComponent struct {
	*FDBaseComponent
}

func NewFDClosureChoiceComponent(depositClient depositPb.DepositClient, genConfig *genconf.Config) *FDClosureChoiceComponent {
	return &FDClosureChoiceComponent{FDBaseComponent: NewFDBaseComponent(retentionScreenfePb.RetentionScreen_RetentionScreen_CLOSURE_CHOICES_SCREEN, depositClient, genConfig)}
}

func (s *FDClosureChoiceComponent) GetComponent(ctx context.Context, retentionParam *retentionScreenfePb.RetentionScreenParams, navigationParam *navigation.NavigationParams) (*retentionScreenfePb.RetentionScreenDisplayData, error) {
	closerChoiceComponent, err := s.getClosureChoiceComponent(ctx, retentionParam, navigationParam)
	if err != nil {
		return nil, err
	}
	return &retentionScreenfePb.RetentionScreenDisplayData{
		RetentionScreen: &retentionScreenfePb.RetentionScreenDisplayData_ClosureChoicesScreen{ClosureChoicesScreen: closerChoiceComponent},
	}, nil
}

func (s *FDClosureChoiceComponent) getClosureChoiceComponent(ctx context.Context, retentionParam *retentionScreenfePb.RetentionScreenParams, navigationParam *navigation.NavigationParams) (*retentionScreenfePb.ClosureChoicesScreen, error) {
	closureComponent := make([]*retentionScreenfePb.ClosureChoiceComponent, 0)
	depositAcc, err := s.GetDepositAccountByDepositAccId(ctx, retentionParam.GetInstrumentData().GetFixedDepositInstrumentData().GetDepositAccountId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting deposit account in FD")
	}
	preCloseDetails, err := s.FDBaseComponent.GetPreClosureDetailsByDepositAccId(ctx, retentionParam.GetInstrumentData().GetFixedDepositInstrumentData().GetDepositAccountId())
	if err != nil {
		return nil, errors.Wrap(err, "error while penalty details from BE FD")
	}
	penaltyComponent, err := s.getPenaltyClosureChoiceComponent(retentionParam, navigationParam, preCloseDetails)
	if err != nil {
		return nil, errors.Wrap(err, "error while getPenaltyClosureChoiceComponent")
	}
	maturityComponent, err := s.getMaturityClosureChoiceComponent(retentionParam, depositAcc)
	if err != nil {
		return nil, errors.Wrap(err, "error while getMaturityClosureChoiceComponent")
	}
	closureComponent = append(closureComponent, penaltyComponent, maturityComponent)
	subtitleForClosureChoice := s.getSubtitleForClosureScreen(preCloseDetails)
	return &retentionScreenfePb.ClosureChoicesScreen{
		TitleVisualElement: getClosureScreenVisualElement(depositAcc.GetDepositIcon().GetImageUrl()),
		ChoiceComponents:   closureComponent,
		Title:              commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(FDClosureScreenTitle, depositAcc.GetName()), "#313234", commontypes.FontStyle_SUBTITLE_1),
		Subtitle:           commontypes.GetTextFromStringFontColourFontStyle(subtitleForClosureChoice, "#929599", commontypes.FontStyle_BODY_S),
	}, nil
}

func (s *FDClosureChoiceComponent) getSubtitleForClosureScreen(preCloseDetails *depositPb.PreClosureFinancialInfo) string {
	if s.isPenaltyNotApplicalbe(preCloseDetails.GetNoOfDaysSinceCreation()) {
		return "Confirm your choice"
	}
	return fmt.Sprintf(SubtitleForClosureChoice, preCloseDetails.GetNoOfDaysSinceCreation(), GetDurationStringForNumberOfDays(preCloseDetails), s.getNonPenaltyInterestRate(preCloseDetails.GetInterestRateAfterPenalty()))
}

func (s *FDClosureChoiceComponent) getPenaltyClosureChoiceComponent(retentionParam *retentionScreenfePb.RetentionScreenParams, navigationParam *navigation.NavigationParams, preCloseDetails *depositPb.PreClosureFinancialInfo) (*retentionScreenfePb.ClosureChoiceComponent, error) {
	deeplinkV3, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_INVESTMENT_RETENTION_SCREEN, &retentionScreenOptionPb.RetentionScreenOptions{
		Params: &retentionScreenfePb.RetentionScreenParams{
			InstrumentData: retentionParam.GetInstrumentData(),
			VisitedScreens: navigationParam.NextActionVisitedScreen,
		}})
	if err != nil {
		return nil, errors.Wrap(err, "error while deeplink v3")
	}
	return &retentionScreenfePb.ClosureChoiceComponent{
		Header: commontypes.GetTextFromStringFontColourFontStyle(PenatlyChoiceScreenTitle, "#6A6D70", commontypes.FontStyle_HEADLINE_M),
		Body: &ui.IconTextComponent{
			Texts: s.getPenaltyChoiceBody(preCloseDetails),
		},
		Footer: &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(s.getPenaltyFooter(preCloseDetails), "#A73F4B", commontypes.FontStyle_BODY_XS)},
		},
		NavigationDeeplink: deeplinkV3,
		NextIcon:           getNextActionVisualElement(),
		BgColor:            "#F6F9FD",
		Shadows: []*ui.Shadow{
			{
				Height:  4,
				Opacity: 1,
				Colour:  &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
			},
		},
	}, nil
}

func (s *FDClosureChoiceComponent) getPenaltyChoiceBody(preCloseDetails *depositPb.PreClosureFinancialInfo) []*commontypes.Text {
	if s.isPenaltyNotApplicalbe(preCloseDetails.GetNoOfDaysSinceCreation()) {
		return []*commontypes.Text{commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("%.2f%% ", preCloseDetails.GetInterestRateAfterPenalty()), "#313234", commontypes.FontStyle_NUMBER_2XL), commontypes.GetTextFromHtmlStringFontColourFontStyle("p.a", "#B2B5B9", commontypes.FontStyle_NUMBER_M)}
	}
	nonPenaltyInterest := s.getNonPenaltyInterestRate(preCloseDetails.GetInterestRateAfterPenalty())
	return []*commontypes.Text{commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("%.2f%% ", preCloseDetails.GetInterestRateAfterPenalty()), "#313234", commontypes.FontStyle_NUMBER_2XL), commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<strike>%.2f%%</strike> p.a", nonPenaltyInterest), "#B2B5B9", commontypes.FontStyle_NUMBER_M)}
}

func (s *FDClosureChoiceComponent) getPenaltyFooter(preCloseDetails *depositPb.PreClosureFinancialInfo) string {
	if s.isPenaltyNotApplicalbe(preCloseDetails.GetNoOfDaysSinceCreation()) {
		if moneyPb.IsZero(preCloseDetails.GetTotalInterest()) {
			return "You haven't earned any interest yet."
		} else {
			return "You have earned interest but no penalty is applied"
		}
	}
	return fmt.Sprintf("%s%% of the interest will be deducted as a pre-closure penalty", s.getNumberWithoutTrailingZeros(s.FDBaseComponent.gconf.Deposit().PreClosurePenaltyPercentage()))
}

func (s *FDClosureChoiceComponent) getMaturityClosureChoiceComponent(retentionParam *retentionScreenfePb.RetentionScreenParams, depositAcc *depositPb.DepositAccount) (*retentionScreenfePb.ClosureChoiceComponent, error) {
	exitScreenNavigation := getRetentionExitScreenNavigation(retentionParam)
	deeplinkV3, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_INVESTMENT_RETENTION_SCREEN, &retentionScreenOptionPb.RetentionScreenOptions{
		Params: &retentionScreenfePb.RetentionScreenParams{
			InstrumentData: retentionParam.GetInstrumentData(),
			VisitedScreens: exitScreenNavigation,
		}})
	if err != nil {
		return nil, errors.Wrap(err, "error while getting deeplink")
	}
	return &retentionScreenfePb.ClosureChoiceComponent{
		Header: commontypes.GetTextFromStringFontColourFontStyle(MaturityChoiceScreenTitle, "#6A6D70", commontypes.FontStyle_HEADLINE_M),
		Body: &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%s%% ", depositAcc.GetInterestRate()), "#313234", commontypes.FontStyle_NUMBER_2XL), commontypes.GetTextFromStringFontColourFontStyle("p.a", "#B2B5B9", commontypes.FontStyle_NUMBER_M)},
		},
		Footer: &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("👍 Returns guaranteed on Deposits", "#648E4D", commontypes.FontStyle_BODY_XS)},
		},
		NavigationDeeplink: deeplinkV3,
		NextIcon:           getNextActionVisualElement(),
		BgColor:            "#F6F9FD",
		Shadows: []*ui.Shadow{
			{
				Height:  4,
				Opacity: 1,
				Colour:  &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
			},
		},
	}, nil
}

func getRetentionExitScreenNavigation(retentionParam *retentionScreenfePb.RetentionScreenParams) []retentionScreenfePb.RetentionScreen {
	res := make([]retentionScreenfePb.RetentionScreen, 0)
	for _, screen := range retentionParam.GetVisitedScreens() {
		res = append(res, screen)
	}
	res = append(res, retentionScreenfePb.RetentionScreen_RetentionScreen_Retention_EXIT_SCREEN)
	return res
}
