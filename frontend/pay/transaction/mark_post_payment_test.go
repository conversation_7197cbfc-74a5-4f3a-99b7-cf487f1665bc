package transaction

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/frontend"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/order"
	orderMock "github.com/epifi/gamma/api/order/mocks"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	payMocksPb "github.com/epifi/gamma/api/pay/mocks"
	"github.com/epifi/gamma/api/pay/signal"
	pkgPay "github.com/epifi/gamma/api/pkg/pay"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	rpMocks "github.com/epifi/gamma/api/recurringpayment/mocks"
)

type mockDependencies struct {
	mockPayV1Client            *payMocksPb.MockPayClient
	mockRecurringPaymentClient *rpMocks.MockRecurringPaymentServiceClient
	mockOrderClient            *orderMock.MockOrderServiceClient
}

func getMockServiceForMarkPostPayment(ctr *gomock.Controller) (*Service, *mockDependencies) {
	mockPayV1Client := payMocksPb.NewMockPayClient(ctr)
	mockRecurringPaymentClient := rpMocks.NewMockRecurringPaymentServiceClient(ctr)
	mockOrderClient := orderMock.NewMockOrderServiceClient(ctr)

	return &Service{
			payV1Client:            mockPayV1Client,
			recurringPaymentClient: mockRecurringPaymentClient,
			orderClient:            mockOrderClient,
		}, &mockDependencies{
			mockPayV1Client:            mockPayV1Client,
			mockRecurringPaymentClient: mockRecurringPaymentClient,
			mockOrderClient:            mockOrderClient,
		}
}

var (
	defaultOrchMd = &pay.ClientIdentificationTxnMetaData{
		ClientReqId:     "client-req-id-1",
		Workflow:        order.OrderWorkflow_P2P_FUND_TRANSFER,
		EntityOwnership: common.Ownership_LIQUILOANS_PL,
		DomainOrderData: &pay.DomainOrderData{
			NextAction:                            &deeplinkpb.Deeplink{ScreenOptionsV2: nil},
			AccountsEligibleForPaymentFulfillment: nil,
			Amount:                                nil,
			ClientRequestIdExpiry:                 timestamppb.New(time.Date(2025, 01, 13, 01, 01, 01, 00, datetime.IST)),
			ToPi:                                  "to-pi-1",
			ToActor:                               "to-actor-1",
			FromPi:                                "from-pi-1",
		},
		TargetActorId: "from-actor-1",
	}

	beOrder1 = &order.Order{
		Id:                    "order-1",
		FromActorId:           "from-actor-1",
		ToActorId:             "to-actor-1",
		Workflow:              order.OrderWorkflow_P2P_FUND_TRANSFER,
		Status:                order.OrderStatus_CREATED,
		PostPaymentDeeplinkV1: &deeplinkpb.Deeplink{Screen: deeplinkpb.Screen_LOANS_STATUS_POLL_SCREEN},
	}

	beTxn1 = &payment.Transaction{
		Id:          "txn-1",
		PiFrom:      "pi-from-1",
		PiTo:        "pi-to-1",
		Utr:         "utr-1",
		PartnerBank: commonvgpb.Vendor_RAZORPAY,
		Status:      payment.TransactionStatus_SUCCESS,
		Ownership:   common.Ownership_LIQUILOANS_PL,
	}
)

func Test_MarkPostPayment(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)

	defaultOrchMdBytes, err := proto.Marshal(defaultOrchMd)
	require.NoError(t, err, "error in marshalling default orch metadata into proto bytes")

	tests := []struct {
		name       string
		req        *transaction.MarkPostPaymentRequest
		res        *transaction.MarkPostPaymentResponse
		err        error
		setupMocks func(req *transaction.MarkPostPaymentRequest, md *mockDependencies)
	}{
		{
			name: "Should signal recurring payment authorization and return ok status when recurring payment id is passed",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				RecurringPaymentId: "rp-id-1",
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					AuthorizeCreationV1(gomock.Any(), &recurringpaymentpb.AuthorizeCreationV1Request{RecurringPaymentId: req.GetRecurringPaymentId()}).
					Return(&recurringpaymentpb.AuthorizeCreationV1Response{Status: rpc.StatusOk()}, nil)
			},
		},
		{
			name: "Should signal recurring payment authorization and return ok status when client req ID is passed",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status:             rpc.StatusOk(),
						RecurringPaymentId: "rp-id-1",
					}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					AuthorizeCreationV1(gomock.Any(), &recurringpaymentpb.AuthorizeCreationV1Request{RecurringPaymentId: "rp-id-1"}).
					Return(&recurringpaymentpb.AuthorizeCreationV1Response{Status: rpc.StatusOk()}, nil)
			},
		},
		{
			name: "should return internal status code when RPC is invoked for one-time payment flow and no order exists",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				OrderId:               "order-1",
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternal()},
			},
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{
						OrderId: req.GetOrderId(),
					}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status:                rpc.StatusRecordNotFound(),
						OrderWithTransactions: nil,
					}, nil)
			},
		},
		{
			name: "Should return OK status code when called for recurring payment flow and AuthorizeCreationV1 RPC returns StatusFailedPrecondition",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status:             rpc.StatusOk(),
						RecurringPaymentId: "rp-id-1",
					}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					AuthorizeCreationV1(gomock.Any(), &recurringpaymentpb.AuthorizeCreationV1Request{RecurringPaymentId: "rp-id-1"}).
					Return(&recurringpaymentpb.AuthorizeCreationV1Response{Status: rpc.StatusFailedPrecondition()}, nil)
			},
		},
		{
			name: "Should return internal error for errors from AuthorizeCreationV1 RPC other than failed precondition error",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternal()},
			},
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status:             rpc.StatusOk(),
						RecurringPaymentId: "rp-id-1",
					}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					AuthorizeCreationV1(gomock.Any(), &recurringpaymentpb.AuthorizeCreationV1Request{RecurringPaymentId: "rp-id-1"}).
					Return(&recurringpaymentpb.AuthorizeCreationV1Response{Status: rpc.StatusInternal()}, nil)
			},
		},
		{
			name: "should return post payment deeplink when the RPC is called for one-time payment & payment completed event and the order & transaction are created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_COMPLETED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: &deeplinkpb.Deeplink{Screen: deeplinkpb.Screen_LOANS_STATUS_POLL_SCREEN},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order:        beOrder1,
							Transactions: []*payment.Transaction{beTxn1},
						},
					}, nil)
			},
		},
		{
			name: "should return post payment deeplink when the RPC is called for one-time payment & payment cancelled event and the order & transaction are created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_CANCELLED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: &deeplinkpb.Deeplink{Screen: deeplinkpb.Screen_LOANS_STATUS_POLL_SCREEN},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order:        beOrder1,
							Transactions: []*payment.Transaction{beTxn1},
						},
					}, nil)
			},
		},
		{
			name: "should return post payment deeplink when the RPC is called for one-time payment & payment cancelled event and only the order is created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_CANCELLED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: &deeplinkpb.Deeplink{Screen: deeplinkpb.Screen_LOANS_STATUS_POLL_SCREEN},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order: beOrder1,
						},
					}, nil)
			},
		},
		{
			name: "should gracefully handle and return StatusOk when the RPC is called for one-time payment & payment cancelled event and no entities are created and no eventNotifier is configured for the entry point",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_CANCELLED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: nil,
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
		},
		{
			name: "should return gracefully return StatusOk when the RPC is called for one-time payment & payment cancelled event with unspecified version of CreateFundTransferOrder and no entities are created and no eventNotifier is configured for the entry point",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_CREATE_FUND_TRANSFER_ORDER_VERSION_UNSPECIFIED,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_CANCELLED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: nil,
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
		},
		{
			name: "should return post payment when RPC is called for one-time payment & payment expiration event and order & transactions are created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_EXPIRED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: beOrder1.GetPostPaymentDeeplinkV1(),
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order:        beOrder1,
							Transactions: []*payment.Transaction{beTxn1},
						},
					}, nil)
			},
		},
		{
			name: "should return post payment when RPC is called for one-time payment & payment expiration event and order is created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_EXPIRED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: beOrder1.GetPostPaymentDeeplinkV1(),
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order: beOrder1,
						},
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					SignalWorkflow(gomock.Any(), &pay.SignalWorkflowRequest{
						SignalIdentifierAndPayload: &pay.SignalWorkflowRequest_PgFundTransferCompleteSignalPayload{
							PgFundTransferCompleteSignalPayload: &pay.PgFundTransferCompleteSignalPayload{
								OrderId:     beOrder1.GetId(),
								ClientReqId: beOrder1.GetClientReqId(),
								SignalPayload: &signal.PgFundTransferCompleteSignal{
									InternalOrderId: beOrder1.GetId(),
									ActionType:      pkgPay.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_EXPIRED,
									Payload:         &signal.ClientSdkResponsePayload{},
								},
							},
						},
					}).
					Return(&pay.SignalWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
		},
		{
			name: "should gracefully when RPC is called for one-time payment & payment expiration event and no entities are created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                    beOrder1.GetId(),
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_EXPIRED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
		},
		{
			name: "should return post payment when RPC is called for one-time payment & payment init failed event and order & transactions are created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                 beOrder1.GetId(),
				TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:              transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_INITIATION_FAILURE,
				PgClientSdkResponsePayload: &transaction.MarkPostPaymentRequest_RazorpayClientFailurePayload{
					RazorpayClientFailurePayload: &transaction.RazorpayFailurePayload{
						HttpStatusCode: "400",
						Error: &transaction.RazorpayClientSdkError{
							Code:        "BAD_REQUEST_ERROR",
							Description: "The number is invalid.",
							Source:      "business",
							Step:        "payment_initiation",
							Reason:      "input_validation_failed",
							Field:       "number",
						},
					},
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: beOrder1.GetPostPaymentDeeplinkV1(),
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order:        beOrder1,
							Transactions: []*payment.Transaction{beTxn1},
						},
					}, nil)
			},
		},
		{
			name: "should return post payment when RPC is called for one-time payment & payment init failed event and order is created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                 beOrder1.GetId(),
				TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:              transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_INITIATION_FAILURE,
				PgClientSdkResponsePayload: &transaction.MarkPostPaymentRequest_RazorpayClientFailurePayload{
					RazorpayClientFailurePayload: &transaction.RazorpayFailurePayload{
						HttpStatusCode: "400",
						Error: &transaction.RazorpayClientSdkError{
							Code:        "BAD_REQUEST_ERROR",
							Description: "The number is invalid.",
							Source:      "business",
							Step:        "payment_initiation",
							Reason:      "input_validation_failed",
							Field:       "number",
						},
					},
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: beOrder1.GetPostPaymentDeeplinkV1(),
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &pay.OrderWithTransactions{
							Order: beOrder1,
						},
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					SignalWorkflow(gomock.Any(), &pay.SignalWorkflowRequest{
						SignalIdentifierAndPayload: &pay.SignalWorkflowRequest_PgFundTransferCompleteSignalPayload{
							PgFundTransferCompleteSignalPayload: &pay.PgFundTransferCompleteSignalPayload{
								OrderId:     beOrder1.GetId(),
								ClientReqId: beOrder1.GetClientReqId(),
								SignalPayload: &signal.PgFundTransferCompleteSignal{
									InternalOrderId: beOrder1.GetId(),
									ActionType:      pkgPay.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_INITIATION_FAILURE,
									Payload: &signal.ClientSdkResponsePayload{
										Payload: &signal.ClientSdkResponsePayload_RazorpayClientFailurePayload_{
											RazorpayClientFailurePayload: &signal.ClientSdkResponsePayload_RazorpayClientFailurePayload{
												HttpStatusCode: "400",
												Error: &signal.ClientSdkResponsePayload_RazorpayClientFailurePayload_RazorpayClientSdkError{
													Code:        "BAD_REQUEST_ERROR",
													Description: "The number is invalid.",
													Source:      "business",
													Step:        "payment_initiation",
													Reason:      "input_validation_failed",
													Field:       "number",
												},
											},
										},
									},
								},
							},
						},
					}).
					Return(&pay.SignalWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
		},
		{
			name: "should gracefully return status OK when RPC is called for one-time payment & payment init failed event and no entities are created",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				OrderId:                 beOrder1.GetId(),
				TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED,
				ActionType:              transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_INITIATION_FAILURE,
				PgClientSdkResponsePayload: &transaction.MarkPostPaymentRequest_RazorpayClientFailurePayload{
					RazorpayClientFailurePayload: &transaction.RazorpayFailurePayload{
						HttpStatusCode: "400",
						Error: &transaction.RazorpayClientSdkError{
							Code:        "BAD_REQUEST_ERROR",
							Description: "The number is invalid.",
							Source:      "business",
							Step:        "payment_initiation",
							Reason:      "input_validation_failed",
							Field:       "number",
						},
					},
				},
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockPayV1Client.
					EXPECT().
					GetOrderWithTransactions(gomock.Any(), &pay.GetOrderWithTransactionsRequest{OrderId: beOrder1.GetId()}).
					Return(&pay.GetOrderWithTransactionsResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
		},
		{
			name: "should return post payment deeplink when orderId is empty and order is fetched using clientReqId",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_COMPLETED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				NextAction: &deeplinkpb.Deeplink{Screen: deeplinkpb.Screen_LOANS_STATUS_POLL_SCREEN},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockOrderClient.
					EXPECT().
					GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{ClientReqId: defaultOrchMd.GetClientReqId()},
							},
						},
					}).
					Return(&order.GetOrdersWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: []*order.OrderWithTransactions{
							{
								Order:        beOrder1,
								Transactions: []*payment.Transaction{beTxn1},
							},
						},
					}, nil)
			},
		},
		{
			name: "should gracefully return when orderId is empty and no orders found for clientReqId",
			req: &transaction.MarkPostPaymentRequest{
				Req:                   &feHeaderPb.RequestHeader{},
				OrchestrationMetadata: defaultOrchMdBytes,
				IntegrationSpecs: &transaction.IntegrationSpecs{
					Vendor:                         frontend.Vendor_RAZORPAY,
					IntegrationMode:                transaction.IntegrationMode_MOBILE_SDK,
					CreateFundTransferOrderVersion: transaction.IntegrationSpecs_VERSION_V1,
				},
				TransactionUiEntryPoint:    timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
				ActionType:                 transaction.PaymentOptionsActionType_PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_COMPLETED,
				PgClientSdkResponsePayload: nil,
			},
			res: &transaction.MarkPostPaymentResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			err: nil,
			setupMocks: func(req *transaction.MarkPostPaymentRequest, md *mockDependencies) {
				md.mockPayV1Client.
					EXPECT().
					GetPlainData(gomock.Any(), &pay.GetPlainDataRequest{SignedData: req.GetOrchestrationMetadata()}).
					Return(&pay.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: defaultOrchMdBytes}, nil)

				md.mockRecurringPaymentClient.
					EXPECT().
					GetRecurringPaymentDetailsByClientReqId(gomock.Any(), &recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdRequest{ClientReqId: defaultOrchMd.GetClientReqId()}).
					Return(&recurringpaymentpb.GetRecurringPaymentDetailsByClientReqIdResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				md.mockOrderClient.
					EXPECT().
					GetOrdersWithTransactions(gomock.Any(), &order.GetOrdersWithTransactionsRequest{
						OrderIdentifiers: []*order.OrderIdentifier{
							{
								Identifier: &order.OrderIdentifier_ClientReqId{ClientReqId: defaultOrchMd.GetClientReqId()},
							},
						},
					}).
					Return(&order.GetOrdersWithTransactionsResponse{
						Status:                rpc.StatusOk(),
						OrderWithTransactions: []*order.OrderWithTransactions{},
					}, nil)
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			service, md := getMockServiceForMarkPostPayment(ctr)

			tc.setupMocks(tc.req, md)

			gotRes, gotErr := service.MarkPostPayment(context.Background(), tc.req)
			if tc.err != nil {
				require.Equal(t, tc.err, gotErr, "expected RPC error is not same as received RPC error")
			}
			require.Equal(t, tc.res, gotRes, "expected RPC response is not same as received RPC response")
		})
	}
}
