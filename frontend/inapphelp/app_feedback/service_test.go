package app_feedback

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/header"
	feAppFeedback "github.com/epifi/gamma/api/frontend/inapphelp/app_feedback"
	appFeedbackPb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	mock_app_feedback "github.com/epifi/gamma/api/inapphelp/app_feedback/mocks"
	"github.com/epifi/gamma/frontend/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	feedbackReq1 = &feAppFeedback.IsEligibleForFeedbackRequest{
		AppFlow: feAppFeedback.AppFlow_APP_FLOW_REWARD_CLAIM_SUCCESS,
		Req: &header.RequestHeader{
			Auth: &header.AuthHeader{
				ActorId: "test-actor1",
				Device: &commontypes.Device{
					Platform: commontypes.Platform_ANDROID,
				},
			},
		},
	}
	beFeedbackReq1 = &appFeedbackPb.IsEligibleForFeedbackRequest{
		ActorId:  "test-actor1",
		AppFlow:  appFeedbackPb.AppFlow_APP_FLOW_REWARD_CLAIM_SUCCESS,
		Platform: commontypes.Platform_ANDROID,
	}
	updateReq1 = &feAppFeedback.UpdateFeedbackAttemptRequest{
		FeedbackAttemptId: "test123",
		FeedbackAsked:     commontypes.BooleanEnum_TRUE,
	}
	updateBeReq1 = &appFeedbackPb.UpdateFeedbackAttemptRequest{
		FeedbackAttemptId: "test123",
		FeedbackAsked:     commontypes.BooleanEnum_TRUE,
	}
	feGetQuestionsReq1 = &feAppFeedback.GetFeedbackQuestionsRequest{
		Req: &header.RequestHeader{
			Auth: &header.AuthHeader{
				ActorId: "test-actor1",
				Device: &commontypes.Device{
					Platform: commontypes.Platform_ANDROID,
				},
			},
		},
		AppScreen: appFeedbackPb.AppScreen_APP_SCREEN_SCREENER_TERMINAL.String(),
	}
	feGetQuestionsSuccessResp = &feAppFeedback.GetFeedbackQuestionsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Questions: []*feAppFeedback.QuestionInfo{
			{
				QuestionCode:    "screener_terminal_q_1",
				ActualQuestion:  "TELL US ABOUT YOUR EXPERIENCE WITH FI",
				AnswerDataType:  feAppFeedback.AnswerDataType_ANSWER_DATA_TYPE_EMOJI_RATING,
				IsOptional:      false,
				CheckboxOptions: &feAppFeedback.CheckBoxOptions{},
			},
			{
				QuestionCode:    "screener_terminal_q_2",
				ActualQuestion:  "WHAT CAN WE DO TO IMPROVE?",
				AnswerDataType:  feAppFeedback.AnswerDataType_ANSWER_DATA_TYPE_TEXT,
				IsOptional:      true,
				CheckboxOptions: &feAppFeedback.CheckBoxOptions{},
			},
		},
	}
	beGetQuestionsReq1 = &appFeedbackPb.GetFeedbackQuestionsRequest{
		ActorId:   "test-actor1",
		AppScreen: appFeedbackPb.AppScreen_APP_SCREEN_SCREENER_TERMINAL.String(),
	}
	beGetQuestionsSuccessResp = &appFeedbackPb.GetFeedbackQuestionsResponse{
		Status: rpcPb.StatusOk(),
		Questions: []*appFeedbackPb.QuestionInfo{
			{
				QuestionCode:    "screener_terminal_q_1",
				ActualQuestion:  "TELL US ABOUT YOUR EXPERIENCE WITH FI",
				AnswerDataType:  appFeedbackPb.AnswerDataType_ANSWER_DATA_TYPE_EMOJI_RATING,
				IsOptional:      false,
				CheckboxOptions: &appFeedbackPb.CheckBoxOptions{},
			},
			{
				QuestionCode:    "screener_terminal_q_2",
				ActualQuestion:  "WHAT CAN WE DO TO IMPROVE?",
				AnswerDataType:  appFeedbackPb.AnswerDataType_ANSWER_DATA_TYPE_TEXT,
				IsOptional:      true,
				CheckboxOptions: &appFeedbackPb.CheckBoxOptions{},
			},
		},
	}
	feSubmitFeedbackInvalidReq = &feAppFeedback.SubmitUserFeedbackRequest{
		Req: &header.RequestHeader{
			Auth: &header.AuthHeader{
				ActorId: "test-actor1",
				Device: &commontypes.Device{
					Platform: commontypes.Platform_ANDROID,
				},
			},
		},
		AppScreen: appFeedbackPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL.String(),
		ScreenMeta: map[string]string{
			"test-key": "test-value",
		},
		FeedbackResponse: &feAppFeedback.FeedbackResponse{
			ResultList: []*feAppFeedback.FeedbackResponseResult{
				{
					QuestionCode: "screener_terminal_q_1",
					Answer:       nil,
				},
			},
		},
	}
	feSubmitFeedbackReq = &feAppFeedback.SubmitUserFeedbackRequest{
		Req: &header.RequestHeader{
			Auth: &header.AuthHeader{
				ActorId: "test-actor1",
				Device: &commontypes.Device{
					Platform: commontypes.Platform_ANDROID,
				},
			},
		},
		AppScreen: appFeedbackPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL.String(),
		ScreenMeta: map[string]string{
			"test-key": "test-value",
		},
		FeedbackResponse: &feAppFeedback.FeedbackResponse{
			ResultList: []*feAppFeedback.FeedbackResponseResult{
				{
					QuestionCode: "screener_terminal_q_1",
					Answer: &feAppFeedback.FeedbackResponseResult_EmojiRatingValue{
						EmojiRatingValue: 3,
					},
				},
				{
					QuestionCode: "screener_terminal_q_2",
					Answer: &feAppFeedback.FeedbackResponseResult_TextValue{
						TextValue: "test text",
					},
				},
			},
		},
	}
	beSubmitFeedbackReq = &appFeedbackPb.SubmitUserFeedbackRequest{
		ActorId:   "test-actor1",
		AppScreen: appFeedbackPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL.String(),
		ScreenMeta: map[string]string{
			"test-key": "test-value",
		},
		FeedbackResponse: &appFeedbackPb.FeedbackResponse{
			ResultList: []*appFeedbackPb.FeedbackResponseResult{
				{
					QuestionCode: "screener_terminal_q_1",
					Answer: &appFeedbackPb.FeedbackResponseResult_EmojiRatingValue{
						EmojiRatingValue: 3,
					},
				},
				{
					QuestionCode: "screener_terminal_q_2",
					Answer: &appFeedbackPb.FeedbackResponseResult_TextValue{
						TextValue: "test text",
					},
				},
			},
		},
	}
)

func TestService_IsEligibleForFeedback(t *testing.T) {
	ctr := gomock.NewController(t)

	mockBeClient := mock_app_feedback.NewMockAppFeedbackClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *feAppFeedback.IsEligibleForFeedbackRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *feAppFeedback.IsEligibleForFeedbackResponse
		wantErr bool
	}{
		{
			name: "error returned from backend",
			args: args{
				ctx: context.Background(),
				req: feedbackReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().IsEligibleForFeedback(context.Background(), beFeedbackReq1).
						Return(&appFeedbackPb.IsEligibleForFeedbackResponse{Status: rpcPb.StatusInternal()}, nil),
				},
			},
			want: &feAppFeedback.IsEligibleForFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr: false,
		},
		{
			name: "user not eligible",
			args: args{
				ctx: context.Background(),
				req: feedbackReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().IsEligibleForFeedback(context.Background(), beFeedbackReq1).
						Return(&appFeedbackPb.IsEligibleForFeedbackResponse{
							Status:         rpcPb.StatusOk(),
							CanAskFeedback: commontypes.BooleanEnum_FALSE,
						}, nil),
				},
			},
			want: &feAppFeedback.IsEligibleForFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusOk(),
				},
				CanAskFeedback: commontypes.BooleanEnum_FALSE,
			},
			wantErr: false,
		},
		{
			name: "user eligible",
			args: args{
				ctx: context.Background(),
				req: feedbackReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().IsEligibleForFeedback(context.Background(), beFeedbackReq1).
						Return(&appFeedbackPb.IsEligibleForFeedbackResponse{
							Status:            rpcPb.StatusOk(),
							CanAskFeedback:    commontypes.BooleanEnum_TRUE,
							FeedbackAttemptId: "test123",
						}, nil),
				},
			},
			want: &feAppFeedback.IsEligibleForFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusOk(),
				},
				CanAskFeedback:    commontypes.BooleanEnum_TRUE,
				FeedbackAttemptId: "test123",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewAppFeedbackService(mockBeClient)
			got, err := s.IsEligibleForFeedback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsEligibleForFeedback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IsEligibleForFeedback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_UpdateFeedbackAttempt(t *testing.T) {
	ctr := gomock.NewController(t)

	mockBeClient := mock_app_feedback.NewMockAppFeedbackClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *feAppFeedback.UpdateFeedbackAttemptRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *feAppFeedback.UpdateFeedbackAttemptResponse
		wantErr bool
	}{
		{
			name: "record not found error returned from be",
			args: args{
				ctx: context.Background(),
				req: updateReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().UpdateFeedbackAttempt(context.Background(), updateBeReq1).
						Return(&appFeedbackPb.UpdateFeedbackAttemptResponse{
							Status: rpcPb.StatusRecordNotFound(),
						}, nil),
				},
			},
			want: &feAppFeedback.UpdateFeedbackAttemptResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusRecordNotFound(),
				},
			},
			wantErr: false,
		},
		{
			name: "internal error returned from be",
			args: args{
				ctx: context.Background(),
				req: updateReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().UpdateFeedbackAttempt(context.Background(), updateBeReq1).
						Return(&appFeedbackPb.UpdateFeedbackAttemptResponse{
							Status: rpcPb.StatusInternal(),
						}, nil),
				},
			},
			want: &feAppFeedback.UpdateFeedbackAttemptResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: updateReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().UpdateFeedbackAttempt(context.Background(), updateBeReq1).
						Return(&appFeedbackPb.UpdateFeedbackAttemptResponse{
							Status: rpcPb.StatusOk(),
						}, nil),
				},
			},
			want: &feAppFeedback.UpdateFeedbackAttemptResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusOk(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewAppFeedbackService(mockBeClient)
			got, err := s.UpdateFeedbackAttempt(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateFeedbackAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateFeedbackAttempt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetFeedbackQuestions(t *testing.T) {
	ctr := gomock.NewController(t)

	mockBeClient := mock_app_feedback.NewMockAppFeedbackClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *feAppFeedback.GetFeedbackQuestionsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *feAppFeedback.GetFeedbackQuestionsResponse
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: feGetQuestionsReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().GetFeedbackQuestions(context.Background(), beGetQuestionsReq1).
						Return(beGetQuestionsSuccessResp, nil),
				},
			},
			want:    feGetQuestionsSuccessResp,
			wantErr: false,
		},
		{
			name: "internal error from be rpc",
			args: args{
				ctx: context.Background(),
				req: feGetQuestionsReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().GetFeedbackQuestions(context.Background(), beGetQuestionsReq1).Return(
						&appFeedbackPb.GetFeedbackQuestionsResponse{Status: rpcPb.StatusInternal()}, nil),
				},
			},
			want: &feAppFeedback.GetFeedbackQuestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr: false,
		},
		{
			name: "grpc error from be service",
			args: args{
				ctx: context.Background(),
				req: feGetQuestionsReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().GetFeedbackQuestions(context.Background(), beGetQuestionsReq1).Return(
						nil, errors.New("failed")),
				},
			},
			want: &feAppFeedback.GetFeedbackQuestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr: false,
		},
		{
			name: "already exists error from be rpc",
			args: args{
				ctx: context.Background(),
				req: feGetQuestionsReq1,
				mocks: []interface{}{
					mockBeClient.EXPECT().GetFeedbackQuestions(context.Background(), beGetQuestionsReq1).Return(
						&appFeedbackPb.GetFeedbackQuestionsResponse{Status: rpcPb.StatusAlreadyExists()}, nil),
				},
			},
			want: &feAppFeedback.GetFeedbackQuestionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusAlreadyExists(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewAppFeedbackService(mockBeClient)
			got, err := s.GetFeedbackQuestions(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFeedbackQuestions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetFeedbackQuestions() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_SubmitUserFeedback(t *testing.T) {
	ctr := gomock.NewController(t)

	mockBeClient := mock_app_feedback.NewMockAppFeedbackClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *feAppFeedback.SubmitUserFeedbackRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *feAppFeedback.SubmitUserFeedbackResponse
		wantErr bool
	}{
		{
			name: "invalid answer type passed",
			args: args{
				ctx:   context.Background(),
				req:   feSubmitFeedbackInvalidReq,
				mocks: []interface{}{},
			},
			want: &feAppFeedback.SubmitUserFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInvalidArgument(),
				},
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: feSubmitFeedbackReq,
				mocks: []interface{}{
					mockBeClient.EXPECT().SubmitUserFeedback(context.Background(), beSubmitFeedbackReq).Return(
						&appFeedbackPb.SubmitUserFeedbackResponse{Status: rpcPb.StatusOk()}, nil),
				},
			},
			want: &feAppFeedback.SubmitUserFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name: "grpc error returned from be",
			args: args{
				ctx: context.Background(),
				req: feSubmitFeedbackReq,
				mocks: []interface{}{
					mockBeClient.EXPECT().SubmitUserFeedback(context.Background(), beSubmitFeedbackReq).Return(
						nil, errors.New("failed")),
				},
			},
			want: &feAppFeedback.SubmitUserFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr: false,
		},
		{
			name: "internal error returned from be",
			args: args{
				ctx: context.Background(),
				req: feSubmitFeedbackReq,
				mocks: []interface{}{
					mockBeClient.EXPECT().SubmitUserFeedback(context.Background(), beSubmitFeedbackReq).Return(
						&appFeedbackPb.SubmitUserFeedbackResponse{Status: rpcPb.StatusInternal()}, nil),
				},
			},
			want: &feAppFeedback.SubmitUserFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr: false,
		},
		{
			name: "already exists status returned from be",
			args: args{
				ctx: context.Background(),
				req: feSubmitFeedbackReq,
				mocks: []interface{}{
					mockBeClient.EXPECT().SubmitUserFeedback(context.Background(), beSubmitFeedbackReq).Return(
						&appFeedbackPb.SubmitUserFeedbackResponse{Status: rpcPb.StatusAlreadyExists()}, nil),
				},
			},
			want: &feAppFeedback.SubmitUserFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusAlreadyExists(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewAppFeedbackService(mockBeClient)
			got, err := s.SubmitUserFeedback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SubmitUserFeedback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("SubmitUserFeedback() got = %v, want %v", got, tt.want)
			}
		})
	}
}
