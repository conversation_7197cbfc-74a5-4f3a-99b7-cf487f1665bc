//nolint:funlen,unparam
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	cardPkg "github.com/epifi/gamma/pkg/card"

	"context"
	"fmt"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	beCasperPb "github.com/epifi/gamma/api/casper"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/rewards"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/card"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/firefly/helper"
)

const (
	primaryOffersTabId                = "CREDIT_CARD_PRIMARY_OFFERS_TAB_ID"
	secondaryOffersTabId              = "CREDIT_CARD_SECONDARY_OFFERS_TAB_ID"
	offerWidgetCardHeight             = 152
	offerWidgetCardWidth              = 136
	offerWidgetCardDefaultLeftMargin  = 12
	offerWidgetCardShadowHeight       = 4
	offerWidgetCardDefaultLeftMargin2 = 20
)

var (
	cardProgramTypeToExclusiveTagNameMap = map[types.CardProgramType]beCasperPb.TagName{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED:    beCasperPb.TagName_UNSPECIFIED_TAG_NAME,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:      beCasperPb.TagName_AMPLIFI_CREDIT_CARD_EXCLUSIVE,
		types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED: beCasperPb.TagName_MAGNIFI_CREDIT_CARD_EXCLUSIVE,
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:        beCasperPb.TagName_SIMPLIFI_CREDIT_CARD_EXCLUSIVE,
	}
	cardProgramTypeToSpecialTagNameMap = map[types.CardProgramType]beCasperPb.TagName{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED:    beCasperPb.TagName_UNSPECIFIED_TAG_NAME,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:      beCasperPb.TagName_AMPLIFI_CREDIT_CARD_CURATED,
		types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED: beCasperPb.TagName_MAGNIFI_CREDIT_CARD_CURATED,
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:        beCasperPb.TagName_SIMPLIFI_CREDIT_CARD_CURATED,
	}
	cardProgramTypeToCardTypeIdMap = map[types.CardProgramType]rewards.CardTypeId{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED:    rewards.CardTypeId_UNSPECIFIED,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:      rewards.CardTypeId_AMPLIFI_CREDIT_CARD_ID,
		types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED: rewards.CardTypeId_MAGNIFI_CREDIT_CARD_ID,
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:        rewards.CardTypeId_SIMPLIFI_CREDIT_CARD_ID,
	}
)

func (s *Service) buildOffersAndPromotionsSection(ctx context.Context, creditAccount *ffAccPb.CreditAccount, cardDesignEnhancementEnabled bool) (*card.OffersAndPromotionsSection, error) {
	var (
		tabDataMap = make(map[string]*card.TabData)
	)
	getOffersResp, err := s.rewardsListingClient.GetCardOffers(ctx, &beCasperPb.GetCardOffersRequest{
		RedemptionMode: beCasperPb.OfferRedemptionMode_FI_CREDIT_CARD,
		FiltersV2: &beCasperPb.CatalogFiltersV2{
			OrTags: []beCasperPb.TagName{
				cardProgramTypeToExclusiveTagNameMap[creditAccount.GetCardProgram().GetCardProgramType()],
			},
			AndTags: nil,
		},
	})
	if rpcErr := epifigrpc.RPCError(getOffersResp, err); rpcErr != nil {
		return nil, fmt.Errorf("error fetching offers from listing service: %w", rpcErr)
	}

	primaryOffersTabsData := buildOffersTabData(getOffersResp.GetOffers(), creditAccount.GetCardProgram().GetCardProgramType(), false, cardDesignEnhancementEnabled)
	if primaryOffersTabsData != nil {
		tabDataMap[primaryOffersTabId] = primaryOffersTabsData
	}
	secondaryOffersTabData := buildOffersTabData(getOffersResp.GetOffers(), creditAccount.GetCardProgram().GetCardProgramType(), true, cardDesignEnhancementEnabled)
	if secondaryOffersTabData != nil {
		tabDataMap[secondaryOffersTabId] = secondaryOffersTabData
	}

	return &card.OffersAndPromotionsSection{
		Filter: &ui.Filter{
			Tabs:              getTabs(creditAccount.GetCardProgram().GetCardProgramType(), primaryOffersTabsData, secondaryOffersTabData, cardDesignEnhancementEnabled),
			FilterType:        ui.Filter_FILTER_TYPE_SWITCH,
			DefaultTabSection: primaryOffersTabId,
		},
		TabDataMap: tabDataMap,
	}, nil
}

func buildOffersTabData(offers []*beCasperPb.Offer, cardProgramType types.CardProgramType, isSpecial bool, cardDesignEnhancementEnabled bool) *card.TabData {
	var (
		offersTabsData        []*components.Component
		specialOffersTagName  = beCasperPb.TagName_UNSPECIFIED_TAG_NAME
		totalValidOffersCount = 0
	)

	// Leading spacer for the first card
	offersTabsData = append(offersTabsData, &components.Component{
		Content: helper.GetAnyWithoutError(&components.Spacer{
			SpacingValue: components.Spacing_SPACING_M,
		}),
	})

	if isSpecial {
		specialOffersTagName = cardProgramTypeToSpecialTagNameMap[cardProgramType]
	}
	// build catalog offers widgets
	for _, offer := range offers {
		if specialOffersTagName != beCasperPb.TagName_UNSPECIFIED_TAG_NAME &&
			!lo.Contains(offer.GetTagsInfo().GetManualTags(), specialOffersTagName) {
			continue
		}
		leftMargin := offerWidgetCardDefaultLeftMargin
		if cardDesignEnhancementEnabled {
			leftMargin = offerWidgetCardDefaultLeftMargin2
		}
		// leftMargin is zero for the first card
		// Comparing with 1 because, we have a spacer added as 1st item
		if len(offersTabsData) == 1 {
			leftMargin = 0
		}
		var offersDetails *sections.DepthWiseListSection
		if cardDesignEnhancementEnabled {
			offersDetails = cardPkg.GetGenericOfferCardV2(offer, colors.ColorSnow, leftMargin, "")
		} else {
			offersDetails = getGenericOfferCard(offer, colors.ColorSnow, nil, leftMargin)
		}
		offersComponent := &components.Component{
			Content: helper.GetAnyWithoutError(offersDetails),
		}
		totalValidOffersCount++
		offersTabsData = append(offersTabsData, offersComponent)
	}

	// Trailing spacer for the last card
	offersTabsData = append(offersTabsData, &components.Component{
		Content: helper.GetAnyWithoutError(&components.Spacer{
			SpacingValue: components.Spacing_SPACING_S,
		}),
	})

	if totalValidOffersCount == 0 {
		return nil
	}

	offersDataBgColor := colors.ColorLightLayer1
	if cardDesignEnhancementEnabled {
		offersDataBgColor = colors.ColorSnow
	}

	offersData := &sections.Section{
		Content: &sections.Section_HorizontalListSection{
			HorizontalListSection: getHorizontalSection(offersTabsData, offersDataBgColor),
		},
	}

	return &card.TabData{
		Section: offersData,
		TopCta: &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("SEE ALL", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)},
			LeftImgTxtPadding:  0,
			RightImgTxtPadding: 0,
			Deeplink: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
					CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
						CardTypeId: cardProgramTypeToCardTypeIdMap[cardProgramType].String(),
					},
				},
			},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/offers-section-chevron-right-icon-4x.png", 16, 16),
		},
	}
}

func getTabs(cardProgram types.CardProgramType, primaryTabData, secondaryTabData *card.TabData, cardDesignEnhancementEnabled bool) []*ui.Tab {
	var (
		primaryTabText   = "All offers"
		secondaryTabText string
		res              = make([]*ui.Tab, 0)
	)
	switch cardProgram {
	case types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
		secondaryTabText = "Curated for you"
	case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		secondaryTabText = "20% off"
	case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
		secondaryTabText = "20% off"
	}
	if primaryTabData != nil {
		res = append(res, &ui.Tab{
			Id:          primaryOffersTabId,
			ActiveCta:   cardPkg.GetActiveStateTab(primaryTabText, cardDesignEnhancementEnabled),
			InactiveCta: cardPkg.GetInactiveStateTab(primaryTabText, colors.ColorMonochromeChalk, cardDesignEnhancementEnabled),
			BorderColor: homeFePb.GetHomeWidgetBorderColor(),
		})
	}
	if secondaryTabData != nil {
		res = append(res, &ui.Tab{
			Id:          secondaryOffersTabId,
			ActiveCta:   cardPkg.GetActiveStateTab(secondaryTabText, cardDesignEnhancementEnabled),
			InactiveCta: cardPkg.GetInactiveStateTab(secondaryTabText, colors.ColorMonochromeChalk, cardDesignEnhancementEnabled),
			BorderColor: homeFePb.GetHomeWidgetBorderColor(),
		})
	}
	return res
}
