//nolint:all
package home

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/pkg/errors"

	"go.uber.org/zap"

	"github.com/epifi/gamma/api/frontend/deeplink"
	contactUsPb "github.com/epifi/gamma/api/frontend/inapphelp/contact_us"
	cxDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/help"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"

	beTicketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/frontend/analytics"
	feHomePb "github.com/epifi/gamma/api/frontend/cx/home"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/cx/metrics"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/frontend/cx"
)

type Service struct {
	ticketClient     beTicketPb.TicketClient
	genConf          *genConf.Cx
	releaseEvaluator release.IEvaluator
	userClient       usersPb.UsersClient
	contactUsGenConf *genConf.InAppContactUsFlowConfig
}

func NewCXHomeService(ticketClient beTicketPb.TicketClient, genConf *genConf.Cx, releaseEvaluator release.IEvaluator,
	userClient usersPb.UsersClient, contactUsGenConf *genConf.InAppContactUsFlowConfig) *Service {
	return &Service{
		ticketClient:     ticketClient,
		genConf:          genConf,
		releaseEvaluator: releaseEvaluator,
		userClient:       userClient,
		contactUsGenConf: contactUsGenConf,
	}
}

var _ feHomePb.HomeServer = &Service{}

func (s *Service) GetSupportTicketWidget(ctx context.Context, req *feHomePb.GetSupportTicketWidgetRequest) (*feHomePb.GetSupportTicketWidgetResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	isDeviceIOS := req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_IOS
	if actorId == "" {
		logger.Error(ctx, "actor id is empty in GetSupportTicketWidgetRequest")
	}

	isCXLandingPageV2EnabledForUser, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_CX_NEW_LANDING_PAGE).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error while checking for cx landing page v2 is enabled for user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	chipParams, err := s.getChipParams(ctx, req, isCXLandingPageV2EnabledForUser)
	if err != nil {
		logger.Error(ctx, "error while fetching chip params", zap.Error(err))
		return &feHomePb.GetSupportTicketWidgetResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}

	metrics.RecordContactOptionMetric(req.GetAnalyticsScreenName().String(), s.getConcatenatedChipTitles(chipParams),
		req.GetReq().GetAuth().GetDevice().GetPlatform().String(), req.GetReq().GetAuth().GetDevice().GetAppVersion())

	// in case displaying ticket is not required, directly return the widget without ticket details
	screenName := req.GetAnalyticsScreenName()
	if !s.isTicketRequired(req.GetAnalyticsScreenName()) {
		return s.homeWidgetWithoutTicket(chipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName), nil
	}

	ticketResp, ticketErr := s.ticketClient.GetSupportTicketsForApp(ctx, &beTicketPb.GetSupportTicketsForAppRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		TicketFilters: &beTicketPb.TicketFiltersForUser{
			StatusList: []beTicketPb.TicketStatusForUser{
				beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
				beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED,
				beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER,
			},
		},
		ShouldUseCache: true,
	})
	if te := epifigrpc.RPCError(ticketResp, ticketErr); te != nil || len(ticketResp.GetTickets()) == 0 {
		logger.Error(ctx, "failed to fetch tickets from BE service", zap.Error(te))
		// in case of failure in fetching ticket, we will send the widget without ticket details populated
		// this is done so that even if ticket is not present CX home component is displayed (chat with us/ help) section
		return s.homeWidgetWithoutTicket(chipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName), nil
	}
	// sort all the tickets in descending order of last interacted at
	// as we want to show latest updated ticket on home banner
	allTickets := ticketResp.GetTickets()
	sort.Slice(allTickets, func(i, j int) bool {
		return allTickets[i].GetLastInteractionTime().AsTime().After(allTickets[j].GetLastInteractionTime().AsTime())
	})
	// performing a check to ensure the ticket which was last updated is not very old ticket
	// it is a product call to only show ticket if the last update was within 2 weeks
	if time.Now().Sub(allTickets[0].GetLastInteractionTime().AsTime()) > s.genConf.Ticket().MaxDurationToShowTicketUpdateOnHome() {
		return s.homeWidgetWithoutTicket(chipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName), nil
	}

	return s.homeWidgetWithTicket(allTickets[0], chipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName), nil
}

func (s *Service) homeWidgetWithoutTicket(chipParams []*IconTextChipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS bool, screenName analytics.AnalyticsScreenName) *feHomePb.GetSupportTicketWidgetResponse {
	return &feHomePb.GetSupportTicketWidgetResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Title: &commontypes.Text{
			FontColor: colors.ColorNight,
			BgColor:   "",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: homeWidgetTitle,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
			},
			FontColorOpacity: 0,
			StringFormatters: nil,
			Alignment:        commontypes.Text_ALIGNMENT_LEFT,
		},
		VerticalKeyValuePairs: getVerticalKeyValuePairs(chipParams),
		CtasV2:                s.getSupportTicketWidgetCTAs(isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName),
	}
}

func (s *Service) homeWidgetWithTicket(ticket *beTicketPb.TicketDetailsForUser, chipParams []*IconTextChipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS bool, screenName analytics.AnalyticsScreenName) *feHomePb.GetSupportTicketWidgetResponse {
	// fetch basic widget without ticket and then populate ticket details
	widget := s.homeWidgetWithoutTicket(chipParams, isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName)
	widget.HomeSupportTicket = getSupportTicketWidget(ticket)
	widget.SupportTicketsCta = getSupportTicketsCTA()
	widget.CtasV2 = s.getSupportTicketWidgetCTAs(isCXLandingPageV2EnabledForUser, isDeviceIOS, screenName)
	return widget
}

func (s *Service) isTicketRequired(screenName analytics.AnalyticsScreenName) bool {
	return s.genConf.Ticket().IsDisplayingTicketOnHomeEnabled() && screenName == analytics.AnalyticsScreenName_HOME_LANDING_V2
}

func (s *Service) getChipParams(ctx context.Context, req *feHomePb.GetSupportTicketWidgetRequest, isCXLandingPageV2EnabledForUser bool) ([]*IconTextChipParams, error) {
	isNrAccountResp, isNrAccountErr := s.userClient.IsNonResidentUser(ctx, &usersPb.IsNonResidentUserRequest{
		Identifier: &usersPb.IsNonResidentUserRequest_ActorId{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		},
	})
	if rErr := epifigrpc.RPCError(isNrAccountResp, isNrAccountErr); rErr != nil {
		return nil, errors.Wrap(rErr, "error in fetching is non resident user")
	}
	isIssueReportingFlowEnabled := s.isIssueReportingFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId())

	return s.getChipParamsStrategy(isIssueReportingFlowEnabled, isNrAccountResp.GetIsNonResidentUser(),
		req.GetAnalyticsScreenName(), req.GetReq().GetAuth().GetDevice(), req.GetTicketId(), isCXLandingPageV2EnabledForUser), nil
}

func (s *Service) shouldHideBrowseFaq(device *commontypes.Device) bool {
	if device.GetPlatform() == commontypes.Platform_ANDROID {
		return int64(device.GetAppVersion()) >= s.genConf.CxHomeWidgetConfig().MinAndroidVersionForBrowseFaqDeprecation()
	}
	return int64(device.GetAppVersion()) >= s.genConf.CxHomeWidgetConfig().MinIosVersionForBrowseFaqDeprecation()
}

func (s *Service) isIssueReportingFlowEnabled(ctx context.Context, actorId string) bool {
	isEligible, releaseErr := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_FEATURE_IN_APP_ISSUE_REPORTING_FLOW).WithActorId(actorId))
	if releaseErr != nil {
		logger.Error(ctx, "error in release evaluator for feature FEATURE_IN_APP_ISSUE_REPORTING_FLOW", zap.Error(releaseErr))
	}
	return isEligible
}

func (s *Service) getConcatenatedChipTitles(chipParams []*IconTextChipParams) string {
	chipTitles := ""
	for _, chipParam := range chipParams {
		chipTitles += chipParam.TitleText + " | "
	}
	return chipTitles
}

func (s *Service) getChipParamsStrategy(isIssueReportingFlowEnabled bool, isNrUser commontypes.BooleanEnum,
	screeName analytics.AnalyticsScreenName, device *commontypes.Device, ticketId int64, isCXLandingPageV2EnabledForUser bool) []*IconTextChipParams {
	switch {
	case isNrUser == commontypes.BooleanEnum_TRUE:
		return s.getNrUserChipParams(screeName)
	case screeName == analytics.AnalyticsScreenName_CX_TICKET_DETAIL_SCREEN || ticketId != 0:
		return s.getTicketDetailChipParams(ticketId, isCXLandingPageV2EnabledForUser, screeName)
	case isIssueReportingFlowEnabled:
		return s.getIssueReportingChipParams(screeName, isCXLandingPageV2EnabledForUser)
	default:
		return s.getOlderVersionChipParams(screeName)
	}
}

func (s *Service) getNrUserChipParams(screeName analytics.AnalyticsScreenName) []*IconTextChipParams {
	if screeName == analytics.AnalyticsScreenName_HOME_LANDING_V2 {
		return []*IconTextChipParams{getHelpChipParams, nrUserEmailUsChipParams}
	}
	return []*IconTextChipParams{nrUserEmailUsChipParams}
}

func (s *Service) getIssueReportingChipParams(screenName analytics.AnalyticsScreenName, isCXLandingPageV2EnabledForUser bool) []*IconTextChipParams {
	contactUsChipParamsForUser := s.getContactUsChipParams(isCXLandingPageV2EnabledForUser, 0, screenName)
	if isCXLandingPageV2EnabledForUser && screenName == analytics.AnalyticsScreenName_CX_CATEGORY_SEARCH_SCREEN {
		return []*IconTextChipParams{s.getQueryScreenIconChipParam()}
	}
	return []*IconTextChipParams{contactUsChipParamsForUser}
}

func (s *Service) getContactUsChipParams(isCXLandingPageV2EnabledForUser bool, ticketId int64, screenName analytics.AnalyticsScreenName) *IconTextChipParams {
	if isCXLandingPageV2EnabledForUser {
		if ticketId != 0 || screenName == analytics.AnalyticsScreenName_CX_TICKET_DETAIL_SCREEN {
			return s.getQueryScreenIconChipParam()
		}
		return contactUsChipParamsV2
	}
	return contactUsChipParams
}

func (s *Service) getOlderVersionChipParams(screenName analytics.AnalyticsScreenName) []*IconTextChipParams {
	if screenName == analytics.AnalyticsScreenName_HOME_LANDING_V2 {
		return []*IconTextChipParams{getHelpChipParams, chatWithUsChipParams}
	}
	return []*IconTextChipParams{callUsChipParams, chatWithUsChipParams}
}

func (s *Service) getTicketDetailChipParams(ticketId int64, isCXLandingPageV2EnabledForUser bool, screeName analytics.AnalyticsScreenName) []*IconTextChipParams {
	return []*IconTextChipParams{s.getContactUsChipParams(isCXLandingPageV2EnabledForUser, ticketId, screeName)}
}

func (s *Service) getSupportTicketWidgetCTAs(isCXLandingPageV2EnabledForUser, isDeviceIOS bool, screenName analytics.AnalyticsScreenName) []*ui.IconTextComponent {
	supportTicketWidgetCTAs := []*ui.IconTextComponent{
		{
			Texts: []*commontypes.Text{
				{
					FontColor: "#313234",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Get Help",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_M},
				},
			},
			LeftImgTxtPadding:   4,
			Deeplink:            &deeplink.Deeplink{Screen: deeplink.Screen_HELP_MAIN},
			ContainerProperties: containerPropertiesForCTAV2,
			LeftVisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v3/cx-help-circle-icon.png", 40, 40),
		},
	}

	if !isDeviceIOS {
		supportTicketWidgetCTAs = append(supportTicketWidgetCTAs,
			&ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor: "#313234",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Chat with Fi",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_M},
					},
				},
				LeftImgTxtPadding:   4,
				Deeplink:            cx.GetContactUsDeeplink(),
				ContainerProperties: containerPropertiesForCTAV2,
				LeftVisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v3/cx-message-icon.png", 40, 40),
			})
	}

	if isCXLandingPageV2EnabledForUser {
		cxLandingPageV2Deeplink := &deeplink.Deeplink{
			Screen: deeplink.Screen_CONTACT_US_LANDING_SCREEN_V2,
		}
		supportTicketWidgetCTAs[0].Deeplink = cxLandingPageV2Deeplink
		if screenName == analytics.AnalyticsScreenName_CX_TICKET_DETAIL_SCREEN {
			supportTicketWidgetCTAs[0].Deeplink = s.getQueryScreenIconChipParam().Deeplink
		}
		if !isDeviceIOS {
			supportTicketWidgetCTAs = supportTicketWidgetCTAs[:1]
		}
	}
	return supportTicketWidgetCTAs
}

func (s *Service) getQueryScreenIconChipParam() *IconTextChipParams {
	queryValidationFailureMessage := fmt.Sprintf(s.contactUsGenConf.QueryValidationFailureMessage(),
		strconv.FormatInt(s.contactUsGenConf.MinQueryLength(), 10), strconv.FormatInt(s.contactUsGenConf.MinWordCount(), 10))
	validationFailureTextComponent := commontypes.GetTextFromStringFontColourFontStyleFontAlignment(queryValidationFailureMessage,
		"#E795AE", commontypes.FontStyle_BODY_XS, commontypes.Text_ALIGNMENT_LEFT)
	screenTitle := commontypes.GetTextFromStringFontColourFontStyleFontAlignment("What can we help you with?", colors.ColorLead,
		commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT)

	return &IconTextChipParams{
		IconUrl: "https://epifi-icons.pointz.in/contact_us/contact_us.png",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CONTACT_US_QUERY_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&cxDlOptions.CXHelpQueryScreenOptions{
				ScreenTitle: screenTitle,
				QueryValidationConfig: &contactUsPb.QueryValidationConfig{
					MinCharacterCount:    s.contactUsGenConf.MinQueryLength(),
					MaxCharacterCount:    s.contactUsGenConf.MaxQueryLength(),
					MinWordCount:         s.contactUsGenConf.MinWordCount(),
					ValidationFailureMsg: validationFailureTextComponent},
			})},
		TitleText: "Get help",
	}
}
