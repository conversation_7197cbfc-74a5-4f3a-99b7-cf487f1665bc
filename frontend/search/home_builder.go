package search

import (
	"context"
	"errors"
	"sync"

	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/search/landing_page"
)

type buildAllComponentsReq struct {
	actorID        string
	requestHeader  *header.RequestHeader
	componentOrder []string
	isFiLiteUser   bool
}

func (b *buildAllComponentsReq) getIsFiLiteUser() bool {
	if b == nil {
		return false
	}
	return b.isFiLiteUser
}

func (b *buildAllComponentsReq) getActorID() string {
	if b == nil {
		return ""
	}
	return b.actorID
}

func (b *buildAllComponentsReq) getComponentOrder() []string {
	if b == nil {
		return nil
	}
	return b.componentOrder
}

func (b *buildAllComponentsReq) getRequestHeader() *header.RequestHeader {
	if b == nil {
		return nil
	}
	return b.requestHeader
}

func (s *Service) buildAllComponents(ctx context.Context, req buildAllComponentsReq) (*landing_page.Layout, error) {
	components := make([]*landing_page.Component, len(req.getComponentOrder()))
	var wg sync.WaitGroup
	wg.Add(len(req.getComponentOrder()))
	for i, eachComponentID := range req.getComponentOrder() {
		ix := i
		eachComponentIDx := eachComponentID
		go func() {
			defer wg.Done()
			components[ix] = s.getComponentByID(ctx, req.getRequestHeader(), eachComponentIDx, req.getActorID())
		}()
	}
	wg.Wait()
	filteredComponents := filterComponents(components)
	if filteredComponents == nil {
		return nil, errors.New("no components retrieved for the actor")
	}
	return &landing_page.Layout{
		Components: filteredComponents,
	}, nil
}

func filterComponents(components []*landing_page.Component) []*landing_page.Component {
	var res []*landing_page.Component
	for _, eachComponent := range components {
		if eachComponent != nil {
			res = append(res, eachComponent)
		}
	}
	return res
}
