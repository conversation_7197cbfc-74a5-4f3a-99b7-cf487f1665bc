package mapper

import (
	authPb "github.com/epifi/gamma/api/auth"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/tsp/auth"
	"github.com/epifi/gamma/api/tsp/comms"
)

var TspToAuthOtpFlow = map[auth.GenerateOTPFlow]authPb.GenerateOTPFlow{
	auth.GenerateOTPFlow_GENERATE_OTP_FLOW_STOCKGUARDIAN_LOAN_APPLICATION_ESIGN: authPb.GenerateOTPFlow_GENERATE_OTP_FLOW_STOCKGUARDIAN_LOAN_APPLICATION_ESIGN,
}

var TspToCommsMedium = map[comms.Medium]commsPb.Medium{
	comms.Medium_SMS:   commsPb.Medium_SMS,
	comms.Medium_EMAIL: commsPb.Medium_EMAIL,
}
