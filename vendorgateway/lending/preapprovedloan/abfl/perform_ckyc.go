// nolint: dupl
package abfl

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	vgAbflPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	vendorAbflPb "github.com/epifi/gamma/api/vendors/abfl/lending"
	abflPkg "github.com/epifi/gamma/pkg/loans/abfl"
	"github.com/epifi/gamma/vendorgateway/abfl"
	"github.com/epifi/gamma/vendorgateway/config"
)

const dateLayout = "2006-01-02"

type PerformCkycRequest struct {
	*abfl.DefaultHeaderSetter
	*abfl.HeaderContentSetter
	Method string
	Req    *vgAbflPb.PerformCkycRequest
	Conf   *config.Abfl
}

func (c *PerformCkycRequest) HTTPMethod() string {
	return c.Method
}

func (c *PerformCkycRequest) URL() string {
	return c.Conf.Url + "/ckyc/performCKYC"
}

func (c *PerformCkycRequest) GetResponse() vendorapi.Response {
	return &PerformCkycResponse{}
}

func (c *PerformCkycRequest) Marshal() ([]byte, error) {
	requestPayload := &vendorAbflPb.PerformCkycRequest{
		AccountId: c.Req.GetAccountId(),
		Dob:       datetime.DateToString(c.Req.GetDob(), dateLayout, datetime.IST),
		PanNumber: c.Req.GetPanNumber(),
	}
	logger.DebugNoCtx("PerformCkycRequest request payload", zap.Any("request_payload", requestPayload))
	return protojson.Marshal(requestPayload)
}

type PerformCkycResponse struct {
}

func (c *PerformCkycResponse) Unmarshal(b []byte) (proto.Message, error) {
	logger.DebugNoCtx("PerformCkycResponse response payload", zap.String("response_payload", string(b)))
	res := vendorAbflPb.PerformCkycResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to abfl proto message")
	}

	vgResponse := &vgAbflPb.PerformCkycResponse{
		Status: rpc.StatusOk(),
		PerformCkycData: &vgAbflPb.PerformCkycResponse_PerformCkycData{
			RequestId: res.GetData().GetRequestId(),
		},
	}
	return vgResponse, nil
}

func (c *PerformCkycResponse) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := vendorAbflPb.PerformCkycResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to abfl proto message")
	}

	if httpStatus == 400 || httpStatus == 401 || httpStatus == 500 {
		if m, ok := abflPkg.ResponseStatus[res.GetResponseStatus()]; ok && !m {
			if len(res.GetErrors()) != 0 {
				return &vgAbflPb.PerformCkycResponse{
					Status: abflPkg.VendorErrorResponseCodesToRpcResponse(res.GetErrors()[0].GetCode(), res.GetErrors()[0].GetDescription()),
				}, nil
			}
			return &vgAbflPb.PerformCkycResponse{
				Status: abflPkg.VendorErrorResponseCodesToRpcResponse(res.GetError().GetCode(), res.GetError().GetDescription()),
			}, nil
		}
	}

	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
