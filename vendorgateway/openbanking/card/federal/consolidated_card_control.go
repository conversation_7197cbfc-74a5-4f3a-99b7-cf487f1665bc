package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	vgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vendorsFederal "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/api/vendors/responsemapping/card"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	vgPciConf "github.com/epifi/gamma/vendorgateway-pci/config"
	"github.com/epifi/gamma/vendorgateway/federal"
	federalCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"
)

var (
	statusCardControlDeviceTempDeactivated rpc.StatusFactoryWithDebugMsg = func(debugMsg string) *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(vgPb.ConsolidatedCardControlOnOffResponse_DEVICE_TEMPORARILY_DEACTIVATED_BY_BANK), debugMsg)
	}
)

type ConsolidatedCardControlReq struct {
	*defaultHeaderAdder
	*federalCryptor.DefaultPGPSecuredExchange

	Method    string
	Req       *vgPb.ConsolidatedCardControlOnOffRequest
	Url       string
	VgPciConf *vgPciConf.Config
}

var (
	consolidatedCardControlResponseActionToStatusFactoryMap = map[string]rpc.StatusFactoryWithDebugMsg{
		federal.SuccessResponseAction: rpc.StatusOkWithDebugMsg,
	}
	statusTechnicalFailure rpc.StatusFactoryWithDebugMsg = func(debugMsg string) *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(vgPb.ConsolidatedCardControlOnOffResponse_TECHNICAL_FAILURE), debugMsg)
	}
	consolidatedCardControlFailureResponseCodeToStatusFactoryMap = map[string]rpc.StatusFactoryWithDebugMsg{
		"OBE0001": statusTechnicalFailure,
		"OBE0002": statusTechnicalFailure,
		"OBE0003": statusTechnicalFailure,
		"OBE0004": statusTechnicalFailure,
		"OBE0007": statusTechnicalFailure,
		"OBE0046": statusTechnicalFailure,
		"OBE0045": statusTechnicalFailure,
		"OBE0044": statusTechnicalFailure,
		"OBE0043": statusTechnicalFailure,
		"F05":     statusTechnicalFailure,
		"F06":     statusTechnicalFailure,
		"F07":     statusTechnicalFailure,
		"F99":     statusTechnicalFailure,
		"F08":     statusTechnicalFailure,
		"18":      statusTechnicalFailure,
		"16":      statusTechnicalFailure,
		"N6":      statusTechnicalFailure,
		"15":      statusTechnicalFailure, // Request already Processed / Status cannot be changed
		"N5":      statusTechnicalFailure,
		"27":      statusTechnicalFailure,
		"OBE8888": statusTechnicalFailure,
		"OBE0059": statusTechnicalFailure,
		"OBE0011": statusTechnicalFailure,
		"OBE0066": statusTechnicalFailure,   // Invalid cred block
		"OBE0061": statusTechnicalFailure,   // TRANSACTION PIN IS NOT SET
		"OBE0062": StatusInvalidSecurePin,   // Invalid transaction pin
		"OBE0064": StatusPinRetriesExceeded, // Pin retries exceeded
		"OBE0072": statusTechnicalFailure,   // Cred Block Details Not Matching With Input Message Data
		"OBE0073": statusTechnicalFailure,   // Number Of Transactions Limit Exceeded, Please Contact Admin
		"OBE0060": statusTechnicalFailure,   // Invalid Customer Id
		"OBE0102": statusTechnicalFailure,
		"OBE0070": statusTechnicalFailure,
		"OBE0113": statusTechnicalFailure, // DeviceKeys not Registered for Given Details
		"OBE0114": statusTechnicalFailure, // Hmac does not match
		"OBE0115": statusTechnicalFailure, // Key id not matched with open bank key
		"OBE0067": statusTechnicalFailure, // No Data found with given Details
		"OBE0065": statusTechnicalFailure, // Account is Not Linked to the given device details
		"OBE0999": statusPanNotUpdated,    // PAN NOT UPDATED
		"OBE0170": statusCardControlDeviceTempDeactivated,
	}
)

const (
	consolidatedCardControlRequestType = "NEO_FHM_ONOFF_CONSL"
	cardStatus                         = "1"
	cardType                           = "00"
)

func (c ConsolidatedCardControlReq) Marshal() ([]byte, error) {
	var (
		commonPayload *map[string]string
	)
	if c.VgPciConf != nil {
		commonPayload = GetCommonRequestParams(c.VgPciConf)
	} else {
		commonPayload = federal.GetCommonRequestParams()
	}

	req := &vendorsFederal.ConsolidatedCardControlRequest{
		SenderCode:   (*commonPayload)[federal.SenderCodeKey],
		AccessId:     (*commonPayload)[federal.ServiceAccessIdKey],
		AccessCode:   (*commonPayload)[federal.ServiceAccessCodeKey],
		DeviceId:     c.Req.GetAuth().GetDeviceId(),
		DeviceToken:  c.Req.GetAuth().GetDeviceToken(),
		CredBlock:    c.Req.GetAuth().GetEncryptedPin(),
		CardUniqueId: c.Req.GetVendorCardId(),
		RequestId:    c.Req.GetRequestId(),
		RequestType:  consolidatedCardControlRequestType,
		AccountDetails: &vendorsFederal.AccountDetails{
			CustomerId:    c.Req.GetAuth().GetCustomerId(),
			AccountNumber: c.Req.GetAccountDetails().GetAccountNumber(),
			MobileNumber:  c.Req.GetAccountDetails().GetPhoneNumber().ToString(),
		},
	}
	req.CardDetails = getControlDetails(c.Req.GetControlDetails())
	req.BiometricFlag = YNStringFromBool(c.Req.GetDeviceBiometricEnabled())
	req.TokenValidation = YNStringFromBool(c.Req.GetTokenValidation())
	return protojson.Marshal(req)
}

func YNStringFromBool(flag bool) string {
	if flag {
		return "Y"
	}
	return "N"
}

// getControlDetails fetches the flag for each control from the request map in the request
func getControlDetails(controlsActionMap map[int32]vgPb.CardControlAction) *vendorsFederal.ConsolidatedCardControlRequest_CardDetails {
	return &vendorsFederal.ConsolidatedCardControlRequest_CardDetails{
		CardStatus:    cardStatus,
		CardType:      cardType,
		CardOnOff:     getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_SUSPEND)]),
		DomesticOnOff: getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_DOM)]),
		IntlOnOff:     getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_INTL)]),
		EcomOnOff:     getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ECOM)]),
		NfcOnOff:      getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_NFC)]),
		TokenQrOnOff:  getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_TOKEN_QR)]),
		TokenNfcOnOff: getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_TOKEN_NFC)]),
		TokenMstOnOff: getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_TOKEN_MST)]),
		TokenCofOnOff: getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_TOKEN_COF)]),
		TokenAppOnOff: getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_TOKEN_APP)]),
		AtmOnOff:      getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ATM)]),
		PosOnOff:      getControlFlag(controlsActionMap[int32(vgPb.CardControlType_CARD_CONTROL_TYPE_POS)]),
	}
}

// getControlFlag returns "Y" and "N" or space for a control based on the card control action flag
func getControlFlag(action vgPb.CardControlAction) string {
	switch action {
	case vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE:
		return "Y"
	case vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE:
		return "N"
	default:
		return " "
	}
}

func (cp ConsolidatedCardControlReq) HTTPMethod() string {
	return cp.Method
}

func (cp ConsolidatedCardControlReq) URL() string {
	return cp.Url
}

func (cc ConsolidatedCardControlReq) GetResponse() vendorapi.Response {
	return ConsolidatedCardControlResp{}
}

type ConsolidatedCardControlResp struct {
}

func (cp ConsolidatedCardControlResp) Unmarshal(b []byte) (proto.Message, error) {
	fedResp := &vendorsFederal.ConsolidatedCardControlResponse{}
	resp := &vgPb.ConsolidatedCardControlOnOffResponse{}

	unMarshalOptions := protojson.UnmarshalOptions{}
	unMarshalOptions.DiscardUnknown = true
	err := unMarshalOptions.Unmarshal(b, fedResp)
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("Could not parse consolidated card control response '%s'", string(b)))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}
	internalResponseCode := card.GetCardResponseInternalStatus(commonvgpb.Vendor_FEDERAL_BANK,
		vgPb.ApiType_CARD_CONTROL, fedResp.GetResponseCode())

	statusFactory, ok := consolidatedCardControlResponseActionToStatusFactoryMap[fedResp.ResponseAction]
	if !ok {
		if fedResp.ResponseAction == federal.FailureResponseAction {
			statusFactory, ok = consolidatedCardControlFailureResponseCodeToStatusFactoryMap[fedResp.ResponseCode]
			if !ok {
				// Log error and set response type as UNKNOWN.
				// When such a status is found, it indicates there is a change at the Vendor's end
				// and we need to update our status mapping.
				logger.InfoNoCtx("Got unknown response code for FAILURE action",
					zap.String(logger.RESPONSE_CODE, fedResp.ResponseCode),
					zap.String(logger.REASON, fedResp.ResponseReason),
					zap.String(logger.REQUEST_ID, fedResp.GetRequestId()),
				)
				resp.Status = rpc.StatusUnknownWithDebugMsg(fedResp.ResponseReason)
				return resp, nil
			}
		} else {
			// Log error and set response type as UNKNOWN.
			// When such a action is found, it indicates there is a change at the Vendor's end
			// and we need to update our status mapping.
			logger.InfoNoCtx("Got unexpected response action",
				zap.String("ResponseAction", fedResp.ResponseAction),
				zap.String(logger.RESPONSE_CODE, fedResp.ResponseCode),
				zap.String(logger.REASON, fedResp.ResponseReason),
				zap.String(logger.REQUEST_ID, fedResp.GetRequestId()),
			)
			resp.Status = rpc.StatusUnknownWithDebugMsg(fedResp.ResponseReason)
			return resp, nil
		}
	}
	resp.InternalResponseCode = internalResponseCode.StatusCode
	resp.Status = statusFactory(fedResp.ResponseReason)
	return resp, nil
}
