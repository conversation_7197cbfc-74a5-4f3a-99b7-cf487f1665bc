package incomeestimator

import (
	"testing"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
)

func Test_verifyRequest(t *testing.T) {
	type args struct {
		req *incomeestimator.GetIncomeEstimateRequest
	}
	var (
		req1 = &incomeestimator.GetIncomeEstimateRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			UseBatchModel:       false,
			UserDetails:         nil,
			ReferrerDetails:     nil,
			CreditReportData:    nil,
			AaDataPresent:       false,
			ServiceConfig:       nil,
			DataSourcesToBeUsed: []string{"aa"},
		}
		req2 = &incomeestimator.GetIncomeEstimateRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			UseBatchModel:       false,
			UserDetails:         nil,
			ReferrerDetails:     nil,
			CreditReportData:    nil,
			AaDataPresent:       false,
			ServiceConfig:       nil,
			DataSourcesToBeUsed: nil,
		}
		req3 = &incomeestimator.GetIncomeEstimateRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			UseBatchModel: false,
			UserDetails: &incomeestimator.UserDetails{
				WorkEmail: "abc",
			},
			ReferrerDetails:     nil,
			CreditReportData:    nil,
			AaDataPresent:       false,
			ServiceConfig:       nil,
			DataSourcesToBeUsed: nil,
		}
		req4 = &incomeestimator.GetIncomeEstimateRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			UseBatchModel:       true,
			UserDetails:         nil,
			ReferrerDetails:     nil,
			CreditReportData:    nil,
			AaDataPresent:       true,
			ServiceConfig:       nil,
			DataSourcesToBeUsed: nil,
		}
	)
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "Happy Flow",
			args:    args{req: req1},
			wantErr: false,
		},
		{
			name:    "mandatory field missing for batchmodel:false",
			args:    args{req: req2},
			wantErr: true,
		},
		{
			name:    "user details passed for batchmodel:false",
			args:    args{req: req3},
			wantErr: false,
		},
		{
			name:    " batchmodel:true",
			args:    args{req: req4},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := verifyRequest(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("verifyRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
