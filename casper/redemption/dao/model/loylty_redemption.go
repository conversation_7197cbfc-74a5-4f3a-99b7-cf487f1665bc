package model

import (
	"time"

	"github.com/epifi/gamma/api/casper"
)

// LoyltyRedemption stores redemptions initiated to loylty vendor.
type LoyltyRedemption struct {
	// unique identifier of loylty redemption
	ID string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`

	// id of epi<PERSON>'s redeemed_offer.
	RefId string

	// type of offer that was redeemed
	OfferType casper.OfferType

	// loylty generated unique order id for each redemption.
	// OrderId can be used to fetched redeemed offer details using loylty's apis.
	OrderId string

	// source of request
	RequestSource casper.RequestSource

	CreatedAt time.Time
	UpdatedAt time.Time
}
