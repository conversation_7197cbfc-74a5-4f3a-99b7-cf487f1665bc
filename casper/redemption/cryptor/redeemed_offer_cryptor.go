package cryptor

import (
	"context"
	"encoding/base64"
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/aws/v2/kms"

	"github.com/google/wire"
)

var RedeemedOfferCryptorWireSet = wire.NewSet(NewRedeemedOfferCryptorImpl, wire.Bind(new(RedeemedOfferCryptor), new(*RedeemedOfferCryptorImpl)))

// provides methods to encrypt and decrypt a string
type RedeemedOfferCryptor interface {
	Encrypt(ctx context.Context, plaintext string, keyId string) (string, error)
	Decrypt(ctx context.Context, base64EncodedCipherText string, keyId string) (string, error)
}

// Implements cryptor.RedeemedOfferCryptor interface.
type RedeemedOfferCryptorImpl struct {
	kmsSymmetricCryptor *kms.KMSSymmetricCryptor
}

func NewRedeemedOfferCryptorImpl(kmsSymmetricCryptor *kms.KMSSymmetricCryptor) *RedeemedOfferCryptorImpl {
	return &RedeemedOfferCryptorImpl{kmsSymmetricCryptor: kmsSymmetricCryptor}
}

func (r *RedeemedOfferCryptorImpl) Encrypt(ctx context.Context, plaintext string, keyId string) (string, error) {
	// if plaintext is empty, then no need to encrypt
	if plaintext == "" {
		return "", nil
	}
	cipherText, err := r.kmsSymmetricCryptor.Encrypt(ctx, plaintext, keyId)
	if err != nil {
		return "", errors.New("error encountered during encryption")
	}
	return base64.StdEncoding.EncodeToString(cipherText), nil
}

func (r *RedeemedOfferCryptorImpl) Decrypt(ctx context.Context, base64EncodedCipherText string, keyId string) (string, error) {
	// if base64EncodedCipherText is empty, then no need to decrypt
	if base64EncodedCipherText == "" {
		return "", nil
	}
	cipherText, err := base64.StdEncoding.DecodeString(base64EncodedCipherText)
	if err != nil {
		return "", fmt.Errorf("error while hex decoding base64EncodedCipherText : %w", err)
	}
	plainText, err := r.kmsSymmetricCryptor.Decrypt(ctx, cipherText, keyId)
	if err != nil {
		return "", errors.Wrap(err, "error while decrypting cipherText")
	}
	return plainText, nil
}

// mock implementation of cryptor.RedeemedOfferCryptor interface.
type MockRedeemedOfferCryptorImpl struct {
}

func NewMockRedeemedOfferCryptorImpl() *MockRedeemedOfferCryptorImpl {
	return &MockRedeemedOfferCryptorImpl{}
}

func (r *MockRedeemedOfferCryptorImpl) Encrypt(ctx context.Context, plaintext string, keyId string) (string, error) {
	if keyId == "" {
		return "", errors.New("empty key id")
	}
	return base64.StdEncoding.EncodeToString([]byte(plaintext)), nil
}

func (r *MockRedeemedOfferCryptorImpl) Decrypt(ctx context.Context, base64EncodedCipherText string, keyId string) (string, error) {
	if keyId == "" {
		return "", errors.New("empty key id")
	}
	plainText, err := base64.StdEncoding.DecodeString(base64EncodedCipherText)
	if err != nil {
		return "", fmt.Errorf("error while hex decoding base64EncodedCipherText : %w", err)
	}
	return string(plainText), err
}
