Application:
  Environment: "development"
  Name: "collection"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

DbConfigMap:
  LIQUILOANS_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Username: "root"
    Password: ""
    Name: "loans_liquiloans"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "collection"
    Host: "localhost"
    Port: 5432
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

AWS:
  Region: "ap-south-1"
