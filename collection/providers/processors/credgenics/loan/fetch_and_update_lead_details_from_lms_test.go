package loan

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	collectionPb "github.com/epifi/gamma/api/collection"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloanMocks "github.com/epifi/gamma/api/preapprovedloan/mocks"
	credgenics2 "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	vgCredgenicsMocks "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics/mocks"
	daoMocks "github.com/epifi/gamma/collection/dao/mocks"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	mock_events "github.com/epifi/be-common/pkg/events/mocks"
)

func TestProvider_UpdateActiveLeadDetailsAtCollectionVendor(t *testing.T) {

	var (
		leadId    = "leadId"
		actorId   = "actorId"
		accId     = "accountId"
		vendorId  = "vendorId"
		leadModel = &collectionPb.Lead{
			Id:        leadId,
			ActorId:   actorId,
			AccountId: accId,
			Vendor:    commonvgpb.Vendor_CREDGENICS,
			VendorId:  vendorId,
			State:     collectionPb.LeadState_LEAD_STATE_ACTIVE,
		}
		allocationDate = &date.Date{
			Year:  2023,
			Month: 10,
			Day:   2,
		}
		defaultDate = &date.Date{
			Year:  2023,
			Month: 9,
			Day:   5,
		}
		allocationModel = &collectionPb.Allocation{
			Id:               "allocationid",
			LeadId:           leadId,
			DateOfAllocation: allocationDate,
			DefaultDate:      defaultDate,
			VendorStatus:     collectionPb.VendorStatus_VENDOR_STATUS_SUCCESS,
			RecoveryStatus:   collectionPb.RecoveryStatus_RECOVERY_STATUS_NOT_RECOVERED,
		}
		allocationModelAlreadyRecovered = &collectionPb.Allocation{
			Id:               "allocationid",
			LeadId:           leadId,
			DateOfAllocation: allocationDate,
			DefaultDate:      defaultDate,
			VendorStatus:     collectionPb.VendorStatus_VENDOR_STATUS_SUCCESS,
			RecoveryStatus:   collectionPb.RecoveryStatus_RECOVERY_STATUS_RECOVERED,
		}
		loanAccount = &palPb.LoanAccount{
			Id:            accId,
			ActorId:       actorId,
			Vendor:        palPb.Vendor_LIQUILOANS,
			AccountNumber: "vendor acc no",
			LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
			IfscCode:      "IFSC",
			LoanAmountInfo: &palPb.LoanAmountInfo{
				LoanAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10000,
				},
				DisbursedAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10000,
				},
				OutstandingAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        5000,
				},
			},
			Details: &palPb.LoanAccountDetails{
				InterestRate:   22,
				TenureInMonths: 48,
			},
			Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_FLDG,
		}
		loanDefaultDetails = &palPb.LoanDefaultDetails{
			FirstDefaultDate: &date.Date{
				Year:  2023,
				Month: 9,
				Day:   5,
			},
			TotalDueAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        2000,
			},
			ChargesApplied: &palPb.ChargesApplied{
				LatePaymentInterest: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        20,
				},
				BounceCharges: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10,
				},
				OtherCharges: nil,
			},
			ReceivedAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500,
			},
		}
		schedule = []*palPb.LoanInstallmentPayout{
			{
				Id: "payout-1",
				Amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500,
				},
				DueDate: &date.Date{
					Year:  2023,
					Month: 9,
					Day:   5,
				},
				PayoutDate: &date.Date{
					Year:  2023,
					Month: 9,
					Day:   10,
				},
				Status: palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID,
			},
		}
		scheduleFullyRecovered = []*palPb.LoanInstallmentPayout{
			{
				Id: "payout-1",
				Amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        500,
				},
				DueDate: &date.Date{
					Year:  2023,
					Month: 9,
					Day:   5,
				},
				PayoutDate: &date.Date{
					Year:  2023,
					Month: 9,
					Day:   10,
				},
				Status: palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
			},
		}
		loanDetails = &credgenics2.LoanDetails{
			Id: vendorId,
			Defaults: []*credgenics2.DefaultDetails{
				{
					AllocationMonth: &date.Date{
						Year:  2023,
						Month: 10,
						Day:   1,
					},
					TotalClaimAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2000,
					},
					LateFee: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        20,
					},
					ClientAmountRecovered: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
					OtherPenalty: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        10,
					},
				},
			},
			OutstandingAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			RepaidAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        500,
			},
		}
	)
	type mocks struct {
		leadDao            *daoMocks.MockLeadDao
		allocationDao      *daoMocks.MockAllocationDao
		vgCredgenicsClient *vgCredgenicsMocks.MockCredgenicsClient
		palClient          *preapprovedloanMocks.MockPreApprovedLoanClient
		time               *dateTimeMocks.MockTime
		eventBroker        *mock_events.MockBroker
	}

	type args struct {
		leadId        string
		productVendor commonvgpb.Vendor
	}

	tests := []struct {
		name         string
		mocksMethods func(mocks)
		args         args
	}{
		{
			name: "success single default case update",
			mocksMethods: func(m mocks) {
				m.leadDao.EXPECT().GetById(gomock.Any(), leadId).Return(leadModel, nil)
				m.time.EXPECT().Now().Return(time.Date(2023, 10, 2, 0, 0, 0, 0, datetimePkg.IST)).Times(2)
				m.allocationDao.EXPECT().GetByLeadIdAndAllocationMonth(gomock.Any(), leadId, &date.Date{
					Year:  2023,
					Month: 10,
				}).Return(allocationModel, nil)
				m.palClient.EXPECT().GetLoanDefaultDetails(gomock.Any(),
					&palPb.GetLoanDefaultDetailsRequest{LoanHeader: &palPb.LoanHeader{Vendor: palPb.Vendor_LIQUILOANS}, LoanAccountId: accId}).
					Return(&palPb.GetLoanDefaultDetailsResponse{
						Status:         rpc.StatusOk(),
						LoanAccount:    loanAccount,
						DefaultDetails: loanDefaultDetails,
						Schedule:       schedule,
					}, nil)
				m.vgCredgenicsClient.EXPECT().UpdateLoan(gomock.Any(), &credgenics2.UpdateLoanRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CREDGENICS},
					LoanId: vendorId,
					Loan:   loanDetails,
				}).Return(&credgenics2.UpdateLoanResponse{Status: rpc.StatusOk()}, nil)
			},
			args: args{
				leadId:        leadId,
				productVendor: commonvgpb.Vendor_LIQUILOANS,
			},
		},
		{
			name: "success, but is already recovered",
			mocksMethods: func(m mocks) {
				m.leadDao.EXPECT().GetById(gomock.Any(), leadId).Return(leadModel, nil)
				m.time.EXPECT().Now().Return(time.Date(2023, 10, 2, 0, 0, 0, 0, datetimePkg.IST)).Times(2)
				m.allocationDao.EXPECT().GetByLeadIdAndAllocationMonth(gomock.Any(), leadId, &date.Date{
					Year:  2023,
					Month: 10,
				}).Return(allocationModelAlreadyRecovered, nil)
			},
			args: args{
				leadId:        leadId,
				productVendor: commonvgpb.Vendor_LIQUILOANS,
			},
		},
		{
			name: "default was not recovered earlier, now is recovered",
			mocksMethods: func(m mocks) {
				m.leadDao.EXPECT().GetById(gomock.Any(), leadId).Return(leadModel, nil)
				m.time.EXPECT().Now().Return(time.Date(2023, 10, 2, 0, 0, 0, 0, datetimePkg.IST)).Times(2)
				m.allocationDao.EXPECT().GetByLeadIdAndAllocationMonth(gomock.Any(), leadId, &date.Date{
					Year:  2023,
					Month: 10,
				}).Return(allocationModel, nil)
				m.palClient.EXPECT().GetLoanDefaultDetails(gomock.Any(),
					&palPb.GetLoanDefaultDetailsRequest{LoanHeader: &palPb.LoanHeader{Vendor: palPb.Vendor_LIQUILOANS}, LoanAccountId: accId}).
					Return(&palPb.GetLoanDefaultDetailsResponse{
						Status:         rpc.StatusOk(),
						LoanAccount:    loanAccount,
						DefaultDetails: loanDefaultDetails,
						Schedule:       scheduleFullyRecovered,
					}, nil)
				m.vgCredgenicsClient.EXPECT().UpdateLoan(gomock.Any(), &credgenics2.UpdateLoanRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CREDGENICS},
					LoanId: vendorId,
					Loan:   loanDetails,
				}).Return(&credgenics2.UpdateLoanResponse{Status: rpc.StatusOk()}, nil)
				m.vgCredgenicsClient.EXPECT().UpdateLoanRecoveryStatus(gomock.Any(), &credgenics2.UpdateLoanRecoveryStatusRequest{
					RecoveryStatus: credgenics2.UpdateLoanRecoveryStatusRequest_RECOVERY_STATUS_CLOSED,
					LoanId:         leadId,
					AllocationMonth: &date.Date{
						Year:  2023,
						Month: 10,
					},
				}).Return(&credgenics2.UpdateLoanRecoveryStatusResponse{Status: rpc.StatusOk()}, nil)
				m.allocationDao.EXPECT().UpdateById(gomock.Any(), allocationModelAlreadyRecovered.GetId(), allocationModelAlreadyRecovered,
					[]collectionPb.AllocationFieldMask{collectionPb.AllocationFieldMask_ALLOCATION_FIELD_MASK_RECOVERY_STATUS}).Return(nil, nil)
				m.eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any())
			},
			args: args{
				leadId:        leadId,
				productVendor: commonvgpb.Vendor_LIQUILOANS,
			},
		},
		{
			name: "allocation was recovered earlier, still mark credgenics loan closed",
			mocksMethods: func(m mocks) {
				m.leadDao.EXPECT().GetById(gomock.Any(), leadId).Return(leadModel, nil)
				m.time.EXPECT().Now().Return(time.Date(2023, 10, 2, 0, 0, 0, 0, datetimePkg.IST)).Times(2)
				m.allocationDao.EXPECT().GetByLeadIdAndAllocationMonth(gomock.Any(), leadId, &date.Date{
					Year:  2023,
					Month: 10,
				}).Return(allocationModelAlreadyRecovered, nil)
				m.palClient.EXPECT().GetLoanDefaultDetails(gomock.Any(),
					&palPb.GetLoanDefaultDetailsRequest{LoanHeader: &palPb.LoanHeader{Vendor: palPb.Vendor_LIQUILOANS}, LoanAccountId: accId}).
					Return(&palPb.GetLoanDefaultDetailsResponse{
						Status:         rpc.StatusOk(),
						LoanAccount:    loanAccount,
						DefaultDetails: loanDefaultDetails,
						Schedule:       scheduleFullyRecovered,
					}, nil)
				m.vgCredgenicsClient.EXPECT().UpdateLoan(gomock.Any(), &credgenics2.UpdateLoanRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CREDGENICS},
					LoanId: vendorId,
					Loan:   loanDetails,
				}).Return(&credgenics2.UpdateLoanResponse{Status: rpc.StatusOk()}, nil)
				m.vgCredgenicsClient.EXPECT().UpdateLoanRecoveryStatus(gomock.Any(), &credgenics2.UpdateLoanRecoveryStatusRequest{
					RecoveryStatus: credgenics2.UpdateLoanRecoveryStatusRequest_RECOVERY_STATUS_CLOSED,
					LoanId:         leadId,
					AllocationMonth: &date.Date{
						Year:  2023,
						Month: 10,
					},
				}).Return(&credgenics2.UpdateLoanRecoveryStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			args: args{
				leadId:        leadId,
				productVendor: commonvgpb.Vendor_LIQUILOANS,
			},
		},
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			leadDao := daoMocks.NewMockLeadDao(ctr)
			allocationDao := daoMocks.NewMockAllocationDao(ctr)
			vgCredgenicsClient := vgCredgenicsMocks.NewMockCredgenicsClient(ctr)
			palClient := preapprovedloanMocks.NewMockPreApprovedLoanClient(ctr)
			timeClient := dateTimeMocks.NewMockTime(ctr)
			eventClient := mock_events.NewMockBroker(ctr)

			mockArgs := mocks{
				leadDao:            leadDao,
				allocationDao:      allocationDao,
				vgCredgenicsClient: vgCredgenicsClient,
				palClient:          palClient,
				time:               timeClient,
				eventBroker:        eventClient,
			}

			if tt.mocksMethods != nil {
				tt.mocksMethods(mockArgs)
			}

			p := &Provider{
				leadDao:            mockArgs.leadDao,
				allocationDao:      mockArgs.allocationDao,
				vgCredgenicsClient: mockArgs.vgCredgenicsClient,
				palClient:          mockArgs.palClient,
				time:               mockArgs.time,
				eventBroker:        eventClient,
			}

			err := p.UpdateActiveLeadDetailsAtCollectionVendor(ctx, tt.args.leadId, tt.args.productVendor)
			if err != nil {
				t.Errorf("%v", err)
			}
		})
	}
}
