package upi_test

import (
	"testing"

	"github.com/epifi/gamma/pkg/upi"
)

func TestGetVpaNameFromVpa(t *testing.T) {
	t.<PERSON>llel()
	tests := []struct {
		name    string
		vpa     string
		vpaName string
		wantErr bool
	}{
		{
			name:    "got the vpa name successfully",
			vpa:     "abcd@fifederal",
			vpaName: "abcd",
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal",
			wantErr: true,
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal@",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(t.Name(), func(t *testing.T) {
			t.Parallel()
			got, err := upi.GetVpaNameFromVpa(tt.vpa)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("GetVpaNameFromVpa() gotErr :%v wantErr :%v", err, tt.wantErr)
				return
			}
			if got != tt.vpaName {
				t.<PERSON>("GetVpaNameFromVpa() got :%v want :%v", got, tt.vpaName)
			}
		})
	}
}

func TestGetVpaHandleFromVpa(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name    string
		vpa     string
		handle  string
		wantErr bool
	}{
		{
			name:   "got the vpa name successfully",
			vpa:    "abcd@fifederal",
			handle: "fifederal",
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal",
			wantErr: true,
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal@",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(t.Name(), func(t *testing.T) {
			t.Parallel()
			got, err := upi.GetVpaHandleFromVpa(tt.vpa)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVpaHandleFromVpa() gotErr :%v wantErr :%v", err, tt.wantErr)
				return
			}
			if got != tt.handle {
				t.Errorf("GetVpaHandleFromVpa() got :%v want :%v", got, tt.handle)
			}
		})
	}
}

func TestIsUpiNumberVpa(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name    string
		vpa     string
		res     bool
		wantErr bool
	}{
		{
			name: "upi number vpa",
			vpa:  "<EMAIL>",
			res:  true,
		},
		{
			name: "not a upi number vpa",
			vpa:  "abcd@fifederal",
		},
		{
			name:    "invalid vpa format",
			vpa:     "abcdfifederal",
			wantErr: true,
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal@",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(t.Name(), func(t *testing.T) {
			t.Parallel()
			got, err := upi.IsUpiNumberVpa(tt.vpa)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUpiNumberVpa() gotErr :%v wantErr :%v", err, tt.wantErr)
				return
			}
			if got != tt.res {
				t.Errorf("IsUpiNumberVpa() got :%v want :%v", got, tt.res)
			}
		})
	}
}

func TestGetVpaNameAndHandleFromVpa(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name    string
		vpa     string
		vpaInfo []string
		wantErr bool
	}{
		{
			name:    "got the vpa name successfully",
			vpa:     "abcd@fifederal",
			vpaInfo: []string{"abcd", "fifederal"},
			wantErr: false,
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal",
			vpaInfo: []string{},
			wantErr: true,
		},
		{
			name:    "vpa format invalid",
			vpa:     "abcdfifederal@",
			vpaInfo: []string{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(t.Name(), func(t *testing.T) {
			t.Parallel()
			got, err := upi.GetVpaNameAndHandleFromVpa(tt.vpa)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVpaNameFromVpa() gotErr :%v wantErr :%v", err, tt.wantErr)
				return
			}
			if !areSlicesEqual(got, tt.vpaInfo) {
				t.Errorf("GetVpaNameFromVpa() got :%v want :%v", got, tt.vpaInfo)
			}
		})
	}
}

func areSlicesEqual(slice1, slice2 []string) bool {
	if len(slice1) != len(slice2) {
		return false
	}

	for i := 0; i < len(slice1); i++ {
		if slice1[i] != slice2[i] {
			return false
		}
	}

	return true
}
