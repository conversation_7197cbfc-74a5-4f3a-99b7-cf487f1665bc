package cachedao

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/epifi/gamma/pkg/cachedao/metrics"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// CacheDao is a wrapper around the DAO and caching layer that provides an abstraction for implementing commonly
// used DAO functions along with caching. The cache functions use cache-aside strategy for performing read, update
// and delete operations.
type CacheDao[Res proto.Message] struct {
	cacheStorage       cache.CacheStorage
	dao                Dao[Res]
	cacheConfig        *cfg.CacheConfig
	primaryKeyGetter   func(Res) string
	secondaryKeyGetter func(Res) []any
	cacheMetrics       *metrics.CachingMetrics
}

// WithSecondaryKeyGetter can be used to initialise the CacheDao struct with a secondaryKeyGetter function, that
// returns the secondary key of an entity as a slice of any type. This option is required when making a call to
// CacheDao.BulkGetBySecondaryKeys method.
// IMPORTANT: Make sure that all the key fields are of type string or implement String()
// method, or else the BulkGetBySecondaryKeys method will throw error while generating the cacheKey.
func WithSecondaryKeyGetter[Res proto.Message](f func(Res) []any) Option[Res] {
	return func(c *CacheDao[Res]) error {
		if c.cacheConfig.SecondaryIdPrefix == "" {
			return errors.New("secondaryCachePrefix is empty")
		}
		c.secondaryKeyGetter = f
		return nil
	}
}

// checkIfConfigValid returns true/false by applying validation to the cacheConfig
func checkIfConfigValid(conf *cfg.CacheConfig) error {
	if conf == nil {
		return errors.New("cacheConfig should not be nil")
	}
	if conf.Prefix == "" {
		return errors.New("cacheConfig is invalid. Make sure cachePrefix is not empty")
	}
	return nil
}

func getConcreteStructName(structName string) string {
	idx := strings.Index(structName, ".")
	if idx == -1 {
		return structName
	}
	return structName[idx+1:]
}

func getPkgPathExcludingGammaRoot(path string) string {
	return path[len("github.com/epifi/gamma/"):]
}

type Option[Res proto.Message] func(c *CacheDao[Res]) error

// NewCacheDao returns a new instance of CacheDao with a particular entity type.
func NewCacheDao[Res proto.Message](cacheStorage cache.CacheStorage, cacheConfig *cfg.CacheConfig, dao Dao[Res], primaryKeyGetter func(Res) string, options ...Option[Res]) (*CacheDao[Res], error) {
	if err := checkIfConfigValid(cacheConfig); err != nil {
		return nil, err
	}
	typeOfDao := reflect.TypeOf(reflect.ValueOf(dao).Elem().Interface())
	pkgPath := getPkgPathExcludingGammaRoot(typeOfDao.PkgPath())
	daoImplName := getConcreteStructName(typeOfDao.String())

	c := &CacheDao[Res]{
		cacheStorage:     cacheStorage,
		dao:              dao,
		cacheConfig:      cacheConfig,
		primaryKeyGetter: primaryKeyGetter,
		cacheMetrics:     metrics.NewCachingMetrics(pkgPath, daoImplName),
	}
	for _, o := range options {
		err := o(c)
		if err != nil {
			return nil, err
		}
	}
	return c, nil
}

// GetByPrimaryKey checks if the provided key exists in the cache. If it exists, then it returns the cached value.
// Else it fetches the data from DB and sets it in the cache before sending it.
func (c *CacheDao[Res]) GetByPrimaryKey(ctx context.Context, key string) (res Res, err error) {
	data, ok := c.getFromCache(ctx, key)
	if ok {
		c.cacheMetrics.RecordCacheHit("GetByPrimaryKey", 1)
		return data, nil
	}

	c.cacheMetrics.RecordCacheMiss("GetByPrimaryKey", 1)
	res, err = c.dao.GetByPrimaryKey(ctx, key)
	if err != nil {
		return res, err
	}
	c.setDataInCache(ctx, res)
	return
}

// filterNilValuesFromSlice filters out the slice entries with nil values and returns the resulting slice.
func (c *CacheDao[Res]) filterNilValuesFromSlice(data []Res) []Res {
	var nilRes Res

	// end stores the end position of resulting array after the loop exits
	var end = 0
	for cur := 0; cur < len(data); cur++ {
		if !proto.Equal(data[cur], nilRes) {
			data[end] = data[cur]
			end++
		}
	}
	return data[:end]
}

type keyGetterFunc[Res proto.Message] func(Res) (string, error)

// mergeResultsFromOrderedSource merges the values from source slice into target slice in the order of keys and returns
// the result. It assumes that the source slice is already sorted in the order of keys. For keys corresponding to
// which the value does not exist in source, the corresponding fields in the resulting slice is nil. Also make sure that
// the length of target and key are equal.
func (c *CacheDao[Res]) mergeResultsFromOrderedSource(target []Res, source []Res, keys []string, keyGetter keyGetterFunc[Res]) ([]Res, error) {
	var nilRes Res
	for i, j := 0, 0; i < len(target) && j < len(source); i++ {
		if !proto.Equal(target[i], nilRes) {
			continue
		}
		key, err := keyGetter(source[j])
		if err != nil {
			return nil, err
		}
		if key == keys[i] {
			target[i] = source[j]
			j++
		}
	}
	return target, nil
}

// mergeResults merges the values from source slice into target slice in the order of keys and returns the result.
// The source values can be in any order. For keys corresponding to which value does not exist in source, the
// corresponding fields in the resulting slice is nil. Also make sure that the length of target and keys are equal.
func (c *CacheDao[Res]) mergeResults(target []Res, source []Res, keys []string, keyGetter keyGetterFunc[Res]) ([]Res, error) {
	var nilRes Res
	mp := make(map[string]Res, len(source))
	for _, data := range source {
		key, err := keyGetter(data)
		if err != nil {
			return nil, err
		}
		mp[key] = data
	}
	for i := 0; i < len(target); i++ {
		if !proto.Equal(target[i], nilRes) {
			continue
		}
		curData, ok := mp[keys[i]]
		if !ok {
			continue
		}
		target[i] = curData
	}
	return target, nil
}

// BulkGetByPrimaryKeys is the same as GetByPrimaryKey but returns a slice of data that match the passed keys.
// The order of returned rows is the same as the order of keys.
func (c *CacheDao[Res]) BulkGetByPrimaryKeys(ctx context.Context, keys []string) ([]Res, error) {
	if len(keys) == 0 {
		return nil, nil
	}

	res := make([]Res, len(keys))

	dataFetchedFromCache, keysToBeFetchedFromDB, err := c.multiGetFromCache(ctx, keys)
	c.cacheMetrics.RecordCacheHit("BulkGetByPrimaryKeys", len(dataFetchedFromCache))
	if err != nil {
		logger.Error(ctx, "error in fetching data from cache", zap.Error(err))
		keysToBeFetchedFromDB = keys
	}

	keyGetterFunc := func(r Res) (string, error) {
		res := c.primaryKeyGetter(r)
		return res, nil
	}
	res, err = c.mergeResultsFromOrderedSource(res, dataFetchedFromCache, keys, keyGetterFunc)
	if err != nil {
		return nil, errors.Wrap(err, "could not stringify one of the passed secondary keys")
	}

	var dataFetchedFromDB []Res
	if len(keysToBeFetchedFromDB) > 0 {
		c.cacheMetrics.RecordCacheMiss("BulkGetByPrimaryKeys", len(keysToBeFetchedFromDB))
		dataFetchedFromDB, err = c.dao.BulkGetByPrimaryKeys(ctx, keysToBeFetchedFromDB)
		if err != nil {
			logger.Error(ctx, "error in fetching data from db", zap.Error(err))
			return nil, err
		}
		_ = c.setMultipleDataInCache(ctx, dataFetchedFromDB)
	}

	res, err = c.mergeResults(res, dataFetchedFromDB, keys, keyGetterFunc)
	if err != nil {
		return nil, errors.Wrap(err, "could not stringify one of the passed secondary keys")
	}
	return c.filterNilValuesFromSlice(res), nil
}

// GetBySecondaryKey allows to fetch entity from cache using a secondary key. This key can be a composite key,
// hence it is passed as a slice of any type. Imp: Make sure that all the key fields are of type string or implement String()
// method, else the method will throw error while generating the cacheKey.
func (c *CacheDao[Res]) GetBySecondaryKey(ctx context.Context, key []any) (res Res, err error) {
	primaryKey, ok := c.fetchPrimaryKeyFromCache(ctx, key)
	if ok {
		res, err = c.GetByPrimaryKey(ctx, primaryKey)
		if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
			// delete secondary key from cache, since record corresponding to primary key does not exist in DB.
			deleteErr := c.deleteKeyMappingsInCache(ctx, key)
			if deleteErr != nil {
				logger.Error(ctx, "could not delete secondaryKey:primaryKey mapping in cache", zap.Error(err))
			}
		}
		c.cacheMetrics.RecordCacheHit("GetBySecondaryKey", 1)
		return res, err
	}

	c.cacheMetrics.RecordCacheMiss("GetBySecondaryKey", 1)
	// fetch data from the db using secondary key
	res, err = c.dao.GetBySecondaryKey(ctx, key)
	if err != nil {
		return res, err
	}
	// Find the primary id of the fetched data
	primaryKey = c.primaryKeyGetter(res)
	c.setDataInCache(ctx, res)

	// set secondaryKey:primaryKey mapping in cache
	c.setKeyMappingInCache(ctx, key, primaryKey)
	return res, nil
}

// BulkGetBySecondaryKeys is same as GetBySecondaryKey but returns a slice of entities matching the passed keys.
// The order of returned values is the same as the order of the passed keys.
func (c *CacheDao[Res]) BulkGetBySecondaryKeys(ctx context.Context, keys [][]any) ([]Res, error) {
	if c.secondaryKeyGetter == nil {
		return nil, errors.Wrap(epifierrors.ErrMethodUnimplemented, "secondaryKeyGetter is nil. Pass a valid secondaryKeyGetter to use this method")
	}
	if len(keys) == 0 {
		return nil, nil
	}

	res := make([]Res, len(keys))
	primaryKeysFetchedFromCache, secondaryKeysToBeFetchedFromDB, err := c.fetchPrimaryKeysFromCache(ctx, keys)
	c.cacheMetrics.RecordCacheHit("BulkGetBySecondaryKeys", len(primaryKeysFetchedFromCache))
	if err != nil {
		logger.Error(ctx, "error in fetching data from cache", zap.Error(err))
		secondaryKeysToBeFetchedFromDB = keys
	}

	secondaryKeysStr, err := c.stringifySecondaryDBKeysToCacheKeys(keys)
	if err != nil {
		return nil, errors.Wrap(err, "could not stringify one of the passed secondary keys")
	}
	keyGetterFunc := func(r Res) (string, error) {
		res, err := c.stringifySecondaryDBKeyToCacheKey(c.secondaryKeyGetter(r))
		return res, err
	}

	if len(primaryKeysFetchedFromCache) > 0 {
		dataFetchedFromCache, err := c.BulkGetByPrimaryKeys(ctx, primaryKeysFetchedFromCache)
		if err != nil {
			logger.Error(ctx, "error in fetching data from cache", zap.Error(err))
			return nil, err
		}
		res, err = c.mergeResultsFromOrderedSource(res, dataFetchedFromCache, secondaryKeysStr, keyGetterFunc)
		if err != nil {
			return nil, errors.Wrap(err, "could not fetch data in the order of keys")
		}
	}

	// fetch remaining data from db if secondaryKeysToBeFetchedFromDB has any values
	if len(secondaryKeysToBeFetchedFromDB) > 0 {
		c.cacheMetrics.RecordCacheMiss("BulkGetBySecondaryKeys", len(secondaryKeysToBeFetchedFromDB))

		dbData, err := c.dao.BulkGetBySecondaryKeys(ctx, secondaryKeysToBeFetchedFromDB)
		if err != nil {
			return nil, err
		}
		res, err = c.mergeResults(res, dbData, secondaryKeysStr, keyGetterFunc)
		if err != nil {
			return nil, errors.Wrap(err, "could not fetch data in the order of keys")
		}
		err = c.setMultipleDataInCache(ctx, dbData)
		if err != nil {
			// return data in case of an error in setting it in the cache
			return c.filterNilValuesFromSlice(res), nil
		}
		var dbPrimaryKeysList []string
		var dbSecondaryKeysList [][]any
		var ttlList []time.Duration
		for _, data := range dbData {
			dbPrimaryKeysList = append(dbPrimaryKeysList, c.primaryKeyGetter(data))
			dbSecondaryKeysList = append(dbSecondaryKeysList, c.secondaryKeyGetter(data))
			ttlList = append(ttlList, c.cacheConfig.CacheTTl)
		}
		c.multiSetKeysMappingInCache(ctx, dbSecondaryKeysList, dbPrimaryKeysList, ttlList)
	}

	logger.Debug(ctx, fmt.Sprintf("fetched %d data from cache and %d data from db", len(primaryKeysFetchedFromCache), len(secondaryKeysToBeFetchedFromDB)))
	return c.filterNilValuesFromSlice(res), nil
}

// Update updates the row matching key in the DB and deletes the cache entry if it exists. The primary key is fetched
// from the newData passed.
func (c *CacheDao[Res]) Update(ctx context.Context, newData Res, updateMask any) error {
	key := c.primaryKeyGetter(newData)
	if key == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	err := c.deleteDataInCache(ctx, key)
	if err != nil {
		return fmt.Errorf("error while deleting data in cache :%w", err)
	}
	err = c.dao.Update(ctx, key, newData, updateMask)
	if err != nil {
		return err
	}
	return nil
}

// Delete removes the row of the key in the DB as well as deletes the entry from the cache.
func (c *CacheDao[Res]) Delete(ctx context.Context, key string) error {
	if key == "" {
		return epifierrors.ErrInvalidArgument
	}
	cacheKey := c.getPrimaryCacheKeyFromDBKey(key)
	err := c.deleteDataInCache(ctx, key)
	if err != nil {
		return fmt.Errorf("error while deleting data from cache: key = %s, %w", cacheKey, err)
	}
	err = c.dao.Delete(ctx, key)
	if err != nil {
		return err
	}
	return nil
}

// DeleteAndClearKeyMappingInCache deletes the data by secondary key from DB and clears the secondary key map entry from cache.
func (c *CacheDao[Res]) DeleteAndClearKeyMappingInCache(ctx context.Context, secondaryKey []any) error {
	key, ok := c.fetchPrimaryKeyFromCache(ctx, secondaryKey)
	if !ok {
		return errors.New("could not fetch primary key from provided secondaryKey")
	}
	err := c.deleteKeyMappingsInCache(ctx, secondaryKey)
	if err != nil {
		return err
	}
	return c.Delete(ctx, key)
}

// BulkDelete is the same as Delete but removes multiple entries that match the passed slice of keys.
func (c *CacheDao[Res]) BulkDelete(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}
	err := c.deleteDataInCache(ctx, keys...)
	if err != nil {
		return fmt.Errorf("error while deleting data in cache :%w", err)
	}
	err = c.dao.BulkDelete(ctx, keys)
	if err != nil {
		return err
	}
	return nil
}

// BulkDeleteAndClearKeyMappingsInCache deletes the data corresponding to the secondary keys from DB and clears the secondary key map entry from cache.
func (c *CacheDao[Res]) BulkDeleteAndClearKeyMappingsInCache(ctx context.Context, secondaryKeys [][]any) error {
	keysInCache, _, err := c.fetchPrimaryKeysFromCache(ctx, secondaryKeys)
	if err != nil {
		return errors.New("could not fetch primary key from provided secondaryKey")
	}
	err = c.deleteKeyMappingsInCache(ctx, secondaryKeys...)
	if err != nil {
		return err
	}
	return c.BulkDelete(ctx, keysInCache)
}

// deleteDataInCache deletes the cache entry corresponding to the passed DB keys and returns an error is deletion fails.
func (c *CacheDao[Res]) deleteDataInCache(ctx context.Context, keys ...string) error {
	cacheKeys := make([]string, 0, len(keys))
	for _, key := range keys {
		cacheKeys = append(cacheKeys, c.getPrimaryCacheKeyFromDBKey(key))
	}
	err := c.cacheStorage.Delete(ctx, cacheKeys...)
	if err != nil {
		return err
	}
	return nil
}

// deleteKeyMappingsInCache removes the secondaryKey to primaryKey mapping from the cache and returns error if any.
func (c *CacheDao[Res]) deleteKeyMappingsInCache(ctx context.Context, keys ...[]any) error {
	cacheKeys := make([]string, 0, len(keys))
	for _, key := range keys {
		cacheKey, err := c.stringifySecondaryDBKeyToCacheKey(key)
		if err != nil {
			return errors.Wrap(err, "could not delete secondary key from cache")
		}
		cacheKeys = append(cacheKeys, cacheKey)
	}
	err := c.cacheStorage.Delete(ctx, cacheKeys...)
	if err != nil {
		return errors.Wrap(err, "could not delete secondary key from cache")
	}
	return nil
}

// getFromCache fetches a cache entry corresponding to passed key and returns it along with a boolean field
// specifying if the fetch is successful or not.
func (c *CacheDao[Res]) getFromCache(ctx context.Context, key string) (res Res, ok bool) {
	cacheKey := c.getPrimaryCacheKeyFromDBKey(key)
	data, err := c.cacheStorage.Get(ctx, cacheKey)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "failed to fetch data from cache", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
		}
		return res, false
	}
	// cache miss
	if data == "" {
		return res, false
	}

	res = reflect.New(reflect.TypeOf(res).Elem()).Interface().(Res)
	err = protojson.Unmarshal([]byte(data), res)
	if err == nil {
		return res, true
	}

	logger.Debug(ctx, "failed to unmarshal data when fetching from cache", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
	// Since we are unable to unmarshal the stored string, delete it
	err = c.cacheStorage.Delete(ctx, cacheKey)
	if err != nil {
		logger.Debug(ctx, "error while deleting data from cache", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
	}
	return res, false
}

// multiGetFromCache fetches the different cache entries corresponding to the passed keys. The first return
// value is a slice of the different entities found inside the cache, the second return value is a slice of keys that
// were not present in the cache & the last return value is an error.
func (c *CacheDao[Res]) multiGetFromCache(ctx context.Context, keys []string) ([]Res, []string, error) {
	cacheKeysList := make([]string, 0, len(keys))
	for _, id := range keys {
		cacheKeysList = append(cacheKeysList, c.getPrimaryCacheKeyFromDBKey(id))
	}
	cacheResp, err := c.cacheStorage.MultiGet(ctx, cacheKeysList)
	if err != nil {
		logger.Error(ctx, "error in fetching data from cache", zap.Error(err))
		return nil, nil, err
	}

	resFromCache, keysToFetchFromDB := make([]Res, 0), make([]string, 0)
	for idx, resp := range cacheResp {
		if resp == "" {
			keysToFetchFromDB = append(keysToFetchFromDB, keys[idx])
			continue
		}

		var res Res
		res = reflect.New(reflect.TypeOf(res).Elem()).Interface().(Res)
		err = protojson.Unmarshal([]byte(resp), res)
		if err != nil {
			logger.Error(ctx, "failed to unmarshal data from cache", zap.Error(err))
			// get this id from db
			keysToFetchFromDB = append(keysToFetchFromDB, keys[idx])
			// Failed to unmarshal value. Delete this from cache (best-effort).
			err = c.deleteDataInCache(ctx, keys[idx])
			if err != nil {
				logger.Error(ctx, "failed to delete data in cache", zap.String(logger.REDIS_KEY, cacheKeysList[idx]), zap.Error(err))
			}
			continue
		}
		resFromCache = append(resFromCache, res)
	}
	return resFromCache, keysToFetchFromDB, nil
}

// fetchPrimaryKeyFromCache takes a composite secondary key of an entity and returns the corresponding primary key
// for the entity.
func (c *CacheDao[Res]) fetchPrimaryKeyFromCache(ctx context.Context, secondaryKey []any) (string, bool) {
	secondaryCacheKey, err := c.stringifySecondaryDBKeyToCacheKey(secondaryKey)
	if err != nil {
		logger.Debug(ctx, "failed to fetch primaryKey using secondaryKey from cache", zap.Error(err))
		return "", false
	}
	primaryKey, err := c.cacheStorage.Get(ctx, secondaryCacheKey)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "failed to fetch primaryKey using secondaryKey from cache", zap.Error(err))
		}
		return "", false
	}
	if primaryKey == "" {
		return "", false
	}
	return primaryKey, true
}

// fetchPrimaryKeysFromCache takes a slice of secondary keys, and returns a slice of primary keys corresponding to the
// passed secondary keys.
func (c *CacheDao[Res]) fetchPrimaryKeysFromCache(ctx context.Context, keys [][]any) ([]string, [][]any, error) {
	cacheKeys, err := c.stringifySecondaryDBKeysToCacheKeys(keys)
	if err != nil {
		logger.Error(ctx, "error in converting one of the passed secondary keys to string", zap.Error(err))
		return nil, nil, err
	}

	cacheResp, err := c.cacheStorage.MultiGet(ctx, cacheKeys)
	if err != nil {
		logger.Error(ctx, "error in fetching primary keys from cache", zap.Error(err))
		return nil, nil, err
	}

	primaryKeysToFetch := make([]string, 0)
	secondaryKeysToBeFetchedFromDB := make([][]any, 0)
	for i, data := range cacheResp {
		if data == "" {
			secondaryKeysToBeFetchedFromDB = append(secondaryKeysToBeFetchedFromDB, keys[i])
			continue
		}
		primaryKeysToFetch = append(primaryKeysToFetch, data)
	}
	return primaryKeysToFetch, secondaryKeysToBeFetchedFromDB, nil
}

// getPrimaryCacheKeyFromDBKey takes a primary key used in the DB and returns the corresponding cache key by
// prefixing the cache prefix.
func (c *CacheDao[Res]) getPrimaryCacheKeyFromDBKey(key string) string {
	return c.cacheConfig.Prefix + key
}

// setDataInCache takes an entity and sets it in the cache by marshalling it into a string.
func (c *CacheDao[Res]) setDataInCache(ctx context.Context, data Res) {
	cacheKey := c.getPrimaryCacheKeyFromDBKey(c.primaryKeyGetter(data))

	dataBytes, err := protojson.Marshal(data)
	if err != nil {
		logger.Debug(ctx, "error while marshaling data for caching", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
		return
	}
	err = c.cacheStorage.Set(ctx, cacheKey, string(dataBytes), c.cacheConfig.CacheTTl)
	if err != nil {
		logger.Debug(ctx, "error while setting data in cache", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
		return
	}
	logger.Debug(ctx, "set data in cache successfully", zap.String(logger.REDIS_KEY, cacheKey))
}

// setMultipleDataInCache takes a slice of data and sets those in the cache as different entries.
func (c *CacheDao[Res]) setMultipleDataInCache(ctx context.Context, data []Res) error {
	keysList := make([]string, 0, len(data))
	dataBytesList := make([]string, 0, len(data))
	expirationTimeList := make([]time.Duration, 0, len(data))

	for i := 0; i < len(data); i++ {
		d, cacheKey := data[i], c.getPrimaryCacheKeyFromDBKey(c.primaryKeyGetter(data[i]))
		dataBytes, err := protojson.Marshal(d)
		if err != nil {
			logger.Debug(ctx, "error while marshaling data for caching", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
			continue
		}

		keysList = append(keysList, cacheKey)
		dataBytesList = append(dataBytesList, string(dataBytes))
		expirationTimeList = append(expirationTimeList, c.cacheConfig.CacheTTl)
	}

	err := c.cacheStorage.MultiSet(ctx, keysList, dataBytesList, expirationTimeList)
	if err != nil {
		logger.Debug(ctx, "error while setting multiple data in cache", zap.Error(err))
		return err
	}
	return nil
}

// multiSetKeysMappingInCache sets a mapping between secondaryCacheKey:primaryCacheKey for each of the
// corresponding cache entries.
func (c *CacheDao[Res]) multiSetKeysMappingInCache(ctx context.Context, secondaryKeysList [][]any, primaryKeysList []string, ttlList []time.Duration) {
	cacheKeys, err := c.stringifySecondaryDBKeysToCacheKeys(secondaryKeysList)
	if err != nil {
		logger.Error(ctx, "error while mapping secondaryKeys to primaryKeys in cache due to error in converting one of the passed secondary keys to string", zap.Error(err))
		return
	}

	err = c.cacheStorage.MultiSet(ctx, cacheKeys, primaryKeysList, ttlList)
	if err != nil {
		logger.Debug(ctx, "error while mapping secondaryKeys to primaryKeys in cache", zap.Error(err))
		return
	}
}

// stringifySecondaryDBKeyToCacheKey takes a secondaryKey (which can be a composite key), and combines it into a single
// string to be used as a cache key. If any of the sub-key in the passed composite key cannot be converted
// to string, an error is returned.
func (c *CacheDao[Res]) stringifySecondaryDBKeyToCacheKey(key []any) (string, error) {
	secondaryKey, err := joinKeys(key, ":")
	if err != nil {
		return "", err
	}
	return c.cacheConfig.SecondaryIdPrefix + secondaryKey, nil
}

// stringifySecondaryDBKeysToCacheKeys takes a slice of secondary keys and returns a slice of corresponding cache keys for each
// of the passed secondary keys.
func (c *CacheDao[Res]) stringifySecondaryDBKeysToCacheKeys(secondaryKeysList [][]any) ([]string, error) {
	cacheKeys := make([]string, 0, len(secondaryKeysList))
	for _, key := range secondaryKeysList {
		secondaryKey, err := c.stringifySecondaryDBKeyToCacheKey(key)
		if err != nil {
			return nil, err
		}
		cacheKeys = append(cacheKeys, secondaryKey)
	}
	return cacheKeys, nil
}

// toString tries to convert the passed any type into a string or returns an error if it fails.
func toString(data any) (string, error) {
	switch value := data.(type) {
	case string:
		return value, nil
	case fmt.Stringer:
		return value.String(), nil
	}
	return "", errors.Wrap(epifierrors.ErrInvalidArgument, fmt.Sprintf("%t is not of type string and does not implement String() method", data))
}

// joinKeys combines the passed elements into a single string with sep as delimiter.
func joinKeys(keys []any, sep string) (string, error) {
	res, err := toString(keys[0])
	for i := 1; i < len(keys); i++ {
		var cur string
		cur, err = toString(keys[i])
		if err != nil {
			break
		}
		res += sep + cur
	}
	if err != nil {
		return "", err
	}
	return res, nil
}

// setKeyMappingInCache sets a mapping between secondaryCacheKey & the primaryKey.
func (c *CacheDao[Res]) setKeyMappingInCache(ctx context.Context, secondaryKey []any, primaryKey string) {
	secondaryCacheKey, err := c.stringifySecondaryDBKeyToCacheKey(secondaryKey)
	if err != nil {
		logger.Debug(ctx, "error while mapping secondaryKey to primaryKey in Cache", zap.Error(err))
		return
	}
	err = c.cacheStorage.Set(ctx, secondaryCacheKey, primaryKey, c.cacheConfig.CacheTTl)
	if err != nil {
		logger.Debug(ctx, "error while mapping secondaryKey to primaryKey in Cache", zap.Error(err), zap.String(logger.REDIS_KEY, secondaryCacheKey))
	}
}
