package group

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var (
	AddEmailGroupMappingReq1 = &userGroupPb.AddEmailGroupMappingRequest{
		UserGroup: commontypes.UserGroup_INTERNAL,
		Emails:    []string{"<EMAIL>", "<EMAIL>"},
	}
	getGroupListReq1 = &userGroupPb.GetGroupsMappedToEmailRequest{
		Email: "<EMAIL>",
	}
	groupList1 = []commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}
)

func TestService_AddEmailGroupMapping(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *userGroupPb.AddEmailGroupMappingRequest
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args, *mockedDependencies)
		want      *userGroupPb.AddEmailGroupMappingResponse
		wantErr   bool
	}{
		{
			name: "error because group no passed in req",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddEmailGroupMappingRequest{
					Emails: []string{"<EMAIL>", "<EMAIL>"},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.AddEmailGroupMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "error because email list is empty in req",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddEmailGroupMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.AddEmailGroupMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "error returned by dao",
			args: args{
				ctx: context.Background(),
				req: AddEmailGroupMappingReq1,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().BatchUpsert(context.Background(), AddEmailGroupMappingReq1.Emails,
					AddEmailGroupMappingReq1.UserGroup).Return(fmt.Errorf("failed to add"))
			},
			want: &userGroupPb.AddEmailGroupMappingResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: AddEmailGroupMappingReq1,
			},
			want: &userGroupPb.AddEmailGroupMappingResponse{
				Status: rpc.StatusOk(),
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().BatchUpsert(context.Background(), AddEmailGroupMappingReq1.Emails,
					AddEmailGroupMappingReq1.UserGroup).Return(nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.AddEmailGroupMapping(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddEmailGroupMapping() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddEmailGroupMapping() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetGroupsMappedToEmail(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx context.Context
		req *userGroupPb.GetGroupsMappedToEmailRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *userGroupPb.GetGroupsMappedToEmailResponse
		wantMocks func(args args, md *mockedDependencies)
		wantErr   bool
	}{
		{
			name: "error because email not passed",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.GetGroupsMappedToEmailRequest{},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.GetGroupsMappedToEmailResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "error returned by dao layer",
			args: args{
				ctx: context.Background(),
				req: getGroupListReq1,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetGroupListForEmail(context.Background(), getGroupListReq1.Email).
					Return(groupList1, nil)
			},
			want: &userGroupPb.GetGroupsMappedToEmailResponse{
				Status: rpc.StatusOk(),
				Groups: groupList1,
			},
			wantErr: false,
		},
		{
			name: "success with no group",
			args: args{
				ctx: context.Background(),
				req: getGroupListReq1,
			},
			want: &userGroupPb.GetGroupsMappedToEmailResponse{
				Status: rpc.StatusOk(),
				Groups: []commontypes.UserGroup{commontypes.UserGroup_NO_GROUP},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetGroupListForEmail(context.Background(), getGroupListReq1.Email).
					Return([]commontypes.UserGroup{}, nil)
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: getGroupListReq1,
			},
			want: &userGroupPb.GetGroupsMappedToEmailResponse{
				Status: rpc.StatusOk(),
				Groups: groupList1,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetGroupListForEmail(context.Background(), getGroupListReq1.Email).
					Return(groupList1, nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.GetGroupsMappedToEmail(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGroupsMappedToEmail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGroupsMappedToEmail() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetUsersMappedToGroups(t *testing.T) {
	t.Parallel()

	type args struct {
		req *userGroupPb.GetUsersMappedToGroupsRequest
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, md *mockedDependencies)
		want      *userGroupPb.GetUsersMappedToGroupsResponse
		wantErr   bool
	}{
		{
			name: "error in dao",
			args: args{
				req: &userGroupPb.GetUsersMappedToGroupsRequest{UserGroups: groupList1},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetUsersForGroups(gomock.Any(), groupList1).Return(nil, fmt.Errorf("error"))
			},
			want: &userGroupPb.GetUsersMappedToGroupsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				req: &userGroupPb.GetUsersMappedToGroupsRequest{UserGroups: groupList1},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetUsersForGroups(gomock.Any(), groupList1).Return([]string{}, nil)
			},
			want: &userGroupPb.GetUsersMappedToGroupsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "success",
			args: args{
				req: &userGroupPb.GetUsersMappedToGroupsRequest{UserGroups: groupList1},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetUsersForGroups(gomock.Any(), groupList1).Return([]string{"test-email-1", "test-email-2"}, nil)
			},
			want: &userGroupPb.GetUsersMappedToGroupsResponse{
				Status: rpc.StatusOk(),
				Emails: []string{"test-email-1", "test-email-2"},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.GetUsersMappedToGroups(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUsersMappedToGroups() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUsersMappedToGroups() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_DeleteEmailGroupMapping(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *userGroupPb.DeleteEmailGroupMappingRequest
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, md *mockedDependencies)
		want      *userGroupPb.DeleteEmailGroupMappingResponse
		wantErr   bool
	}{
		{
			name: "invalid argument - default user group",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteEmailGroupMappingRequest{
					UserGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
					Email:     "<EMAIL>",
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.DeleteEmailGroupMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "invalid argument - empty email",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteEmailGroupMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					Email:     "",
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.DeleteEmailGroupMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "invalid argument - both argument missing",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteEmailGroupMappingRequest{
					UserGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
					Email:     "",
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.DeleteEmailGroupMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "deleted successfully",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteEmailGroupMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					Email:     "<EMAIL>",
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().DeleteMapping(gomock.Any(), args.req.GetEmail(), args.req.GetUserGroup()).Return(nil).Times(1)
			},
			want: &userGroupPb.DeleteEmailGroupMappingResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "deletion unsuccessful",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteEmailGroupMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					Email:     "<EMAIL>",
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().DeleteMapping(gomock.Any(), args.req.GetEmail(), args.req.GetUserGroup()).Return(epifierrors.ErrRecordNotFound).Times(1)
			},
			want: &userGroupPb.DeleteEmailGroupMappingResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.DeleteEmailGroupMapping(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteEmailGroupMapping() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteEmailGroupMapping() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_AddMappings(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *userGroupPb.AddMappingsRequest
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, md *mockedDependencies)
		want      *userGroupPb.AddMappingsResponse
		wantErr   bool
	}{
		{
			name: "invalid argument - default user group",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddMappingsRequest{
					UserGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
					IdentifierValues: []*userGroupPb.IdentifierValue{
						{
							Identifier: &userGroupPb.IdentifierValue_Email{
								Email: "<EMAIL>",
							},
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.AddMappingsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "invalid argument - empty email",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddMappingsRequest{
					UserGroup:        commontypes.UserGroup_INTERNAL,
					IdentifierValues: nil,
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.AddMappingsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "Agent missing",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddMappingsRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValues: []*userGroupPb.IdentifierValue{
						{
							Identifier: &userGroupPb.IdentifierValue_Email{
								Email: "<EMAIL>",
							},
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.AddMappingsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "add successful",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddMappingsRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValues: []*userGroupPb.IdentifierValue{
						{
							Identifier: &userGroupPb.IdentifierValue_Email{
								Email: "<EMAIL>",
							},
						},
					},
					Agent: &userGroupPb.Agent{
						Email:  "<EMAIL>",
						Reason: "reason",
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().BatchUpsertUserGroupMappings(gomock.Any(), args.req.GetIdentifierValues(), args.req.GetUserGroup(), args.req.GetAgent()).Return(nil)
			},
			want: &userGroupPb.AddMappingsResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "add unsuccessful",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.AddMappingsRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValues: []*userGroupPb.IdentifierValue{
						{
							Identifier: &userGroupPb.IdentifierValue_Email{
								Email: "<EMAIL>",
							},
						},
					},
					Agent: &userGroupPb.Agent{
						Email:  "<EMAIL>",
						Reason: "reason",
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().BatchUpsertUserGroupMappings(gomock.Any(), args.req.GetIdentifierValues(), args.req.GetUserGroup(), args.req.GetAgent()).Return(errors.New("add mapping error"))
			},
			want: &userGroupPb.AddMappingsResponse{
				Status: rpc.StatusInternalWithDebugMsg(errors.New("add mapping error").Error()),
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.AddMappings(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteMapping() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteMapping() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_DeleteMapping(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *userGroupPb.DeleteMappingRequest
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, md *mockedDependencies)
		want      *userGroupPb.DeleteMappingResponse
		wantErr   bool
	}{
		{
			name: "invalid argument - default user group",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteMappingRequest{
					UserGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.DeleteMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "invalid argument - empty email",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteMappingRequest{
					UserGroup:       commontypes.UserGroup_INTERNAL,
					IdentifierValue: nil,
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.DeleteMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "Agent missing",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.DeleteMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "deleted successfully",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
					Agent: &userGroupPb.Agent{
						Email:  "<EMAIL>",
						Reason: "reason",
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().DeleteUserGroupMapping(gomock.Any(), args.req.GetIdentifierValue(), args.req.GetUserGroup(), args.req.GetAgent()).Return(nil)
			},
			want: &userGroupPb.DeleteMappingResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "deletion unsuccessful",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.DeleteMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
					Agent: &userGroupPb.Agent{
						Email:  "<EMAIL>",
						Reason: "reason",
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().DeleteUserGroupMapping(gomock.Any(), args.req.GetIdentifierValue(), args.req.GetUserGroup(), args.req.GetAgent()).Return(errors.New("delete error"))
			},
			want: &userGroupPb.DeleteMappingResponse{
				Status: rpc.StatusInternalWithDebugMsg(errors.New("delete error").Error()),
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.DeleteMapping(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteMapping() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteMapping() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_CheckMapping(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *userGroupPb.CheckMappingRequest
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, md *mockedDependencies)
		want      *userGroupPb.CheckMappingResponse
		wantErr   bool
	}{
		{
			name: "invalid argument - default user group",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.CheckMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "invalid argument - empty email",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.CheckMappingRequest{
					UserGroup:       commontypes.UserGroup_INTERNAL,
					IdentifierValue: nil,
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.CheckMappingResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "check successful",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetUserGroupMappingByIdentifier(gomock.Any(), args.req.GetIdentifierValue(), args.req.GetUserGroup()).Return(nil, nil)
			},
			want: &userGroupPb.CheckMappingResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "check unsuccessful",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_INTERNAL,
					IdentifierValue: &userGroupPb.IdentifierValue{
						Identifier: &userGroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				},
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetUserGroupMappingByIdentifier(gomock.Any(), args.req.GetIdentifierValue(), args.req.GetUserGroup()).Return(nil, errors.New("delete error"))
			},
			want: &userGroupPb.CheckMappingResponse{
				Status: rpc.StatusInternalWithDebugMsg(errors.New("delete error").Error()),
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.CheckMapping(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckMapping() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteMapping() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetGroupsMappedToIdentifier(t *testing.T) {
	t.Parallel()

	var (
		getGroupListReqEmail = &userGroupPb.GetGroupsMappedToIdentifierRequest{
			IdentifierValue: &userGroupPb.IdentifierValue{
				Identifier: &userGroupPb.IdentifierValue_Email{
					Email: "<EMAIL>",
				},
			},
		}
		getGroupListReqPhone = &userGroupPb.GetGroupsMappedToIdentifierRequest{
			IdentifierValue: &userGroupPb.IdentifierValue{
				Identifier: &userGroupPb.IdentifierValue_PhoneNumber{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 99887711,
					},
				},
			},
		}
	)
	type args struct {
		ctx context.Context
		req *userGroupPb.GetGroupsMappedToIdentifierRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *userGroupPb.GetGroupsMappedToIdentifierResponse
		wantMocks func(args args, md *mockedDependencies)
		wantErr   bool
	}{
		{
			name: "error because identifier not passed",
			args: args{
				ctx: context.Background(),
				req: &userGroupPb.GetGroupsMappedToIdentifierRequest{},
			},
			wantMocks: func(args args, md *mockedDependencies) {},
			want: &userGroupPb.GetGroupsMappedToIdentifierResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("No identifier is provided"),
			},
			wantErr: false,
		},
		{
			name: "error returned by dao layer",
			args: args{
				ctx: context.Background(),
				req: getGroupListReqEmail,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetGroupListForIdentifier(context.Background(), getGroupListReqEmail.GetIdentifierValue()).
					Return(groupList1, nil)
			},
			want: &userGroupPb.GetGroupsMappedToIdentifierResponse{
				Status: rpc.StatusOk(),
				Groups: groupList1,
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: getGroupListReqPhone,
			},
			want: &userGroupPb.GetGroupsMappedToIdentifierResponse{
				Status: rpc.StatusOk(),
				Groups: groupList1,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.userGroupMappingDao.EXPECT().GetGroupListForIdentifier(context.Background(), getGroupListReqPhone.GetIdentifierValue()).
					Return(groupList1, nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := serviceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.GetGroupsMappedToIdentifier(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGroupsMappedToIdentifier() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGroupsMappedToIdentifier() got = %v, want %v", got, tt.want)
			}
		})
	}
}
