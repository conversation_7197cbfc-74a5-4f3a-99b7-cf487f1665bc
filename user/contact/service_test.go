package contact

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	userContactPb "github.com/epifi/gamma/api/user/contact"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/lock/mocks"
	"github.com/epifi/gamma/user/contact/dao/model"
	daoMock "github.com/epifi/gamma/user/contact/test/mocks"
)

func TestService_RecordHashedContacts(t *testing.T) {
	t.Parallel()
	ctx := context.WithValue(context.Background(), epificontext.CtxActorKey, "actor-1")
	type mockGetByActorId struct {
		enable  bool
		actorId string
		want    []*userContactPb.UserContact
		err     error
	}

	type mockDelete struct {
		enable      bool
		actorId     string
		phoneNumber []string
		err         error
	}

	type mockCreate struct {
		enable   bool
		contacts []*userContactPb.UserContact
		err      error
	}

	tests := []struct {
		name             string
		req              *userContactPb.RecordHashedContactsRequest
		mockGetByActorId mockGetByActorId
		mockDelete       mockDelete
		mockCreate       mockCreate
		want             *userContactPb.RecordHashedContactsResponse
		wantErr          bool
	}{
		{
			name: "Only Add contacts successfully",
			req: &userContactPb.RecordHashedContactsRequest{
				Contact: []*userContactPb.RecordHashedContactsRequest_Contact{
					{
						PhoneNumberHash: "1234",
					},
					{
						PhoneNumberHash: "5678",
					},
					{
						PhoneNumberHash: "5678",
					},
				},
			},
			mockCreate: mockCreate{
				enable: true,
				contacts: []*userContactPb.UserContact{
					{
						ActorId:         "actor-1",
						PhoneNumberHash: "1234",
					},
					{
						ActorId:         "actor-1",
						PhoneNumberHash: "5678",
					},
				},
				err: nil,
			},
			want: &userContactPb.RecordHashedContactsResponse{Status: rpc.StatusOk()},
		},
		{
			name: "Error while deleting the contacts",
			req: &userContactPb.RecordHashedContactsRequest{
				Contact: []*userContactPb.RecordHashedContactsRequest_Contact{
					{
						PhoneNumberHash: "123456",
						IsDeleted:       true,
					},
					{
						PhoneNumberHash: "5678",
					},
				},
			},
			mockDelete: mockDelete{
				enable:  true,
				actorId: "actor-1",
				phoneNumber: []string{
					"123456",
				},
				err: errors.New("error deleting contacts"),
			},
			want: &userContactPb.RecordHashedContactsResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "Error adding contacts",
			req: &userContactPb.RecordHashedContactsRequest{
				Contact: []*userContactPb.RecordHashedContactsRequest_Contact{
					{
						PhoneNumberHash: "1234",
						IsDeleted:       true,
					},
					{
						PhoneNumberHash: "5678",
					},
				},
			},
			mockDelete: mockDelete{
				enable:  true,
				actorId: "actor-1",
				phoneNumber: []string{
					"1234",
				},
			},
			mockCreate: mockCreate{
				enable: true,
				contacts: []*userContactPb.UserContact{
					{
						ActorId:         "actor-1",
						PhoneNumberHash: "5678",
					},
				},
				err: errors.New("error adding contacts"),
			},
			want: &userContactPb.RecordHashedContactsResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "Added and deleted contacts successfully",
			req: &userContactPb.RecordHashedContactsRequest{
				Contact: []*userContactPb.RecordHashedContactsRequest_Contact{
					{
						PhoneNumberHash: "1234",
						IsDeleted:       true,
					},
					{
						PhoneNumberHash: "5678",
					},
				},
			},
			mockDelete: mockDelete{
				enable:  true,
				actorId: "actor-1",
				phoneNumber: []string{
					"1234",
				},
			},
			mockCreate: mockCreate{
				enable: true,
				contacts: []*userContactPb.UserContact{
					{
						ActorId:         "actor-1",
						PhoneNumberHash: "5678",
					},
				},
			},
			want: &userContactPb.RecordHashedContactsResponse{Status: rpc.StatusOk()},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockUserContactDao := daoMock.NewMockUserContactsDao(ctrl)
			mockLockManager := mocks.NewMockILockManager(ctrl)
			s := NewService(mockUserContactDao, nil, nil, nil, gconf, mockLockManager)
			mockLockManager.EXPECT().GetLock(gomock.Any(), recordHashedContactLockPrefix+"actor-1", gconf.RecordHashedContactLockTimeout()).Return(nil, nil)
			if tt.mockGetByActorId.enable {
				mockUserContactDao.EXPECT().GetByActorId(ctx, tt.mockGetByActorId.actorId).
					Return(tt.mockGetByActorId.want, tt.mockGetByActorId.err)
			}
			if tt.mockDelete.enable {
				mockUserContactDao.EXPECT().BatchDelete(ctx, tt.mockDelete.actorId, tt.mockDelete.phoneNumber).
					Return(tt.mockDelete.err)
			}
			if tt.mockCreate.enable {
				mockUserContactDao.EXPECT().BatchUpsert(ctx, tt.mockCreate.contacts).
					Return(tt.mockCreate.err)
			}
			got, err := s.RecordHashedContacts(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecordHashedContacts() got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DumpHashedContacts() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestService_SyncContactDetails(t *testing.T) {
	t.Parallel()
	ctx := context.WithValue(context.Background(), epificontext.CtxActorKey, "actor-1")
	newOnFiTimeStamp1 := time.Now().Add(-1 * 10 * time.Hour)
	newOnFiTimeStamp2 := time.Now().Add(-1 * 20 * time.Hour)
	notNewOnFiTimeStamp := time.Now().Add(-1 * 30 * time.Hour)

	type mockStruct struct {
		mockUserContactDao *daoMock.MockUserContactsDao
		mockActorClient    *mockActor.MockActorClient
		mockUserClient     *mockUser.MockUsersClient
		mockContactPropDao *daoMock.MockContactPropertiesDao
	}

	tests := []struct {
		name    string
		req     *userContactPb.SyncContactDetailsRequest
		want    *userContactPb.SyncContactDetailsResponse
		mocks   func(mock *mockStruct)
		wantErr bool
	}{
		{
			name: "error fetching users by hashed phone number",
			req:  &userContactPb.SyncContactDetailsRequest{},
			mocks: func(mock *mockStruct) {
				mock.mockUserContactDao.EXPECT().GetByActorId(ctx, "actor-1").
					Return([]*userContactPb.UserContact{
						{
							PhoneNumberHash: "1234",
						},
					}, nil)
				mock.mockContactPropDao.EXPECT().GetByHashedContacts(ctx, []string{
					"1234",
				}).Return(nil, fmt.Errorf("error fetching users"))
			},
			want: &userContactPb.SyncContactDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Errorf("error fetching users").Error()),
			},
		},
		{
			name: "no contacts present for actor",
			req:  &userContactPb.SyncContactDetailsRequest{},
			mocks: func(mock *mockStruct) {
				mock.mockUserContactDao.EXPECT().GetByActorId(ctx, "actor-1").
					Return(nil, nil)
			},
			want: &userContactPb.SyncContactDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "No contacts present on fi",
			req:  &userContactPb.SyncContactDetailsRequest{},
			mocks: func(mock *mockStruct) {
				mock.mockUserContactDao.EXPECT().GetByActorId(ctx, "actor-1").
					Return([]*userContactPb.UserContact{
						{
							PhoneNumberHash: "1234",
						},
						{
							PhoneNumberHash: "5678",
						},
					}, nil)
				mock.mockContactPropDao.EXPECT().GetByHashedContacts(ctx, []string{
					"1234",
					"5678",
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &userContactPb.SyncContactDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "Synced successfully. Some contacts on fi and some eligible for new on fi",
			req:  &userContactPb.SyncContactDetailsRequest{},
			mocks: func(mock *mockStruct) {
				mock.mockUserContactDao.EXPECT().GetByActorId(ctx, "actor-1").
					Return([]*userContactPb.UserContact{
						{
							PhoneNumberHash: "1234",
						},
						{
							PhoneNumberHash: "5678",
						},
						{
							PhoneNumberHash: "4789",
						},
						{
							PhoneNumberHash: "3214",
						},
					}, nil)
				mock.mockContactPropDao.EXPECT().GetByHashedContacts(ctx, []string{
					"1234",
					"5678",
					"4789",
					"3214",
				}).Return([]*model.ContactProperties{
					{
						HashedPhoneNumber:     "1234",
						OnboardingCompletedAt: &newOnFiTimeStamp2,
						KycName: &commontypes.Name{
							FirstName: "test",
							LastName:  "user1",
						},
						ActorId: "actor-1",
					},
					{
						HashedPhoneNumber:     "5678",
						OnboardingCompletedAt: &newOnFiTimeStamp1,
						KycName: &commontypes.Name{
							FirstName: "test",
							LastName:  "user2",
						},
						ActorId: "actor-2",
					},
					{
						HashedPhoneNumber:     "4789",
						OnboardingCompletedAt: &notNewOnFiTimeStamp,
						KycName: &commontypes.Name{
							FirstName: "test",
							LastName:  "user3",
						},
						ActorId: "actor-3",
					},
				}, nil)
			},
			want: &userContactPb.SyncContactDetailsResponse{
				Status: rpc.StatusOk(),
				ContactDetails: []*userContactPb.SyncContactDetailsResponse_ContactDetails{
					{
						PhoneNumberHash: "1234",
						VerifiedName: &commontypes.Name{
							FirstName: "test",
							LastName:  "user1",
						},
						ColourCode: actor.GetColourCodeForActor("actor-1"),
					},
					{
						PhoneNumberHash: "5678",
						VerifiedName: &commontypes.Name{
							FirstName: "test",
							LastName:  "user2",
						},
						ColourCode: actor.GetColourCodeForActor("actor-2"),
						IsNewOnFi:  true,
					},
					{
						PhoneNumberHash: "4789",
						VerifiedName: &commontypes.Name{
							FirstName: "test",
							LastName:  "user3",
						},
						ColourCode: actor.GetColourCodeForActor("actor-3"),
					},
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockUserContactDao := daoMock.NewMockUserContactsDao(ctrl)
			mockActorClient := mockActor.NewMockActorClient(ctrl)
			mockUserClient := mockUser.NewMockUsersClient(ctrl)
			mockContactPropDao := daoMock.NewMockContactPropertiesDao(ctrl)
			s := NewService(mockUserContactDao, mockContactPropDao, mockActorClient, mockUserClient, gconf, nil)
			tt.mocks(&mockStruct{
				mockUserContactDao: mockUserContactDao,
				mockActorClient:    mockActorClient,
				mockUserClient:     mockUserClient,
				mockContactPropDao: mockContactPropDao,
			})
			got, err := s.SyncContactDetails(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncContactDetails() got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SyncContactDetails() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestService_GetContactsByBatch(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	pgResp := &rpc.PageContextResponse{AfterToken: "after-token", HasAfter: false}
	contact1 := &userContactPb.UserContact{
		Id:              "some-id",
		ActorId:         "someActorId",
		PhoneNumberHash: "some-phone-number-hash",
	}
	type args struct {
		ctx context.Context
		req *userContactPb.GetContactsByBatchRequest
	}
	type mockBatchGetByActorId struct {
		enable      bool
		identifier  *userContactPb.ContactsQueryIdentifier
		pageToken   interface{}
		pageLimit   interface{}
		want        []*userContactPb.UserContact
		wantPageCtx *rpc.PageContextResponse
		err         error
	}
	tests := []struct {
		name    string
		args    *args
		mockdao mockBatchGetByActorId
		want    *userContactPb.GetContactsByBatchResponse
		wantErr bool
	}{
		{
			name: "get the error for wrong token",
			args: &args{
				ctx: context.Background(),
				req: &userContactPb.GetContactsByBatchRequest{
					Identifier: &userContactPb.ContactsQueryIdentifier{
						Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
							ActorId: "some-actor",
						},
					},
					PageContextRequest: &rpc.PageContextRequest{Token: &rpc.
						PageContextRequest_AfterToken{AfterToken: "random"}},
				},
			},
			mockdao: mockBatchGetByActorId{
				enable: false,
				identifier: &userContactPb.ContactsQueryIdentifier{
					Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
						ActorId: "some-actor",
					}},
				want:        nil,
				wantPageCtx: nil,
			},
			want: &userContactPb.GetContactsByBatchResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("failed to unmarshal page token: illegal base64 data at input byte 4"),
			},
			wantErr: false,
		},
		{
			name: "error no record found",
			args: &args{
				ctx: context.Background(),
				req: &userContactPb.GetContactsByBatchRequest{
					Identifier: &userContactPb.ContactsQueryIdentifier{
						Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
							ActorId: "some-actor",
						},
					},
				},
			},
			mockdao: mockBatchGetByActorId{
				enable: true,
				identifier: &userContactPb.ContactsQueryIdentifier{
					Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
						ActorId: "some-actor",
					}},
				pageToken:   gomock.Any(),
				pageLimit:   gomock.Any(),
				want:        nil,
				err:         epifierrors.ErrRecordNotFound,
				wantPageCtx: nil,
			},
			want: &userContactPb.GetContactsByBatchResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error some db error",
			args: &args{
				ctx: context.Background(),
				req: &userContactPb.GetContactsByBatchRequest{
					Identifier: &userContactPb.ContactsQueryIdentifier{
						Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
							ActorId: "some-actor",
						},
					},
				},
			},
			mockdao: mockBatchGetByActorId{
				enable: true,
				identifier: &userContactPb.ContactsQueryIdentifier{
					Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
						ActorId: "some-actor",
					}},
				pageToken:   gomock.Any(),
				pageLimit:   gomock.Any(),
				want:        nil,
				err:         errors.New("some error"),
				wantPageCtx: nil,
			},
			want: &userContactPb.GetContactsByBatchResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching users contact details from db"),
			},
			wantErr: false,
		},
		{
			name: "successfully get the contacts for actor id",
			args: &args{
				ctx: context.Background(),
				req: &userContactPb.GetContactsByBatchRequest{
					Identifier: &userContactPb.ContactsQueryIdentifier{
						Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
							ActorId: "some-actor",
						},
					},
				},
			},
			mockdao: mockBatchGetByActorId{
				enable: true,
				identifier: &userContactPb.ContactsQueryIdentifier{
					Identifier: &userContactPb.ContactsQueryIdentifier_ActorId{
						ActorId: "some-actor",
					}},
				pageToken: gomock.Any(),
				pageLimit: gomock.Any(),
				want: []*userContactPb.UserContact{
					contact1,
				},
				wantPageCtx: pgResp,
			},
			want: &userContactPb.GetContactsByBatchResponse{
				Status: rpc.StatusOk(),
				Contacts: []*userContactPb.UserContact{
					contact1,
				},
				PageContextResponse: pgResp,
			},
			wantErr: false,
		},
		{
			name: "successfully get the contacts for hashed phone no",
			args: &args{
				ctx: context.Background(),
				req: &userContactPb.GetContactsByBatchRequest{
					Identifier: &userContactPb.ContactsQueryIdentifier{
						Identifier: &userContactPb.ContactsQueryIdentifier_HashedPhoneNumber{
							HashedPhoneNumber: "alksdjlaksd",
						},
					},
				},
			},
			mockdao: mockBatchGetByActorId{
				enable: true,
				identifier: &userContactPb.ContactsQueryIdentifier{
					Identifier: &userContactPb.ContactsQueryIdentifier_HashedPhoneNumber{
						HashedPhoneNumber: "alksdjlaksd",
					},
				},
				pageToken: gomock.Any(),
				pageLimit: gomock.Any(),
				want: []*userContactPb.UserContact{
					contact1,
				},
				wantPageCtx: pgResp,
			},
			want: &userContactPb.GetContactsByBatchResponse{
				Status: rpc.StatusOk(),
				Contacts: []*userContactPb.UserContact{
					contact1,
				},
				PageContextResponse: pgResp,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockUserContactDao := daoMock.NewMockUserContactsDao(ctrl)
			s := NewService(mockUserContactDao, nil, nil, nil, gconf, nil)
			if tt.mockdao.enable {
				mockUserContactDao.EXPECT().GetByEntityInBatch(ctx, tt.mockdao.pageToken, tt.mockdao.pageLimit,
					tt.mockdao.identifier).
					Return(tt.mockdao.want, tt.mockdao.wantPageCtx, tt.mockdao.err)
			}
			got, err := s.GetContactsByBatch(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetContactsByActorId() got err: %v, want: %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetContactsByActorId() got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}
