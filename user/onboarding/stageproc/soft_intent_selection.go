package stageproc

import (
	"context"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	"github.com/epifi/gamma/api/creditreportv2"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	onbDl "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
)

const (
	SoftIntentScreenFeature = "feature_soft_intent_selection"
)

type SoftIntentSelectionStage struct {
	eventLogger userEvents.EventLogger
	onbConf     *genconf.OnboardingConfig
	userClient  userPb.UsersClient
	crClient    creditreportv2.CreditReportManagerClient
}

func NewSoftIntentSelectionStage(eventLogger userEvents.EventLogger, onbConf *genconf.OnboardingConfig, userClient userPb.UsersClient,
	crClient creditreportv2.CreditReportManagerClient) *SoftIntentSelectionStage {
	return &SoftIntentSelectionStage{
		eventLogger: eventLogger,
		onbConf:     onbConf,
		userClient:  userClient,
		crClient:    crClient,
	}
}

func (s *SoftIntentSelectionStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.GetOnb()
	if isUserPastCurrentStage(SAOnboardingStagesOrder, req.GetOnb()) {
		return nil, NoActionError
	}

	// consider stage succeeded if metadata is not nil since user can select zero intents
	if req.GetOnb().GetStageMetadata().GetSoftIntentSelectionMetadata() != nil {
		return nil, NoActionError
	}

	if !isSoftIntentSelectionScreenEnabledForUser(ctx, req.GetOnb().GetActorId(), s.onbConf.SoftIntentSelectionConfig()) {
		return nil, NoActionError
	}

	acIntent, acChannel, err := getIntentAndChannelFromAcquisitionInfo(ctx, s.onbConf, s.userClient, onb.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error in getIntentAndChannelFromAcquisitionInfo")
	}
	if acIntent != userPb.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED || acChannel == userPb.AcquisitionChannel_ACQUISITION_CHANNEL_REFERRALS {
		return nil, SkipStageError
	}

	if s.onbConf.SoftIntentSelectionConfig().PreOnboardingCompletionSoftIntentScreenConfig().Enabled(ctx) {
		return &StageProcessorResponse{
			NextAction: &dlPb.Deeplink{
				Screen: dlPb.Screen_ONBOARDING_SOFT_INTENT_SELECTION,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.OnboardingSoftIntentSelectionScreenOptions{
					EntryPoint: onbPb.SoftIntentSelectionEntryPoint_SOFT_INTENT_SELECTION_ENTRY_POINT_ONBOARDING_SOFT_INTENT_SELECTION_STAGE.String(),
				}),
			},
		}, nil
	}

	return nil, SkipStageError
}

func isSoftIntentSelectionScreenEnabledForUser(ctx context.Context, actorId string, conf *genconf.SoftIntentSelectionConfig) bool {
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.SoftIntentCollectionScreenFeatureConfig()) &&
		CheckReleaseStickinessConstraint(ctx, actorId, SoftIntentScreenFeature, conf.SoftIntentCollectionScreenPercentageRollout())
}
