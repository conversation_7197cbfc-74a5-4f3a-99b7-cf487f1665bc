package actor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"sync"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	latLngPb "google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	locationPb "github.com/epifi/gamma/api/auth/location"
	mocks3 "github.com/epifi/gamma/api/auth/location/mocks"
	mocks4 "github.com/epifi/gamma/api/auth/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upi"
	userPb "github.com/epifi/gamma/api/user"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestProcessor_GetInternalDeviceForActor(t *testing.T) {
	udpCompleteList := []*userPb.UserDeviceProperty{
		{
			DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
			PropertyValue:  &types.PropertyValue{PropValue: &types.PropertyValue_DeviceId{DeviceId: "1234"}},
		},
		{
			DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
			PropertyValue:  &types.PropertyValue{PropValue: &types.PropertyValue_LocationToken{LocationToken: "1234"}},
		},
		{
			DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
			PropertyValue: &types.PropertyValue{
				PropValue: &types.PropertyValue_DeviceModelInfo{DeviceModelInfo: &commontypes.DeviceModelInfo{
					Platform: commontypes.Platform_ANDROID, SwVersion: "14",
				}},
			},
		},
	}
	udpWithoutDeviceId := []*userPb.UserDeviceProperty{
		{
			DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
			PropertyValue:  &types.PropertyValue{PropValue: &types.PropertyValue_LocationToken{LocationToken: "1234"}},
		},
		{
			DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
			PropertyValue: &types.PropertyValue{
				PropValue: &types.PropertyValue_DeviceModelInfo{DeviceModelInfo: &commontypes.DeviceModelInfo{
					Platform: commontypes.Platform_ANDROID, SwVersion: "14",
				}},
			},
		},
	}

	type args struct {
		ctx     context.Context
		actorId string
	}
	type mockClients struct {
		mockActorClient    *mocks.MockActorClient
		mockUserClient     *mocks2.MockUsersClient
		mockLocationClient *mocks3.MockLocationClient
		mockAuthClient     *mocks4.MockAuthClient
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mc *mockClients)
		want       *upi.Device
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "should return transient error if UDP calls returns error",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(nil, fmt.Errorf("network error"))
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return permanent error if phone number is not found for the actor",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpCompleteList,
				}, nil)

				mc.mockLocationClient.EXPECT().GetCoordinates(context.Background(), &locationPb.GetCoordinatesRequest{
					LocationToken: "1234",
				}).Return(&locationPb.GetCoordinatesResponse{Status: rpc.StatusOk(), LatLng: &latLngPb.LatLng{}}, nil)

				mc.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrPermanent)
			},
		},
		{
			name: "should return transient error if fetching phone number fails",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpCompleteList,
				}, nil)

				mc.mockLocationClient.EXPECT().GetCoordinates(context.Background(), &locationPb.GetCoordinatesRequest{
					LocationToken: "1234",
				}).Return(&locationPb.GetCoordinatesResponse{Status: rpc.StatusOk(), LatLng: &latLngPb.LatLng{}}, nil)

				mc.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return transient error if fallback RPC call returns error",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpWithoutDeviceId,
				}, nil)

				mc.mockAuthClient.EXPECT().GetDeviceDetails(context.Background(), &authPb.GetDeviceDetailsRequest{
					ActorId: "actor-id",
				}).Return(nil, fmt.Errorf("network error"))
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return permanent error if fallback RPC call returns NotFound response",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpWithoutDeviceId,
				}, nil)

				mc.mockAuthClient.EXPECT().GetDeviceDetails(context.Background(), &authPb.GetDeviceDetailsRequest{
					ActorId: "actor-id",
				}).Return(&authPb.GetDeviceDetailsResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrPermanent)
			},
		},
		{
			name: "should return transient error if fallback RPC call returns non-OK response",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpWithoutDeviceId,
				}, nil)

				mc.mockAuthClient.EXPECT().GetDeviceDetails(context.Background(), &authPb.GetDeviceDetailsRequest{
					ActorId: "actor-id",
				}).Return(&authPb.GetDeviceDetailsResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return the upi.Device by using UDP response ONLY if device-id is found",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpCompleteList,
				}, nil)

				mc.mockLocationClient.EXPECT().GetCoordinates(context.Background(), &locationPb.GetCoordinatesRequest{
					LocationToken: "1234",
				}).Return(&locationPb.GetCoordinatesResponse{Status: rpc.StatusOk(), LatLng: &latLngPb.LatLng{}}, nil)

				mc.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:       rpc.StatusOk(),
					MobileNumber: &commontypes.PhoneNumber{NationalNumber: 1234567890},
				}, nil)
			},
			want: &upi.Device{
				PhoneNumber:   &commontypes.PhoneNumber{NationalNumber: 1234567890},
				Geocode:       &latLngPb.LatLng{},
				Id:            "1234",
				OsVersion:     "Android 14",
				App:           upi.Device_ANDROID,
				Type:          "MOB",
				LocationToken: "1234",
				IpAddr:        "",
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return the upi.Device by using fallback RPC if device-id is not found in UDP",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpWithoutDeviceId,
				}, nil)

				mc.mockAuthClient.EXPECT().GetDeviceDetails(context.Background(), &authPb.GetDeviceDetailsRequest{
					ActorId: "actor-id",
				}).Return(&authPb.GetDeviceDetailsResponse{
					Status: rpc.StatusOk(),
					Device: &commontypes.Device{DeviceId: "12345"},
				}, nil)

				mc.mockLocationClient.EXPECT().GetCoordinates(context.Background(), &locationPb.GetCoordinatesRequest{
					LocationToken: "1234",
				}).Return(&locationPb.GetCoordinatesResponse{Status: rpc.StatusOk(), LatLng: &latLngPb.LatLng{}}, nil)

				mc.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:       rpc.StatusOk(),
					MobileNumber: &commontypes.PhoneNumber{NationalNumber: 1234567890},
				}, nil)
			},
			want: &upi.Device{
				PhoneNumber:   &commontypes.PhoneNumber{NationalNumber: 1234567890},
				Geocode:       &latLngPb.LatLng{},
				Id:            "12345",
				OsVersion:     "Android 14",
				App:           upi.Device_ANDROID,
				Type:          "MOB",
				LocationToken: "1234",
				IpAddr:        "",
			},
			wantErr: assert.NoError,
		},
		{
			name: "should still return upi.Device if fetching LatLng from location-token fails",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(mc *mockClients) {
				mc.mockUserClient.EXPECT().GetUserDeviceProperties(context.Background(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: "actor-id",
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status:                 rpc.StatusOk(),
					UserDevicePropertyList: udpCompleteList,
				}, nil)

				mc.mockLocationClient.EXPECT().GetCoordinates(context.Background(), &locationPb.GetCoordinatesRequest{
					LocationToken: "1234",
				}).Return(&locationPb.GetCoordinatesResponse{Status: rpc.StatusInternal()}, nil)

				mc.mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				}).Return(&actorPb.GetEntityDetailsByActorIdResponse{
					Status:       rpc.StatusOk(),
					MobileNumber: &commontypes.PhoneNumber{NationalNumber: 1234567890},
				}, nil)
			},
			want: &upi.Device{
				PhoneNumber:   &commontypes.PhoneNumber{NationalNumber: 1234567890},
				Id:            "1234",
				OsVersion:     "Android 14",
				App:           upi.Device_ANDROID,
				Type:          "MOB",
				LocationToken: "1234",
				IpAddr:        "",
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockActorClient := mocks.NewMockActorClient(ctrl)
			mockUserClient := mocks2.NewMockUsersClient(ctrl)
			mockLocationClient := mocks3.NewMockLocationClient(ctrl)
			mockAuthClient := mocks4.NewMockAuthClient(ctrl)

			p := NewProcessor(mockActorClient, mockUserClient, mockLocationClient, mockAuthClient)
			tt.setupMocks(&mockClients{
				mockActorClient:    mockActorClient,
				mockUserClient:     mockUserClient,
				mockLocationClient: mockLocationClient,
				mockAuthClient:     mockAuthClient,
			})

			got, err := p.GetInternalDeviceForActor(tt.args.ctx, tt.args.actorId)
			if !tt.wantErr(t, err) {
				t.Errorf("GetInternalDeviceForActor() error = %v", err)
				return
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("GetInternalDeviceForActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProcessor_GetInternalDeviceFromRegisteredDevice(t *testing.T) {
	type mockClients struct {
		mockActorClient *mocks.MockActorClient
		mockAuthClient  *mocks4.MockAuthClient
	}

	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(wg *sync.WaitGroup, mockClients *mockClients)
		want       *upi.Device
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "should return transient error if GetDeviceAuth call fails",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return nil, fmt.Errorf("some transient error")
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk()}, nil
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return permanent error if no device details are found",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{Status: rpc.StatusRecordNotFound()}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk()}, nil
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrPermanent)
			},
		},
		{
			name: "should return transient error if GetDeviceAuth call returns non-OK response",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{Status: rpc.StatusInternal()}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusOk()}, nil
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return transient error if GetEntityDetailsByActorId call fails",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{Status: rpc.StatusOk()}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return nil, fmt.Errorf("some transient error")
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return permanent error if no entity-details are found",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{Status: rpc.StatusOk()}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusRecordNotFound()}, nil
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrPermanent)
			},
		},
		{
			name: "should return permanent error if invalid-argument is returned while fetching entity-details",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{Status: rpc.StatusOk()}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusInvalidArgument()}, nil
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrPermanent)
			},
		},
		{
			name: "should return transient error if GetEntityDetailsByActorId return non-OK response",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{Status: rpc.StatusOk()}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{Status: rpc.StatusInternal()}, nil
					})
			},
			want: nil,
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, epifierrors.ErrTransient)
			},
		},
		{
			name: "should return the UPI Device if dependant calls succeed",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id",
			},
			setupMocks: func(wg *sync.WaitGroup, mockClients *mockClients) {
				wg.Add(2)
				mockClients.mockAuthClient.
					EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *authPb.GetDeviceAuthRequest, opts ...interface{}) (*authPb.GetDeviceAuthResponse, error) {
						defer wg.Done()
						return &authPb.GetDeviceAuthResponse{
							Status: rpc.StatusOk(),
							Device: &commontypes.Device{
								DeviceId:      "device-id",
								LatLng:        &latLngPb.LatLng{},
								SwVersion:     "14",
								Platform:      commontypes.Platform_ANDROID,
								LocationToken: "location-token",
							},
						}, nil
					})
				mockClients.mockActorClient.
					EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-id"}).
					DoAndReturn(func(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest, opts ...interface{}) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
						defer wg.Done()
						return &actorPb.GetEntityDetailsByActorIdResponse{
							Status:       rpc.StatusOk(),
							MobileNumber: &commontypes.PhoneNumber{NationalNumber: 9999999999},
						}, nil
					})
			},
			want: &upi.Device{
				PhoneNumber:   &commontypes.PhoneNumber{NationalNumber: 9999999999},
				Geocode:       &latLngPb.LatLng{},
				Id:            "device-id",
				OsVersion:     "Android 14",
				App:           upi.Device_ANDROID,
				Type:          "MOB",
				LocationToken: "location-token",
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockActorClient := mocks.NewMockActorClient(ctrl)
			mockAuthClient := mocks4.NewMockAuthClient(ctrl)

			var (
				wg sync.WaitGroup
			)
			defer wg.Wait()

			p := NewProcessor(mockActorClient, nil, nil, mockAuthClient)
			tt.setupMocks(&wg, &mockClients{mockActorClient: mockActorClient, mockAuthClient: mockAuthClient})

			got, err := p.GetInternalDeviceFromRegisteredDevice(tt.args.ctx, tt.args.actorId)
			if !tt.wantErr(t, err) {
				t.Errorf("GetInternalDeviceFromRegisteredDevice() error = %v", err)
				return
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("GetInternalDeviceFromRegisteredDevice() got = %v, want %v", got, tt.want)
			}
		})
	}
}
