package dao

import (
	"context"
	"fmt"
	"time"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/nulltypes"

	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/order/dao/model"
)

type OrderAttemptDaoCRDB struct {
	db *gormv2.DB
}

func NewOrderAttemptDao(db types.EpifiCRDB) *OrderAttemptDaoCRDB {
	return &OrderAttemptDaoCRDB{db: db}
}

// Ensure OrderAttemptDaoCRDB implements OrderAttemptDao at compile time
var _ OrderAttemptDao = &OrderAttemptDaoCRDB{}

// Create creates a new entry in the DB
func (o *OrderAttemptDaoCRDB) Create(ctx context.Context, orderAttempt *orderPb.OrderAttempt) (*orderPb.OrderAttempt, error) {
	defer metric_util.TrackDuration("order/dao", "OrderAttemptDaoCRDB", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, o.db)
	attemptModel := convertToOrderAttemptModel(orderAttempt)

	if err := db.Create(&attemptModel).Error; err != nil {
		return nil, fmt.Errorf("failed to create order attempt entry: %w", err)
	}

	return convertToOrderAttemptProto(attemptModel), nil
}

// GetByOrderIdAndStage fetches order attempt entry by order and order stage
func (o *OrderAttemptDaoCRDB) GetByOrderIdAndStage(ctx context.Context, orderId string, stage orderPb.OrderStage) (*orderPb.OrderAttempt, error) {
	defer metric_util.TrackDuration("order/dao", "OrderAttemptDaoCRDB", "GetByOrderIdAndStage", time.Now())
	modelAttempt := &model.OrderAttempt{}
	db := gormctxv2.FromContextOrDefault(ctx, o.db)

	if err := db.Model(&model.OrderAttempt{}).
		Where("order_id = ? AND stage = ?", orderId, stage).
		First(&modelAttempt).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch order attemp record: %w", err)
	}

	return convertToOrderAttemptProto(modelAttempt), nil
}

// IncrementAttemptCount increments the attempt count in a single atomic query for a given order and order stage
func (o *OrderAttemptDaoCRDB) IncrementAttemptCount(ctx context.Context, orderId string, stage orderPb.OrderStage) error {
	defer metric_util.TrackDuration("order/dao", "OrderAttemptDaoCRDB", "IncrementAttemptCount", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, o.db)

	err := db.Exec("UPDATE order_attempts SET num_attempts = num_attempts + 1, updated_at = ? WHERE order_id = ? "+
		"AND stage = ?", time.Now(), orderId, stage).Error
	if err != nil {
		return fmt.Errorf("failed to incremement attempt counter: %w", err)
	}

	return nil
}

// UpdateReqId updates reqId identifier for a given order and order stage
func (o *OrderAttemptDaoCRDB) UpdateReqId(ctx context.Context, orderId string, stage orderPb.OrderStage, reqId string) error {
	defer metric_util.TrackDuration("order/dao", "OrderAttemptDaoCRDB", "UpdateReqId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, o.db)

	err := db.Model(&model.OrderAttempt{}).
		Where("order_id = ?", orderId).
		Where("stage = ?", stage).
		Update("req_id", reqId).Error
	if err != nil {
		return fmt.Errorf("failed to update reqId: %w", err)
	}

	return nil
}

// GetLatestOrderAttempt fetches latest order attempt for a given order
func (o *OrderAttemptDaoCRDB) GetLatestOrderAttempt(ctx context.Context, orderId string) (*orderPb.OrderAttempt, error) {
	defer metric_util.TrackDuration("order/dao", "OrderAttemptDaoCRDB", "GetLatestOrderAttempt", time.Now())
	modelAttempt := &model.OrderAttempt{}
	db := gormctxv2.FromContextOrDefault(ctx, o.db)

	if err := db.Model(&model.OrderAttempt{}).
		Where("order_id = ?", orderId).Order("created_at DESC").
		First(&modelAttempt).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch order attemp record: %w", err)
	}

	return convertToOrderAttemptProto(modelAttempt), nil
}

// convertToOrderAttemptModel converts order attempt domain proto to model struct
func convertToOrderAttemptModel(attempt *orderPb.OrderAttempt) *model.OrderAttempt {
	return &model.OrderAttempt{
		Id:          nulltypes.NewNullInt64(attempt.Id),
		OrderId:     attempt.OrderId,
		Stage:       attempt.Stage,
		NumAttempts: attempt.NumAttempts,
		ReqId:       nulltypes.NewNullString(attempt.ReqId),
	}
}

// convertToOrderAttemptProto converts order attempt model struct to domain proto
func convertToOrderAttemptProto(attempt *model.OrderAttempt) *orderPb.OrderAttempt {
	protoAttempt := &orderPb.OrderAttempt{
		Id:          attempt.Id.GetValue(),
		OrderId:     attempt.OrderId,
		Stage:       attempt.Stage,
		NumAttempts: attempt.NumAttempts,
		ReqId:       attempt.ReqId.GetValue(),
		CreatedAt:   timestampPb.New(attempt.CreatedAt),
		UpdatedAt:   timestampPb.New(attempt.UpdatedAt),
	}

	return protoAttempt
}
