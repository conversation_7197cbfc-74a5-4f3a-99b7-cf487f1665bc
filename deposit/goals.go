package deposit

import (
	"context"
	"fmt"

	depositPb "github.com/epifi/gamma/api/deposit"
	goalsPb "github.com/epifi/gamma/api/goals"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

// isGoalFieldMaskEnabled checks if goal details field mask is present in given list of field mask
func isGoalFieldMaskEnabled(fieldMask []depositPb.ListDepositAccountsRequest_FieldMask) bool {
	for _, fm := range fieldMask {
		if fm == depositPb.ListDepositAccountsRequest_FIELD_MASK_GOAL_DETAILS {
			return true
		}
	}
	return false
}

// getGoalDetailsForListDepositAccounts gets the depositAccounts and returns a map of DepositAccountId to GoalDetails
// this function is used in ListDepositAccounts RPC to populate the DepositAccountIdToGoalDetails field
func (s *Service) getGoalDetailsForListDepositAccounts(ctx context.Context,
	fieldMask []depositPb.ListDepositAccountsRequest_FieldMask, depositAccounts []*depositPb.DepositAccount) (
	map[string]*depositPb.ListDepositAccountsResponse_GoalDetails, error) {

	if !isGoalFieldMaskEnabled(fieldMask) {
		return nil, nil
	}

	depositAccountIds := make([]string, 0)
	goalIds := make([]string, 0)
	depositAccountIdToGoalId := make(map[string]string)

	for _, depositAccount := range depositAccounts {
		if depositAccount.GetGoalId() != "" {
			goalIds = append(goalIds, depositAccount.GetGoalId())
			depositAccountIds = append(depositAccountIds, depositAccount.GetId())
			depositAccountIdToGoalId[depositAccount.GetId()] = depositAccount.GetGoalId()
		}
	}

	getGoalsByIdsRes, err := s.goalsClient.GetGoalsByIds(ctx, &goalsPb.GetGoalsByIdsRequest{
		Ids: goalIds,
	})
	if err = epifigrpc.RPCError(getGoalsByIdsRes, err); err != nil {
		return nil, fmt.Errorf("error occurred while calling GetGoalsByIds %v, %w", goalIds, err)
	}

	// making a map with goalId -> Goal
	goalIdToGoal := make(map[string]*goalsPb.Goal)
	for _, goalDetail := range getGoalsByIdsRes.GetGoals() {
		goalIdToGoal[goalDetail.GetId()] = goalDetail
	}

	// making a map with depositAccountId -> GoalDetails
	depositAccountIdToGoalDetails := make(map[string]*depositPb.ListDepositAccountsResponse_GoalDetails)
	for _, depositAccountId := range depositAccountIds {
		goalId := depositAccountIdToGoalId[depositAccountId]
		depositAccountIdToGoalDetails[depositAccountId] = &depositPb.ListDepositAccountsResponse_GoalDetails{
			GoalId:       goalId,
			TargetAmount: goalIdToGoal[goalId].GetTargetAmount(),
			TargetDate:   goalIdToGoal[goalId].GetTargetDate(),
		}
	}
	return depositAccountIdToGoalDetails, nil
}
