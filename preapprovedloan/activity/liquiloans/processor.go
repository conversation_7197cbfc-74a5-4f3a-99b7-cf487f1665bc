// nolint:dupl, funlen, protogetter
package liquiloans

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	authPb "github.com/epifi/gamma/api/auth"
	authOrchestratorPb "github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	brePb "github.com/epifi/gamma/api/bre"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	employmentPb "github.com/epifi/gamma/api/employment"
	orderPb "github.com/epifi/gamma/api/order"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	recurringPaymentsPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/risk"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	userPb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/loans"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	workerGenConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/lms"
	loandataprovider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
)

type ApplicantDetails struct {
	Pan    string
	Dob    *date.Date
	Name   *commontypes.Name
	Gender types.Gender
}

type Processor struct {
	loanStepExecutionDao      dao.LoanStepExecutionsDao
	loanApplicantDao          dao.LoanApplicantDao
	loanOffersDao             dao.LoanOffersDao
	loanRequestDao            dao.LoanRequestsDao
	loanAccountDao            dao.LoanAccountsDao
	loanActivityDao           dao.LoanActivityDao
	loanInstallmentInfoDao    dao.LoanInstallmentInfoDao
	loecDao                   dao.LoanOfferEligibilityCriteriaDao
	llPalVgClient             llVgPb.LiquiloansClient
	rpcHelper                 *helper.RpcHelper
	txnExecutorProvider       *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	authOrchestratorClient    authOrchestratorPb.OrchestratorClient
	deeplinkProvider          *liquiloans.PlProvider
	deeplinkProviderEs        *liquiloans.EsProvider
	deeplinkProviderFlPl      *liquiloans.FiLiteProvider
	deeplinkProviderAtl       *liquiloans.AcqToLendProvider
	s3Client                  s3.S3Client
	config                    *worker.Config
	deeplinkFactory           *deeplink.ProviderFactory
	recurringPaymentClient    recurringPaymentsPb.RecurringPaymentServiceClient
	creditReportManagerClient creditReportPb.CreditReportManagerClient
	creditReportV2Client      creditReportV2Pb.CreditReportManagerClient
	creditReportConfig        *commonGenConf.CreditReportConfig
	palClient                 preApprovedLoanPb.PreApprovedLoanClient
	lms                       lms.ILms
	orderClient               orderPb.OrderServiceClient
	lmsProviders              loandataprovider.IFactory
	eventsBroker              events.Broker
	authClient                authPb.AuthClient
	breClient                 brePb.BreClient
	dataDevS3Client           types2.DataDevS3Client
	connectedAccountClient    connectedAccountPb.ConnectedAccountClient
	incomeEstimatorClient     incomeestimator.IncomeEstimatorClient
	commsHelper               *helper.CommsHelper
	multiDbDOnceMgr           onceV2.MultiDbDoOnce
	dynamicConf               *workerGenConf.Config
	employmentClient          employmentPb.EmploymentClient
	piClient                  piPb.PiClient
	usersClient               userPb.UsersClient
	salaryClient              salaryPb.SalaryProgramClient
	onbClient                 onbPb.OnboardingClient
	panVgClient               panVgPb.PANClient
	releaseEvaluator          release.IEvaluator
	finFluxVgClient           finflux.FinfluxClient
	userIntelClient           userintel.UserIntelServiceClient
	bankCustClient            bankcust.BankCustomerServiceClient
	compClient                compliancePb.ComplianceClient
	riskClient                risk.RiskClient
	oprStatusClient           operStatusPb.OperationalStatusServiceClient
	savingsClient             savingsPb.SavingsClient
	mandateRequestDao         dao.MandateRequestDao
	mandateConf               *commonGenConf.MandateConfig
	calculatorFactory         calculatorTypes.FactoryProvider
	creditReportHelper        *palActivity.CreditReportHelper
}

// nolint: funlen
func NewProcessor(
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanApplicantDao dao.LoanApplicantDao,
	loanOffersDao dao.LoanOffersDao,
	loanRequestDao dao.LoanRequestsDao,
	loanAccountDao dao.LoanAccountsDao,
	loanActivityDao dao.LoanActivityDao,
	loecDao dao.LoanOfferEligibilityCriteriaDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	llPalVgClient llVgPb.LiquiloansClient,
	rpcHelper *helper.RpcHelper,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	authOrchestratorClient authOrchestratorPb.OrchestratorClient,
	deeplinkProvider *liquiloans.PlProvider,
	s3client types2.PreApprovedLoanS3Client,
	config *worker.Config,
	deeplinkProviderEs *liquiloans.EsProvider,
	deeplinkProviderFlPl *liquiloans.FiLiteProvider,
	deeplinkFactory *deeplink.ProviderFactory,
	recurringPaymentClient recurringPaymentsPb.RecurringPaymentServiceClient,
	creditReportManagerClient creditReportPb.CreditReportManagerClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	creditReportConfig *commonGenConf.CreditReportConfig,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	lms lms.ILms,
	orderClient orderPb.OrderServiceClient,
	lmsProviders loandataprovider.IFactory,
	eventsBroker events.Broker,
	authClient authPb.AuthClient,
	breClient brePb.BreClient,
	dataDevS3Client types2.DataDevS3Client,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	incomeEstimatorClient incomeestimator.IncomeEstimatorClient,
	deeplinkProviderAtl *liquiloans.AcqToLendProvider,
	commsHelper *helper.CommsHelper,
	multiDbDOnceMgr onceV2.MultiDbDoOnce,
	dynamicConf *workerGenConf.Config,
	employmentClient employmentPb.EmploymentClient,
	piClient piPb.PiClient,
	usersClient userPb.UsersClient,
	salaryClient salaryPb.SalaryProgramClient,
	onbClient onbPb.OnboardingClient,
	panVgClient panVgPb.PANClient,
	releaseEvaluator release.IEvaluator,
	finFluxVgClient finflux.FinfluxClient,
	userIntelClient userintel.UserIntelServiceClient,
	bankCustClient bankcust.BankCustomerServiceClient,
	compClient compliancePb.ComplianceClient,
	riskClient risk.RiskClient,
	oprStatusClient operStatusPb.OperationalStatusServiceClient,
	savingsClient savingsPb.SavingsClient,
	mandateRequestDao dao.MandateRequestDao,
	mandateConf *commonGenConf.MandateConfig,
	calculatorFactory calculatorTypes.FactoryProvider,
	creditReportHelper *palActivity.CreditReportHelper,
) *Processor {
	return &Processor{
		loanStepExecutionDao:      loanStepExecutionDao,
		loanApplicantDao:          loanApplicantDao,
		loanOffersDao:             loanOffersDao,
		loanRequestDao:            loanRequestDao,
		loanAccountDao:            loanAccountDao,
		loanActivityDao:           loanActivityDao,
		loanInstallmentInfoDao:    loanInstallmentInfoDao,
		llPalVgClient:             llPalVgClient,
		rpcHelper:                 rpcHelper,
		txnExecutorProvider:       txnExecutorProvider,
		authOrchestratorClient:    authOrchestratorClient,
		deeplinkProvider:          deeplinkProvider,
		s3Client:                  s3client,
		config:                    config,
		deeplinkProviderEs:        deeplinkProviderEs,
		deeplinkProviderFlPl:      deeplinkProviderFlPl,
		deeplinkFactory:           deeplinkFactory,
		recurringPaymentClient:    recurringPaymentClient,
		loecDao:                   loecDao,
		creditReportManagerClient: creditReportManagerClient,
		creditReportV2Client:      creditReportV2Client,
		creditReportConfig:        creditReportConfig,
		palClient:                 palClient,
		lms:                       lms,
		orderClient:               orderClient,
		lmsProviders:              lmsProviders,
		eventsBroker:              eventsBroker,
		authClient:                authClient,
		breClient:                 breClient,
		dataDevS3Client:           dataDevS3Client,
		connectedAccountClient:    connectedAccountClient,
		incomeEstimatorClient:     incomeEstimatorClient,
		deeplinkProviderAtl:       deeplinkProviderAtl,
		commsHelper:               commsHelper,
		multiDbDOnceMgr:           multiDbDOnceMgr,
		dynamicConf:               dynamicConf,
		employmentClient:          employmentClient,
		piClient:                  piClient,
		usersClient:               usersClient,
		salaryClient:              salaryClient,
		onbClient:                 onbClient,
		panVgClient:               panVgClient,
		releaseEvaluator:          releaseEvaluator,
		finFluxVgClient:           finFluxVgClient,
		userIntelClient:           userIntelClient,
		bankCustClient:            bankCustClient,
		compClient:                compClient,
		riskClient:                riskClient,
		oprStatusClient:           oprStatusClient,
		savingsClient:             savingsClient,
		mandateRequestDao:         mandateRequestDao,
		mandateConf:               mandateConf,
		calculatorFactory:         calculatorFactory,
		creditReportHelper:        creditReportHelper,
	}
}

const disbursalTimeoutDuration = 3 * 24 * time.Hour

func (p *Processor) getFederalCustomerDetails(ctx context.Context, actorID string) (*userPb.GetCustomerDetailsResponse, error) {
	user, err := p.rpcHelper.GetUserByActorId(ctx, actorID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user by actorID")
	}

	res, err := p.usersClient.GetCustomerDetails(ctx, &userPb.GetCustomerDetailsRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		UserId:     user.GetId(),
		ActorId:    actorID,
		Provenance: userPb.Provenance_APP,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, errors.Wrap(te, "failed to fetch customer details for user")
	}
	return res, nil
}

// nolint: funlen
func (p *Processor) LLCreateApplicant(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)
		// fetch personal details for the applicant
		user, err := p.rpcHelper.GetUserByActorId(ctx, req.GetLoanStep().GetActorId())
		if err != nil {
			lg.Error("unable to get applicant's personal details", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			lg.Error("unable to get loan request", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		var applicantId string
		// Note : we are assuming here that applicant will be present in loan_applicant table from the offline process.
		// In case we want to allow other users to explore offer from LL side, we need to make changes in this activity
		applicant, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lse.GetActorId(), preApprovedLoanPb.Vendor_LIQUILOANS, req.GetLoanProgram(), lr.GetDetails().GetProgramVersion())
		if err != nil {
			lg.Error("unable to fetch applicant.", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		// If applicant is already in APPROVED status, skip applicant creation step
		if applicant.GetStatus() == preApprovedLoanPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_APPROVED {
			palActivity.MarkLoanStepSuccess(res.GetLoanStep())
			return res, nil
		}

		isBankingDetailsAdded := false
		isAaIncomeUpdateNeeded := false
		// Call applicant lookup rpc to check is user is already registered at vendor's end
		lookupRes, err := p.llPalVgClient.ApplicantLookup(ctx, &llVgPb.ApplicantLookupRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			Pan:           user.GetProfile().GetPAN(),
			LoanProgram:   p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
			SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[lr.GetDetails().GetProgramVersion()],
		})
		if te := epifigrpc.RPCError(lookupRes, err); te != nil && !lookupRes.GetStatus().IsRecordNotFound() {
			lg.Error("failed to perform applicant lookup", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}

		if lookupRes.GetStatus().IsRecordNotFound() {
			applicantDetails, applicantDetailsErr := p.getUserDetailsForPersonalDetails(ctx, applicant, user)
			if applicantDetailsErr != nil {
				lg.Error("failed to get user details for personal details api", zap.Error(applicantDetailsErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, applicantDetailsErr.Error())
			}

			// If record not found, means user is not registered, call AddPersonalDetails rpc
			createUserRes, createUserErr := p.llPalVgClient.AddPersonalDetails(ctx, &llVgPb.AddPersonalDetailsRequest{
				Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
				Name:             applicantDetails.Name,
				ContactNumber:    user.GetProfile().GetPhoneNumber(),
				Email:            user.GetProfile().GetEmail(),
				Pan:              applicantDetails.Pan,
				Gender:           applicantDetails.Gender,
				Dob:              applicantDetails.Dob,
				Urn:              applicant.GetVendorRequestId(),
				LoanProgram:      p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
				MonthlyIncome:    applicant.GetEmploymentDetails().GetIncomeEstimateData().GetPredictedIncome().GetBeMoney(),
				IncomeDataSource: helper.ConvertToVGIncomeDataSource(applicant.GetEmploymentDetails().GetIncomeEstimateData().GetIncomeDataSource()),
				SchemeVersion:    loans.LoanProgramVersionToLLSchemeVersion[lr.GetDetails().GetProgramVersion()],
			})
			te := epifigrpc.RPCError(createUserRes, createUserErr)
			if te != nil {
				res.LseFieldMasks = []preApprovedLoanPb.LoanStepExecutionFieldMask{preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS}
				res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
				switch {
				case createUserRes.GetStatus().IsPermissionDenied():
					lg.Error("duplicate lead: user has open credit line with another partner")
					res.GetLoanStep().SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE
					return res, nil
				case createUserRes.GetStatus().IsAlreadyExists():
					lg.Error("dedupe check failed: user details already registered against a different user")
					res.GetLoanStep().SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DEDUPE_CHECK_FAILURE
					return res, nil
				default:
					lg.Error("failed to register user at liquiloans side", zap.Error(te))
					return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
				}
			}
			applicantId = createUserRes.GetData().GetApplicantId()
		}
		if lookupRes.GetStatus().IsSuccess() {
			applicantId = lookupRes.GetData().GetApplicantId()
			isBankingDetailsAdded = lookupRes.GetData().GetDetailsStatus().GetBankingDetailStatus()
			isAaIncomeUpdateNeeded = true
		}

		// update vendor applicant id in loan_applicants table
		applicant.VendorApplicantId = applicantId
		applicant.SubStatus = preApprovedLoanPb.LoanApplicantSubStatus_LOAN_APPLICANT_SUB_STATUS_CREATED_AT_VENDOR
		err = p.loanApplicantDao.Update(ctx, applicant, []preApprovedLoanPb.LoanApplicantFieldMask{
			preApprovedLoanPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID,
			preApprovedLoanPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_SUB_STATUS,
		})
		if err != nil {
			lg.Error("failed to update vendor applicant id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		// need to update LL with the latest AA income (if available) that we have in the DB
		if isAaIncomeUpdateNeeded {
			errToReturn := p.updateApplicantUdf(ctx, applicant, req.GetLoanProgram())
			if errToReturn != nil {
				lg.Error("failed to update applicant udf", zap.Error(errToReturn))
				return nil, errToReturn
			}
		}

		errToReturn := p.addBankingDetailsAtVendor(ctx, isBankingDetailsAdded, req, applicantId, user.GetProfile().GetPanName().ToString(), lr.GetDetails().GetProgramVersion())
		if errToReturn != nil {
			lg.Error("failed to update applicant banking details", zap.Error(errToReturn))
			return nil, errToReturn
		}

		palActivity.MarkLoanStepSuccess(res.GetLoanStep())

		return res, nil
	})
	return actRes, actErr
}

// nolint: funlen
func (p *Processor) addBankingDetailsAtVendor(ctx context.Context, isBankingDetailsAdded bool, req *palActivityPb.PalActivityRequest, loanApplicantId string, panName string, lpVersion enums.LoanProgramVersion) error {
	switch req.GetLoanProgram() {
	//  for the below programs banking details will be sent to vendor after they are collected from the user
	case preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION, preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return nil
	default:
		// if applicant was already created at vendor's end, check if banking details were successfully added for the user or not
		// call the AddBankingDetails API only if details are not added yet
		if !isBankingDetailsAdded {
			errToReturn := p.updateBankingDetails(ctx, req.GetLoanProgram(), req.GetLoanStep().GetActorId(), loanApplicantId, panName, lpVersion)
			if errToReturn != nil {
				return errToReturn
			}
		}
	}
	return nil
}

func (p *Processor) getUserDetailsForPersonalDetails(ctx context.Context, loanApplicant *preApprovedLoanPb.LoanApplicant, user *userPb.User) (*ApplicantDetails, error) {
	applicantDetails := &ApplicantDetails{
		Pan:    user.GetProfile().GetPAN(),
		Dob:    user.GetProfile().GetDateOfBirth(),
		Name:   user.GetProfile().GetPanName(),
		Gender: user.GetProfile().GetKycGender(),
	}
	switch loanApplicant.GetLoanProgram() {
	case preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION, preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		userDetails := helper.GetUserDetailsForNonFiCore(user)
		applicantDetails.Name = user.GetProfile().GetGivenName()
		applicantDetails.Pan = userDetails.Pan
		applicantDetails.Gender = user.GetProfile().GetGivenGender()
		applicantDetails.Dob = userDetails.Dob
	default:
		// If gender is Others, fetch gender using customer fetch API
		if user.GetProfile().GetKycGender() == types.Gender_OTHER ||
			user.GetProfile().GetKycGender() == types.Gender_GENDER_UNSPECIFIED {
			customerRes, custErr := p.getFederalCustomerDetails(ctx, loanApplicant.GetActorId())
			if custErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, custErr.Error())
			}
			applicantDetails.Gender = customerRes.GetGender()
		}

	}
	return applicantDetails, nil
}
func (p *Processor) updateBankingDetails(ctx context.Context, lp preApprovedLoanPb.LoanProgram, actorId, applicantId, accountName string, lpVersion enums.LoanProgramVersion) error {
	savingsAccount, err := p.rpcHelper.GetSavingsAccountDetails(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting savings account: %v", err))
	}
	// Add Banking details for the applicant
	addDetailsRes, err := p.llPalVgClient.AddBankingDetails(ctx, &llVgPb.AddBankingDetailsRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
		ApplicantId: applicantId,
		BankAccountDetails: &types.BankAccountDetails{
			AccountNumber: savingsAccount.AccountNo,
			BankName:      savingsAccount.GetPartnerBank().String(),
			AccountName:   accountName,
			AccountType:   accounts.Type_SAVINGS,
			Ifsc:          savingsAccount.GetIfscCode(),
		},
		LoanProgram:   p.rpcHelper.ConvertToVGLoanProgram(lp),
		SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[lpVersion],
	})
	if te := epifigrpc.RPCError(addDetailsRes, err); te != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to add banking details: %v", te.Error()))
	}
	return nil
}

func (p *Processor) updateApplicantUdf(ctx context.Context, applicant *preApprovedLoanPb.LoanApplicant, lp preApprovedLoanPb.LoanProgram) error {
	if applicant.GetEmploymentDetails().GetIncomeEstimateData().GetIncomeDataSource() == preApprovedLoanPb.IncomeDataSource_INCOME_DATA_SOURCE_UNSPECIFIED {
		return nil
	}
	updateRes, err := p.llPalVgClient.UpdateApplicantUdf(ctx, &llVgPb.UpdateApplicantUdfRequest{
		Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
		ApplicantId:      applicant.GetVendorApplicantId(),
		LoanProgram:      p.rpcHelper.ConvertToVGLoanProgram(lp),
		MonthlyIncome:    applicant.GetEmploymentDetails().GetIncomeEstimateData().GetPredictedIncome().GetBeMoney(),
		IncomeDataSource: helper.ConvertToVGIncomeDataSource(applicant.GetEmploymentDetails().GetIncomeEstimateData().GetIncomeDataSource()),
		SchemeVersion:    loans.LoanProgramVersionToLLSchemeVersion[applicant.GetLoanProgramVersion()],
	})
	if te := epifigrpc.RPCError(updateRes, err); te != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in UpdateApplicantUdf: %v", te.Error()))
	}
	return nil
}

func (p *Processor) LLAddEmploymentDetails(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []preApprovedLoanPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)
		loanReq, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			lg.Error("failed to get loan req", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		applicant, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lse.GetActorId(), preApprovedLoanPb.Vendor_LIQUILOANS, req.GetLoanProgram(), loanReq.GetDetails().GetProgramVersion())
		if err != nil {
			lg.Error("unable to fetch applicant.", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		applicantId := applicant.GetVendorApplicantId()

		// Call Add Employment Details rpc.
		empDetailsRes, err := p.llPalVgClient.AddEmploymentDetails(ctx, &llVgPb.AddEmploymentDetailsRequest{
			Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			ApplicantId:      applicantId,
			Occupation:       res.GetLoanStep().GetDetails().GetOnboardingData().GetEmploymentDetails().GetOccupation(),
			OrganizationName: res.GetLoanStep().GetDetails().GetOnboardingData().GetEmploymentDetails().GetOrganizationName(),
			MonthlyIncome:    res.GetLoanStep().GetDetails().GetOnboardingData().GetEmploymentDetails().GetMonthlyIncome(),
			WorkEmail:        res.GetLoanStep().GetDetails().GetOnboardingData().GetEmploymentDetails().GetWorkEmail(),
			LoanProgram:      p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
			SchemeVersion:    loans.LoanProgramVersionToLLSchemeVersion[loanReq.GetDetails().GetProgramVersion()],
		})
		if te := epifigrpc.RPCError(empDetailsRes, err); te != nil && !empDetailsRes.GetStatus().IsRecordNotFound() && empDetailsRes.GetStatus().GetCode() != uint32(llVgPb.AddDetailsResponse_INCOME_VALIDATION_ERROR) {
			lg.Error("failed to add employment details", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}
		if empDetailsRes.GetStatus().IsRecordNotFound() {
			lg.Error("applicant id not found to add employment details")
			return nil, errors.Wrap(epifierrors.ErrTransient, "applicant id not found to add employment details")
		}
		if empDetailsRes.GetStatus().GetCode() == uint32(llVgPb.AddDetailsResponse_INCOME_VALIDATION_ERROR) {
			lg.Error("income validation error while adding employment details")
			palActivity.MarkLoanStepFailWithSubStatus(res, preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_NOT_IN_ACCEPTABLE_RANGE_FOR_VENDOR)
			return res, nil
		}

		res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		res.GetLoanStep().SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED
		res.LseFieldMasks = append(res.LseFieldMasks, preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) LLAddAddressDetails(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
		nextAction := deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), res.GetLoanStep().GetRefId())
		updateLrErr := p.updateNextActionInLoanRequest(ctx, res.GetLoanStep().GetRefId(), nextAction)
		if updateLrErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, updateLrErr.Error())
		}
		lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		applicant, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lse.GetActorId(), preApprovedLoanPb.Vendor_LIQUILOANS, req.GetLoanProgram(), lr.GetDetails().GetProgramVersion())
		if err != nil {
			lg.Error("unable to fetch applicant.", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		applicantId := applicant.GetVendorApplicantId()

		// Call Add Address Details rpc.
		loanStep, err := p.loanStepExecutionDao.GetById(ctx, req.GetLoanStep().GetId())
		if err != nil {
			return res, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		addDetailsRes, err := p.llPalVgClient.AddAddressDetails(ctx, &llVgPb.AddAddressDetailsRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			ApplicantId:   applicantId,
			AddressType:   types.AddressType_MAILING,
			Address:       loanStep.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails(),
			LoanProgram:   p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
			SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[lr.GetDetails().GetProgramVersion()],
		})
		if te := epifigrpc.RPCError(addDetailsRes, err); te != nil && !addDetailsRes.GetStatus().IsRecordNotFound() {
			lg.Error("failed to add address details", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}
		if addDetailsRes.GetStatus().IsRecordNotFound() {
			lg.Error("applicant id not found to add address details")
			return nil, errors.Wrap(epifierrors.ErrPermanent, "applicant id not found to add address details")
		}
		lse.SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED
		if err = p.loanStepExecutionDao.Update(ctx, lse, []preApprovedLoanPb.LoanStepExecutionFieldMask{
			preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		}); err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		if req.GetLoanProgram() == preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
			return res, nil
		}

		nextAction = p.deeplinkProvider.GetEmploymentDetailsDeeplink(deeplinkProvider.GetLoanHeader(), res.GetLoanStep().GetRefId())
		updateLrErr = p.updateNextActionInLoanRequest(ctx, res.GetLoanStep().GetRefId(), nextAction)
		if updateLrErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, updateLrErr.Error())
		}
		return res, nil
	})
	return actRes, actErr
}

// LLGetAddressStatus is a polling activity to check if user's address details has been added to the loan_step_executions table
func (p *Processor) LLGetAddressStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: req.GetLoanStep(),
		}
		lg := activity.GetLogger(ctx)

		// check if address details are added in loan_step details column
		loanStep, err := p.loanStepExecutionDao.GetById(ctx, lse.GetId())
		if err != nil {
			lg.Error("failed to fetch loan step by Id", zap.Error(err))
			return nil, palActivity.GetActivityErrFromDaoError(err)
		}
		if loanStep.GetDetails().GetOnboardingData().GetAddressDetails() != nil {
			res.LoanStep = loanStep
			return res, nil
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, "address details not added by user yet")
	})
	return actRes, actErr
}

// LLGetEmploymentStatus is a polling activity to check if user's employment details has been added to the loan_step_executions table
func (p *Processor) LLGetEmploymentStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: req.GetLoanStep(),
		}
		lg := activity.GetLogger(ctx)

		// check if employment details are added in loan_step details column
		loanStep, err := p.loanStepExecutionDao.GetById(ctx, lse.GetId())
		if err != nil {
			lg.Error("failed to fetch loan step by Id", zap.Error(err))
			return nil, palActivity.GetActivityErrFromDaoError(err)
		}
		if loanStep.GetDetails().GetOnboardingData().GetEmploymentDetails() != nil {
			res.LoanStep = loanStep
			res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			return res, nil
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("employment details not added by user yet, %v", err))
	})
	return actRes, actErr
}

// nolint: funlen
func (p *Processor) LLCheckDisbursal(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []preApprovedLoanPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			lg.Error("failed to fetch loan request by id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		loanStatusRes, err := p.llPalVgClient.GetLoanStatus(ctx, &llVgPb.GetLoanStatusRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			ApplicationId: lr.GetVendorRequestId(),
			LoanProgram:   p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
			SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[lr.GetDetails().GetProgramVersion()],
		})
		if te := epifigrpc.RPCError(loanStatusRes, err); te != nil {
			lg.Error("failed to add address details", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}
		switch loanStatusRes.GetLoanStatus().GetLoanData().GetStatus() {
		case llVgPb.Status_STATUS_PENDING, llVgPb.Status_STATUS_APPROVED, llVgPb.Status_STATUS_CREATED:
			if time.Now().Sub(lse.GetCreatedAt().AsTime()) > disbursalTimeoutDuration {
				lap, err := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), lr.GetDetails().GetProgramVersion())
				if err != nil {
					lg.Error("failed to fetch loan applicant", zap.Error(err))
					return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch loan applicant")
				}
				vgResp, err := p.llPalVgClient.CancelLead(ctx, &llVgPb.CancelLeadRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
					ApplicationId: lr.GetVendorRequestId(),
					ApplicantId:   lap.GetVendorApplicantId(),
					LoanProgram:   p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
					SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[lr.GetDetails().GetProgramVersion()],
				})
				if te := epifigrpc.RPCError(vgResp, err); te != nil {
					lg.Error("failed to cancel lead", zap.Error(te))
					return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
				}
				palActivity.MarkLoanStepFailWithSubStatus(res, preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DISBURSAL_TIMED_OUT)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, "loan not disbursed yet")
		case llVgPb.Status_STATUS_READY_TO_DISBURSED:
			if time.Now().Sub(lse.GetCreatedAt().AsTime()) > disbursalTimeoutDuration && lse.GetSubStatus() != preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_STUCK_IN_READY_TO_DISBURSE_STATE {
				res.LoanStep.SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_STUCK_IN_READY_TO_DISBURSE_STATE
				if err := p.loanStepExecutionDao.Update(ctx, res.LoanStep, []preApprovedLoanPb.LoanStepExecutionFieldMask{
					preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				}); err != nil {
					lg.Error("failed to update loan step execution sub status", zap.Error(err))
					return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
				}
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, "loan not disbursed yet")
		case llVgPb.Status_STATUS_WITHDRAWN:
			palActivity.MarkLoanStepFailWithSubStatus(res, preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_WITHDRAWN)
			return res, nil
		case llVgPb.Status_STATUS_DISBURSED:
			res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

			lo, err := p.loanOffersDao.GetById(ctx, lr.GetOfferId())
			if err != nil {
				lg.Error("failed to fetch loan offer by id", zap.Error(err), zap.String(logger.LOAN_OFFER_ID, lr.GetOfferId()))
				return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
			}

			// TODO(sharath): decouple the loan account creation activity from disbursal activity
			// create loan account after LL disbursal only if LL is owning the primary lms for the loan account.
			// otherwise if Fi is owning the primary lms then loan account will be created as part of PartnerLmsLoanDisbursal step.
			if lo.GetLmsPartner() == enums.LmsPartner_LMS_PARTNER_UNSPECIFIED {
				createErr := p.createLiquiloansLoanAccount(ctx, lr, loanStatusRes.GetLoanStatus())
				if createErr != nil {
					lg.Error("failed to create loan account for LL", zap.Error(createErr))
					return nil, errors.Wrap(epifierrors.ErrTransient, createErr.Error())
				}
			}
			lr.GetDetails().GetLoanInfo().DisbursementUtr = loanStatusRes.GetLoanStatus().GetLoanData().GetUtr()
			lr.GetDetails().GetLoanInfo().ActualDisbursalAmount = loanStatusRes.GetLoanStatus().GetLoanDetails().GetDisbursedAmount()
			err = p.loanRequestDao.Update(ctx, lr, []preApprovedLoanPb.LoanRequestFieldMask{preApprovedLoanPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS})
			if err != nil {
				lg.Error("failed to update loan request", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, lr.GetId()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan request: %v", err.Error()))
			}

			lr, err = helper.RoundLoanRequest(lr)
			if err != nil {
				lg.Error("failed to round loan request", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
			}
			deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
			dl, dlErr := deeplinkProvider.GetLoanApplicationStatusScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), lr)
			if dlErr != nil {
				lg.Error("failed to get loan application status screen deeplink", zap.Error(dlErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting loan application status screen deeplink: %v", dlErr))
			}
			updateLrErr := p.updateNextActionInLoanRequest(ctx, lse.GetRefId(), dl)
			if updateLrErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateLrErr))
			}
			// Trigger loan disbursal event for Acquire To Lend loan program
			// TODO(mounish): should ideally be part of ACQ_TO_LEND provider as new vendors can be added in future
			err = p.SendLoanDisbursedEvent(ctx, lr)
			if err != nil {
				lg.Error("failed to send loan disbursed event", zap.Error(err))
			}
			return res, nil
		case llVgPb.Status_STATUS_REJECTED:
			res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			res.LseFieldMasks = append(res.LseFieldMasks, preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
			return nil, errors.Wrap(epifierrors.ErrPermanent, "loan disbursal rejected from vendor's end")
		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, "loan not disbursed yet")
		}
	})
	return actRes, actErr
}

// nolint: funlen
func (p *Processor) createLiquiloansLoanAccount(ctx context.Context, lr *preApprovedLoanPb.LoanRequest, data *llVgPb.GetLoanStatusResponse_LoanStatus) error {
	year, month, _ := time.Now().In(datetime.IST).Date()
	fifthOfCurrMonth := time.Date(year, month, 5, 23, 59, 59, 0, datetime.IST)
	twentyFifthOfCurrMonth := time.Date(year, month, 25, 23, 59, 59, 0, datetime.IST)
	startDate := fifthOfCurrMonth.AddDate(0, 1, 0)
	if time.Now().In(datetime.IST).After(twentyFifthOfCurrMonth) {
		startDate = fifthOfCurrMonth.AddDate(0, 2, 0)
	}
	endDate := startDate.AddDate(0, int(lr.GetDetails().GetLoanInfo().GetTenureInMonths())-1, 0)

	loanAccount := &preApprovedLoanPb.LoanAccount{
		ActorId: lr.GetActorId(),
		Vendor:  lr.GetVendor(),
		// Note that LL doesn't give any special loan account to us. For our use-case, we create a loan account
		// with the loan_id given by LL
		AccountNumber: lr.GetVendorRequestId(),
		LoanType:      helper.GetLaLoanTypeByLoanProgram(lr.GetLoanProgram()),
		LoanAmountInfo: &preApprovedLoanPb.LoanAmountInfo{
			LoanAmount:         lr.GetDetails().GetLoanInfo().GetAmount(),
			DisbursedAmount:    lr.GetDetails().GetLoanInfo().GetDisbursalAmount(),
			OutstandingAmount:  lr.GetDetails().GetLoanInfo().GetTotalPayable(),
			TotalPayableAmount: lr.GetDetails().GetLoanInfo().GetTotalPayable(),
		},
		MaturityDate: datetime.TimeToDateInLoc(endDate, endDate.Location()),
		Details: &preApprovedLoanPb.LoanAccountDetails{
			InterestRate:       data.GetLoanDetails().GetRoi(),
			TenureInMonths:     data.GetLoanDetails().GetTenure(),
			LoanName:           "Loan #1",
			AprRate:            lr.GetDetails().GetLoanInfo().GetAprRate(),
			LoanProgramVersion: lr.GetDetails().GetProgramVersion(),
		},
		Status:      preApprovedLoanPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
		LoanProgram: lr.GetLoanProgram(),
	}

	lse, lseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lr.GetId(), preApprovedLoanPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, preApprovedLoanPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
	if lseErr != nil {
		return errors.Wrap(lseErr, "failed to get the loan step execution")
	}

	gracePeriod := int64(palActivity.PrepayGracePeriodInDays)
	if lr.GetLoanProgram() == preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		gracePeriod = palActivity.EarlySalaryPrepayGracePeriodInDays
	}
	totalPayable := moneyPb.Multiply(data.GetLoanDetails().GetEmiAmount(), decimal.NewFromInt32(data.GetLoanDetails().GetTenure()))
	loanInstallmentInfo := &preApprovedLoanPb.LoanInstallmentInfo{
		// Note: Account ID is intentionally left empty. It will get populated in the txn block
		AccountId:             "",
		TotalAmount:           totalPayable,
		StartDate:             datetime.TimeToDateInLoc(time.Now(), datetime.IST),
		EndDate:               datetime.TimeToDateInLoc(endDate, endDate.Location()),
		TotalInstallmentCount: data.GetLoanDetails().GetTenure(),
		NextInstallmentDate:   datetime.TimeToDateInLoc(startDate, datetime.IST),
		Details: &preApprovedLoanPb.LoanInstallmentInfoDetails{
			NextEmiAmount:               data.GetLoanDetails().GetEmiAmount(),
			MaxSingleRepaymentTxnAmount: lse.GetDetails().GetMandateData().GetMaxTxnAmount(),
			GracePeriod:                 gracePeriod,
		},
		Status: preApprovedLoanPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
	}

	referenceId := uuid.New().String()
	loanActivity := &preApprovedLoanPb.LoanActivity{
		Type: preApprovedLoanPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT,
		Details: &preApprovedLoanPb.LoanActivityDetails{
			Amount:        data.GetLoanDetails().GetDisbursedAmount(),
			Utr:           data.GetLoanData().GetUtr(),
			TransactionId: referenceId,
			TxnTime:       timestampPb.New(time.Now().In(datetime.IST)),
		},
		ReferenceId: referenceId,
	}

	txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
	if txnExecErr != nil {
		return errors.Wrap(txnExecErr, "failed to get txn executor by ownership")
	}
	var err error
	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		// Create loan account
		loanAccount, err = p.loanAccountDao.Create(txnCtx, loanAccount)
		if err != nil {
			return errors.Wrap(err, "failed to create loan account")
		}

		// Create loan installment info
		loanInstallmentInfo.AccountId = loanAccount.GetId()
		_, err = p.loanInstallmentInfoDao.Create(txnCtx, loanInstallmentInfo)
		if err != nil {
			return errors.Wrap(err, "failed to create loan installment info")
		}

		// Update loan account id in loan request
		lr.LoanAccountId = loanAccount.GetId()
		updateErr := p.loanRequestDao.Update(txnCtx, lr, []preApprovedLoanPb.LoanRequestFieldMask{preApprovedLoanPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER})
		if updateErr != nil {
			return errors.Wrap(updateErr, "failed to update loan account id in lr")
		}

		// create loan activity
		loanActivity.LoanAccountId = loanAccount.GetId()
		_, err = p.loanActivityDao.Create(txnCtx, loanActivity)
		if err != nil {
			return errors.Wrap(err, "failed to create loan activity")
		}

		err = p.loanOffersDao.DeactivateLoanOffer(txnCtx, lr.GetOfferId())
		if err != nil {
			return errors.Wrap(err, "failed to deactivate loan offer for already disbursed loan")
		}

		return nil
	})
	if txnErr != nil {
		return txnErr
	}
	return nil
}

// nolint: govet
func (p *Processor) LLCheckApplicantStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *preApprovedLoanPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)
		lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		// fetch the applicant id
		applicant, err := p.loanApplicantDao.
			GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lse.GetActorId(), preApprovedLoanPb.Vendor_LIQUILOANS, req.GetLoanProgram(), lr.GetDetails().GetProgramVersion())
		if err != nil {
			lg.Error("unable to fetch applicant.", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		// check the status of applicant at vendor side
		status, err := p.llPalVgClient.GetApplicantStatus(ctx, &llVgPb.GetApplicantStatusRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			ApplicantId:   applicant.GetVendorApplicantId(),
			LoanProgram:   p.rpcHelper.ConvertToVGLoanProgram(req.GetLoanProgram()),
			SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[lr.GetDetails().GetProgramVersion()],
		})
		if te := epifigrpc.RPCError(status, err); te != nil {
			lg.Error("failed to get applicant status from vendor", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}

		// mark the loan step sub status as "applicant status fetched"
		res.GetLoanStep().SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_STATUS_FETCHED
		res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		if err = p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), []preApprovedLoanPb.LoanStepExecutionFieldMask{
			preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		}); err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		switch status.GetApplicantStatus() {
		case llVgPb.ApplicantStatus_APPLICANT_STATUS_CREATED:
			return nil, errors.Wrap(epifierrors.ErrTransient, "credit line not approved for user")
		case llVgPb.ApplicantStatus_APPLICANT_STATUS_REJECTED:
			lg.Error("applicant rejected at vendor's end after BRE checks")
			res.LseFieldMasks = []preApprovedLoanPb.LoanStepExecutionFieldMask{preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS}
			res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			res.GetLoanStep().SubStatus = preApprovedLoanPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION
			applicant.Status = preApprovedLoanPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_REJECTED
			if updateErr := p.updateLoanApplicantStatus(ctx, applicant); updateErr != nil {
				lg.Error("failed to update applicant status", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, updateErr.Error())
			}
			return res, nil
		case llVgPb.ApplicantStatus_APPLICANT_STATUS_APPROVED:
			// if applicant is approved at vendor side, mark it as Approved in loan_applicant table
			applicant.Status = preApprovedLoanPb.LoanApplicantStatus_LOAN_APPLICANT_STATUS_APPROVED
			if updateErr := p.updateLoanApplicantStatus(ctx, applicant); updateErr != nil {
				lg.Error("failed to update applicant status", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, updateErr.Error())
			}
			return res, nil
		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, "credit line details not found for the user")
		}
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) updateLoanApplicantStatus(ctx context.Context, la *preApprovedLoanPb.LoanApplicant) error {
	err := p.loanApplicantDao.Update(ctx, la, []preApprovedLoanPb.LoanApplicantFieldMask{
		preApprovedLoanPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_STATUS,
	})
	if err != nil {
		return errors.Wrap(err, "error in updating loan applicant status")
	}
	return nil
}
