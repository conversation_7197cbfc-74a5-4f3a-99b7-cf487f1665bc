package federal_test

import (
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	actorPb "github.com/epifi/gamma/api/actor"
	eSignPb "github.com/epifi/gamma/api/docs/esign"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func TestProcessor_FederalPreKfs(t *testing.T) {
	type args struct {
		req *palActivityPb.PalActivityRequest
	}
	t.Parallel()
	tests := []struct {
		name       string
		args       args
		want       *palActivityPb.PalActivityResponse
		wantErr    bool
		setupMocks func(md *mockedDependencies)
		assertErr  func(err error) bool
	}{
		{
			name: "#1 successfully initiating e-sign for federal flow",
			args: args{
				req: &palActivityPb.PalActivityRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: getMockLseWithDetails(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS, &palPb.LoanStepExecutionDetails{
					Details: &palPb.LoanStepExecutionDetails_ESignStepData{
						ESignStepData: &palPb.ESignStepData{
							SignUrl: "url-link",
						},
					},
				}),
			},
			wantErr:   false,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS),
					nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
				md.loanOffersDao.EXPECT().GetById(gomock.Any(), dummyOfferId).Return(getMockOffer(), nil).Times(1)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: dummyActorId}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: dummyActorEntityId,
					},
				}, nil).Times(2)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: dummyActorEntityId},
				}).Return(&userPb.GetUserResponse{
					User:   &userPb.User{},
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-id-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
					FeatureInfo: &onbPb.FeatureInfo{
						FeatureStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE,
					},
				}, nil)

				md.userClient.EXPECT().GetCustomerDetails(gomock.Any(), &userPb.GetCustomerDetailsRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					UserId:     dummyActorEntityId,
					ActorId:    dummyActorId,
					Provenance: userPb.Provenance_APP,
				}).Return(&userPb.GetCustomerDetailsResponse{
					Status:    rpc.StatusOk(),
					Addresses: nil,
				}, nil).Times(1)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
							ActorId:                dummyActorId,
							AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						AccountNo: dummyAccountNumber,
						IfscCode:  dummyIfscCode,
					},
				}, nil).Times(1)
				md.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST)).Times(5)
				md.esignClient.EXPECT().InitiateESign(gomock.Any(), gomock.Any()).Return(&eSignPb.InitiateESignResponse{
					Status:   rpc.StatusOk(),
					SignUrl:  "url-link",
					ExpiryAt: nil,
				}, nil).Times(1)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
		},
		{
			name: "#2 retryable error in initiating e-sign",
			args: args{
				req: &palActivityPb.PalActivityRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS),
					nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
				md.loanOffersDao.EXPECT().GetById(gomock.Any(), dummyOfferId).Return(getMockOffer(), nil).Times(1)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: dummyActorId}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: dummyActorEntityId,
					},
				}, nil).Times(2)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: dummyActorEntityId},
				}).Return(&userPb.GetUserResponse{
					User:   &userPb.User{},
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				md.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-id-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
					FeatureInfo: &onbPb.FeatureInfo{
						FeatureStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE,
					},
				}, nil)
				md.userClient.EXPECT().GetCustomerDetails(gomock.Any(), &userPb.GetCustomerDetailsRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					UserId:     dummyActorEntityId,
					ActorId:    dummyActorId,
					Provenance: userPb.Provenance_APP,
				}).Return(&userPb.GetCustomerDetailsResponse{
					Status:    rpc.StatusOk(),
					Addresses: nil,
				}, nil).Times(1)
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
							ActorId:                dummyActorId,
							AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						AccountNo: dummyAccountNumber,
						IfscCode:  dummyIfscCode,
					},
				}, nil).Times(1)
				md.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST)).Times(5)
				md.esignClient.EXPECT().InitiateESign(gomock.Any(), gomock.Any()).Return(nil, errors.New("error")).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newFederalActProcessorWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(s)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.FederalPreKfs, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("FederalPreKfs() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("FederalPreKfs() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("FederalPreKfs() error = %v assertion failed", err)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, result, opts...); diff != "" {
				t.Errorf("FederalPreKfs() Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}
