package helper

import (
	"errors"
	"math"
)

const (
	irrMaxInterations = 20
	irrAccuracy       = 1e-7
	irrInitialGuess   = 0
)

// Irr returns the Internal Rate of Return (IRR).
func Irr(values []float64) (float64, error) {
	if len(values) == 0 {
		return 0, errors.New("values must include the initial investment (usually negative number) and period cash flows")
	}
	x0 := float64(irrInitialGuess)
	var x1 float64
	for i := 0; i < irrMaxInterations; i++ {
		fValue := float64(0)
		fDerivative := float64(0)
		for k := 0; k < len(values); k++ {
			fk := float64(k)
			fValue += values[k] / math.Pow(1.0+x0, fk)
			fDerivative += -fk * values[k] / math.Pow(1.0+x0, fk+1.0)
		}
		x1 = x0 - fValue/fDerivative
		if math.Abs(x1-x0) <= irrAccuracy {
			return x1, nil
		}
		x0 = x1
	}
	return 0, errors.New("could not find irr for the provided values")
}

func GetIrr(disbursedAmount float64, emiAmount float64, tenure int32) float64 {
	var reqFloat []float64
	reqFloat = append(reqFloat, disbursedAmount*(-1))
	for i := int32(0); i < tenure; i++ {
		reqFloat = append(reqFloat, emiAmount)
	}
	irr, err := Irr(reqFloat)
	if err != nil {
		return 0
	}
	return irr
}
