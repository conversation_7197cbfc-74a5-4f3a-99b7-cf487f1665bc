package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

// LoecCreatedEvent represents when a loan offer eligibility criteria is created
type LoecCreatedEvent struct {
	ActorId        string
	SessionId      string
	ProspectId     string
	EventId        string
	EventType      string
	Timestamp      time.Time
	Vendor         palPb.Vendor
	LoanProgram    palPb.LoanProgram
	BatchId        string
	Status         string
	SubStatus      string
	ProveyancyType string
}

// NewLoecCreatedEvent creates a new LoecCreatedEvent
func NewLoecCreatedEvent(loec *palPb.LoanOfferEligibilityCriteria, provenanceType string) *LoecCreatedEvent {
	return &LoecCreatedEvent{
		ActorId:        loec.GetActorId(),
		EventId:        uuid.New().String(),
		EventType:      events.EventTrack,
		Timestamp:      time.Now(),
		Vendor:         loec.GetVendor(),
		LoanProgram:    loec.GetLoanProgram(),
		BatchId:        loec.GetBatchId(),
		Status:         loec.GetStatus().String(),
		SubStatus:      loec.GetSubStatus().String(),
		ProveyancyType: provenanceType,
	}
}

func (c *LoecCreatedEvent) GetProspectId() string {
	return ""
}

func (c *LoecCreatedEvent) GetEventType() string {
	return c.EventType
}

func (c *LoecCreatedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *LoecCreatedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *LoecCreatedEvent) GetEventId() string {
	return c.EventId
}

func (c *LoecCreatedEvent) GetUserId() string {
	return c.ActorId
}

func (c *LoecCreatedEvent) GetEventName() string {
	return EventLoecCreated
}
