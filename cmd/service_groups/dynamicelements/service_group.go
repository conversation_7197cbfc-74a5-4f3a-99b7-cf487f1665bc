package dynamicelements

import (
	"github.com/epifi/be-common/tools/servergen/meta"

	depb "github.com/epifi/gamma/api/dynamic_elements"
	wire "github.com/epifi/gamma/dynamicelements/wire"
)

var shouldInitQuest = true

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeDynamicElementsService,
		GRPCRegisterMethods: []any{depb.RegisterDynamicElementsServer},
	},
}
