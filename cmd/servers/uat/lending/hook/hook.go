package servergenhook

import (
	"context"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"

	inhousebrePb "github.com/epifi/gamma/api/inhousebre"
	ffInterceptor "github.com/epifi/gamma/firefly/interceptor"
	inhouseBreConf "github.com/epifi/gamma/inhousebre/config"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
)

const JsonContentType = "json"

func InitLendingServer(gconf *genconf.Config, s *grpc.Server, httpMux *http.ServeMux, inhouseBreClient inhousebrePb.BreClient) (func(), error) {
	cleanupFn := func() {}

	ffInterceptor.LoadFireflyMethodDescriptors(s)

	inhouseBreConfig, err := inhouseBreConf.Load()
	if err != nil {
		return cleanupFn, err
	}

	grpcGwMux := runtime.NewServeMux(
		runtime.WithMarshalerOption(JsonContentType, &runtime.JSONPb{
			MarshalOptions: protojson.MarshalOptions{
				EmitUnpopulated: false,
			},
			UnmarshalOptions: protojson.UnmarshalOptions{
				DiscardUnknown: true,
			},
		}),
		runtime.WithMarshalerOption(runtime.MIMEWildcard, &runtime.JSONPb{
			MarshalOptions: protojson.MarshalOptions{
				EmitUnpopulated: false,
			},
			UnmarshalOptions: protojson.UnmarshalOptions{
				DiscardUnknown: true,
			},
		}),
	)

	handlers := []func(ctx context.Context, mux *runtime.ServeMux, client inhousebrePb.BreClient) error{
		inhousebrePb.RegisterBreHandlerClient,
	}

	g, grpCtx := errgroup.WithContext(context.Background())
	for _, RegisterBreHandlerClient := range handlers {
		if err1 := RegisterBreHandlerClient(grpCtx, grpcGwMux, inhouseBreClient); err1 != nil {
			logger.Panic("failed to register grpc handler from endpoint", zap.Error(err1))
		}
	}

	epifiserver.RegisterHealthCheckEndpoint(httpMux, inhouseBreConfig.Application.Name)
	httpMux.Handle("/", grpcGwMux)

	if cfg.IsRemoteDebugEnabled() || cfg.IsLocalEnv(gconf.Environment()) || cfg.IsTeamSpaceTenant() {
		httpsServer := epifiserver.NewHttpServer(gconf.HttpServerPorts().HttpSecurePort, httpMux)
		cleanupFn = epifiserver.StartHttpServer(grpCtx, g.Group, httpsServer)
	} else {
		httpsServer := epifiserver.NewSecureHttpServer(gconf.HttpServerPorts().HttpSecurePort, httpMux, string(gconf.Name()))
		cleanupFn = epifiserver.StartHttpsServer(grpCtx, g.Group, httpsServer, "", "")
	}
	return cleanupFn, nil
}
