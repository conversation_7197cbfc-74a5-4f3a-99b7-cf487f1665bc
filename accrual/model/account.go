package model

import (
	"time"

	"github.com/pkg/errors"

	pb "github.com/epifi/gamma/api/accrual"
)

// todo(utkarsh) : add documentation on dtos

type AccountIdentifier struct {
	ActorId     string
	AccountType pb.AccountType
}

func (a *AccountIdentifier) Validate() error {
	if a.ActorId == "" {
		return errors.New("AccountIdentifier : actor id nil")
	}
	if a.AccountType == pb.AccountType_ACCOUNT_TYPE_UNSPECIFIED {
		return errors.New("AccountIdentifier : account type unspecified")
	}
	return nil
}

type CreateAccountRequest struct {
	AccIdentifier         *AccountIdentifier
	OpeningBalance        int32
	BalanceNextExpiryTime time.Time
}

func (a *CreateAccountRequest) Validate() error {
	if err := a.AccIdentifier.Validate(); err != nil {
		return err
	}
	if a.OpeningBalance < 0 {
		return errors.New("CreateAccountRequest : account opening balance shouldn't be negative")
	}
	if a.BalanceNextExpiryTime.IsZero() {
		return errors.New("CreateAccountRequest : balance_next_expiry time is nil")
	}
	return nil
}

type UpdateAccBalanceRequest struct {
	AccountId           string
	NewCurrentBalance   int32
	NewAvailableBalance int32
	NewExpiryTime       time.Time
}

func (a *UpdateAccBalanceRequest) Validate() error {
	if a.AccountId == "" {
		return errors.New("UpdateAccBalanceRequest : account id nil")
	}
	if a.NewCurrentBalance < 0 {
		return errors.New("UpdateAccBalanceRequest : new balance shouldn't be negative")
	}
	if a.NewAvailableBalance > a.NewCurrentBalance {
		return errors.New("UpdateAccBalanceRequest : NewAvailableBalance shouldn't be more than NewCurrentBalance")
	}
	// only is account balance is positive then only validate the expiry time
	if a.NewCurrentBalance > 0 && a.NewExpiryTime.IsZero() {
		return errors.New("UpdateAccBalanceRequest : new_expiry time is nil")
	}
	return nil
}

type Account struct {
	AccountId             string
	CurrentBalance        int32
	AvailableBalance      int32
	BalanceNextExpiryTime time.Time
}

// IsBalanceDirty returns whether the current balance field is dirty or not.
// If true then the account balance should be updated using resolveBalance method.
func (a *Account) IsBalanceDirty() bool {
	return a.CurrentBalance > 0 && a.BalanceNextExpiryTime.Before(time.Now())
}
