package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	gormctx "github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	goalsPb "github.com/epifi/gamma/api/goals"
	"github.com/epifi/gamma/goals/dao"
	"github.com/epifi/gamma/goals/dao/model"
)

// GoalDaoCrdb implements GoalDao using CRDB
type GoalDaoCrdb struct {
	db    *gorm.DB
	idGen idgen.IdGenerator
}

// ensure GoalDaoCrdb implements GoalDao at compile time
var _ dao.GoalDao = &GoalDaoCrdb{}

// NewGoalDaoCrdb is factory method for creating an instance of goal dao.
// This method will be used by the injector when providing the dependencies at initialization time.
func NewGoalDaoCrdb(db cmdTypes.EpifiCRDB, idGen idgen.IdGenerator) *GoalDaoCrdb {
	return &GoalDaoCrdb{db: db, idGen: idGen}
}

// filters out fields which cannot be updated
var goalDaoUpdateFieldFilter = func(field goalsPb.GoalFieldMask) bool {
	return goalsPb.GoalFieldMask_GOAL_FIELD_MASK_UNSPECIFIED != field &&
		goalsPb.GoalFieldMask_GOAL_FIELD_MASK_ID != field &&
		goalsPb.GoalFieldMask_GOAL_FIELD_MASK_ACTOR_ID != field
}

// Create creates an entry in the goals table
func (g *GoalDaoCrdb) Create(ctx context.Context, goal *goalsPb.Goal) (*goalsPb.Goal, error) {
	defer metric_util.TrackDuration("goals/dao/impl", "GoalDaoCrdb", "Create", time.Now())
	db := gormctx.FromContextOrDefault(ctx, g.db)

	id, err := g.idGen.Get(idgen.Goal)
	if err != nil {
		return nil, fmt.Errorf("id generation failed: %w", err)
	}

	goalModel, err := model.NewGoal(goal)
	if err != nil {
		return nil, fmt.Errorf("failed to call model.NewGoal: %w", err)
	}

	goalModel.Id = id
	if err = db.Create(goalModel).Error; err != nil {
		return nil, fmt.Errorf("failed to call db.Create: %w", err)
	}

	goalProto, err := goalModel.ToProto()
	if err != nil {
		return nil, fmt.Errorf("failed to call goalModel.ToProto: %w", err)
	}
	return goalProto, nil
}

// GetById gets the goal for an ID
func (g *GoalDaoCrdb) GetById(ctx context.Context, goalId string) (*goalsPb.Goal, error) {
	defer metric_util.TrackDuration("goals/dao/impl", "GoalDaoCrdb", "GetById", time.Now())
	db := gormctx.FromContextOrDefault(ctx, g.db)

	var fetchedGoal model.Goal
	if err := db.Where("id = ?", goalId).First(&fetchedGoal).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to get goal by id: %s, error: %v: %w", goalId, err,
				epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to get goal by id: %s: %w", goalId, err)
	}

	goalProto, err := fetchedGoal.ToProto()
	if err != nil {
		return nil, fmt.Errorf("failed to call fetchedGoal.ToProto: %w", err)
	}
	return goalProto, nil
}

// GetByIds fetches goals for given list of ids
func (g *GoalDaoCrdb) GetByIds(ctx context.Context, goalIds []string) ([]*goalsPb.Goal, error) {
	defer metric_util.TrackDuration("goals/dao/impl", "GoalDaoCrdb", "GetByIds", time.Now())
	db := gormctx.FromContextOrDefault(ctx, g.db)
	fetchedGoals := make([]*model.Goal, 0)

	if err := db.Where("id in (?)", goalIds).Find(&fetchedGoals).Error; err != nil {
		return nil, fmt.Errorf("failed to get goals by ids %v: %w", goalIds, err)
	}

	goalProtos, err := convertGoalModelsToGoalsProtos(fetchedGoals)
	if err != nil {
		return nil, fmt.Errorf("failed to convert goal models to protos: %w", err)
	}
	return goalProtos, nil
}

// UpdateById updates columns specified in the fields mask
func (g *GoalDaoCrdb) UpdateById(ctx context.Context, goal *goalsPb.Goal, updateMask []goalsPb.GoalFieldMask) (*goalsPb.Goal, error) {
	defer metric_util.TrackDuration("goals/dao/impl", "GoalDaoCrdb", "UpdateById", time.Now())
	db := gormctx.FromContextOrDefault(ctx, g.db)

	if goal.Id == "" {
		return nil, fmt.Errorf("goal id can't be empty for an update operation")
	}

	// filter out field masks which cannot be updated
	updateMask = filterGoalDaoFieldMaskSlice(updateMask, goalDaoUpdateFieldFilter)
	if len(updateMask) == 0 {
		return nil, fmt.Errorf("update mask can't be empty")
	}

	goalModel, err := model.NewGoal(goal)
	if err != nil {
		return nil, fmt.Errorf("failed to convert from proto to model: %w", err)
	}

	// convert fields masks to column name
	updateColumns := getSelectColumnsForGoal(updateMask)

	res := db.Model(goalModel).Where(
		"id = ?", goal.Id).Select(updateColumns).Updates(goalModel)
	if res.Error != nil {
		return nil, fmt.Errorf("failed to update goal: id: %s: %w",
			goal.GetId(), res.Error)
	}
	if res.RowsAffected == 0 {
		return nil, fmt.Errorf("failed to update goal: id: %s, %w",
			goal.GetId(),
			epifierrors.ErrRecordNotFound)
	}
	return goalModel.ToProto()
}

func convertGoalModelsToGoalsProtos(goalModels []*model.Goal) ([]*goalsPb.Goal, error) {
	goalProtos := make([]*goalsPb.Goal, 0)

	for _, goalModel := range goalModels {
		goalProto, err := goalModel.ToProto()
		if err != nil {
			return nil, err
		}
		goalProtos = append(goalProtos, goalProto)
	}
	return goalProtos, nil
}

// filterGoalDaoFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterGoalDaoFieldMaskSlice(fieldMasks []goalsPb.GoalFieldMask,
	check func(field goalsPb.GoalFieldMask) bool) []goalsPb.GoalFieldMask {
	filteredFieldMasks := make([]goalsPb.GoalFieldMask, 0)
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			filteredFieldMasks = append(filteredFieldMasks, fieldMask)
		}
	}
	return filteredFieldMasks
}

// getSelectColumnsForGoal converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForGoal(fieldMasks []goalsPb.GoalFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, GoalColumnNameMap[field])
	}
	return selectColumns
}
