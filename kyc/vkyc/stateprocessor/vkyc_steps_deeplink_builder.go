package stateprocessor

import (
	"fmt"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	types "github.com/epifi/gamma/api/typesv2"
	vkycDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	panPkg "github.com/epifi/gamma/pkg/pan"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

const (
	stepAadhaarVerification = "Aadhaar verification"
	stepConfirmDetails      = "Confirm your details"
	panStepCompleted        = "PAN card available"
	ePAnUploaded            = "e-PAN uploaded"
	aadhaarStepCompleted    = "Aadhaar verified"
)

type VKYCStepsScreenOptionBuilder struct {
	vkycStepsScreenOptions *vkycDlPb.VkycStepsScreenOptions
}

func NewVKYCStepsScreenOptionBuilder() *VKYCStepsScreenOptionBuilder {
	return &VKYCStepsScreenOptionBuilder{
		vkycStepsScreenOptions: &vkycDlPb.VkycStepsScreenOptions{},
	}
}

func (s *VKYCStepsScreenOptionBuilder) Build() *vkycDlPb.VkycStepsScreenOptions {
	return s.vkycStepsScreenOptions
}

func (s *VKYCStepsScreenOptionBuilder) WithHeaderBar(leftIconDl *deeplink.Deeplink, rightIconDl *deeplink.Deeplink) *VKYCStepsScreenOptionBuilder {
	s.vkycStepsScreenOptions.HeaderBar = &ui.HeaderBar{
		RightItc: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 28, 28)).
			WithDeeplink(rightIconDl).WithContainerPadding(2, 0, 2, 0),
		LeftItc: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/savingsAccountClosure/back-icon.png", 28, 28)).
			WithDeeplink(leftIconDl).WithContainerPadding(2, 0, 2, 0),
		CenterItc: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/federal_bank_powered_by_fi.png", 40, 80)).
			WithContainerPadding(2, 0, 2, 0),
	}
	return s
}

func (s *VKYCStepsScreenOptionBuilder) WithTitle() *VKYCStepsScreenOptionBuilder {
	s.vkycStepsScreenOptions.Title = commontypes.GetTextFromStringFontColourFontStyle("Let’s get you all set", "#313234", commontypes.FontStyle_HEADLINE_XL)
	return s
}

func (s *VKYCStepsScreenOptionBuilder) WithSubtitle() *VKYCStepsScreenOptionBuilder {
	s.vkycStepsScreenOptions.Description = commontypes.GetTextFromStringFontColourFontStyle("A quick checklist to make sure your call with the bank’s Relationship Manager goes smoothly ", "#6A6D70", commontypes.FontStyle_BODY_S)
	return s
}

func (s *VKYCStepsScreenOptionBuilder) WithFooterText() *VKYCStepsScreenOptionBuilder {
	s.vkycStepsScreenOptions.FooterText = ui.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle("Not ready? <span style=\"color: #00B899\">Take the call later</span>", "#6A6D70", commontypes.FontStyle_HEADLINE_S)).
		WithDeeplink(&deeplink.Deeplink{
			Screen: deeplink.Screen_VKYC_SCHEDULE_CALL,
		}).WithContainerPaddingSymmetrical(32, 24).WithContainerBackgroundColor("#EEF2F6")
	return s
}

func (s *VKYCStepsScreenOptionBuilder) AppendStepCompletedCard(stepInfo string) *VKYCStepsScreenOptionBuilder {
	if len(s.vkycStepsScreenOptions.GetSteps()) == 0 {
		s.vkycStepsScreenOptions.Steps = make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep, 0)
	}
	s.vkycStepsScreenOptions.Steps = append(s.vkycStepsScreenOptions.Steps, &vkycDlPb.VkycStepsScreenOptions_VkycStep{
		Step: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ClosedStep_{
			ClosedStep: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ClosedStep{
				StepInfo: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(stepInfo, "#648E4D", commontypes.FontStyle_SUBTITLE_M)).WithLeftImagePadding(12).
					WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/check_full_circle.png", 16, 16)),
			},
		},
	})
	return s
}

func (s *VKYCStepsScreenOptionBuilder) AppendStepPendingCard(stepName string) *VKYCStepsScreenOptionBuilder {
	if len(s.vkycStepsScreenOptions.GetSteps()) == 0 {
		s.vkycStepsScreenOptions.Steps = make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep, 0)
	}
	stepNumber := len(s.vkycStepsScreenOptions.GetSteps()) + 1
	s.vkycStepsScreenOptions.Steps = append(s.vkycStepsScreenOptions.Steps, &vkycDlPb.VkycStepsScreenOptions_VkycStep{
		Step: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ClosedStep_{
			ClosedStep: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ClosedStep{
				StepInfo: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(stepName, "#6A6D70", commontypes.FontStyle_SUBTITLE_M)).WithLeftImagePadding(8).
					WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(fmt.Sprintf("https://epifi-icons.pointz.in/onboarding/num_%v_icon.png", stepNumber), 28, 28)),
			},
		},
	})
	return s
}

func (s *VKYCStepsScreenOptionBuilder) AppendPhysicalPanActionCard(entryPoint vkyc.EntryPoint, shouldShowEPANOption bool) *VKYCStepsScreenOptionBuilder {
	if len(s.vkycStepsScreenOptions.GetSteps()) == 0 {
		s.vkycStepsScreenOptions.Steps = make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep, 0)
	}

	ctas := []*deeplink.Cta{
		{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Yes, I have my PAN now",
			DisplayTheme: deeplink.Cta_PRIMARY,
			Deeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
				EntryPoint:      entryPoint.String(),
				ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY.String(),
			}),
		},
	}

	if shouldShowEPANOption {
		ctas = append(ctas, &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "I don’t have PAN",
			DisplayTheme: deeplink.Cta_SECONDARY,
			Deeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
				EntryPoint:      entryPoint.String(),
				ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS.String(),
			}),
		})
	}
	s.vkycStepsScreenOptions.Steps = append(s.vkycStepsScreenOptions.Steps, &vkycDlPb.VkycStepsScreenOptions_VkycStep{
		Step: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_{
			OpenStep: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep{
				OpenState: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_Action{
					Action: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ActionStepDetails{
						StepTitle: commontypes.GetTextFromStringFontColourFontStyle("Do you have your original PAN card with you now?", "#333333", commontypes.FontStyle_SUBTITLE_M),
						StepImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/physical_pan.png", 64, 84),
						Ctas:      ctas,
					},
				},
			},
		},
	})
	return s
}

// TODO add new EPAN deeplink
func (s *VKYCStepsScreenOptionBuilder) AppendEPanActionCard(entryPoint vkyc.EntryPoint, epanClientReqId string) *VKYCStepsScreenOptionBuilder {
	if len(s.vkycStepsScreenOptions.GetSteps()) == 0 {
		s.vkycStepsScreenOptions.Steps = make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep, 0)
	}
	epanInstrCta := []*deeplink.Cta{
		{
			Type:         deeplink.Cta_DONE,
			Text:         vkycPkg.OkGotItCtaTxt,
			DisplayTheme: deeplink.Cta_PRIMARY,
			Deeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
				ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS.String(),
				EntryPoint:      entryPoint.String(),
				ShowCtaLoader:   true,
			}),
		},
	}
	s.vkycStepsScreenOptions.Steps = append(s.vkycStepsScreenOptions.Steps, &vkycDlPb.VkycStepsScreenOptions_VkycStep{
		Step: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_{
			OpenStep: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep{
				OpenState: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_Action{
					Action: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ActionStepDetails{
						StepTitle: commontypes.GetTextFromStringFontColourFontStyle("Verify by uploading e-PAN (as PDF)", "#333333", commontypes.FontStyle_SUBTITLE_M),
						StepImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/physical_pan.png", 64, 84),
						BottomCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("What is e-PAN and how to download it?", "#007A56", commontypes.FontStyle_SUBTITLE_XS)).
							WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/nudge/chevron-right-jade.png", 20, 20)).WithRightImagePadding(4).
							WithDeeplink(getEpanInstructionScreen(epanInstrCta)),
						Ctas: []*deeplink.Cta{
							{
								Type:         deeplink.Cta_CUSTOM,
								Text:         "Upload",
								DisplayTheme: deeplink.Cta_PRIMARY,
								Deeplink: panPkg.GetUploadEPANDeeplink(epanClientReqId, &panPkg.EPANDeeplinks{
									EPANSuccessDeeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
										EntryPoint:      entryPoint.String(),
										ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY.String(),
									}),
									EPANRetryableDeeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
										EntryPoint:      entryPoint.String(),
										ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS.String(),
									}),
								}),
							},
							{
								Type:         deeplink.Cta_CUSTOM,
								Text:         "I have original PAN card",
								DisplayTheme: deeplink.Cta_SECONDARY,
								Deeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
									EntryPoint:      entryPoint.String(),
									ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
								}),
							},
						},
					},
				},
			},
		},
	})
	return s
}

func (s *VKYCStepsScreenOptionBuilder) AppendEKYCActionCard(entryPoint vkyc.EntryPoint) *VKYCStepsScreenOptionBuilder {
	if len(s.vkycStepsScreenOptions.GetSteps()) == 0 {
		s.vkycStepsScreenOptions.Steps = make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep, 0)
	}
	s.vkycStepsScreenOptions.Steps = append(s.vkycStepsScreenOptions.Steps, &vkycDlPb.VkycStepsScreenOptions_VkycStep{
		Step: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_{
			OpenStep: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep{
				OpenState: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_Action{
					Action: &vkycDlPb.VkycStepsScreenOptions_VkycStep_ActionStepDetails{
						StepTitle: commontypes.GetTextFromStringFontColourFontStyle("Let’s verify your Aadhaar details", "#333333", commontypes.FontStyle_SUBTITLE_M),
						StepImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/aadhaar.png", 46, 63),
						BottomCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Why am I verifying my Aadhaar again?", "#646464", commontypes.FontStyle_SUBTITLE_XS)).
							WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/information-icon.png", 20, 20)).WithRightImagePadding(4).
							WithDeeplink(getEKycExpireInfoBs()),
						Ctas: []*deeplink.Cta{
							{
								Type:         deeplink.Cta_CUSTOM,
								Text:         "Continue",
								DisplayTheme: deeplink.Cta_PRIMARY,
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_START_EKYC,
									ScreenOptions: &deeplink.Deeplink_StartEkycOptions{
										StartEkycOptions: &deeplink.StartEKYCOptions{
											KycLevel: types.KYCLevel_MIN_KYC,
											NextAction: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
												EntryPoint:      entryPoint.String(),
												ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_EKYC_CHECK.String(),
												ShowCtaLoader:   true,
											}),
											EkycSource: kyc.EkycSource_EKYC_SOURCE_VKYC.String(),
											EkycFlow:   deeplink.StartEKYCOptions_EKYC_FLOW_VKYC,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	})
	return s

}

func (s *VKYCStepsScreenOptionBuilder) AppendDetailConfirmationCard(entryPoint vkyc.EntryPoint, isDetailsEditable bool, detailsEditDl *deeplink.Deeplink, details map[string]string, orderedKeys []string) *VKYCStepsScreenOptionBuilder {
	if len(s.vkycStepsScreenOptions.GetSteps()) == 0 {
		s.vkycStepsScreenOptions.Steps = make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep, 0)
	}
	ctas := []*deeplink.Cta{
		{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Yes, start video call",
			DisplayTheme: deeplink.Cta_PRIMARY,
			Deeplink: vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
				EntryPoint:      entryPoint.String(),
				ClientLastState: vkyc.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS.String(),
			}),
			CtaLeadingImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/white_tick.png", 24, 24),
		},
	}
	if isDetailsEditable {
		ctas = append(ctas, &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Edit details",
			DisplayTheme: deeplink.Cta_TEXT,
			Deeplink:     detailsEditDl,
		})
	}

	detailsRows := make([]*vkycDlPb.VkycStepsScreenOptions_VkycStep_DetailConfirmationStepDetails_DetailRow, 0)
	for _, detailsLabel := range orderedKeys {
		detailsRows = append(detailsRows, &vkycDlPb.VkycStepsScreenOptions_VkycStep_DetailConfirmationStepDetails_DetailRow{
			Header: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(detailsLabel, "#929599", commontypes.FontStyle_OVERLINE_S_CAPS)),
			Value:  ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(details[detailsLabel], "#333333", commontypes.FontStyle_SUBTITLE_M)),
		})
	}

	s.vkycStepsScreenOptions.Steps = append(s.vkycStepsScreenOptions.Steps, &vkycDlPb.VkycStepsScreenOptions_VkycStep{
		Step: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_{
			OpenStep: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep{
				OpenState: &vkycDlPb.VkycStepsScreenOptions_VkycStep_OpenStep_DetailConfirmation{
					DetailConfirmation: &vkycDlPb.VkycStepsScreenOptions_VkycStep_DetailConfirmationStepDetails{
						Title:       commontypes.GetTextFromStringFontColourFontStyle("Are these details correct?", "#333333", commontypes.FontStyle_SUBTITLE_M),
						Description: commontypes.GetTextFromStringFontColourFontStyle("Double-check before proceeding to the call", "#6A6D70", commontypes.FontStyle_BODY_XS),
						Ctas:        ctas,
						Rows:        detailsRows,
					},
				},
			},
		},
	})
	return s

}

func GetVkycFQAScreenDl(categoryId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_FAQ_CATEGORY,
		ScreenOptions: &deeplink.Deeplink_FaqCategoryOptions{
			FaqCategoryOptions: &deeplink.FaqCategoryOptions{
				CategoryId: categoryId,
			},
		},
	}
}

// nolint:dupl
func getEKycExpireInfoBs() *deeplink.Deeplink {
	mainSectionComponents := []*components.Component{
		{
			Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XL)),
		},
		{
			Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Why am I verifying my Aadhaar again?", "#333333", commontypes.FontStyle_SUBTITLE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
		},
		{
			Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_L)),
		},
		{
			Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Your Aadhaar verification has a 3-day expiry.", "#333333", commontypes.FontStyle_BODY_S).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
		},
		{
			Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_M)),
		},
		{
			Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("As it’s been more than 3 days since you completed Aadhaar eKYC, you’re required to verify it again before Video KYC. ", "#333333", commontypes.FontStyle_BODY_S).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
		},
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_GENERIC_SDUI_BOTTOMSHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&vkycDlPb.VkycGenericSDUIBottomSheet{
			SduiSection: &sections.Section{
				Content: &sections.Section_DepthWiseListSection{
					DepthWiseListSection: &sections.DepthWiseListSection{
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithPadding(24, 16, 16, 24).
										WithAllCornerRadii(20, 20, 0, 0).
										WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
										WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
										WithBgColor(widget.GetBlockBackgroundColour("#FFFFFF")),
								},
							},
						},
						Alignment: sections.DepthWiseListSection_TOP_RIGHT,
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(&sections.VerticalListSection{
									Components: mainSectionComponents,
									VisualProperties: []*properties.VisualProperty{
										{
											Properties: &properties.VisualProperty_ContainerProperty{
												ContainerProperty: properties.GetContainerProperty().
													WithPadding(0, 0, 8, 0).
													WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
													WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
											},
										},
									},
								}),
							},
							// top left cross icon
							{
								Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/ic_cross_circle_bg_lead.png", 24, 24)),
								InteractionBehaviors: []*behaviors.InteractionBehavior{
									{
										Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
											OnClickBehavior: &behaviors.OnClickBehavior{
												Action: GetAnyWithoutError(&deeplink.Cta{
													Type: deeplink.Cta_CANCEL,
												}),
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}),
	}
}

func GetAnyWithoutError(msg proto.Message) *anypb.Any {
	res, _ := anypb.New(msg)
	return res
}
