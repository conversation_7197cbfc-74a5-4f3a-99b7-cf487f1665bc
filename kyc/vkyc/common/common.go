package common

import (
	"context"
	"time"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/kyc/config/genconf"
)

func IsBusinessHours(curTime time.Time, vkycStartHour int, vkycEndHour int) bool {
	if curTime.Hour() >= vkycStartHour && curTime.Hour() < vkycEndHour {
		return true
	}
	return false
}

// IsVKYCFlowV2Enabled checks if VKYC revamp flow is enabled
// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
func IsVKYCFlowV2Enabled(ctx context.Context, entryPoint vkyc.EntryPoint, conf *genconf.Config) bool {
	if entryPoint == vkyc.EntryPoint_ENTRY_POINT_VKYC_HOME {
		return apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.VKYC().EnableVKYCFlowV2ForHome())
	}

	if isVKYCFlowV2DisableForEntryPoint(entryPoint) {
		return false
	}

	// for rest of the flow this will be behind the feature flag
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.VKYC().EnableVKYCFlowV2())
}

func isVKYCFlowV2DisableForEntryPoint(entryPoint vkyc.EntryPoint) bool {
	vKYCFlowV2DisabledEntryPoint := []vkyc.EntryPoint{
		vkyc.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING,
		vkyc.EntryPoint_ENTRY_POINT_STOCKGUARDIAN_LOANS,
		vkyc.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING_QATAR,
		vkyc.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING_QATAR_FORM_60_FLOW,
		vkyc.EntryPoint_ENTRY_POINT_NON_RESIDENT_ONBOARDING_UAE_FORM_60_FLOW,
	}
	return lo.Contains(vKYCFlowV2DisabledEntryPoint, entryPoint)
}

// IsVKYCBenefitScreenV2Enabled checks if VKYC comparison based benefit screen is enabled
// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34490&t=mJhct9OMnhgejSEG-4
func IsVKYCBenefitScreenV2Enabled(ctx context.Context, entryPoint vkyc.EntryPoint, conf *genconf.Config) bool {
	if entryPoint != vkyc.EntryPoint_ENTRY_POINT_VKYC_HOME {
		return false
	}
	return conf.VKYC().EnableVKYCBenefitScreenV2(ctx)
}
