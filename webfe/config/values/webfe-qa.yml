Application:
  Environment: "qa"
  Name: "webfe"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true


Accounts:
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 12
    HystrixCommand:
      CommandName: "webfe_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 200
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80

RiskS3Config:
  BucketName: "epifi-qa-risk"

DebitCardS3Buckets:
  DcDocsBucketName: "epifi-qa-debit-card-docs"
