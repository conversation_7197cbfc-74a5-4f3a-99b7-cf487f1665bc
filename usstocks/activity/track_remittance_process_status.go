package activity

import (
	"context"

	errors2 "github.com/pkg/errors"
	activity2 "go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/activity"
	"github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) TrackRemittanceProcessStatus(ctx context.Context, req *activity.TrackRemittanceProcessStatusRequest) (*activity.TrackRemittanceProcessStatusResponse, error) {
	lg := activity2.GetLogger(ctx)
	remittanceProcess, err := p.remittanceProcessesDao.GetBySwiftTransferId(ctx, req.GetSwiftTransferId(),
		usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
		[]order.RemittanceProcessFieldMask{order.RemittanceProcessFieldMask_REMITTANCE_PROCESS_FIELD_MASK_STATUS})
	if err != nil {
		lg.Error("error in getting remittance processes by filters", zap.Error(err), zap.String(logger.SWIFT_TRANSFER_ID, req.GetSwiftTransferId()))
		// NOTE: not considering RecordNotFound case as PermanentFailure
		// as there is possibility that the remittance status polling workflow is not started yet
		return nil, errors2.Wrap(epifierrors.ErrTransient, err.Error())
	}

	if isRemittanceStatusTerminal(remittanceProcess.GetStatus()) {
		lg.Info("remittance process transitioned to terminal state", zap.String(logger.STATUS, remittanceProcess.GetStatus().String()),
			zap.String(logger.SWIFT_TRANSFER_ID, remittanceProcess.GetSwiftTransferId()))
		return &activity.TrackRemittanceProcessStatusResponse{
			Status: remittanceProcess.GetStatus(),
		}, nil
	}

	return nil, epifierrors.ErrTransient
}

func isRemittanceStatusTerminal(status usstocks.RemittanceProcessStatus) bool {
	if status == usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_FAILED ||
		status == usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_SUCCESS {
		return true
	}
	return false
}
