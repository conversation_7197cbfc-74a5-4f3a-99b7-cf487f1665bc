Application:
  Environment: "uat"
  Name: "usstocks"

Server:
  Ports:
    GrpcPort: 8110
    GrpcSecurePort: 9526
    HttpPort: 9999

Flags:
  EnableCatalogSearchSync: false

# Since zinc ingestion is failing on UAT because of no data being present in DBs,
# we initialize zinc in a separate goroutine to avoid failing server initialization
EnableZincIngestionInSync: false

USStocksAlpacaDb:
  DbType: "CRDB"
  AppName: "usstocks"
  StatementTimeout: 1s
  Username: "usstocks_alpaca_dev_user"
  Password: ""
  Name: "usstocks_alpaca"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.usstocks_alpaca_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.usstocks_alpaca_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "usstocks_alpaca_pgdb"
        DbDsn:
          DbType: "PGDB"
          Name: "usstocks_alpaca"
          SSLMode: "disable"
          SecretName: "uat/rds/epifimetis/usstocks_alpaca_dev_user"

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    ZincCredentials: "uat/investment/zinc"
    UsStocksSlackBotOauthToken: "uat/ift/slack-bot-oauth-token"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CatalogS3Conf:
  BucketName: "epifi-uat-usstocks-alpaca"

ProcessUSStockCatalogUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstock-process-catalog-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

ProcessUSEtfCatalogUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usetf-process-catalog-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

ProcessOrderUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-oms-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

AmlActionEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-aml-action-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

CelestialWorkflowUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-celestial-wf-update-consumer-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 12
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 84
          TimeUnit: "Hour"
      MaxAttempts: 96
      CutOff: 12

AccountManagerConfig:
  KycDocumentsBucketName: "epifi-uat-usstocks-alpaca"

ProcessAccountActivitySyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-account-activity-sync-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

ProcessAccountActivitySyncPublisher:
  QueueName: "uat-usstocks-account-activity-sync-queue"

OrderUpdateEventsConnectionInfo:
  Enable: false

AccountUpdateEventsConnectionInfo:
  Enable: false

VendorActorId:
  1: "*********/N8DUqhRVqEUGWi4AB7EA==" # Vendor 1 is ALPACA

LivenessS3BucketName: "epifi-uat-liveness"

FaceMatchThreshold: 30

USStockCatalogRefreshPublisher:
  QueueName: "uat-usstocks-catalog-refresh-queue"

USStockCatalogRefreshSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "uat-usstocks-catalog-refresh-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

UsStocksSendMailToUsersPublisher:
  QueueName: "uat-usstocks-send-mail-to-users-queue"

UsStocksSendMailToUsersSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-send-mail-to-users-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

CommsEmail:
  FromEmailId:
    EmailId: "<EMAIL>"
    EmailName: "Fi Money"
  AlpacaEmail:
    EmailId: "<EMAIL>"
    EmailName: "Fi Money"

VendorMappingRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10

NonFinancialEventSqsPublisher:
  QueueName: "uat-non-financial-investment-event-queue"

USStocksRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password

SkipPanValidation: false

USStocksIFTRemittanceFileProcessingEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-remittance-file-processing-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

MorningStarS3Bucket:
  BucketName: "epifi-uat-usstocks-morningstar-u"


PgdbMigrationConf:
  UsePgdb: true

VendorAccountActivitiesBucketName: "epifi-uat-usstocks-alpaca"

USStocksIncomeUpdateEventsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-income-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

USStocksTaxDocumentGenerationRequestSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-usstocks-tax-document-generation-request-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

TaxDocumentsBucketName: "epifi-uat-usstocks-alpaca"

IsNewStockUniverseEnabled: true
