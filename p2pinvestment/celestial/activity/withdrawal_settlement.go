package activity

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/pkg/errors"
	temporalActivity "go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	activityPb "github.com/epifi/gamma/api/p2pinvestment/celestial/activity"
	p2pVgPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	"github.com/epifi/gamma/p2pinvestment/helper"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	pkgLogger "github.com/epifi/be-common/pkg/logger"
)

// nolint:funlen
func (p *Processor) SettleWithdrawal(ctx context.Context, req *activityPb.SettleWithdrawalRequest) (*activityPb.SettleWithdrawalResponse, error) {
	logger := temporalActivity.GetLogger(ctx)
	logger.Info("settling withdrawal", zap.String(pkgLogger.P2P_INVESTMENT_ID, req.GetTxnId()))

	txn, err := p.investmentTransactionDao.GetById(ctx, req.GetTxnId())
	if err != nil {
		logger.Error("error in getting txn from db", zap.Error(err), zap.String(pkgLogger.P2P_INVESTMENT_ID, req.GetTxnId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting txn from db: %v", err.Error()))
	}

	switch txn.GetStatus() {
	case p2pPb.InvestmentTransactionStatus_SUCCESS,
		p2pPb.InvestmentTransactionStatus_APPROVED:
		return &activityPb.SettleWithdrawalResponse{}, nil
	case p2pPb.InvestmentTransactionStatus_FAILED:
		return nil, errors.Wrap(epifierrors.ErrPermanent, "txn failed")
	case p2pPb.InvestmentTransactionStatus_INITIATED,
		p2pPb.InvestmentTransactionStatus_IN_PROGRESS:
		investor, invErr := p.investorDao.GetById(ctx, txn.GetInvestorId())
		if invErr != nil {
			logger.Error("error in getting investor", zap.Error(invErr), zap.String(pkgLogger.P2P_INVESTOR_ID, txn.GetInvestorId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting investor: %v", invErr.Error()))
		}

		vgRes, vgErr := p.p2pVgClient.GetTransactionById(ctx, &p2pVgPb.GetTransactionByIdRequest{
			Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
			InvestorId: investor.GetVendorInvestorId(),
			RequestId:  txn.GetVendorRefId(),
		})
		if vgErr = epifigrpc.RPCError(vgRes, vgErr); vgErr != nil {
			_ = helper.UpdateFailureDebugReason(ctx, txn, p.investmentTransactionDao, fmt.Sprintf("error in getting txn from vendor: %v", vgErr.Error()))
			logger.Error("error in getting txn from vendor", zap.Error(vgErr), zap.String(pkgLogger.P2P_INVESTMENT_ID, req.GetTxnId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting txn from vendor: %v", vgErr.Error()))
		}
		_ = helper.ResetFailureDebugReason(ctx, txn, p.investmentTransactionDao)

		// TODO(sharath): update failure reason in db
		errorToReturn := p.updateWithdrawalTxnFromVendorData(ctx, vgRes, txn)
		if errorToReturn != nil {
			return nil, errorToReturn
		} else {
			return &activityPb.SettleWithdrawalResponse{}, nil
		}
	default:
		_ = helper.UpdateFailureDebugReason(ctx, txn, p.investmentTransactionDao, fmt.Sprintf("invalid txn status: %v", txn.GetStatus().String()))
		logger.Error(fmt.Sprintf("invalid txn status: %v", txn.GetStatus().String()), zap.String(pkgLogger.P2P_INVESTMENT_ID, req.GetTxnId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid txn status: %v", txn.GetStatus().String()))
	}
}

// nolint:funlen
func (p *Processor) updateWithdrawalTxnFromVendorData(ctx context.Context, vgRes *p2pVgPb.GetTransactionByIdResponse, txn *p2pPb.InvestmentTransaction) error {
	var errToReturn error
	var status p2pPb.InvestmentTransactionStatus
	var subStatus p2pPb.InvestmentTransactionSubStatus
	var updateMasks []p2pPb.InvestmentTransactionFieldMask
	switch vgRes.GetData().GetTransactionStatus() {
	case p2pVgPb.TransactionStatus_TRANSACTION_STATUS_PENDING,
		p2pVgPb.TransactionStatus_TRANSACTION_STATUS_PROGRESS,
		p2pVgPb.TransactionStatus_TRANSACTION_STATUS_APPROVED:
		// in some cases, withdrawal credit happens to user's account but the txn status in vendor API response is stale
		// if we were able to map a credit to this withdrawal txn, we can move it to success early
		if txn.GetPaymentRefId() != "" {
			status = p2pPb.InvestmentTransactionStatus_APPROVED
		} else {
			status = p2pPb.InvestmentTransactionStatus_IN_PROGRESS
			errToReturn = errors.Wrap(epifierrors.ErrTransient, "settlement in progress")
		}
	case p2pVgPb.TransactionStatus_TRANSACTION_STATUS_REJECTED:
		status = p2pPb.InvestmentTransactionStatus_FAILED
		subStatus = p2pPb.InvestmentTransactionSubStatus_FAILED_WITHDRAWAL
		errToReturn = errors.Wrap(epifierrors.ErrPermanent, "withdrawal failed")
	case p2pVgPb.TransactionStatus_TRANSACTION_STATUS_BANKING,
		p2pVgPb.TransactionStatus_TRANSACTION_STATUS_EXECUTED:
		status = p2pPb.InvestmentTransactionStatus_APPROVED
		subStatus = p2pPb.InvestmentTransactionSubStatus_APPROVED_WITHDRAWAL
		if vgRes.GetData().GetTimestampDetails() != nil && vgRes.GetData().GetTimestampDetails().GetExecutionDate() != nil {
			txn.Details.ExecutionTimestamp = vgRes.GetData().GetTimestampDetails().GetExecutionDate()
			updateMasks = append(updateMasks, p2pPb.InvestmentTransactionFieldMask_DETAILS)
		}
	default:
		errToReturn = errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unhandled vg txn status: %v", vgRes.GetData().GetTransactionStatus().String()))
	}
	_, upErr := p.updateTxnInDb(ctx, getExpectedStatusInDbForWithdrawal(status), status, subStatus, txn, updateMasks)
	if upErr != nil {
		temporalActivity.GetLogger(ctx).Error("error in updating txn", zap.Error(upErr), zap.String(pkgLogger.P2P_INVESTMENT_ID, txn.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, upErr.Error())
	}
	return errToReturn
}
