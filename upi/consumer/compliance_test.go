package consumer

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	upiConsumerPb "github.com/epifi/gamma/api/upi/consumer"
)

func TestService_ProcessOrderUpdateEventForCompliance(t *testing.T) {
	var (
		piIdFix = "pi-1"
	)
	type mocks struct {
		piClient *mockPi.MockPiClient
	}
	tests := []struct {
		name    string
		req     *orderPb.OrderUpdate
		setupFn func(m *mocks)
		want    *upiConsumerPb.ConsumerResponse
		wantErr error
	}{
		{
			name: "success updating capabilities",
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Transactions: []*paymentPb.Transaction{
						{
							Id:     "txn-1",
							Status: paymentPb.TransactionStatus_SUCCESS,
							PiFrom: piIdFix,
						},
					},
				},
			},
			setupFn: func(m *mocks) {
				m.piClient.EXPECT().
					GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: piIdFix}).
					Return(&piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id:                   piIdFix,
							Type:                 piPb.PaymentInstrumentType_UPI,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Capabilities: map[string]bool{
								piPb.Capability_INBOUND_TXN.String():  false,
								piPb.Capability_OUTBOUND_TXN.String(): true,
							},
						},
					}, nil)
				m.piClient.EXPECT().
					UpdatePi(gomock.Any(), &piPb.UpdatePiRequest{
						PaymentInstrument: &piPb.PaymentInstrument{
							Id:                   piIdFix,
							Type:                 piPb.PaymentInstrumentType_UPI,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Capabilities: map[string]bool{
								piPb.Capability_INBOUND_TXN.String():  true,
								piPb.Capability_OUTBOUND_TXN.String(): true,
							},
						},
						UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN},
						Source:          piPb.Source_SYSTEM,
						UpdateReason:    OC180CapabilityUpdate,
					}).
					Return(&piPb.UpdatePiResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			want: &upiConsumerPb.ConsumerResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
		{
			name: "pi not found",
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Transactions: []*paymentPb.Transaction{
						{
							Id:     "txn-1",
							Status: paymentPb.TransactionStatus_SUCCESS,
							PiFrom: piIdFix,
						},
					},
				},
			},
			setupFn: func(m *mocks) {
				m.piClient.EXPECT().GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: piIdFix}).Return(&piPb.GetPiByIdResponse{
					Status:            rpc.StatusRecordNotFound(),
					PaymentInstrument: nil,
				}, nil)
			},
			want: &upiConsumerPb.ConsumerResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
		},
		{
			name: "error updating pi",
			req: &orderPb.OrderUpdate{
				OrderWithTransactions: &orderPb.OrderWithTransactions{
					Transactions: []*paymentPb.Transaction{
						{
							Id:     "txn-1",
							Status: paymentPb.TransactionStatus_SUCCESS,
							PiFrom: piIdFix,
						},
					},
				},
			},
			setupFn: func(m *mocks) {
				m.piClient.EXPECT().
					GetPiById(gomock.Any(), &piPb.GetPiByIdRequest{Id: piIdFix}).
					Return(&piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id:                   piIdFix,
							Type:                 piPb.PaymentInstrumentType_UPI,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Capabilities: map[string]bool{
								piPb.Capability_INBOUND_TXN.String():  false,
								piPb.Capability_OUTBOUND_TXN.String(): true,
							},
						},
					}, nil)
				m.piClient.EXPECT().
					UpdatePi(gomock.Any(), &piPb.UpdatePiRequest{
						PaymentInstrument: &piPb.PaymentInstrument{
							Id:                   piIdFix,
							Type:                 piPb.PaymentInstrumentType_UPI,
							IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
							Capabilities: map[string]bool{
								piPb.Capability_INBOUND_TXN.String():  true,
								piPb.Capability_OUTBOUND_TXN.String(): true,
							},
						},
						UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_CAPABILITY_INBOUND_TXN},
						Source:          piPb.Source_SYSTEM,
						UpdateReason:    OC180CapabilityUpdate,
					}).
					Return(&piPb.UpdatePiResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want: &upiConsumerPb.ConsumerResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			piClient := mockPi.NewMockPiClient(ctrl)
			s := &Service{
				piClient: piClient,
			}
			m := &mocks{
				piClient: piClient,
			}
			if tt.setupFn != nil {
				tt.setupFn(m)
			}
			got, err := s.ProcessOrderUpdateEventForCompliance(context.Background(), tt.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("ProcessOrderUpdateEventForCompliance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && !errors.Is(err, tt.wantErr) {
				t.Errorf("ProcessOrderUpdateEventForCompliance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessOrderUpdateEventForCompliance() got = %v, want %v", got, tt.want)
			}
		})
	}
}
