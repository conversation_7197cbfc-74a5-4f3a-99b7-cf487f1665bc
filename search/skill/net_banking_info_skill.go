//nolint:dupl
package skill

import (
	"context"
	"sync"

	"github.com/epifi/gamma/api/search/actionbar"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/gamma/search/constant"
	dao2 "github.com/epifi/gamma/search/dao"

	"go.uber.org/zap"

	searchPb "github.com/epifi/gamma/api/search"
	skillPb "github.com/epifi/gamma/api/search/skill"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/search/common"
	dao "github.com/epifi/gamma/search/dao/query"
	"github.com/epifi/gamma/search/elasticsearch"
	"github.com/epifi/gamma/search/entity"
)

type NetBankingInfo struct {
	configFetcher      SkillConfigGet
	searchResultConfig *skillPb.SearchResultConfig
	faqSearcher        elasticsearch.FaqSearcherI
	txnSearcher        elasticsearch.TxnRepoSearcherI
	currActorID        string
}

func NewNetBankingInfo(configFetcher SkillConfigGet, faqSearcher elasticsearch.FaqSearcherI, txnSearcher elasticsearch.TxnRepoSearcherI, currActorID string) *NetBankingInfo {
	return &NetBankingInfo{configFetcher: configFetcher, faqSearcher: faqSearcher, txnSearcher: txnSearcher, currActorID: currActorID}
}

func (n *NetBankingInfo) AddConfigValues(ctx context.Context, config Config) error {
	var currSkillConfig *skillPb.SearchResultConfig
	var err error
	// fetch the config for skill
	if currSkillConfig, err = n.configFetcher.FetchSkillConfig(ctx, config.SkillName); err != nil {
		return err
	}
	n.searchResultConfig = currSkillConfig
	return nil
}

func (n *NetBankingInfo) AddEntityData(ctx context.Context) {
}

func (n *NetBankingInfo) GetFinancialActivity(ctx context.Context, getFinancialActivityRequest *GetFinancialActivityRequest) (*actionbar.QuickInfoResponse, error) {
	finActivity, err := common.FetchLastNTxn(ctx, n.currActorID, n.txnSearcher, &dao.TxnWithMultipleActorQueryParams{
		PayerActorId: n.currActorID,
		IsCredit:     true,
		IsDebit:      true,
		From:         getFinancialActivityRequest.Offset,
		EsRespSize:   getFinancialActivityRequest.Size,
	})
	if err != nil {
		return nil, err
	}
	return finActivity, nil
}

func (n *NetBankingInfo) BuildFiLiteSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	return nil, nil
}

//nolint:gosec
func (n *NetBankingInfo) BuildSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	var searchResultUnits = make([]*searchPb.SearchResultUnit, 5)
	var wg sync.WaitGroup
	// build different sections of search results
	if n.searchResultConfig.GetFiSummaryConfig().GetEnable() {
		searchResultUnits[0] = common.GetSearchResultUnitFromSummary(&actionbar.SummaryResponse{
			Summary: n.searchResultConfig.GetFiSummaryConfig().GetSummaryText(),
			TabName: constant.FiTabName,
		}, constant.FiTabName)
	}
	if n.searchResultConfig.GetFiQuickLinkConfig().GetEnable() {
		qlUnit := getQuickLinks(n.searchResultConfig.GetFiQuickLinkConfig().GetLinks())
		if qlUnit != nil {
			searchResultUnits[1] = qlUnit
		}
	}

	// this will fetch last 5 transactions for actor --> if more filters are required, add it in TxnWithMultipleActorQueryParams
	if n.searchResultConfig.GetFinActivityConfig().GetEnable() {
		wg.Add(1)
		goroutine.Run(ctx, dao2.GoroutineTimeoutDuration, func(ctx context.Context) {
			defer wg.Done()
			finActivity, err := n.GetFinancialActivity(ctx, &GetFinancialActivityRequest{
				Offset: 0,
				Size:   dao.DefaultFAItems,
			})
			if err != nil {
				logger.Error(ctx, "error while fetching financial activity", zap.Error(err))
				return
			}
			if finActivity != nil {
				searchResultUnits[2] = common.GetSearchResultUnitFromPastTxn(finActivity, constant.FiTabName)
			}
		})
	}
	if n.searchResultConfig.GetFaqsConfig().GetEnable() {
		wg.Add(1)
		goroutine.Run(ctx, dao2.GoroutineTimeoutDuration, func(ctx context.Context) {
			defer wg.Done()
			supportResp, err := getSupportResp(ctx, n.faqSearcher, n.searchResultConfig.GetFaqsConfig().GetSearchTexts())
			if err != nil {
				logger.Error(ctx, "error in getting support resp", zap.Error(err))
				return
			}
			if supportResp != nil {
				searchResultUnits[3] = common.GetSearchRUnitFromHelp(supportResp)
			}
		})
	}
	wg.Wait()
	// TODO: add support for ca and gmail related configs
	return common.ReorderSearchResultUnits(n.searchResultConfig.GetResponseTypeOrder(), searchResultUnits), nil
}
