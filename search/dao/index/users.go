package index

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"

	"github.com/epifi/be-common/pkg/logger"
)

type BaseUser struct {
	UserId          string                                  `json:"user_id,omitempty"`
	FullName        string                                  `json:"full_name,omitempty"`
	Pan             string                                  `json:"pan,omitempty"`
	Address         map[string]*postaladdress.PostalAddress `json:"address,omitempty"`
	PhoneNumber     *commontypes.PhoneNumber                `json:"phone_number,omitempty"`
	Email           string                                  `json:"email"`
	ProfileImageUrl string                                  `json:"profile_image_url"`
}

func (user *BaseUser) UnmarshalJSON(b []byte) error {
	var objMap map[string]interface{}
	err := json.Unmarshal(b, &objMap)
	if err != nil {
		return err
	}
	if len(objMap) == 0 {
		logger.InfoNoCtx("no userdetail are found")
		return nil
	}
	// es response comes as map[string]interface
	if _, ok := objMap["address"]; ok {
		if addressMap, ok1 := objMap["address"].(map[string]interface{}); ok1 {
			user.Address = map[string]*postaladdress.PostalAddress{}
			for each := range addressMap {
				user.Address[each] = &postaladdress.PostalAddress{}
				addressStr, _ := json.Marshal(addressMap[each])
				err = protojson.Unmarshal(addressStr, user.Address[each])
				if err != nil {
					logger.ErrorNoCtx("error ", zap.Error(err))
				}
			}
		}
	}
	if _, ok := objMap["phone_number"]; ok {
		phoneStr, _ := json.Marshal(objMap["phone_number"])
		user.PhoneNumber = &commontypes.PhoneNumber{}
		err = protojson.Unmarshal(phoneStr, user.PhoneNumber)
		if err != nil {
			logger.ErrorNoCtx("error ", zap.Error(err))
		}
	}
	user.FullName, _ = objMap["full_name"].(string)
	user.Pan, _ = objMap["pan"].(string)
	user.UserId, _ = objMap["user_id"].(string)
	return nil
}

func (user *BaseUser) GetPhoneNumberStr() string {
	if user.PhoneNumber == nil {
		return ""
	}
	// TODO(shubhra): add proper separator here in phone number
	return fmt.Sprintf("%d %d", user.GetPhoneNumber().GetCountryCode(), user.GetPhoneNumber().GetNationalNumber())
}

func (user *BaseUser) GetNationalNumberStr() string {
	if user.PhoneNumber == nil {
		return ""
	}
	return fmt.Sprintf("%d", user.PhoneNumber.GetNationalNumber())
}

func (user *BaseUser) GetFullName() string {
	if user != nil {
		return user.FullName
	}
	return ""
}

func (user *BaseUser) GetPhoneNumber() *commontypes.PhoneNumber {
	if user != nil {
		return user.PhoneNumber
	}
	return nil
}

func (user *BaseUser) GetProfileImageUrl() string {
	if user != nil {
		return user.ProfileImageUrl
	}
	return ""
}

func (user *BaseUser) GetUserID() string {
	if user != nil {
		return user.UserId
	}
	return ""
}
