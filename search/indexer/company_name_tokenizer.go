package indexer

import (
	"context"
	"regexp"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	daoIndex "github.com/epifi/gamma/search/dao/index"
)

var (
	// regex to capture text between round brackets
	textBetweenBracketRegex = regexp.MustCompile(`\(([^\[\]]*)\)`)
)

// implements Tokenizer interface
// has a stopwords field, which is constant list of stopwords taken from web
type CompanyNameTokenizer struct {
	StopWords        map[string]int
	CompanyStopWords map[string]int
}

func prepareCompanyStopWords() map[string]int {
	var STOP_WORDS = []string{"limited", "private", "llp", "ltd", "&", "pvt", "-", ")", "(", ".", ",", "+", "*", "@", "[", "]", ":", "}", "%"}
	STOP_WORDS_MAP := map[string]int{}

	for _, each := range STOP_WORDS {
		STOP_WORDS_MAP[each] = 1
	}
	return STOP_WORDS_MAP
}

// method to initialize BankAcTokenizer; uses PrepareStopWords to initiate stopwords
func NewCompanyNameTokenizer() *CompanyNameTokenizer {
	tokenizer := CompanyNameTokenizer{}
	tokenizer.StopWords = PrepareStopWords()
	tokenizer.CompanyStopWords = prepareCompanyStopWords()
	return &tokenizer
}

func (t CompanyNameTokenizer) Tokenize(ctx context.Context, text string) []string {
	var tokens []string
	for _, each := range strings.Split(text, " ") {
		tokens = append(tokens, strings.TrimSpace(each))
	}
	tokens = append(tokens, text)
	return tokens
}

func (t CompanyNameTokenizer) TokenizeCompanyName(ctx context.Context, companyName string, currCompanyName string) []string {
	origName := companyName
	noDotCompanyName := strings.ReplaceAll(companyName, ".", " ")
	noDotCompanyName = strings.Join(strings.Fields(strings.TrimSpace(noDotCompanyName)), " ")
	tokens := t.Tokenize(ctx, noDotCompanyName)
	tokens = t.removeCompanyStopWords(ctx, tokens)
	concatTokens := t.concatAdjacentSmallWords(ctx, tokens)
	tokens = append(tokens, concatTokens...)
	tokens = t.removeSingleLetterTokens(tokens)
	tokens = append(tokens, origName)
	tokens = append(tokens, t.getTextBetweenBrackets(companyName)...)
	tokens = t.getUniqueTokens(ctx, tokens)
	return tokens
}

func (t CompanyNameTokenizer) getTextBetweenBrackets(name string) []string {
	matches := textBetweenBracketRegex.FindAllString(name, -1)
	if len(matches) == 0 {
		return make([]string, 0)
	}
	tokens := make([]string, 0)
	for _, match := range matches {
		match = strings.Trim(match, "(")
		match = strings.Trim(match, ")")
		tokens = append(tokens, match)
	}
	return tokens
}

func (t CompanyNameTokenizer) removeSingleLetterTokens(tokens []string) []string {
	newTokensArr := make([]string, 0)
	for _, token := range tokens {
		if len(token) > 1 {
			newTokensArr = append(newTokensArr, token)
		}
	}
	return newTokensArr
}

func (t CompanyNameTokenizer) getUniqueTokens(_ context.Context, tokens []string) []string {
	tokensSet := map[string]bool{}
	for idx := range tokens {
		tokensSet[tokens[idx]] = true
	}
	var uniqueTokens []string
	for k := range tokensSet {
		if k == "" {
			continue
		}
		uniqueTokens = append(uniqueTokens, k)
	}
	return uniqueTokens
}

// func (t CompanyNameTokenizer) handleDots(_ context.Context, tokens []string) ([]string, []string) {
//	// original token list with some tokens modified
//	modifiedTokens := make([]string, 0)
//	// additional tokens that we want to add to the token list after all operations on the list are over.
//	additionalTokens := make([]string, 0)
//	for _, token := range tokens {
//		splitArr := strings.Split(token, ".")
//		if len(splitArr) == 1 {
//			modifiedTokens = append(modifiedTokens, token)
//			continue
//		}
//		additionalTokens = append(additionalTokens, token, strings.Join(splitArr, " "), strings.Join(splitArr, ""))
//		modifiedTokens = append(modifiedTokens, splitArr...)
//	}
//	return modifiedTokens, additionalTokens
// }

func (t CompanyNameTokenizer) removeCompanyStopWords(_ context.Context, tokens []string) []string {
	filteredTokens := make([]string, 0)
	// remove commonly used words in company names from tokens.
	for _, token := range tokens {
		if _, ok := t.CompanyStopWords[strings.ToLower(token)]; ok {
			continue
		}
		filteredTokens = append(filteredTokens, token)
	}
	return filteredTokens
}

func (t CompanyNameTokenizer) concatAdjacentSmallWords(_ context.Context, tokens []string) []string {
	tokenOutput := make([]string, 0)
	end := 0
	for start := 0; start < len(tokens); start = end + 1 {
		for end = start; end < len(tokens); end++ {
			if len(tokens[end]) > 2 {
				break
			}
		}
		numWords := end - start + 1
		if end == len(tokens) {
			end--
		}
		if numWords > 1 {
			tokenOutput = append(tokenOutput, strings.Join(tokens[start:end], "")+" "+tokens[end], strings.Join(tokens[start:end+1], " "))
		}
	}
	return tokenOutput
}

// for BankAcTokenizer only `branch` field tokenizer is defined.
// define different tokenizer here if need to tokenize differently
func (t CompanyNameTokenizer) TokenizeField(ctx context.Context, field string, allField interface{}) []string {
	companyData, ok := allField.(*daoIndex.CompanyName)
	if !ok {
		logger.Error(ctx, "type cast failed from interface to BankAc")
		return nil
	}
	switch field {
	case "company_name":
		return t.TokenizeCompanyName(ctx, companyData.CompanyName, companyData.CurrentCompanyName)
	default:
		logger.Error(ctx, "field tokenization not supported for", zap.String("field", field))
		return nil
	}
}

func (t CompanyNameTokenizer) GetSuggestInput(ctx context.Context, allFields interface{}) ([]*daoIndex.SuggestInput, error) {
	panic("implement-me")
}
