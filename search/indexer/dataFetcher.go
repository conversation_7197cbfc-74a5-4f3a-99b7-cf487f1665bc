package indexer

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"io/ioutil"
	"log"
	"math/rand"
	"path/filepath"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"github.com/google/wire"
	"github.com/opensearch-project/opensearch-go/v2/opensearchutil"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	daoIndex "github.com/epifi/gamma/search/dao/index"
	glos "github.com/epifi/gamma/search/dao/index"
	"github.com/epifi/gamma/search/fileUtils"
)

var bankToAcronym = map[string]string{"abu dhabi commercial bank": "ACB",
	"american express":                            "AE",
	"andhra pradesh grameena vikas bank":          "APGUB",
	"asian development bank":                      "ADB",
	"bank of america":                             "BOA",
	"bank of bahrain and kuwait":                  "BBK",
	"bank of baroda":                              "BOB",
	"bank of ceylon":                              "BOC",
	"bank of india":                               "BOI",
	"bank of maharashtra":                         "BOM",
	"bank of rajasthan":                           "BOR",
	"bharatiya mahila bank":                       "BMB",
	"catholic syrian bank":                        "CSB",
	"central bank of india":                       "CBI",
	"city union bank":                             "CUB",
	"deutsche bank":                               "DB",
	"export-import bank":                          "EXIM",
	"hong kong and shanghai banking corporation":  "HSBC",
	"housing development and finance corporation": "HDFC",
	"indian overseas bank":                        "IOB",
	"industrial credit and investment corporation of india limited": "ICICI",
	"industrial development bank of india":                          "IDBI",
	"industrial investment bank of india":                           "IIBI",
	"ing vysya bank":                                                "IVB",
	"karur vysya bank":                                              "KVB",
	"lakshmi vilas bank":                                            "LVB",
	"national bank for agriculture and rural development":           "NABARD",
	"national housing bank":                                         "NHB",
	"oriental bank of commerce":                                     "OBC",
	"punjab & sind bank":                                            "PSB",
	"punjab national bank":                                          "PNB",
	"reserve bank of india":                                         "RBI",
	"royal bank of scotland":                                        "RBS",
	"shamrao vitthal co-operative bank":                             "SVC",
	"small industries development bank of india":                    "SIDBI",
	"south indian bank":                                             "SIB",
	"standard chartered bank":                                       "SCB",
	"state bank of india":                                           "SBI",
	"tamilnad mercantile bank":                                      "TMB",
	"telangana grameena bank":                                       "TGB",
	"telangana state cooperative apex bank ltd":                     "TSCAB",
	"the development bank of singapore":                             "DBS",
	"union bank of india":                                           "UOB",
	"united bank of india":                                          "UBI",
	"united commercial bank":                                        "UCO"}

func getBankAcronym(bankName string) string {
	if acr, ok := bankToAcronym[strings.ToLower(bankName)]; ok {
		// logger.InfoNoCtx("acronym found", zap.String("bank-name", bankName))
		return acr
	}
	return ""
}

var FileDataFetcherWithoutPathsWireSet = wire.NewSet(NewFileDataFetcherWithoutPaths, wire.Bind(new(DataFetcher), new(*FileDataFetcher)))

/*
interface for fetching data to index
wraps methods:
GetBulkData : given indexname return data to be indexed as `[]opensearchutil.BulkIndexItem`
// TODO(shubhra): change this interface to define GetBulkData method for each index, instead of keeping it generic
*/
//go:generate mockgen -source=dataFetcher.go -destination=./mocks/mock_dataFetcher.go -package=indexer
type DataFetcher interface {
	GetBulkData(ctx context.Context, indexName string) ([]*opensearchutil.BulkIndexerItem, error)
	GetBankSuggestInput(ctx context.Context, bank *daoIndex.BankAc) []daoIndex.SuggestInput
	GetBanksBulkData(ctx context.Context, bankList []*daoIndex.BankAc) []*opensearchutil.BulkIndexerItem
	GetDeeplinksBulkData(ctx context.Context, data []*daoIndex.DeeplinkKeyword) ([]*opensearchutil.BulkIndexerItem, error)
	GetCompanyNameBulkData(ctx context.Context, mcaCompanyNameList []*daoIndex.CompanyName) []*opensearchutil.BulkIndexerItem
	GetCompanyNameSuggestInput(ctx context.Context, bank *daoIndex.CompanyName) []daoIndex.SuggestInput
	GetGlossaryBulkData(ctx context.Context, glossaryData []*glos.Glossary) []*opensearchutil.BulkIndexerItem
	GetSuggestionQueryBulkData(ctx context.Context, SuggestionData []*glos.Suggestion) []*opensearchutil.BulkIndexerItem
	GetEmployersBulkData(ctx context.Context, employers []*daoIndex.Employer) []*opensearchutil.BulkIndexerItem
	GetTxnOntologiesBulkData(ctx context.Context, suggestionData []*glos.TransactionOntologies) []*opensearchutil.BulkIndexerItem
}

/*
implements `DataFetcher` interface.
`FileDataFetcher` fetches data from file
file paths for different indexes are stored as field
*/
type FileDataFetcher struct {
	BankDataPath         string
	TxnDataPath          string
	EmbeddingPath        string
	TimeLinePath         string
	InstruemntsPath      string
	DeeplinksPath        string
	FaqsPath             string
	GmailInsightsPath    string
	BankDataTokenizer    Tokenizer
	TxnDataTokenizer     Tokenizer
	ConnectionsTokenizer Tokenizer
	CompanyNameTokenizer Tokenizer
	EmployerTokenizer    Tokenizer
}

/*
initializes `FileDataFetcher` given `bankDataPath` for banks_ac index and `txnDataPath` for transactions index
*/
func NewFileDataFetcher(bankDataPath, txnDataPath, embeddingsPath, timelinePath, instrumentsPath, deeplinkPath, faqsPath, gmailInsightsPath string) *FileDataFetcher {
	bankTokenizer := NewBankAcTokenizer()
	companyNameTokenizer := NewCompanyNameTokenizer()
	connectionTokenizer := NewConnectionsAcTokenizer()
	employerTokenizer := NewEmployerTokenizer()
	return &FileDataFetcher{
		BankDataPath:         bankDataPath,
		TxnDataPath:          txnDataPath,
		TimeLinePath:         timelinePath,
		EmbeddingPath:        embeddingsPath,
		InstruemntsPath:      instrumentsPath,
		DeeplinksPath:        deeplinkPath,
		FaqsPath:             faqsPath,
		GmailInsightsPath:    gmailInsightsPath,
		BankDataTokenizer:    bankTokenizer,
		ConnectionsTokenizer: connectionTokenizer,
		CompanyNameTokenizer: companyNameTokenizer,
		EmployerTokenizer:    employerTokenizer,
	}
}

func NewFileDataFetcherWithoutPaths() *FileDataFetcher {
	bankTokenizer := NewBankAcTokenizer()
	companyNameTokenizer := NewCompanyNameTokenizer()
	connectionTokenizer := NewConnectionsAcTokenizer()
	employerTokenizer := NewEmployerTokenizer()
	return &FileDataFetcher{
		BankDataTokenizer:    bankTokenizer,
		ConnectionsTokenizer: connectionTokenizer,
		CompanyNameTokenizer: companyNameTokenizer,
		EmployerTokenizer:    employerTokenizer,
	}
}

// maps indexname to corresponding data fetch function
func (fetcher *FileDataFetcher) GetBulkData(ctx context.Context, indexName string) ([]*opensearchutil.BulkIndexerItem, error) {
	switch indexName {
	case INDEX_TRANSACTIONS:
		return fetcher.GetTxnBulkData(ctx)
	case INDEX_GMAIL_TRANSACTIONS:
		return fetcher.GetGmailTxnBulkData(ctx)
	case INDEX_BANKS_AC:
		return fetcher.GetBanksAcData(ctx)
	case INDEX_EMBEDDING:
		return fetcher.GetEmbeddingBulkData(ctx)
	case INDEX_TIMELINE:
		return fetcher.GetTimeLineBulkData(ctx)
	case INDEX_INSTRUMENTS:
		return fetcher.GetInstrumentsBulkData(ctx)
	case INDEX_DEEPLINKS:
		data, err := fetcher.readDeepLinkDataFromDump(ctx)
		if err != nil {
			return nil, err
		}
		return fetcher.GetDeeplinksBulkData(ctx, data)
	case INDEX_FAQS:
		return fetcher.GetFaqsBulkData(ctx)
	default:
		return nil, fmt.Errorf("index %s not supported", indexName)
	}
}

//nolint:dupl
func (fetcher *FileDataFetcher) GetTxnBulkData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	rand.Seed(time.Now().Unix())
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	type transactionList struct {
		Transactions []*daoIndex.Transaction `json:"transactions"`
	}
	var data transactionList
	err := fetcher.convertDbDumpToList(ctx, fetcher.TxnDataPath, &data)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "number of transactions", zap.Int("#transactions", len(data.Transactions)))
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data.Transactions {
		curr := data.Transactions[idx]
		trans, err := json.Marshal(&curr)
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, fetcher.getIndexItem(ctx, countSuccessful, curr.ID, trans))
	}
	return bulkIndexItems, nil
}

//nolint:dupl
func (fetcher *FileDataFetcher) GetGmailTxnBulkData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	rand.Seed(time.Now().Unix())
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	type transactionList struct {
		Transactions []*daoIndex.GmailTransaction `json:"gmail_insights"`
	}
	var data transactionList
	err := fetcher.convertDbDumpToList(ctx, fetcher.GmailInsightsPath, &data)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "number of gmail-transactions", zap.Int("#transactions", len(data.Transactions)))
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data.Transactions {
		curr := data.Transactions[idx]
		trans, err := json.Marshal(&curr)
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, fetcher.getIndexItem(ctx, countSuccessful, curr.ID, trans))
	}
	return bulkIndexItems, nil
}

//nolint:funlen,dupl
func (fetcher *FileDataFetcher) GetBanksAcData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	type bankACList struct {
		Banks []daoIndex.BankAc `json:"banks"`
	}
	var data bankACList
	file, err := ioutil.ReadFile(fetcher.BankDataPath)
	if err != nil {
		logger.Error(ctx, "error in reading file", zap.Error(err))
		return nil, err
	}
	if err := json.Unmarshal(file, &data); err != nil {
		logger.Error(ctx, "error in json unmarshal", zap.Error(err))
		return nil, err
	}
	var banks []*daoIndex.BankAc
	for idx := range data.Banks {
		banks = append(banks, &data.Banks[idx])
	}
	return fetcher.GetBanksBulkData(ctx, banks), nil
}

//nolint:dupl
func (fetcher *FileDataFetcher) GetBanksBulkData(ctx context.Context, bankList []*daoIndex.BankAc) []*opensearchutil.BulkIndexerItem {
	for i := range bankList {
		s := bankList[i]
		bankList[i].Suggest = fetcher.GetBankSuggestInput(ctx, s)
	}
	logger.Info(ctx, "number of banks", zap.Int("#banks", len(bankList)))

	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range bankList {
		curr := bankList[idx]
		trans, err := json.Marshal(curr)
		if err != nil {
			logger.ErrorNoCtx("error in encoding data", zap.Error(err))
			continue
		}
		bulkIndexItems = append(bulkIndexItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			DocumentID:      curr.Ifsc,
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					logger.ErrorNoCtx("error indexing", zap.Error(err))
				} else {
					logger.ErrorNoCtx("error indexing", zap.String("error-type", res.Error.Type), zap.String("error-reason", res.Error.Reason))
				}
			},
		})
	}
	return bulkIndexItems
}

func (fetcher *FileDataFetcher) GetBankSuggestInput(ctx context.Context, bank *daoIndex.BankAc) []daoIndex.SuggestInput {
	tokenizedName := fetcher.BankDataTokenizer.Tokenize(ctx, bank.Name)
	if v := getBankAcronym(bank.Name); len(v) != 0 {
		tokenizedName = append(tokenizedName, v)
	}
	suggestInp := []daoIndex.SuggestInput{{
		Input:    fetcher.BankDataTokenizer.TokenizeField(ctx, "branch", bank),
		Weight:   10,
		Contexts: map[string][]string{"field_type": {"branch"}},
	}, {
		Input:    fetcher.BankDataTokenizer.Tokenize(ctx, bank.Ifsc),
		Weight:   10,
		Contexts: map[string][]string{"field_type": {"ifsc"}},
	}, {
		Input:    tokenizedName,
		Weight:   10,
		Contexts: map[string][]string{"field_type": {"name"}},
	}, {
		Input:    fetcher.BankDataTokenizer.Tokenize(ctx, bank.District),
		Weight:   10,
		Contexts: map[string][]string{"field_type": {"district"}},
	}, {
		Input:    fetcher.BankDataTokenizer.Tokenize(ctx, bank.City),
		Weight:   10,
		Contexts: map[string][]string{"field_type": {"city"}},
	}}
	return suggestInp
}

// nolint:dupl
func (fetcher *FileDataFetcher) GetCompanyNameBulkData(ctx context.Context, companyList []*daoIndex.CompanyName) []*opensearchutil.BulkIndexerItem {
	for _, company := range companyList {
		company.Suggest = fetcher.GetCompanyNameSuggestInput(ctx, company)
	}
	logger.Info(ctx, "number of company", zap.Int("#banks", len(companyList)))
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for _, curr := range companyList {
		curr.Id = uuid.New().String()
		trans, err := json.Marshal(curr)
		if err != nil {
			logger.ErrorNoCtx("error in encoding data", zap.Error(err))
			continue
		}
		bulkIndexItems = append(bulkIndexItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			DocumentID:      curr.Id,
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					logger.ErrorNoCtx("error indexing", zap.Error(err))
				} else {
					logger.ErrorNoCtx("error indexing", zap.String("error-type", res.Error.Type), zap.String("error-reason", res.Error.Reason))
				}
			},
		})
	}
	return bulkIndexItems
}

func (fetcher *FileDataFetcher) GetCompanyNameSuggestInput(ctx context.Context, companyNames *daoIndex.CompanyName) []daoIndex.SuggestInput {
	suggestInp := []daoIndex.SuggestInput{{
		Input:    fetcher.CompanyNameTokenizer.TokenizeField(ctx, "company_name", companyNames),
		Contexts: map[string][]string{"field_type": {"name"}},
	}}
	return suggestInp
}

//nolint:funlen,dupl
func (fetcher *FileDataFetcher) GetEmbeddingBulkData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	type embeddingList struct {
		embeddings []daoIndex.Embedding
	}
	var data embeddingList

	// channel to read file lines
	fileLineChannel := make(chan string)
	// start go routine to read lines
	startTime := time.Now()
	go fileUtils.ReadFileLines(fetcher.EmbeddingPath, fileLineChannel)
	logger.Info(ctx, "loading word vectors")
	// wrapped function to let `defer func` work for calcultaing time in loading word vectors, as this is a big set of words
	func(startTime time.Time) {
		// calculate time lapsed with defer statement
		defer func(startTime time.Time) {
			logger.Info(ctx, "time in loading word vectors", zap.Any("time lapsed", time.Since(startTime)))
		}(startTime)
		// for loop for reading on channel
		for line := range fileLineChannel {
			splitted := strings.Split(line, " ")
			word := splitted[0]
			tokens := splitted[1:]
			embed := daoIndex.Embedding{
				Name: word,
			}
			for idx := range tokens {
				if f, err := strconv.ParseFloat(tokens[idx], 64); err == nil {
					embed.Vector = append(embed.Vector, f)
				} else {
					logger.Error(ctx, "error in str to float", zap.Error(err))
				}
			}
			data.embeddings = append(data.embeddings, embed)
		}
	}(startTime)

	logger.Info(ctx, "number of words", zap.Int("#banks", len(data.embeddings)))

	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data.embeddings {
		curr := data.embeddings[idx]
		trans, err := json.Marshal(curr)
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
				log.Printf("success")
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					log.Printf("error: %s", err)
				} else {
					log.Printf("error: %s %s", res.Error.Type, res.Error.Reason)
				}
			},
		})
	}
	return bulkIndexItems, nil
}

//nolint:dupl,novet
func (fetcher *FileDataFetcher) GetTimeLineBulkData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	type timeLineList struct {
		Timelines []daoIndex.TimeLine `json:"timelines"`
	}
	var data timeLineList
	err := fetcher.convertDbDumpToList(ctx, fetcher.TimeLinePath, &data)
	if err != nil {
		return nil, err
	}
	for i := range data.Timelines {
		data.Timelines[i].Suggest, err = fetcher.ConnectionsTokenizer.GetSuggestInput(ctx, data.Timelines[i])
		if err != nil {
			logger.Error(ctx, "error in getting suggest input", zap.String("error", err.Error()))
		}
	}
	logger.Info(ctx, "number of timelines", zap.Int("#timelines", len(data.Timelines)))
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data.Timelines {
		trans, err := json.Marshal(data.Timelines[idx])
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, fetcher.getIndexItem(ctx, countSuccessful, data.Timelines[idx].TimeLineId, trans))
	}
	return bulkIndexItems, nil
}

func formatTime(t string) (string, error) {
	if t == "" {
		return "", nil
	}
	x := strings.Split(t, ".")[0]
	parsedTime, err := time.Parse(daoIndex.TimeFormat, x)
	if err != nil {
		logger.ErrorNoCtx("error in time parseing- created-at", zap.Error(err))
		return "", err
	}
	return parsedTime.Format(daoIndex.TimeFormat), nil
}

func (fetcher *FileDataFetcher) GetFaqsBulkData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	type faqsList struct {
		Faqs []daoIndex.FaqRecord `json:"faqs"`
	}
	var data faqsList
	err := fetcher.convertDbDumpToList(ctx, fetcher.FaqsPath, &data)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "number of faq records", zap.Int("#faqs", len(data.Faqs)))
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data.Faqs {
		trans, err := json.Marshal(data.Faqs[idx])
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, fetcher.getIndexItem(ctx, countSuccessful, fmt.Sprintf("%s", data.Faqs[idx].ArticleId), trans))
	}
	return bulkIndexItems, nil
}

func (fetcher *FileDataFetcher) readDeepLinkDataFromDump(ctx context.Context) ([]*daoIndex.DeeplinkKeyword, error) {
	type deeplinkList struct {
		Deeplinks []daoIndex.DeeplinkKeyword `json:"deeplinks"`
	}
	var data deeplinkList
	err := fetcher.convertDbDumpToList(ctx, fetcher.DeeplinksPath, &data)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "number of deeplinks", zap.Int("#deeplinks", len(data.Deeplinks)))
	var deeplinks []*daoIndex.DeeplinkKeyword
	for idx := range data.Deeplinks {
		deeplinks = append(deeplinks, &data.Deeplinks[idx])
	}
	return deeplinks, nil
}

func (fetcher *FileDataFetcher) GetDeeplinksBulkData(ctx context.Context, data []*daoIndex.DeeplinkKeyword) ([]*opensearchutil.BulkIndexerItem, error) {
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data {
		trans, err := json.Marshal(data[idx])
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, fetcher.getIndexItem(ctx, countSuccessful, "", trans))
	}
	return bulkIndexItems, nil
}

//nolint:dupl,novet
func (fetcher *FileDataFetcher) GetInstrumentsBulkData(ctx context.Context) ([]*opensearchutil.BulkIndexerItem, error) {
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	type instrumentList struct {
		Instruments []daoIndex.Instrument `json:"instruments"`
	}
	var data instrumentList
	err := fetcher.convertDbDumpToList(ctx, fetcher.InstruemntsPath, &data)
	if err != nil {
		return nil, err
	}
	logger.Info(ctx, "number of instruments", zap.Int("#instruments", len(data.Instruments)))
	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for idx := range data.Instruments {
		if ft, err := formatTime(data.Instruments[idx].CreatedAt); err != nil {
			logger.Error(ctx, "error in time parsing- created-at", zap.Error(err))
			continue
		} else {
			data.Instruments[idx].CreatedAt = ft
		}
		if ft, err := formatTime(data.Instruments[idx].UpdatedAt); err != nil {
			logger.Error(ctx, "error in time parsing- updated-at", zap.Error(err))
			continue
		} else {
			data.Instruments[idx].UpdatedAt = ft
		}
		if ft, err := formatTime(data.Instruments[idx].DeletedAt); err != nil {
			logger.Error(ctx, "error in time parsing- updated-at", zap.Error(err))
			continue
		} else {
			data.Instruments[idx].DeletedAt = ft
		}
		trans, err := json.Marshal(data.Instruments[idx])
		if err != nil {
			log.Printf("error in encoding data")
			continue
		}
		bulkIndexItems = append(bulkIndexItems, fetcher.getIndexItem(ctx, countSuccessful, data.Instruments[idx].PiId, trans))
	}
	return bulkIndexItems, nil
}

//nolint:dupl,unparam
func (fetcher *FileDataFetcher) getIndexItem(ctx context.Context, countSuccessful uint64, docId string, data []byte) *opensearchutil.BulkIndexerItem {
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	indexItem := &opensearchutil.BulkIndexerItem{
		Action:          "index",
		Body:            bytes.NewReader(data),
		RetryOnConflict: nil,
		OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
			atomic.AddUint64(&countSuccessful, 1)
			log.Printf("success")
		},
		OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
			if err != nil {
				log.Printf("error: %s", err)
			} else {
				log.Printf("error: %s %s", res.Error.Type, res.Error.Reason)
			}
		},
	}
	if docId != "" {
		indexItem.DocumentID = docId
	}
	return indexItem
}

func (fetcher *FileDataFetcher) convertDbDumpToList(ctx context.Context, dataFilePath string, data interface{}) error {
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	file, err := ioutil.ReadFile(filepath.Clean(dataFilePath))
	if err != nil {
		logger.Error(ctx, "error in reading file", zap.Error(err))
		return err
	}
	if err := json.Unmarshal(file, data); err != nil {
		logger.Error(ctx, "error in json unmarshal", zap.Error(err))
		return err
	}
	return nil
}

func (fetcher *FileDataFetcher) GetGlossaryBulkData(ctx context.Context, glossaryData []*glos.Glossary) []*opensearchutil.BulkIndexerItem {

	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for _, curr := range glossaryData {
		curr.Status = true
		trans, err := json.Marshal(curr)
		if err != nil {
			logger.ErrorNoCtx("error in encoding data", zap.Error(err))
			continue
		}
		bulkIndexItems = append(bulkIndexItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			DocumentID:      uuid.New().String(),
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					logger.ErrorNoCtx("error indexing", zap.Error(err))
				} else {
					logger.ErrorNoCtx("error indexing", zap.String("error-type", res.Error.Type), zap.String("error-reason", res.Error.Reason))
				}
			},
		})
	}
	return bulkIndexItems
}

func DocumentIdHash(s string) uint32 {
	h := fnv.New32a()
	_, err := h.Write([]byte(s))
	if err != nil {
		logger.ErrorNoCtx("error in hashing string")
	}
	return h.Sum32()
}

func (fetcher *FileDataFetcher) GetSuggestionQueryBulkData(ctx context.Context, suggestionData []*glos.Suggestion) []*opensearchutil.BulkIndexerItem {

	var bulkItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for _, curr := range suggestionData {
		docId := int(DocumentIdHash(curr.Query))
		curr.Status = true
		trans, err := json.Marshal(curr)
		if err != nil {
			logger.ErrorNoCtx("error in encoding data", zap.Error(err))
			continue
		}
		bulkItems = append(bulkItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			DocumentID:      strconv.Itoa(docId),
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					logger.ErrorNoCtx("error indexing suggestions", zap.Error(err))
				} else {
					logger.ErrorNoCtx("error indexing", zap.String("error-reason", res.Error.Reason))
				}
			},
		})
	}
	return bulkItems
}

// nolint: dupl
func (fetcher *FileDataFetcher) GetEmployersBulkData(ctx context.Context, employers []*daoIndex.Employer) []*opensearchutil.BulkIndexerItem {

	for _, employer := range employers {
		employer.Suggest = fetcher.GetEmployerSuggestInput(ctx, employer)
	}

	var bulkIndexItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for _, employer := range employers {
		trans, err := json.Marshal(employer)
		if err != nil {
			logger.ErrorNoCtx("error in encoding data", zap.Error(err))
			continue
		}
		bulkIndexItems = append(bulkIndexItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			DocumentID:      employer.Id,
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					logger.ErrorNoCtx("error indexing", zap.Error(err))
				} else {
					logger.ErrorNoCtx("error indexing", zap.String("error-type", res.Error.Type), zap.String("error-reason", res.Error.Reason))
				}
			},
		})
	}
	return bulkIndexItems
}

func (fetcher *FileDataFetcher) GetEmployerSuggestInput(ctx context.Context, employer *daoIndex.Employer) []daoIndex.SuggestInput {
	var (
		inputs = []string{}
	)

	if employer.NameBySource != "" {
		inputs = append(inputs, employer.NameBySource)
	}
	if employer.TradeName != "" {
		inputs = append(inputs, employer.TradeName)
	}

	suggestInp := []daoIndex.SuggestInput{{
		Input:    inputs,
		Contexts: map[string][]string{"source_and_constitution": employer.SourceAndConstitution},
	}}
	return suggestInp
}

func (fetcher *FileDataFetcher) GetTxnOntologiesBulkData(ctx context.Context, suggestionData []*glos.TransactionOntologies) []*opensearchutil.BulkIndexerItem {

	var bulkItems []*opensearchutil.BulkIndexerItem
	var countSuccessful uint64
	for _, curr := range suggestionData {
		docId := curr.OntologyId
		trans, err := json.Marshal(curr)
		if err != nil {
			logger.ErrorNoCtx("error in encoding data", zap.Error(err))
			continue
		}
		bulkItems = append(bulkItems, &opensearchutil.BulkIndexerItem{
			Action:          "index",
			DocumentID:      docId,
			Body:            bytes.NewReader(trans),
			RetryOnConflict: nil,
			OnSuccess: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem) {
				atomic.AddUint64(&countSuccessful, 1)
			},
			OnFailure: func(ctx context.Context, item opensearchutil.BulkIndexerItem, res opensearchutil.BulkIndexerResponseItem, err error) {
				if err != nil {
					logger.ErrorNoCtx("error indexing txn ontologies data", zap.Error(err))
				} else {
					logger.ErrorNoCtx("error indexing", zap.String("error-reason", res.Error.Reason))
				}
			},
		})
	}
	return bulkItems
}
