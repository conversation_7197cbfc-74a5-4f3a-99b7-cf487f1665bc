package related_queries_criteria

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/jinzhu/now"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/gamma/search/constant"

	"github.com/epifi/gamma/api/connected_account/external"
	metaPb "github.com/epifi/gamma/api/frontend/search/meta"
	"github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/search/dao"
	daoIndex "github.com/epifi/gamma/search/dao/index"
	"github.com/epifi/gamma/search/dao/parser"
	dao_query "github.com/epifi/gamma/search/dao/query"
	"github.com/epifi/gamma/search/elasticsearch"
	"github.com/epifi/gamma/search/entity"
)

type CategoryCriteria struct {
	txnOntologiesSearcher elasticsearch.TxnOntologiesIndexSearcherI
	txnSearcher           elasticsearch.TxnRepoSearcherI
	aaSearcher            elasticsearch.AATxnSearcherI
}

func NewCategoryCriteria(txnOntologiesSearcher elasticsearch.TxnOntologiesIndexSearcherI, txnSearcher elasticsearch.TxnRepoSearcherI, aaSearcher elasticsearch.AATxnSearcherI) *CategoryCriteria {
	return &CategoryCriteria{
		txnOntologiesSearcher: txnOntologiesSearcher,
		txnSearcher:           txnSearcher,
		aaSearcher:            aaSearcher,
	}
}

func (t *CategoryCriteria) GetRelatedQueryByCriteria(ctx context.Context, request *GetRelatedQueryByCriteriaRequest) ([]*RelatedQueriesTab, error) {
	var (
		relatedQueries []*RelatedQueriesTab
		err            error
		txnDirection   string
	)
	if len(request.NluResponse.GetEntities().GetTxnDirection()) > 0 {
		txnDirection = request.NluResponse.GetEntities().GetTxnDirection()[0]
	}
	if len(request.ConnectedActorsId) > 0 {
		relatedQueries, err = t.computeCategoryRelatedQueriesFromActor(ctx, request.NluResponse.GetQueryIntent(), txnDirection, request.ActorOntologyIdsThisYear, request.ActorOntologyIdsLastYear)
		if err != nil {
			return nil, err
		}
	} else if len(request.OntologyIds) > 0 {
		l1FilterOntologyIds, l2FilterOntologyIds, err := t.getOntologyFilteredOntologyIds(ctx, request.OntologyIds)
		if err != nil {
			return nil, err
		}
		relatedQueries, err = t.computeCategoryRelatedQueriesFromCategory(ctx, request.OntologyIds, request.UserActorId, request.NluResponse.GetQueryIntent(), txnDirection, request.NluResponse.GetEntities().GetDisplayCategory()[0], l1FilterOntologyIds, l2FilterOntologyIds)
		if err != nil {
			return nil, err
		}
	}
	return relatedQueries, nil
}

func (t *CategoryCriteria) GetRelatedQueriesByCriteriaForCa(ctx context.Context, request *GetRelatedQueryByCriteriaRequest) ([]*RelatedQueriesTab, error) {
	var (
		relatedQueries []*RelatedQueriesTab
		err            error
		txnDirection   string
	)
	if len(request.NluResponse.GetEntities().GetTxnDirection()) > 0 {
		txnDirection = request.NluResponse.GetEntities().GetTxnDirection()[0]
	}
	if len(request.ConnectedActorsId) > 0 {
		relatedQueries, err = t.computeCategoryRelatedQueriesFromActorForCa(ctx, request.NluResponse.GetQueryIntent(), txnDirection, request.ActorOntologyIdsThisYearForCa, request.ActorOntologyIdsLastYearForCa, request.AaFipToDepositAccount)
		if err != nil {
			return nil, err
		}
	} else if len(request.OntologyIds) > 0 {
		l1FilterOntologyIds, l2FilterOntologyIds, err := t.getOntologyFilteredOntologyIds(ctx, request.OntologyIds)
		if err != nil {
			return nil, err
		}
		relatedQueries, err = t.computeCategoryRelatedQueriesFromCategoryForCa(ctx, request.UserActorId, request.NluResponse.GetQueryIntent(), txnDirection, request.NluResponse.GetEntities().GetDisplayCategory()[0], l1FilterOntologyIds, l2FilterOntologyIds, request.AaFipToDepositAccount)
		if err != nil {
			return nil, err
		}
	}
	return relatedQueries, nil
}

func (t *CategoryCriteria) computeCategoryRelatedQueriesFromActor(ctx context.Context, intent string, txnDirection string, actorOntologyIdsThisYear []string, actorOntologyIdsLastYear []string) ([]*RelatedQueriesTab, error) {
	var (
		relatedQueries                                       []*QueryDetail
		wg                                                   sync.WaitGroup
		displayCategoriesThisYear, displayCategoriesLastYear []string
		thisYearError, lastYearError                         error
	)
	uniqueCategoryL1 := make(map[string]bool)
	uniqueCategoryL2 := make(map[string]bool)
	wg.Add(2)
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		defer wg.Done()
		displayCategoriesThisYear, thisYearError = t.txnOntologiesSearcher.GetDisplayCategoryFromOntologyIds(ctx, actorOntologyIdsThisYear)
	})
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		defer wg.Done()
		displayCategoriesLastYear, lastYearError = t.txnOntologiesSearcher.GetDisplayCategoryFromOntologyIds(ctx, actorOntologyIdsLastYear)
	})
	wg.Wait()
	if thisYearError != nil {
		return nil, errors.Wrapf(thisYearError, "error in getting display categories this year")
	}
	if lastYearError != nil {
		return nil, errors.Wrapf(lastYearError, "error in getting display categories last year")
	}
	intentTemplate := getTemplateForIntent(intent, txnDirection, true)

	for _, category := range displayCategoriesThisYear {
		if uniqueCategoryL1[category] {
			continue
		}
		if len(relatedQueries) == ThresholdQueriesPerCriteria {
			break
		}
		if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
			categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
			relatedQueries = append(relatedQueries, &QueryDetail{
				RelatedQuery: intentTemplate + "on " + categoryName,
				TxnFilter: &metaPb.TransactionFilter{
					FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			})
		}
		uniqueCategoryL1[category] = true
	}
	for _, category := range displayCategoriesLastYear {
		if uniqueCategoryL2[category] {
			continue
		}
		if len(relatedQueries) == ThresholdQueriesPerCriteria {
			break
		}
		if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
			categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
			relatedQueries = append(relatedQueries, &QueryDetail{
				RelatedQuery: intentTemplate + "on " + dao.EsCategoryToDisplayCat[strings.ToUpper(category)] + " " + LAST_YEAR,
				TxnFilter: &metaPb.TransactionFilter{
					FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			})
		}
		uniqueCategoryL2[category] = true
	}
	return []*RelatedQueriesTab{
		{
			RelatedQueries: relatedQueries,
			TabName:        constant.FiTabName,
		},
	}, nil
}

// nolint:funlen
func (t *CategoryCriteria) computeCategoryRelatedQueriesFromCategory(ctx context.Context, ontologyIds []string, userActorId string, intent string, txnDirection string, displayCat string, l1FilterOntologyIds []string, l2FilterOntologyIds []string) ([]*RelatedQueriesTab, error) {
	var (
		displayCategoriesThisYear, displayCategoriesLastYear []string
		relatedQueries                                       []*QueryDetail
	)
	ontologyIdsThisYear, ontologyIdsLastYear, err := t.getUserTxnOntologies(ctx, userActorId, intent, txnDirection, l1FilterOntologyIds, l2FilterOntologyIds)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting user txn ontologies")
	}
	displayCategoriesThisYear, displayCategoriesLastYear, err = t.getDisplayCategory(ctx, ontologyIdsThisYear, ontologyIdsLastYear)
	if err != nil {
		return nil, errors.Wrapf(err, "error in getting displayCategory")
	}
	intentTemplate := getTemplateForIntent(intent, txnDirection, true)
	for _, category := range displayCategoriesThisYear {
		if strings.EqualFold(strings.ToLower(dao.EsCategoryToDisplayCat[strings.ToUpper(category)]), strings.ToLower(displayCat)) {
			continue
		}
		if len(relatedQueries) == ThresholdQueriesPerCriteria {
			break
		}
		if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
			categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
			relatedQueries = append(relatedQueries, &QueryDetail{
				RelatedQuery: intentTemplate + "on " + categoryName,
				TxnFilter: &metaPb.TransactionFilter{
					FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			})
		}
	}
	for _, category := range displayCategoriesLastYear {
		if strings.EqualFold(strings.ToLower(dao.EsCategoryToDisplayCat[strings.ToUpper(category)]), strings.ToLower(displayCat)) {
			continue
		}
		if len(relatedQueries) == ThresholdQueriesPerCriteria {
			break
		}
		if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
			categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
			relatedQueries = append(relatedQueries, &QueryDetail{
				RelatedQuery: intentTemplate + "on " + categoryName + " " + LAST_YEAR,
				TxnFilter: &metaPb.TransactionFilter{
					FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			})
		}
	}
	return []*RelatedQueriesTab{
		{
			RelatedQueries: relatedQueries,
			TabName:        constant.FiTabName,
		},
	}, nil
}
func (t *CategoryCriteria) fetchOntologiesDetailFromLevel(ctx context.Context, ontologyIds []string, l1 []string, l2 []string) ([]*daoIndex.TransactionOntologies, error) {
	ontologyDetails, err := t.txnOntologiesSearcher.GetOntologiesDetailFromLevel(ctx, ontologyIds, l1, l2)
	if err != nil {
		return nil, err
	}
	return ontologyDetails, nil
}

// nolint:funlen
func (t *CategoryCriteria) getUserTxnOntologies(ctx context.Context, userActorId string, intent string, txnDirection string, l1FilterOntologyIds []string, l2FilterOntologyIds []string) ([]string, []string, error) {
	var (
		wg                                                                                                                         sync.WaitGroup
		ontologyIdCountFromL1ThisYear, ontologyIdCountFromL1LastYear, ontologyIdCountFromL2ThisYear, ontologyIdCountFromL2LastYear []*elasticsearch.CategoryCount
		errFromL1ThisYear, errFromL1lastYear, errFromL2ThisYear, errFromL2LastYear                                                 error
		relevantOntologyIdsThisYear                                                                                                []string
		relevantOntologyIdsLastYear                                                                                                []string
	)
	if len(l1FilterOntologyIds) > 0 {
		wg.Add(2)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l1FilterOntologyIds,
				FromTime:     now.BeginningOfYear().Format(parser.ESTimeFormat),
				ToTime:       time.Now().In(datetime.IST).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL1ThisYear, errFromL1ThisYear = t.txnSearcher.FetchOntologiesFromActorByAggs(ctx, params)
		})

		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l1FilterOntologyIds,
				FromTime:     time.Now().In(datetime.IST).AddDate(yearDelta, 0, 0).Format(parser.ESTimeFormat),
				ToTime:       now.BeginningOfYear().AddDate(0, 0, 0).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL1LastYear, errFromL1lastYear = t.txnSearcher.FetchOntologiesFromActorByAggs(ctx, params)
		})
	}
	if len(l2FilterOntologyIds) > 0 {
		wg.Add(2)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l2FilterOntologyIds,
				FromTime:     now.BeginningOfYear().Format(parser.ESTimeFormat),
				ToTime:       time.Now().In(datetime.IST).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL2ThisYear, errFromL2ThisYear = t.txnSearcher.FetchOntologiesFromActorByAggs(ctx, params)
		})
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l2FilterOntologyIds,
				FromTime:     time.Now().In(datetime.IST).AddDate(yearDelta, 0, 0).Format(parser.ESTimeFormat),
				ToTime:       now.BeginningOfYear().AddDate(0, 0, 0).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL2LastYear, errFromL2LastYear = t.txnSearcher.FetchOntologiesFromActorByAggs(ctx, params)
		})
	}
	wg.Wait()
	switch {
	case errFromL1ThisYear != nil:
		return nil, nil, errors.Wrapf(errFromL1ThisYear, "error in getting categories from l1 this year")
	case errFromL2ThisYear != nil:
		return nil, nil, errors.Wrapf(errFromL2ThisYear, "error in getting categories from l2 last year")
	case errFromL1lastYear != nil:
		return nil, nil, errors.Wrapf(errFromL1lastYear, "error in getting categories from l1 this year")
	case errFromL2LastYear != nil:
		return nil, nil, errors.Wrapf(errFromL2LastYear, "error in getting categories from l2 last year")
	}
	for i := 0; i < len(ontologyIdCountFromL1ThisYear) && len(relevantOntologyIdsThisYear) <= 2; i++ {
		relevantOntologyIdsThisYear = append(relevantOntologyIdsThisYear, ontologyIdCountFromL1ThisYear[i].OntologyId)
	}
	for i := 0; i < len(ontologyIdCountFromL2ThisYear) && len(relevantOntologyIdsThisYear) <= 2; i++ {
		relevantOntologyIdsThisYear = append(relevantOntologyIdsThisYear, ontologyIdCountFromL2ThisYear[i].OntologyId)
	}
	for i := 0; i < len(ontologyIdCountFromL1LastYear) && len(relevantOntologyIdsLastYear) <= 2; i++ {
		relevantOntologyIdsLastYear = append(relevantOntologyIdsLastYear, ontologyIdCountFromL1LastYear[i].OntologyId)
	}
	for i := 0; i < len(ontologyIdCountFromL2LastYear) && len(relevantOntologyIdsLastYear) <= 2; i++ {
		relevantOntologyIdsLastYear = append(relevantOntologyIdsLastYear, ontologyIdCountFromL2LastYear[i].OntologyId)
	}

	return relevantOntologyIdsThisYear, relevantOntologyIdsLastYear, nil
}

func (t *CategoryCriteria) getDisplayCategory(ctx context.Context, ontologyIdsThisYear []string, ontologyIdsLastYear []string) ([]string, []string, error) {
	var (
		displayCategoriesThisYear, displayCategoriesLastYear []string
		dcThisYearErr, dcLastYearErr                         error
		wg                                                   sync.WaitGroup
	)
	if ontologyIdsThisYear != nil {
		wg.Add(1)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			displayCategoriesThisYear, dcThisYearErr = t.txnOntologiesSearcher.GetDisplayCategoryFromOntologyIds(ctx, ontologyIdsThisYear)
		})

	}
	if ontologyIdsLastYear != nil {
		wg.Add(1)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			displayCategoriesLastYear, dcLastYearErr = t.txnOntologiesSearcher.GetDisplayCategoryFromOntologyIds(ctx, ontologyIdsLastYear)
		})
	}
	wg.Wait()
	if dcThisYearErr != nil {
		return nil, nil, errors.Wrapf(dcThisYearErr, "error in getting displayCategories for this year")
	}
	if dcLastYearErr != nil {
		return nil, nil, errors.Wrapf(dcLastYearErr, "error in getting displayCategories for last year")
	}
	return displayCategoriesThisYear, displayCategoriesLastYear, nil
}

func (t *CategoryCriteria) getLevelFilteredOntologyIds(ctx context.Context, ontologyIds []string, uniqueL1 []string, uniqueL2 []string) ([]string, []string, error) {
	var (
		wg                                                     sync.WaitGroup
		ontologyDetailFromL1Filter, ontologyDetailFromL2Filter []*daoIndex.TransactionOntologies
		l1Err, l2Err                                           error
		l1FilterOntologyIds, l2FilterOntologyIds               []string
	)
	if len(uniqueL1) > 0 {
		wg.Add(1)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			ontologyDetailFromL1Filter, l1Err = t.fetchOntologiesDetailFromLevel(ctx, ontologyIds, uniqueL1, nil)
		})
	}
	if len(uniqueL2) > 0 {
		wg.Add(1)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			ontologyDetailFromL2Filter, l2Err = t.fetchOntologiesDetailFromLevel(ctx, ontologyIds, nil, uniqueL2)
		})
	}
	wg.Wait()
	if l1Err != nil {
		return nil, nil, errors.Wrapf(l1Err, "error in getting ontologyDetail from L1 filter")
	}
	if l2Err != nil {
		return nil, nil, errors.Wrapf(l2Err, "error in getting ontologyDetail from L2 filter")
	}
	for _, detail := range ontologyDetailFromL1Filter {
		l1FilterOntologyIds = append(l1FilterOntologyIds, detail.OntologyId)
	}
	for _, detail := range ontologyDetailFromL2Filter {
		l2FilterOntologyIds = append(l2FilterOntologyIds, detail.OntologyId)
	}
	return l1FilterOntologyIds, l2FilterOntologyIds, nil
}

//nolint:funlen
func (t *CategoryCriteria) computeCategoryRelatedQueriesFromCategoryForCa(ctx context.Context, userActorId string, intent string, txnDirection string, displayCat string, l1FilterOntologyIds []string, l2FilterOntologyIds []string, aaSavingsAccount map[string][]*external.AccountDetails) ([]*RelatedQueriesTab, error) {
	var (
		displayCategoriesThisYear, displayCategoriesLastYear []string
		wg                                                   sync.WaitGroup
		idx                                                  int
	)
	relatedQueries := make([]*RelatedQueriesTab, len(aaSavingsAccount))
	wg.Add(len(aaSavingsAccount))
	for fipId, accountDetails := range aaSavingsAccount {
		accountIds := entity.GetAccountIdsFromAccDetail(accountDetails)
		bankMeta, err := connectedaccount.GetFipMetaById(fipId)
		if err != nil {
			return nil, errors.Wrapf(err, "error in getting bank meta by fip id %s", fipId)
		}
		go func(currFipId string, currAccIds []string, index int, bankName string) {
			defer wg.Done()
			ontologyIdsThisYear, ontologyIdsLastYear, err := t.getUserTxnOntologiesForCa(ctx, userActorId, intent, txnDirection, l1FilterOntologyIds, l2FilterOntologyIds, accountIds)
			if err != nil {
				logger.Error(ctx, "error in getting getUserTxnOntologiesForCa", zap.Error(err), zap.String(logger.FIP_ID, currFipId))
				return
			}
			displayCategoriesThisYear, displayCategoriesLastYear, err = t.getDisplayCategory(ctx, ontologyIdsThisYear, ontologyIdsLastYear)
			if err != nil {
				logger.Error(ctx, "error in getting getDisplayCategory", zap.Error(err), zap.String(logger.FIP_ID, currFipId))
				return
			}
			intentTemplate := getTemplateForIntent(intent, txnDirection, true)
			for _, category := range displayCategoriesThisYear {
				relatedQueries[index] = new(RelatedQueriesTab)
				if strings.EqualFold(strings.ToLower(dao.EsCategoryToDisplayCat[strings.ToUpper(category)]), strings.ToLower(displayCat)) {
					continue
				}
				if len(relatedQueries[index].RelatedQueries) == ThresholdQueriesPerCriteria {
					break
				}
				if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
					categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
					relatedQueries[index].RelatedQueries = append(relatedQueries[index].RelatedQueries, &QueryDetail{
						RelatedQuery: intentTemplate + "on " + categoryName,
						TxnFilter: &metaPb.TransactionFilter{
							FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
							DisplayCategories: []string{categoryName},
							QueryIntent:       constant.IntentTransactionsDetails,
							TxnDirection:      constant.TxnDirectionAll,
						},
					})
				}
			}
			for _, category := range displayCategoriesLastYear {
				if relatedQueries[index] == nil {
					relatedQueries[index] = new(RelatedQueriesTab)
				}
				if strings.EqualFold(strings.ToLower(dao.EsCategoryToDisplayCat[strings.ToUpper(category)]), strings.ToLower(displayCat)) {
					continue
				}
				if len(relatedQueries[index].RelatedQueries) == ThresholdQueriesPerCriteria {
					break
				}
				if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
					categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
					relatedQueries[index].RelatedQueries = append(relatedQueries[index].RelatedQueries, &QueryDetail{
						RelatedQuery: intentTemplate + "on " + categoryName + " " + LAST_YEAR,
						TxnFilter: &metaPb.TransactionFilter{
							FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
							DisplayCategories: []string{categoryName},
							QueryIntent:       constant.IntentTransactionsDetails,
							TxnDirection:      constant.TxnDirectionAll,
						},
					})
				}
			}
			if relatedQueries[index] != nil {
				relatedQueries[index].TabName = bankName
			}
		}(fipId, accountIds, idx, bankMeta.DisplayName)
		idx++
	}
	wg.Wait()
	return relatedQueries, nil
}

func (t *CategoryCriteria) getOntologyFilteredOntologyIds(ctx context.Context, ontologyIds []string) ([]string, []string, error) {
	var (
		uniqueL1, uniqueL2, l1FilterOntologyIds, l2FilterOntologyIds []string
	)

	l1 := make(map[string]bool)
	l2 := make(map[string]bool)
	ontologiesDetail, err := t.txnOntologiesSearcher.GetOntologiesDetailsFromFromOntologyIds(ctx, ontologyIds)
	if err != nil {
		return nil, nil, err
	}
	for _, detail := range ontologiesDetail {
		l1[detail.L1] = true
		l2[detail.L2] = true
	}
	for value := range l1 {
		if value == "other" || value == "" {
			continue
		}
		uniqueL1 = append(uniqueL1, value)
	}
	for value := range l2 {
		if value == "other" || value == "" {
			continue
		}
		uniqueL2 = append(uniqueL2, value)
	}
	l1FilterOntologyIds, l2FilterOntologyIds, err = t.getLevelFilteredOntologyIds(ctx, ontologyIds, uniqueL1, uniqueL2)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "error in getting FilteredOntologyIds")
	}
	return l1FilterOntologyIds, l2FilterOntologyIds, err
}

// nolint
func (t *CategoryCriteria) getUserTxnOntologiesForCa(ctx context.Context, userActorId string, intent string, txnDirection string, l1FilterOntologyIds []string, l2FilterOntologyIds []string, accountIds []string) ([]string, []string, error) {
	var (
		wg                                                                                                                         sync.WaitGroup
		ontologyIdCountFromL1ThisYear, ontologyIdCountFromL1LastYear, ontologyIdCountFromL2ThisYear, ontologyIdCountFromL2LastYear []*elasticsearch.CategoryCount
		errFromL1ThisYear, errFromL1lastYear, errFromL2ThisYear, errFromL2LastYear                                                 error
		relevantOntologyIdsThisYear, relevantOntologyIdsLastYear                                                                   []string
	)
	if len(l1FilterOntologyIds) > 0 {
		wg.Add(2)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l1FilterOntologyIds,
				FromTime:     now.BeginningOfYear().Format(parser.ESTimeFormat),
				ToTime:       time.Now().In(datetime.IST).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL1ThisYear, errFromL1ThisYear = t.aaSearcher.FetchOntologiesFromActorByAggs(ctx, params, accountIds)
		})

		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l1FilterOntologyIds,
				FromTime:     time.Now().In(datetime.IST).AddDate(yearDelta, 0, 0).Format(parser.ESTimeFormat),
				ToTime:       now.BeginningOfYear().AddDate(0, 0, 0).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL1LastYear, errFromL1lastYear = t.aaSearcher.FetchOntologiesFromActorByAggs(ctx, params, accountIds)
		})
	}
	if len(l2FilterOntologyIds) > 0 {
		wg.Add(2)
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l2FilterOntologyIds,
				FromTime:     now.BeginningOfYear().Format(parser.ESTimeFormat),
				ToTime:       time.Now().In(datetime.IST).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL2ThisYear, errFromL2ThisYear = t.aaSearcher.FetchOntologiesFromActorByAggs(ctx, params, accountIds)
		})
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			params := &dao_query.TxnWithMultipleActorQueryParams{
				PayerActorId: userActorId,
				OntologyIds:  l2FilterOntologyIds,
				FromTime:     time.Now().In(datetime.IST).AddDate(yearDelta, 0, 0).Format(parser.ESTimeFormat),
				ToTime:       now.BeginningOfYear().AddDate(0, 0, 0).Format(parser.ESTimeFormat),
			}
			AddTxnTypeInParams(intent, txnDirection, params)
			ontologyIdCountFromL2LastYear, errFromL2LastYear = t.aaSearcher.FetchOntologiesFromActorByAggs(ctx, params, accountIds)
		})
	}
	wg.Wait()
	switch {
	case errFromL1ThisYear != nil:
		return nil, nil, errors.Wrapf(errFromL1ThisYear, "error in getting categories from l1 this year")
	case errFromL2ThisYear != nil:
		return nil, nil, errors.Wrapf(errFromL2ThisYear, "error in getting categories from l2 last year")
	case errFromL1lastYear != nil:
		return nil, nil, errors.Wrapf(errFromL1lastYear, "error in getting categories from l1 this year")
	case errFromL2LastYear != nil:
		return nil, nil, errors.Wrapf(errFromL2LastYear, "error in getting categories from l2 last year")
	}
	for i := 0; i < len(ontologyIdCountFromL1ThisYear) && len(relevantOntologyIdsThisYear) <= 2; i++ {
		relevantOntologyIdsThisYear = append(relevantOntologyIdsThisYear, ontologyIdCountFromL1ThisYear[i].OntologyId)
	}
	for i := 0; i < len(ontologyIdCountFromL2ThisYear) && len(relevantOntologyIdsThisYear) <= 2; i++ {
		relevantOntologyIdsThisYear = append(relevantOntologyIdsThisYear, ontologyIdCountFromL2ThisYear[i].OntologyId)
	}
	for i := 0; i < len(ontologyIdCountFromL1LastYear) && len(relevantOntologyIdsLastYear) <= 2; i++ {
		relevantOntologyIdsLastYear = append(relevantOntologyIdsLastYear, ontologyIdCountFromL1LastYear[i].OntologyId)
	}
	for i := 0; i < len(ontologyIdCountFromL2LastYear) && len(relevantOntologyIdsLastYear) <= 2; i++ {
		relevantOntologyIdsLastYear = append(relevantOntologyIdsLastYear, ontologyIdCountFromL2LastYear[i].OntologyId)
	}

	return relevantOntologyIdsThisYear, relevantOntologyIdsLastYear, nil
}

func (t *CategoryCriteria) computeCategoryRelatedQueriesFromActorForCa(ctx context.Context, intent string, txnDirection string, actorOntologyIdsThisYearCa map[string][]string, actorOntologyIdsLastYearCa map[string][]string, aaSavingsAccount map[string][]*external.AccountDetails) ([]*RelatedQueriesTab, error) {
	var (
		wg  sync.WaitGroup
		idx int
	)
	categoryRelatedQueries := make([]*RelatedQueriesTab, len(aaSavingsAccount))
	wg.Add(len(aaSavingsAccount))
	for fipId := range aaSavingsAccount {
		bankMeta, err := connectedaccount.GetFipMetaById(fipId)
		if err != nil {
			return nil, errors.Wrapf(err, "error in getting bank meta by fip id: %s", fipId)
		}
		go func(currFipId string, index int, bankName string) {
			defer wg.Done()
			queries, err := t.computeRelatedQueriesFromOntologyIds(ctx, intent, txnDirection, actorOntologyIdsThisYearCa[currFipId], actorOntologyIdsLastYearCa[currFipId])
			if err != nil {
				logger.Error(ctx, "error in getting computeRelatedQueriesFromOntologyIds", zap.Error(err), zap.String(logger.FIP_ID, currFipId))
				return
			}
			if len(queries) != 0 {
				categoryRelatedQueries[index] = new(RelatedQueriesTab)
				categoryRelatedQueries[index].RelatedQueries = queries
				categoryRelatedQueries[index].TabName = bankName
			}
		}(fipId, idx, bankMeta.DisplayName)
		idx++
	}
	wg.Wait()
	return categoryRelatedQueries, nil
}

func (t *CategoryCriteria) computeRelatedQueriesFromOntologyIds(ctx context.Context, intent string, txnDirection string, actorOntologyIdsThisYear []string, actorOntologyIdsLastYear []string) ([]*QueryDetail, error) {
	var (
		relatedQueries                                       []*QueryDetail
		wg                                                   sync.WaitGroup
		displayCategoriesThisYear, displayCategoriesLastYear []string
		thisYearError, lastYearError                         error
	)
	uniqueCategoryL1 := make(map[string]bool)
	uniqueCategoryL2 := make(map[string]bool)
	wg.Add(2)
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		defer wg.Done()
		displayCategoriesThisYear, thisYearError = t.txnOntologiesSearcher.GetDisplayCategoryFromOntologyIds(ctx, actorOntologyIdsThisYear)
	})
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		defer wg.Done()
		displayCategoriesLastYear, lastYearError = t.txnOntologiesSearcher.GetDisplayCategoryFromOntologyIds(ctx, actorOntologyIdsLastYear)
	})
	wg.Wait()
	if thisYearError != nil {
		return nil, errors.Wrapf(thisYearError, "error in getting display categories this year")
	}
	if lastYearError != nil {
		return nil, errors.Wrapf(lastYearError, "error in getting display categories last year")
	}
	intentTemplate := getTemplateForIntent(intent, txnDirection, true)

	for _, category := range displayCategoriesThisYear {
		if uniqueCategoryL1[category] {
			continue
		}
		if len(relatedQueries) == ThresholdQueriesPerCriteria {
			break
		}
		if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
			categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
			relatedQueries = append(relatedQueries, &QueryDetail{
				RelatedQuery: intentTemplate + "on " + categoryName,
				TxnFilter: &metaPb.TransactionFilter{
					FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			})
		}
		uniqueCategoryL1[category] = true
	}
	for _, category := range displayCategoriesLastYear {
		if uniqueCategoryL2[category] {
			continue
		}
		if len(relatedQueries) == ThresholdQueriesPerCriteria {
			break
		}
		if dao.EsCategoryToDisplayCat[strings.ToUpper(category)] != "" {
			categoryName := dao.EsCategoryToDisplayCat[strings.ToUpper(category)]
			relatedQueries = append(relatedQueries, &QueryDetail{
				RelatedQuery: intentTemplate + "on " + categoryName + " " + LAST_YEAR,
				TxnFilter: &metaPb.TransactionFilter{
					FilterTypes:       []metaPb.FilterType{metaPb.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			})
		}
		uniqueCategoryL2[category] = true
	}
	return relatedQueries, nil
}
func AddTxnTypeInParams(intent string, txnDirection string, params *dao_query.TxnWithMultipleActorQueryParams) {
	switch txnDirection {
	case constant.TxnDirectionAll, constant.TxnDirectionSave:
		params.IsDebit = true
		params.IsCredit = true
	case constant.TxnDirectionDebit:
		params.IsDebit = true
	case constant.TxnDirectionCredit:
		params.IsCredit = true
	default:
		params.IsCredit = true
		params.IsDebit = true
	}

}
