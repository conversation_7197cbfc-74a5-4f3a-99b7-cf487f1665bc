// nolint:dupl,funlen,unparam,govet,ineffassign
package control

import (
	"context"
	"errors"
	"fmt"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/epifi/gamma/api/bankcust"
	kycPb "github.com/epifi/gamma/api/kyc"
	userGroup "github.com/epifi/gamma/api/user/group"
	genConf "github.com/epifi/gamma/card/config/genconf"
	"github.com/epifi/gamma/card/helper"
	wireTypes "github.com/epifi/gamma/card/wire/types"

	"google.golang.org/grpc/codes"

	"github.com/epifi/be-common/pkg/names"

	commsPb "github.com/epifi/gamma/api/comms"

	piPb "github.com/epifi/gamma/api/paymentinstrument"

	txn "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/epificontext"

	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	moneyPkg "github.com/epifi/be-common/pkg/money"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/card/config"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"go.uber.org/zap"
	"gorm.io/gorm"

	cardEvents "github.com/epifi/gamma/card/events"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	cardPb "github.com/epifi/gamma/api/card"
	ccPb "github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/provisioning"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	vgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/card/dao"

	"github.com/epifi/gamma/api/card/action_attempt"
)

var (
	allTransactionTypes = []cardPb.CardTransactionType{
		cardPb.CardTransactionType_ATM,
		cardPb.CardTransactionType_ECOMMERCE,
		cardPb.CardTransactionType_NFC,
		cardPb.CardTransactionType_POS,
	}
	allUsageLocationTypes = []cardPb.CardUsageLocationType{
		cardPb.CardUsageLocationType_DOMESTIC,
		cardPb.CardUsageLocationType_INTERNATIONAL,
	}
	beToVgLocType = map[cardPb.CardUsageLocationType]vgPb.CardUsageLocationType{
		cardPb.CardUsageLocationType_INTERNATIONAL: vgPb.CardUsageLocationType_INTERNATIONAL,
		cardPb.CardUsageLocationType_DOMESTIC:      vgPb.CardUsageLocationType_INTERNATIONAL,
	}

	cardProvenanceToPiProvenance = map[cardPb.Provenance]piPb.Source{
		cardPb.Provenance_USER_APP: piPb.Source_EPIFI_USER,
		cardPb.Provenance_SHERLOCK: piPb.Source_SHERLOCK,
		cardPb.Provenance_BANK:     piPb.Source_SYSTEM,
		cardPb.Provenance_IVR:      piPb.Source_EPIFI_USER,
	}

	maxTxnRetriesToBlockCard   = uint(2)
	maxTxnRetriesToSuspendCard = uint(2)
)

const (
	InternalCardResponseStatusCode = "CARD0013"
	SuccessCardResponseStatusCode  = "CARD0000"
)

// Service is the server implementation of the Card control service
type Service struct {
	config                  *config.Config
	cardDao                 dao.CardDao
	cardClient              vgPb.CardProvisioningClient
	authClient              authPb.AuthClient
	actorClient             actorPb.ActorClient
	userClient              userPb.UsersClient
	savingsClient           savingsPb.SavingsClient
	eventBroker             events.Broker
	cardLimitDao            dao.CardLimitDao
	cardDeliveryTrackingDao dao.CardDeliveryTrackingDao
	cardBlockDao            dao.CardBlockDao
	piClient                piPb.PiClient
	commsClient             commsPb.CommsClient
	cardActionAttemptDao    dao.CardActionAttemptDao
	dynamicConf             *genConf.Config
	vgPciCardClient         CardProvisioningClientToVendorGatewayPCIServer
	userGroupClient         userGroup.GroupClient
	cardsCacheStorage       wireTypes.CardsCacheStorage
	bcClient                bankcust.BankCustomerServiceClient
	txnExecutor             storageV2.IdempotentTxnExecutor
	ccPb.UnimplementedCardControlServer
}

type CardProvisioningClientToVendorGatewayPCIServer vgPb.CardProvisioningClient

// NewService initiates a new instance of the Card control service.
func NewService(config *config.Config, cardDao dao.CardDao, cardClient vgPb.CardProvisioningClient, authClient authPb.AuthClient,
	actorClient actorPb.ActorClient, userClient userPb.UsersClient, savingsClient savingsPb.SavingsClient, eventBroker events.Broker,
	cardLimitDao dao.CardLimitDao, cardDeliveryTrackingDao dao.CardDeliveryTrackingDao, cardBlockDao dao.CardBlockDao,
	piClient piPb.PiClient, commsClient commsPb.CommsClient, cardActionAttemptDao dao.CardActionAttemptDao,
	dynamicConf *genConf.Config, vgPciCardClient CardProvisioningClientToVendorGatewayPCIServer,
	userGroupClient userGroup.GroupClient, bcClient bankcust.BankCustomerServiceClient, txnExecutor storageV2.IdempotentTxnExecutor,
	cardsCacheStorage wireTypes.CardsCacheStorage,
) *Service {
	return &Service{
		config:                  config,
		cardDao:                 cardDao,
		cardClient:              cardClient,
		authClient:              authClient,
		actorClient:             actorClient,
		userClient:              userClient,
		savingsClient:           savingsClient,
		eventBroker:             eventBroker,
		cardLimitDao:            cardLimitDao,
		cardDeliveryTrackingDao: cardDeliveryTrackingDao,
		cardBlockDao:            cardBlockDao,
		piClient:                piClient,
		commsClient:             commsClient,
		cardActionAttemptDao:    cardActionAttemptDao,
		dynamicConf:             dynamicConf,
		vgPciCardClient:         vgPciCardClient,
		userGroupClient:         userGroupClient,
		txnExecutor:             txnExecutor,
		cardsCacheStorage:       cardsCacheStorage,
		bcClient:                bcClient,
	}
}

// Rpc blocks the card.
// Block is a permanent operation i.e. the same card cannot be used after blocking.
// Users may require to request a new card to make ATM/POS transactions.
// Block implies:
// - transactionType = {POS, ATM, ECOMM, NFC}
// - locationType = {DOM, INTL}
// - ACTION = ENABLE/DISABLE
//
// If card is blocked, no other control is possible on the card.
func (s *Service) BlockCard(ctx context.Context, req *ccPb.BlockCardRequest) (*ccPb.BlockCardResponse, error) {
	resp := &ccPb.BlockCardResponse{}
	idToCard := make(map[string]*cardPb.Card)

	for _, cardId := range req.CardIds {
		// TODO(anand): can do each of these in a separate go-routine?
		card, err := s.blockCardById(ctx, cardId, req.GetBlockCardReason(), req.GetBlockCardProvenance(), req.GetSkipVendorCall())
		if err != nil {
			logger.Error(ctx, "error while blocking card",
				zap.String(logger.CARD_ID, cardId), zap.Error(err))
			continue
		}
		idToCard[cardId] = card
		// nocustomlint:goroutine
		if !req.GetSkipVendorCall() {
			goroutine.Run(ctx, 10*time.Second, func(goCtx context.Context) {
				s.sendCardControlNotification(goCtx, card, cardPb.CardControlType_BLOCK, cardPb.CardControlAction_ENABLE)
			})
		}
	}
	resp.Status = rpc.StatusOk()
	resp.BlockedCards = idToCard
	return resp, nil
}

// Applies the Action to the following transaction and location types:
// - transactionType = {POS, ATM, ECOMM, NFC}
// - locationType = {DOM, INTL}
// - ACTION = ENABLE/DISABLE
//
//  1. SUSPEND:
//     => (Action == ENABLE)
//     => current card state must be ACTIVATED
//     => on vendor call success, new card state is SUSPENDED
//
//  2. UNSUSPEND
//     => (Action == DISABLE)
//     => current card state must be SUSPENDED
//     => on vendor call success, new card state is ACTIVATED
func (s *Service) SuspendCard(ctx context.Context, req *ccPb.SuspendCardRequest) (*ccPb.SuspendCardResponse, error) {
	resp := &ccPb.SuspendCardResponse{}
	idToStateInfo := make(map[string]*ccPb.SuspendStatesInfo)

	for _, cardId := range req.CardIds {
		// TODO(anand): can do each of these in a separate go-routine?
		// action = UNSPECIFIED implies that incoming action wasn't performed, setting as DEFAULT
		card, err, internalStateCode := s.suspendCardById(ctx, cardId, req.Action, req.GetCredBlock(), req.GetRequestId())
		if err != nil {
			idToStateInfo[cardId] = &ccPb.SuspendStatesInfo{
				Card:               card,
				Action:             cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				InternalStatusCode: internalStateCode,
			}
		} else {
			idToStateInfo[cardId] = &ccPb.SuspendStatesInfo{
				Card:               card,
				Action:             req.Action,
				InternalStatusCode: internalStateCode,
			}
			if req.GetControlActionWorkflow() != ccPb.ControlActionWorkflow_INTERNAL {
				// nocustomlint:goroutine
				go func() {
					s.sendCardControlNotification(ctx, card, cardPb.CardControlType_SUSPEND, req.GetAction())
				}()
			}
		}
	}
	resp.Status = rpc.StatusOk()
	resp.SuspendStates = idToStateInfo
	return resp, nil
}

// LocationOnOff
// - transactionType = ALL
// - locationType = [input]
// - ACTION = ENABLE/DISABLE
// - State = SUCCESS/FAIL
// TODO(vivek/anand): Discuss and change accordingly: One credblock can be use to make one request only. So it taking multiple card-id do not make sense here.
// nolint:dupl
func (s *Service) LocationOnOff(ctx context.Context, req *ccPb.LocationOnOffRequest) (*ccPb.LocationOnOffResponse, error) {
	var (
		resp           = &ccPb.LocationOnOffResponse{}
		grpcStatusCode uint32
	)
	idToStateInfo := make(map[string]*ccPb.LocationOnOffStatesInfo)

	ctrlType, err := getControlTypeByLocType(req.LocType)
	if err != nil {
		logger.Error(ctx, "error in usage location on off ", zap.Any(logger.CARD_ID, req.CardIds), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	for _, cardId := range req.CardIds {
		// TODO(anand): can do each of these in a separate go-routine?
		card, appliedAction, errorWrapper := s.ControlOnOffByType(ctx, cardId, ctrlType, req.Action, req.GetCredBlock(),
			req.GetRequestId(), ccPb.CardControlWorkflow_CARD_CTRL_WF_UNSPECIFIED)
		if errorWrapper == nil {
			idToStateInfo[cardId] = &ccPb.LocationOnOffStatesInfo{
				Card:               card,
				Action:             appliedAction,
				InternalStatusCode: SuccessCardResponseStatusCode,
			}
			grpcStatusCode = uint32(codes.OK)
			continue
		}
		if errorWrapper.GetError() != nil {
			idToStateInfo[cardId] = &ccPb.LocationOnOffStatesInfo{
				Card:               card,
				Action:             cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				InternalStatusCode: errorWrapper.GetInternalStatusCode(),
			}
			grpcStatusCode = errorWrapper.GetGrpcStatusCode()
		} else {
			idToStateInfo[cardId] = &ccPb.LocationOnOffStatesInfo{
				Card:               card,
				Action:             appliedAction,
				InternalStatusCode: errorWrapper.GetInternalStatusCode(),
			}
			grpcStatusCode = errorWrapper.GetGrpcStatusCode()
			if ctrlType == cardPb.CardControlType_INTERNATIONAL_ON_OFF {
				if req.GetControlActionWorkflow() != ccPb.ControlActionWorkflow_INTERNAL {
					// nocustomlint:goroutine
					go func() {
						s.sendCardControlNotification(ctx, card, ctrlType, appliedAction)
					}()
				}
			}
		}
	}
	resp.Status = rpc.NewStatusWithoutDebug(grpcStatusCode, "")
	resp.LocationOnOffStates = idToStateInfo
	return resp, nil
}

// ECommerceOnOff
// - transactionType = ECOMMERCE
// - locationType = ALL
// - ACTION = ENABLE/DISABLE
// - State = SUCCESS/FAIL
// nolint:dupl
func (s *Service) ECommerceOnOff(ctx context.Context, req *ccPb.ECommerceOnOffRequest) (*ccPb.ECommerceOnOffResponse, error) {
	var (
		resp           = &ccPb.ECommerceOnOffResponse{}
		grpcStatusCode uint32
	)
	idToStateInfo := make(map[string]*ccPb.ECommerceOnOffStatesInfo)

	ctrlType, err := getControlTypeByTxnType(cardPb.CardTransactionType_ECOMMERCE)
	if err != nil {
		logger.Error(ctx, "error in ecomm on off ", zap.Any(logger.CARD_ID, req.CardIds), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	for _, cardId := range req.CardIds {
		// TODO(anand): can do each of these in a separate go-routine?
		card, appliedAction, errorWrapper := s.ControlOnOffByType(ctx, cardId, ctrlType, req.Action,
			req.GetCredBlock(), req.GetRequestId(), req.GetEcommEnableFlow())
		if errorWrapper == nil {
			idToStateInfo[cardId] = &ccPb.ECommerceOnOffStatesInfo{
				Card:               card,
				Action:             appliedAction,
				InternalStatusCode: SuccessCardResponseStatusCode,
			}
			grpcStatusCode = uint32(codes.OK)
			continue
		}
		if errorWrapper.GetError() != nil {
			idToStateInfo[cardId] = &ccPb.ECommerceOnOffStatesInfo{
				Card:               card,
				Action:             cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				InternalStatusCode: errorWrapper.GetInternalStatusCode(),
			}
			grpcStatusCode = errorWrapper.GetGrpcStatusCode()
		} else {
			idToStateInfo[cardId] = &ccPb.ECommerceOnOffStatesInfo{
				Card:               card,
				Action:             appliedAction,
				InternalStatusCode: errorWrapper.GetInternalStatusCode(),
			}
			grpcStatusCode = errorWrapper.GetGrpcStatusCode()
			if req.GetControlActionWorkflow() != ccPb.ControlActionWorkflow_INTERNAL {
				// nocustomlint:goroutine
				go func() {
					s.sendCardControlNotification(ctx, card, cardPb.CardControlType_ECOMM_ON_OFF, appliedAction)
				}()
			}
		}
	}
	resp.Status = rpc.NewStatusWithoutDebug(grpcStatusCode, "")
	resp.EcommOnOffStates = idToStateInfo
	return resp, nil
}

// Rpc facilitates to enable/disable cards for NFC transactions for all locations.
// - transactionType = NFC
// - locationType = ALL
// - ACTION = ENABLE/DISABLE
// - State = SUCCESS/FAIL
// nolint:dupl
func (s *Service) NfcOnOff(ctx context.Context, req *ccPb.NfcOnOffRequest) (*ccPb.NfcOnOffResponse, error) {
	var (
		resp           = &ccPb.NfcOnOffResponse{}
		grpcStatusCode uint32
	)
	idToStateInfo := make(map[string]*ccPb.NfcOnOffStatesInfo)

	ctrlType, err := getControlTypeByTxnType(cardPb.CardTransactionType_NFC)
	if err != nil {
		logger.Error(ctx, "error in nfc on off ", zap.Any(logger.CARD_ID, req.CardIds), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	for _, cardId := range req.CardIds {
		// TODO(anand): can do each of these in a separate go-routine?
		card, appliedAction, errorWrapper := s.ControlOnOffByType(ctx, cardId, ctrlType, req.Action, req.GetCredBlock(),
			req.GetRequestId(), ccPb.CardControlWorkflow_CARD_CTRL_WF_UNSPECIFIED)
		logger.Info(ctx, "error wrapper value", zap.Any("errorWrapper", errorWrapper))
		if errorWrapper == nil {
			idToStateInfo[cardId] = &ccPb.NfcOnOffStatesInfo{
				Card:               card,
				Action:             appliedAction,
				InternalStatusCode: SuccessCardResponseStatusCode,
			}
			grpcStatusCode = uint32(codes.OK)
			continue
		}
		if errorWrapper.GetError() != nil {
			idToStateInfo[cardId] = &ccPb.NfcOnOffStatesInfo{
				Card:               card,
				Action:             cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				InternalStatusCode: errorWrapper.GetInternalStatusCode(),
			}
			grpcStatusCode = errorWrapper.GetGrpcStatusCode()
		} else {
			idToStateInfo[cardId] = &ccPb.NfcOnOffStatesInfo{
				Card:               card,
				Action:             appliedAction,
				InternalStatusCode: errorWrapper.GetInternalStatusCode(),
			}
			grpcStatusCode = errorWrapper.GetGrpcStatusCode()
			if req.GetControlActionWorkflow() != ccPb.ControlActionWorkflow_INTERNAL {
				// nocustomlint:goroutine
				go func() {
					s.sendCardControlNotification(ctx, card, cardPb.CardControlType_NFC_ON_OFF, appliedAction)
				}()
			}
		}
	}
	resp.Status = rpc.NewStatusWithoutDebug(grpcStatusCode, "")
	resp.NfcOnOffStates = idToStateInfo
	return resp, nil
}

func (s *Service) ATMOnOff(ctx context.Context, req *ccPb.ATMOnOffRequest) (*ccPb.ATMOnOffResponse, error) {
	var (
		res = &ccPb.ATMOnOffResponse{}
	)
	card, action, errorWrapper := s.cardOfflineTxnControlOnOff(ctx, req.GetCardId(), cardPb.CardTransactionType_ATM,
		req.GetAction(), req.GetCredBlock(), req.GetRequestId(), req.GetControlActionWorkflow())
	switch {
	case errorWrapper == nil:
		res.AtmOnOffState = &ccPb.ATMOnOffStatesInfo{
			Card:               card,
			Action:             action,
			InternalStatusCode: SuccessCardResponseStatusCode,
		}
		res.Status = rpc.StatusOk()
		return res, nil
	case errorWrapper.GetError() != nil:
		logger.Error(ctx, "error in atm on off", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.String("action", req.GetAction().String()), zap.Error(errorWrapper.GetError()))
	case errorWrapper.GetError() == nil:
		if req.GetControlActionWorkflow() != ccPb.ControlActionWorkflow_INTERNAL {
			// nocustomlint:goroutine
			go func() {
				s.sendCardControlNotification(ctx, card, cardPb.CardControlType_ATM_ON_OFF, req.GetAction())
			}()
		}
	}
	res.AtmOnOffState = &ccPb.ATMOnOffStatesInfo{
		Card:               card,
		Action:             action,
		InternalStatusCode: errorWrapper.GetInternalStatusCode(),
	}
	res.Status = rpc.NewStatusWithoutDebug(errorWrapper.GetGrpcStatusCode(), "")
	return res, nil
}

func (s *Service) POSOnOff(ctx context.Context, req *ccPb.POSOnOffRequest) (*ccPb.POSOnOffResponse, error) {
	var (
		res = &ccPb.POSOnOffResponse{}
	)
	card, action, errorWrapper := s.cardOfflineTxnControlOnOff(ctx, req.GetCardId(), cardPb.CardTransactionType_POS,
		req.GetAction(), req.GetCredBlock(), req.GetRequestId(), req.GetControlActionWorkflow())
	switch {
	case errorWrapper == nil:
		res.PosOnOffState = &ccPb.POSOnOffStatesInfo{
			Card:               card,
			Action:             action,
			InternalStatusCode: SuccessCardResponseStatusCode,
		}
		res.Status = rpc.StatusOk()
		return res, nil
	case errorWrapper.GetError() != nil:
		logger.Error(ctx, "error in pos on off", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.String("action", req.GetAction().String()), zap.Error(errorWrapper.GetError()))
	case errorWrapper.GetError() == nil:
		if req.GetControlActionWorkflow() != ccPb.ControlActionWorkflow_INTERNAL {
			// nocustomlint:goroutine
			go func() {
				s.sendCardControlNotification(ctx, card, cardPb.CardControlType_POS_ON_OFF, req.GetAction())
			}()
		}
	}
	res.PosOnOffState = &ccPb.POSOnOffStatesInfo{
		Card:               card,
		Action:             action,
		InternalStatusCode: errorWrapper.GetInternalStatusCode(),
	}
	res.Status = rpc.NewStatusWithoutDebug(errorWrapper.GetGrpcStatusCode(), "")
	return res, nil
}

// cardOfflineTxnControlOnOff does the following :
//  1. It checks if card is in activated state and is delivered to the user
//  2. For enabling ATM/POS we will first check if the current limit is 1 or some other value :
//     2.1 If the current limit is 1 then we will enable the control by updating the limit to the higher values based on the config map
//     2.2 If not 1 then we will check if the new consolidated api is enabled or not. If enabled we will use it to enable ATM or POS
//     otherwise we will use limit update
//  3. For disabling ATM/POS we will check if the new api is enabled or not. If enabled we will use it otherwise we will go ahead with limit update
//
// In case of limits update, ATM enable limit will be 50K and for ATM disable limit will be 1 rupee similarly for POS enable limit will be
// 200 K and for disable 1 rupee.
// These limits are configurable and are picked from config
// We will also update the card controls accordingly
// TODO(priyansh) : Change limits according to account scheme(Id : 2769)
//
//nolint:funlen
func (s *Service) cardOfflineTxnControlOnOff(ctx context.Context, cardId string, txnType cardPb.CardTransactionType,
	action cardPb.CardControlAction, credBlock, requestId string, workflow ccPb.ControlActionWorkflow) (*cardPb.Card, cardPb.CardControlAction, *errorWrapper) {
	var (
		controlType                     cardPb.CardControlType
		changeControlViaConsolidatedApi bool
	)
	card, err := s.getCard(ctx, cardId)
	if err != nil {
		return nil, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("error in fetching card %w", err), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	if card.State != cardPb.CardState_ACTIVATED {
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("card is not in activated state %s", card.State.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	delivered, err := s.checkCardDeliveryStatus(ctx, card.Id, action)
	if err != nil || !delivered {
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("error in fetching card delivery tracking %w", err), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	if (action == cardPb.CardControlAction_ENABLE && TxnEnabled(card.GetControls(), txnType)) ||
		(action == cardPb.CardControlAction_DISABLE && !TxnEnabled(card.GetControls(), txnType)) {
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("control already enabled/disabled"), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	cardLimit, err := s.cardLimitDao.GetByCardId(ctx, cardId)
	if err != nil {
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("error in fetching card limit %w", err), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	switch action {
	case cardPb.CardControlAction_ENABLE:
		for idx := range cardLimit.GetCardLimitData().GetCardLimitDetails() {
			cardLimitDetail := cardLimit.GetCardLimitData().GetCardLimitDetails()[idx]
			if cardLimitDetail.GetTxnType() == txnType && !moneyPkg.AreEquals(cardLimitDetail.GetCurrentAllowedAmount(), &money.Money{
				CurrencyCode: moneyPkg.RupeeCurrencyCode,
				Units:        1,
			}) && helper.IsConsolidatedCardControlsEnabled(ctx, s.config, s.dynamicConf, card,
				s.userGroupClient, s.userClient, s.actorClient) {
				changeControlViaConsolidatedApi = true
			}
		}
	case cardPb.CardControlAction_DISABLE:
		if helper.IsConsolidatedCardControlsEnabled(ctx, s.config, s.dynamicConf, card, s.userGroupClient, s.userClient, s.actorClient) {
			changeControlViaConsolidatedApi = true
		}
	default:
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("invalid action type %s", action.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	if txnType == cardPb.CardTransactionType_ATM {
		controlType = cardPb.CardControlType_ATM_ON_OFF
	} else {
		controlType = cardPb.CardControlType_POS_ON_OFF
	}
	if changeControlViaConsolidatedApi {
		consolidatedCardControlRes, err := s.ConsolidatedCardControlOnOff(ctx, &ccPb.ConsolidatedCardControlOnOffRequest{
			CardId: cardId,
			ControlActions: map[int32]cardPb.CardControlAction{
				int32(controlType): action,
			},
			RequestId:             requestId,
			CredBlock:             credBlock,
			ControlWorkflow:       ccPb.CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF,
			ControlActionWorkflow: ccPb.ControlActionWorkflow_USER_INITIATED,
		})
		switch {
		case err != nil:
			return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
		case !consolidatedCardControlRes.GetStatus().IsSuccess():
			return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, newErrorWrapper(fmt.Errorf("error in ConsolidatedCardControlOnOff"),
				consolidatedCardControlRes.GetStatus().Code, consolidatedCardControlRes.GetInternalResponseCode())
		default:
			logger.Info(ctx, "control updated successfully", zap.String(logger.CARD_ID, card.GetId()),
				zap.String("control-type", txnType.String()), zap.String(logger.ACTION_TYPE, action.String()))
			return card, action, newErrorWrapper(nil, uint32(codes.OK), SuccessCardResponseStatusCode)
		}
	} else {
		var wrappedError *errorWrapper
		card, action, wrappedError = s.changeControlViaLimits(ctx, card, txnType, action, credBlock, requestId, cardLimit)
		if codes.Code(wrappedError.GetGrpcStatusCode()) != codes.OK {
			return card, action, wrappedError
		}
	}
	currControls := card.GetControls()
	controlsToUpdate := generateControlDefinitions(action, card.GetIssuerBank(), controlType)
	if isControlAlreadyApplied(currControls, controlsToUpdate) {
		logger.Info(ctx, "control already applied. ignoring", zap.String(logger.CARD_ID, card.Id),
			zap.String("control-type", controlType.String()), zap.String("action", action.String()))
		return card, action, newErrorWrapper(nil, uint32(codes.OK), SuccessCardResponseStatusCode)
	}
	card.Controls = controlsToUpdate
	updateMask := []cardPb.CardFieldMask{cardPb.CardFieldMask_CARD_CONTROLS}
	updatedCard, err := s.cardDao.UpdateIfStateMatches(ctx, card, updateMask, cardPb.CardState_CARD_STATE_UNSPECIFIED, card.GetState())
	if err != nil {
		logger.Error(ctx, "failed to update DB for card control", zap.String(logger.CARD_ID, card.Id),
			zap.String("control-type", controlType.String()), zap.String("action", action.String()))
		card.Controls = currControls
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	return updatedCard, action, newErrorWrapper(nil, uint32(codes.OK), SuccessCardResponseStatusCode)
}

// changeControlViaLimits : we will update the limit for offline controls to enable/disable them
func (s *Service) changeControlViaLimits(ctx context.Context, savedCard *cardPb.Card, txnType cardPb.CardTransactionType,
	action cardPb.CardControlAction, credBlock, requestId string, cardLimit *ccPb.CardLimit) (*cardPb.Card, cardPb.CardControlAction, *errorWrapper) {
	var (
		limitToBeModified int64
		isLimitIncreased  bool
		err               error
		kycLevel          kycPb.KYCLevel
	)
	cardId := savedCard.GetId()
	kycLevel, err = s.fetchKYCLevel(ctx, savedCard.GetActorId())
	if err != nil {
		return nil, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("failed to fetch kyc level for actor %w", err),
				uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	limitsDefaultValue, ok := (*s.config.DefaultLimitsByKycLevelMap)[kycLevel.String()]
	if !ok {
		return nil, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("default limits is not present for kyc level %s", kycLevel.String()),
				uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	switch action {
	case cardPb.CardControlAction_ENABLE:
		isLimitIncreased, err = s.isLimitIncreased(cardLimit.GetCardLimitData().GetCardLimitDetails(), txnType, limitsDefaultValue)
		if err != nil {
			return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
		}
		switch txnType {
		case cardPb.CardTransactionType_ATM:
			limitToBeModified = int64(limitsDefaultValue.ATMEnable)
		case cardPb.CardTransactionType_POS:
			limitToBeModified = int64(limitsDefaultValue.POSEnable)
		default:
			return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				newErrorWrapper(fmt.Errorf("invalid control type %s", txnType.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
		}
	case cardPb.CardControlAction_DISABLE:
		isLimitIncreased = false
		// This is a vendor specific call.
		// Before having a call for updating the card limit we need the request id for get card limit.
		// This is not required in case of enable operation as UI is having the call for generate txn id where get card
		// limits is called and request id is returned.
		getCardLimitRes, err := s.GetCardLimits(ctx, &ccPb.GetCardLimitsRequest{CardId: savedCard.GetId()})
		if te := epifigrpc.RPCError(getCardLimitRes, err); te != nil {
			logger.Error(ctx, "error in get card limits response", zap.String(logger.CARD_ID, savedCard.GetId()), zap.Error(te))
			return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				newErrorWrapper(fmt.Errorf("error in get card limits %w", te), uint32(codes.Internal), InternalCardResponseStatusCode)
		}
		requestId = getCardLimitRes.GetRequestId()
		switch txnType {
		case cardPb.CardTransactionType_ATM:
			limitToBeModified = int64(limitsDefaultValue.ATMDisable)
		case cardPb.CardTransactionType_POS:
			limitToBeModified = int64(limitsDefaultValue.POSDisable)
		default:
			return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
				newErrorWrapper(fmt.Errorf("invalid control type %s", txnType.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
		}
	default:
		return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("invalid action type %s", action.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	for idx := range cardLimit.GetCardLimitData().GetCardLimitDetails() {
		cardLimitDetail := cardLimit.GetCardLimitData().GetCardLimitDetails()[idx]
		if cardLimitDetail.GetTxnType() == txnType {
			cardLimitDetail.CurrentAllowedAmount = &money.Money{
				CurrencyCode: moneyPkg.RupeeCurrencyCode,
				Units:        limitToBeModified,
				Nanos:        0,
			}
		}
	}
	updateCardLimitRes, err := s.UpdateCardLimits(ctx, &ccPb.UpdateCardLimitsRequest{
		CardId:                 savedCard.GetId(),
		RequestId:              requestId,
		UpdateCardLimitDetails: cardLimit.GetCardLimitData().GetCardLimitDetails(),
		CredBlock:              credBlock,
		IsLimitIncreased:       isLimitIncreased,
		IsUserInitiated:        false,
	})
	switch {
	case err != nil:
		return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	case !updateCardLimitRes.GetStatus().IsSuccess():
		return savedCard, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, newErrorWrapper(fmt.Errorf("error while updating limits"),
			updateCardLimitRes.GetStatus().Code, updateCardLimitRes.GetInternalStatusCode())
	default:
		logger.Info(ctx, "initiating control update", zap.String(logger.CARD_ID, savedCard.GetId()),
			zap.String("control-type", txnType.String()), zap.String("action", action.String()))
		savedCard, err = s.cardDao.GetByID(ctx, cardId)
		if err != nil {
			return nil, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, newErrorWrapper(err, uint32(codes.Internal),
				InternalCardResponseStatusCode)
		}
		return savedCard, action, newErrorWrapper(nil, uint32(codes.OK), SuccessCardResponseStatusCode)
	}
}

// isLimitIncreased checks if limit for a particular control is increased or decreased in case of enabling controls.
func (s *Service) isLimitIncreased(cardLimitDetails []*ccPb.CardLimitDetail,
	txnType cardPb.CardTransactionType, limitsDefaultValue *config.LimitsDefaultValue) (bool, error) {
	switch txnType {
	case cardPb.CardTransactionType_ATM:
		for idx := range cardLimitDetails {
			cardLimitDetail := cardLimitDetails[idx]
			if cardLimitDetail.GetTxnType() == cardPb.CardTransactionType_ATM {
				if cardLimitDetail.GetCurrentAllowedAmount().GetUnits() < int64(limitsDefaultValue.ATMEnable) {
					return true, nil
				}
			}
		}
	case cardPb.CardTransactionType_POS:
		for idx := range cardLimitDetails {
			cardLimitDetail := cardLimitDetails[idx]
			if cardLimitDetail.GetTxnType() == cardPb.CardTransactionType_POS {
				if cardLimitDetail.GetCurrentAllowedAmount().GetUnits() < int64(limitsDefaultValue.POSEnable) {
					return true, nil
				}
			}
		}
	default:
		return false, fmt.Errorf("invalid control type %s", txnType.String())
	}
	return false, nil
}

// A generic RPC to enable/disable a given card controls.
// The type of card controls is passed as a parameter along with the controlAction (ENABLE/DISABLE).
//
// Current DB states are verified to check if control can be applied at the vendor end.
func (s *Service) ControlOnOffByType(ctx context.Context, cardId string, controlType cardPb.CardControlType,
	action cardPb.CardControlAction, credBlock string, requestId string, workflow ccPb.CardControlWorkflow) (*cardPb.Card, cardPb.CardControlAction, *errorWrapper) {
	card, err := s.getCard(ctx, cardId)
	if err != nil {
		return nil, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	vendorToManageControls := commonvgpb.Vendor_FEDERAL_BANK

	canApply, appliedAction, controlsToUpdate, internalStatusCode := s.canApplyControl(ctx, card, controlType, action, vendorToManageControls)

	if !canApply {
		return card, appliedAction, newErrorWrapper(nil, uint32(codes.OK), internalStatusCode)
	}

	return s.applyControlAtVendor(ctx, card, controlType, controlsToUpdate, action, vendorToManageControls, credBlock, requestId, workflow)
}

/*
 1. card must be ACTIVATED to apply any card control.

2. If the control was already applied to card, do nothing.
3. To enable any of the ATM/POS/NFC/International txns we need to ensure if card is delivered to user
*/
func (s *Service) canApplyControl(ctx context.Context, card *cardPb.Card, controlType cardPb.CardControlType, action cardPb.CardControlAction,
	vendor commonvgpb.Vendor) (bool, cardPb.CardControlAction, *cardPb.ControlData, string) {
	if card.State != cardPb.CardState_ACTIVATED {
		return false, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, nil, InternalCardResponseStatusCode
	}
	controlsToUpdate := generateControlDefinitions(action, vendor, controlType)
	// TODO(anand) [bug-id 2720]: possible race conditions not handled. We are doing GET and UPDATE later.
	// The thread that writes later may update stale entries for other controls.
	if isControlAlreadyApplied(card.Controls, controlsToUpdate) {
		logger.Info(ctx, "control already applied. ignoring", zap.String(logger.CARD_ID, card.Id),
			zap.String("control-type", controlType.String()), zap.String("action", action.String()))
		return false, action, nil, SuccessCardResponseStatusCode
	}

	switch controlType {
	case cardPb.CardControlType_ATM_ON_OFF, cardPb.CardControlType_POS_ON_OFF:
		delivered, err := s.checkCardDeliveryStatus(ctx, card.Id, action)
		if err != nil || !delivered {
			return false, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, nil, InternalCardResponseStatusCode
		}
	}
	return true, action, controlsToUpdate, SuccessCardResponseStatusCode
}

func (s *Service) checkCardDeliveryStatus(ctx context.Context, cardId string, action cardPb.CardControlAction) (bool, error) {
	if action != cardPb.CardControlAction_ENABLE {
		return true, nil
	}
	cardDeliveryTracking, err := s.cardDeliveryTrackingDao.GetByCardId(ctx, cardId)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return false, nil
	case err != nil:
		logger.Error(ctx, "error while fetching card delivery tracking info",
			zap.String(logger.CARD_ID, cardId), zap.Error(err))
		return false, err
	}

	if cardDeliveryTracking.State != provisioning.CardDeliveryTrackingState_RECEIVED_BY_USER {
		logger.Info(ctx, "card not delivered to user",
			zap.String("state", cardDeliveryTracking.State.String()), zap.String(logger.CARD_ID, cardId))
		return false, nil
	}
	return true, nil
}

// Invoke the vendor to apply the control at their end. If successful, update the DB accordingly.
func (s *Service) applyControlAtVendor(ctx context.Context, card *cardPb.Card, controlType cardPb.CardControlType,
	controlsToUpdate *cardPb.ControlData, action cardPb.CardControlAction, vendor commonvgpb.Vendor, credBlock string, requestId string,
	workflow ccPb.CardControlWorkflow) (
	*cardPb.Card, cardPb.CardControlAction, *errorWrapper) {
	var (
		cardControlEventType string
		errorWrapper         *errorWrapper
	)
	switch controlType {
	case cardPb.CardControlType_DOMESTIC_ON_OFF:
		cardControlEventType = cardEvents.Domestic
		errorWrapper = s.cardLocationOnOffAtVendor(ctx, card, action, cardPb.CardUsageLocationType_DOMESTIC, vendor, credBlock, requestId)
	case cardPb.CardControlType_INTERNATIONAL_ON_OFF:
		cardControlEventType = cardEvents.International
		errorWrapper = s.cardLocationOnOffAtVendor(ctx, card, action, cardPb.CardUsageLocationType_INTERNATIONAL, vendor, credBlock, requestId)
	case cardPb.CardControlType_ECOMM_ON_OFF:
		cardControlEventType = cardEvents.ECOMM
		errorWrapper = s.cardTransactionOnOffAtVendor(ctx, card, action, cardPb.CardTransactionType_ECOMMERCE, vendor, credBlock, requestId, workflow)
	case cardPb.CardControlType_NFC_ON_OFF:
		cardControlEventType = cardEvents.NFC
		errorWrapper = s.cardTransactionOnOffAtVendor(ctx, card, action, cardPb.CardTransactionType_NFC, vendor, credBlock,
			requestId, ccPb.CardControlWorkflow_CARD_CTRL_WF_UNSPECIFIED)
	default:
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED,
			newErrorWrapper(fmt.Errorf("card control type not supported %v", controlType.String()),
				uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	if errorWrapper != nil && errorWrapper.GetError() != nil {
		s.publishEvent(ctx, cardControlEventType, action, card, cardEvents.FailureStatus)
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, errorWrapper
	}

	// set the new control data to be updated in the DB
	currControls := card.Controls
	card.Controls = controlsToUpdate
	updateMask := []cardPb.CardFieldMask{cardPb.CardFieldMask_CARD_CONTROLS}
	updatedCard, err := s.cardDao.UpdateIfStateMatches(ctx, card, updateMask, cardPb.CardState_CARD_STATE_UNSPECIFIED, card.GetState())
	if err != nil {
		logger.Error(ctx, "failed to update DB for card control", zap.String(logger.CARD_ID, card.Id),
			zap.String("control-type", controlType.String()), zap.String("action", action.String()))
		card.Controls = currControls
		return card, cardPb.CardControlAction_CARD_CTRL_ACTION_UNSPECIFIED, newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	s.publishEvent(ctx, cardControlEventType, action, card, cardEvents.SuccessStatus)
	return updatedCard, action, newErrorWrapper(nil, uint32(codes.OK), SuccessCardResponseStatusCode)
}

// Invoke the vendor directly to turn on/off the location parameters.
// nolint:dupl
func (s *Service) cardLocationOnOffAtVendor(ctx context.Context, card *cardPb.Card, action cardPb.CardControlAction,
	locType cardPb.CardUsageLocationType, vendor commonvgpb.Vendor, credBlock string, requestId string) *errorWrapper {
	var (
		deviceBiometricFlag  bool
		cardAction           action_attempt.Action
		vendorResponseCode   string
		vendorResponseReason string
		internalResponseCode string
	)
	actionState := action_attempt.CardActionState_CARD_ACTION_STATE_FAILURE
	// We will create action attempt to log if the action was successful or failure
	// If the attempt was failure we will update the response code, reason and internal status code
	defer s.createActionAttempt(ctx, &cardAction, &actionState, &vendorResponseCode,
		&vendorResponseReason, &internalResponseCode, card)
	// if credBlock is blank and requestId is blank then generate a new requestId
	// If credBlock is present then requestId need to be use from request input itself as it will be used to generate credblock.
	if credBlock == "" && requestId == "" {
		requestId = idgen.FederalRandomSequence("NEOCDCON", 5)
	}
	// log this vendor call for debugging/audit
	logger.Info(ctx, "attempt to turn Location_ONOFF the card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String("location-type", locType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))

	vgActionFlag, err := getEnableFlag(action)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	vgLocType, ok := beToVgLocType[locType]
	if !ok {
		return newErrorWrapper(fmt.Errorf("failed to convert BE location type to VG card usage location type %s", locType.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	if locType == cardPb.CardUsageLocationType_INTERNATIONAL {
		if vgActionFlag {
			cardAction = action_attempt.Action_ENABLE_INTERNATIONAL_USAGE
		} else {
			cardAction = action_attempt.Action_DISABLE_INTERNATIONAL_USAGE
		}
	}
	deviceBiometricFlag = getDeviceBiometricFlagEnabled()

	deviceId, deviceToken, userProfileId, err := GetDeviceAuthDetails(ctx, card.ActorId, s.authClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	userId, phoneNumber, err := GetEntityDetails(ctx, card.GetActorId(), s.actorClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	customerId, err := GetCustomerId(ctx, userId, card.GetIssuerBank(), s.bcClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	savingsAccount, err := GetSavingsAccountDetails(ctx, card.SavingsAccountId, s.savingsClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	vgResp, err := helper.GetCardVendorgatewayClient(s.dynamicConf, s.cardClient, s.vgPciCardClient).LocationOnOff(ctx, &vgPb.LocationOnOffRequest{
		Header: &commonvgpb.RequestHeader{Vendor: vendor},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			EncryptedPin:  credBlock,
		},
		RequestId: requestId,
		AccountDetails: &vgPb.AccountDetails{
			AccountNumber: savingsAccount.AccountNo,
			CustomerId:    customerId,
			PhoneNumber:   phoneNumber,
		},
		VendorCardId:           card.BankIdentifier,
		DeviceBiometricEnabled: deviceBiometricFlag,
		LocType:                vgLocType,
		Enable:                 vgActionFlag,
	})
	if vgResp != nil {
		vendorResponseCode = vgResp.GetVendorResponseCode()
		vendorResponseReason = vgResp.GetVendorResponseReason()
		internalResponseCode = vgResp.GetInternalStatusCode()
	}
	switch {
	case err != nil:
		logger.Error(ctx, "error in VG, failed to turn LOCATION_ONOFF for card",
			zap.String(logger.CARD_ID, card.Id), zap.Error(err))
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	case vgResp.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "Location_ONOFF in same state in vendor as requested", zap.String(logger.CARD_ID, card.Id),
			zap.String("location-type", locType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
		actionState = action_attempt.CardActionState_CARD_ACTION_STATE_SUCCESS
		return newErrorWrapper(nil, uint32(codes.OK), vgResp.GetInternalStatusCode())
	case vgResp.GetStatus().IsSuccess():
		logger.Info(ctx, "Successfully turned the Location_ONOFF for card: ", zap.String(logger.CARD_ID, card.Id),
			zap.String("location-type", locType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
		actionState = action_attempt.CardActionState_CARD_ACTION_STATE_SUCCESS
		return newErrorWrapper(nil, uint32(codes.OK), vgResp.GetInternalStatusCode())
	case vgResp.GetStatus().IsTransientFailure():
		return newErrorWrapper(fmt.Errorf("transient error state while turning Location_ONOFF for card: %v request id %v", card.Id, requestId), vgResp.GetStatus().Code, vgResp.GetInternalStatusCode())
	default:
		return newErrorWrapper(fmt.Errorf("non success state while turning Location_ONOFF for card: %v request id %v", card.Id, requestId), vgResp.GetStatus().Code, vgResp.GetInternalStatusCode())
	}
}

// Invoke the vendor directly to turn on/off the location parameters.
// nolint:dupl
// nolint:funlen
func (s *Service) cardTransactionOnOffAtVendor(ctx context.Context, card *cardPb.Card, action cardPb.CardControlAction,
	txnType cardPb.CardTransactionType, vendor commonvgpb.Vendor, credBlock string, requestId string, workflow ccPb.CardControlWorkflow) *errorWrapper {
	var (
		deviceBiometricFlag  bool
		pinSetToken          string
		internalStatusCode   string
		grpcStatusCode       uint32
		cardAction           action_attempt.Action
		vendorResponseCode   string
		vendorResponseReason string
		internalResponseCode string
	)
	actionState := action_attempt.CardActionState_CARD_ACTION_STATE_FAILURE
	// We will create action attempt to log if the action was successful or failure
	// If the attempt was failure we will update the response code, reason and internal status code
	defer s.createActionAttempt(ctx, &cardAction, &actionState, &vendorResponseCode,
		&vendorResponseReason, &internalResponseCode, card)
	// if credBlock is blank and requestId is blank then generate a new requestId
	// If credBlock is present then requestId need to be use from request input itself as it will be used to generate credblock.
	if credBlock == "" && requestId == "" {
		requestId = idgen.FederalRandomSequence("NEOCDCON", 5)
	}

	// log this vendor call for debugging/audit
	logger.Info(ctx, "Attempt to turn transaction_ONOFF the card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String("transaction-type", txnType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))

	vgActionFlag, err := getEnableFlag(action)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	deviceBiometricFlag = getDeviceBiometricFlagEnabled()

	deviceId, deviceToken, userProfileId, err := GetDeviceAuthDetails(ctx, card.ActorId, s.authClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	userId, phoneNumber, err := GetEntityDetails(ctx, card.GetActorId(), s.actorClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	customerId, err := GetCustomerId(ctx, userId, card.GetIssuerBank(), s.bcClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}
	savingsAccount, err := GetSavingsAccountDetails(ctx, card.SavingsAccountId, s.savingsClient)
	if err != nil {
		return newErrorWrapper(err, uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	if workflow == ccPb.CardControlWorkflow_CARD_CTRL_TOKEN_WF {
		pinSetToken = card.GetPinSetOtpToken().GetOtp()
	}

	var txnCtrlErr error
	switch txnType {
	case cardPb.CardTransactionType_ECOMMERCE:
		if vgActionFlag {
			cardAction = action_attempt.Action_ENABLE_ECOMM
		} else {
			cardAction = action_attempt.Action_DISABLE_ECOMM
		}
		vgResp, err := helper.GetCardVendorgatewayClient(s.dynamicConf, s.cardClient, s.vgPciCardClient).ECommerceOnOff(ctx, &vgPb.ECommerceOnOffRequest{
			Header: &commonvgpb.RequestHeader{Vendor: vendor},
			Auth: &header.Auth{
				DeviceId:      deviceId,
				DeviceToken:   deviceToken,
				UserProfileId: userProfileId,
				EncryptedPin:  credBlock,
			},
			RequestId: requestId,
			AccountDetails: &vgPb.AccountDetails{
				AccountNumber: savingsAccount.AccountNo,
				CustomerId:    customerId,
				PhoneNumber:   phoneNumber,
			},
			VendorCardId:           card.BankIdentifier,
			DeviceBiometricEnabled: deviceBiometricFlag,
			Enable:                 vgActionFlag,
			PinSetToken:            pinSetToken,
		})
		if vgResp != nil {
			vendorResponseCode = vgResp.GetVendorResponseCode()
			vendorResponseReason = vgResp.GetVendorResponseReason()
			internalResponseCode = vgResp.GetInternalStatusCode()
		}
		switch {
		case err != nil:
			logger.Error(ctx, "error in VG, failed to turn ECOMM_ONOFF for card",
				zap.String(logger.CARD_ID, card.Id), zap.Error(err))
			txnCtrlErr = err
			internalStatusCode = InternalCardResponseStatusCode
			grpcStatusCode = uint32(codes.Internal)
		case vgResp.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "ECOMM_ONOFF in same state in vendor as requested", zap.String(logger.CARD_ID, card.Id),
				zap.String("transaction-type", txnType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
			txnCtrlErr = nil
			internalStatusCode = vgResp.GetInternalStatusCode()
			grpcStatusCode = uint32(codes.OK)
		case vgResp.GetStatus().IsSuccess():
			logger.Info(ctx, "Successfully turned the ECOMM_ONOFF for card: ", zap.String(logger.CARD_ID, card.Id),
				zap.String("transaction-type", txnType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
			txnCtrlErr = nil
			internalStatusCode = vgResp.GetInternalStatusCode()
			grpcStatusCode = uint32(codes.OK)
		case vgResp.GetStatus().IsTransientFailure():
			txnCtrlErr = fmt.Errorf("transient error state while turning ECOMM_ONOFF for card: %v request id %v", card.Id, requestId)
			internalStatusCode = vgResp.GetInternalStatusCode()
			grpcStatusCode = vgResp.GetStatus().Code
		default:
			txnCtrlErr = fmt.Errorf("non success state while turning ECOMM_ONOFF for card: %v request id %v", card.Id, requestId)
			grpcStatusCode = vgResp.GetStatus().Code
			internalStatusCode = vgResp.GetInternalStatusCode()
		}
	case cardPb.CardTransactionType_NFC:
		if vgActionFlag {
			cardAction = action_attempt.Action_ENABLE_NFC
		} else {
			cardAction = action_attempt.Action_DISABLE_NFC
		}
		vgResp, err := helper.GetCardVendorgatewayClient(s.dynamicConf, s.cardClient, s.vgPciCardClient).NfcOnOff(ctx, &vgPb.NfcOnOffRequest{
			Header: &commonvgpb.RequestHeader{Vendor: vendor},
			Auth: &header.Auth{
				DeviceId:      deviceId,
				DeviceToken:   deviceToken,
				UserProfileId: userProfileId,
				EncryptedPin:  credBlock,
			},
			RequestId: requestId,
			AccountDetails: &vgPb.AccountDetails{
				AccountNumber: savingsAccount.AccountNo,
				CustomerId:    customerId,
				PhoneNumber:   phoneNumber,
			},
			VendorCardId:           card.BankIdentifier,
			DeviceBiometricEnabled: deviceBiometricFlag,
			Enable:                 vgActionFlag,
		})
		if vgResp != nil {
			vendorResponseCode = vgResp.GetVendorResponseCode()
			vendorResponseReason = vgResp.GetVendorResponseReason()
			internalResponseCode = vgResp.GetInternalStatusCode()
		}
		switch {
		case err != nil:
			logger.Error(ctx, "error in VG, failed to turn NFC_ONOFF for card",
				zap.String(logger.CARD_ID, card.Id), zap.Error(err))
			txnCtrlErr = err
			internalStatusCode = InternalCardResponseStatusCode
			grpcStatusCode = uint32(codes.Internal)
		case vgResp.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "NFC_ONOFF in same state in vendor as requested", zap.String(logger.CARD_ID, card.Id),
				zap.String("transaction-type", txnType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
			txnCtrlErr = nil
			internalStatusCode = vgResp.GetInternalStatusCode()
			grpcStatusCode = uint32(codes.OK)
		case vgResp.GetStatus().IsSuccess():
			logger.Info(ctx, "Successfully turned the NFC_ONOFF for card: ", zap.String(logger.CARD_ID, card.Id),
				zap.String("transaction-type", txnType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
			txnCtrlErr = nil
			internalStatusCode = vgResp.GetInternalStatusCode()
			grpcStatusCode = uint32(codes.OK)
		case vgResp.GetStatus().IsTransientFailure():
			txnCtrlErr = fmt.Errorf("transient error state while turning NFC_ONOFF for card: %v request id %v", card.Id, requestId)
			internalStatusCode = vgResp.GetInternalStatusCode()
			grpcStatusCode = vgResp.GetStatus().Code
		default:
			grpcStatusCode = vgResp.GetStatus().Code
			txnCtrlErr = fmt.Errorf("non success state while turning NFC_ONOFF for card: %v request id %v", card.Id, requestId)
			internalStatusCode = vgResp.GetInternalStatusCode()
		}
	default:
		return newErrorWrapper(fmt.Errorf("txn type not supported at vendor %s", txnType.String()), uint32(codes.Internal), InternalCardResponseStatusCode)
	}

	// If call to vendor failed, return error to the caller
	if txnCtrlErr != nil {
		logger.Error(ctx, "Failed to turn transaction_ONOFF for card. Error in VG ", zap.String(logger.CARD_ID,
			card.Id), zap.String("transaction-type", txnType.String()), zap.String("action", action.String()),
			zap.String(logger.REQUEST_ID, requestId), zap.Error(txnCtrlErr))
		return newErrorWrapper(txnCtrlErr, grpcStatusCode, internalStatusCode)
	}
	logger.Info(ctx, "Successfully turned the transaction_ONOFF for card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String("transaction-type", txnType.String()), zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
	actionState = action_attempt.CardActionState_CARD_ACTION_STATE_SUCCESS
	return newErrorWrapper(nil, uint32(codes.OK), internalStatusCode)
}

func (s *Service) blockCardById(ctx context.Context, cardId string,
	blockCardReason string, blockCardProvenance cardPb.Provenance, skipVendorCall bool) (*cardPb.Card, error) {
	card, err := s.getCard(ctx, cardId)
	if err != nil {
		return nil, err
	}
	return s.blockCard(ctx, card, blockCardReason, blockCardProvenance, skipVendorCall)
}

func (s *Service) blockCard(ctx context.Context, card *cardPb.Card,
	blockCardReason string, blockCardProvenance cardPb.Provenance, skipVendorCall bool) (*cardPb.Card, error) {
	if !canBlockCard(ctx, card) {
		return card, nil
	}
	// TODO(anand): make vendor configurable? May be once we integrate with VISA
	vendorToManageControls := commonvgpb.Vendor_FEDERAL_BANK
	return s.blockCardAtVendor(ctx, card, vendorToManageControls, blockCardReason, blockCardProvenance, skipVendorCall)
}

// if the card is not ACTIVATED or SUSPENDED, it cannot be blocked
func canBlockCard(ctx context.Context, card *cardPb.Card) bool {
	switch card.GetState() {
	case cardPb.CardState_BLOCKED:
		logger.Error(ctx, "card is already blocked, ignoring", zap.String(logger.CARD_ID, card.Id))
		return false
	default:
		return true
	}
}

// Invoke the vendor directly to block the card.
func (s *Service) blockCardAtVendor(ctx context.Context, card *cardPb.Card, vendor commonvgpb.Vendor,
	blockCardReason string, blockCardProvenance cardPb.Provenance, skipVendorCall bool) (*cardPb.Card, error) {
	var (
		vendorResponseCode   string
		vendorResponseReason string
		internalResponseCode string
	)
	actionState := action_attempt.CardActionState_CARD_ACTION_STATE_FAILURE
	action := action_attempt.Action_BLOCK_CARD
	// We will create action attempt to log if the action was successful or failure
	// If the attempt was failure we will update the response code, reason and internal status code
	defer s.createActionAttempt(ctx, &action, &actionState, &vendorResponseCode,
		&vendorResponseReason, &internalResponseCode, card)
	requestId := idgen.FederalRandomSequence("NEOCDCON", 5)
	// log this vendor call for debugging/audit
	logger.Info(ctx, "Attempt to block the card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String(logger.REQUEST_ID, requestId))

	deviceBiometricFlag := getDeviceBiometricFlagEnabled()

	deviceId, deviceToken, userProfileId, err := GetDeviceAuthDetails(ctx, card.ActorId, s.authClient)
	if err != nil {
		return card, err
	}
	userId, phoneNumber, err := GetEntityDetails(ctx, card.GetActorId(), s.actorClient)
	if err != nil {
		return card, err
	}
	customerId, err := GetCustomerId(ctx, userId, card.GetIssuerBank(), s.bcClient)
	if err != nil {
		return card, err
	}
	savingsAccount, err := GetSavingsAccountDetails(ctx, card.SavingsAccountId, s.savingsClient)
	if err != nil {
		return card, err
	}

	// TODO(anand): set the correct fields in VG block request. Waiting on updated card specs.
	if !skipVendorCall {
		vgResp, err := helper.GetCardVendorgatewayClient(s.dynamicConf, s.cardClient, s.vgPciCardClient).BlockCard(ctx, &vgPb.BlockCardRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: vendor,
			},
			Auth: &header.Auth{
				DeviceId:      deviceId,
				DeviceToken:   deviceToken,
				UserProfileId: userProfileId,
			},
			RequestId: requestId,
			AccountDetails: &vgPb.AccountDetails{
				CustomerId:    customerId,
				PhoneNumber:   phoneNumber,
				AccountNumber: savingsAccount.GetAccountNo(),
			},
			DeviceBiometricEnabled: deviceBiometricFlag,
			VendorCardId:           card.GetBankIdentifier(),
		})
		if vgResp != nil {
			vendorResponseCode = vgResp.GetVendorResponseCode()
			vendorResponseReason = vgResp.GetVendorResponseReason()
		}
		// If call to vendor failed, return the status as FAILED to the caller
		// Else update DB to mark the card state as Blocked.
		switch {
		case err != nil:
			logger.Error(ctx, "error in VG, failed to block card",
				zap.String(logger.CARD_ID, card.GetId()), zap.Error(err))
			return card, err
		case vgResp.GetStatus().IsAlreadyExists():
			logger.Info(ctx, "Card already blocked at vendor", zap.String(logger.CARD_ID, card.GetId()),
				zap.String(logger.REQUEST_ID, requestId))
		case vgResp.GetStatus().IsSuccess():
			logger.Info(ctx, "Successfully blocked card", zap.String(logger.CARD_ID, card.GetId()),
				zap.String(logger.REQUEST_ID, requestId))
		case vgResp.GetStatus().IsTransientFailure():
			return card, fmt.Errorf("transient error state while blocking card: %v request id %v", card.GetId(), requestId)
		default:
			return card, fmt.Errorf("non success state while blocking card: %v request id %v", card.GetId(), requestId)
		}
	}

	updatedCard, err := s.updateCardBlockIdempotently(ctx, card, blockCardReason, blockCardProvenance)
	if err != nil {
		logger.Error(ctx, "failed to update block card state", zap.Error(err),
			zap.String(logger.CARD_ID, card.Id))
		return card, err
	}
	actionState = action_attempt.CardActionState_CARD_ACTION_STATE_SUCCESS
	logger.Info(ctx, "Successfully blocked the card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String(logger.REQUEST_ID, requestId))
	return updatedCard, nil
}

// updateCardBlockIdempotently is an example of `InIdempotentTransaction` (in transaction.go) definition.
// Executes update to card table and insert to card block table and updates the card pi state.
// The function is passed on to the idempotent txn wrapper. On ambiguous error, the function is expected to be
// called multiple times with the guarantee that there is no change in the DB state beyond the initial application.
func (s *Service) updateCardBlockIdempotently(ctx context.Context, card *cardPb.Card, blockCardReason string,
	blockCardProvenance cardPb.Provenance) (*cardPb.Card, error) {
	var (
		updatedCard *cardPb.Card
	)
	txErr := s.txnExecutor.RunIdempotentTxn(ctx, maxTxnRetriesToBlockCard, func(txnCtx context.Context) error {
		var err error
		updatedCard, err = s.cardDao.UpdateIfStateMatches(txnCtx, card, nil, cardPb.CardState_BLOCKED, card.GetState())
		if err != nil {
			logger.Error(ctx, "error while updating card state", zap.Error(err),
				zap.String(logger.CARD_ID, card.Id))
			return err
		}
		cardBlock := &cardPb.CardBlockDetail{
			CardId:     card.Id,
			Reason:     blockCardReason,
			Provenance: blockCardProvenance,
			State:      cardPb.BlockCardState_BLOCK_CARD_SUCCESS,
		}
		_, err = s.cardBlockDao.Create(txnCtx, cardBlock)
		if err != nil {
			logger.Error(ctx, "error while creating card block entry", zap.Error(err),
				zap.String(logger.CARD_ID, updatedCard.Id))
			return err
		}

		err = s.updatePiState(txnCtx, card, piPb.PaymentInstrumentState_BLOCKED, blockCardProvenance, "Card blocked")
		if err != nil {
			logger.Error(ctx, "failed to update pi state", zap.Error(err))
			return err
		}
		return nil
	})
	if txErr != nil {
		if txn.IsErrorAmbiguous(txErr) {
			// unclear if it committed or not, cannot blindly retry on this error method.
			// its fine to fail this API even in case of ambiguous errors. Even if the card entries were
			// committed, it will never be tracked and can be ignored/cleaned later.
			logger.Error(ctx, logger.TXN_AMBIGUOUS_ERR+" error in block card txn", zap.String(logger.CARD_ID, card.Id))
		}
		logger.Error(ctx, logger.TXN_NONRETRYABLE_ERR+" error in block card txn", zap.String(logger.CARD_ID, card.Id),
			zap.Error(txErr))
		return nil, txErr
	}
	updatedCard.State = cardPb.CardState_BLOCKED
	return updatedCard, nil
}

func (s *Service) suspendCardById(ctx context.Context, cardId string,
	action cardPb.CardControlAction, credBlock string, requestId string) (*cardPb.Card, error, string) {
	card, err := s.getCard(ctx, cardId)
	if err != nil {
		return nil, err, InternalCardResponseStatusCode
	}
	return s.suspendCard(ctx, card, action, credBlock, requestId)
}

// Verify if the input action can be applied on the input card.
// - Do nothing if the action is already applied.
// - If the action can be applied, apply the action at the vendor
// - Else throw error
func (s *Service) suspendCard(ctx context.Context, card *cardPb.Card,
	action cardPb.CardControlAction, credBlock string, requestId string) (*cardPb.Card, error, string) {
	canSuspendAtVendor, err := verifySuspendRequest(ctx, card, action)
	if err != nil {
		return card, err, InternalCardResponseStatusCode
	} else if !canSuspendAtVendor {
		return card, nil, SuccessCardResponseStatusCode
	}
	vendorToManageControls := commonvgpb.Vendor_FEDERAL_BANK
	return s.suspendCardAtVendor(ctx, card, action, vendorToManageControls, credBlock, requestId)
}

// if the input action is ENABLE, card must be ACTIVATED
// else if the input action is DISABLE, card must be in SUSPENDED state
func verifySuspendRequest(ctx context.Context, card *cardPb.Card, action cardPb.CardControlAction) (bool, error) {
	switch action {
	case cardPb.CardControlAction_ENABLE:
		switch card.State {
		case cardPb.CardState_SUSPENDED:
			logger.Info(ctx, "card is already suspended, ignoring", zap.String(logger.CARD_ID, card.Id))
			return false, nil
		case cardPb.CardState_ACTIVATED:
			return true, nil
		default:
			logger.Error(ctx, "invalid card state for suspend Enable", zap.String("expected", cardPb.CardState_ACTIVATED.String()),
				zap.String("found", card.State.String()), zap.String(logger.CARD_ID, card.Id))
		}
	case cardPb.CardControlAction_DISABLE:
		switch card.State {
		case cardPb.CardState_SUSPENDED:
			return true, nil
		default:
			logger.Error(ctx, "invalid card state for disable suspend", zap.String("expected", cardPb.CardState_SUSPENDED.String()),
				zap.String("found", card.State.String()), zap.String(logger.CARD_ID, card.Id))
		}
	default:
		logger.Error(ctx, "invalid control action", zap.String(logger.CARD_ID, card.Id), zap.String("action", action.String()))
	}
	return false, fmt.Errorf("invalid state for suspend, card-id: %v, action: %v, state: %v",
		card.Id, action, card.State)
}

// Invoke the vendor to turn on the card suspend ON and OFF
func (s *Service) suspendCardAtVendor(ctx context.Context, card *cardPb.Card,
	action cardPb.CardControlAction, vendor commonvgpb.Vendor, credBlock string, requestId string) (*cardPb.Card, error, string) {
	var (
		deviceBiometricFlag  bool
		cardAction           action_attempt.Action
		vendorResponseCode   string
		vendorResponseReason string
		internalResponseCode string
	)
	actionState := action_attempt.CardActionState_CARD_ACTION_STATE_FAILURE
	// We will create action attempt to log if the action was successful or failure
	// If the attempt was failure we will update the response code, reason and internal status code
	defer s.createActionAttempt(ctx, &cardAction, &actionState, &vendorResponseCode,
		&vendorResponseReason, &internalResponseCode, card)
	// if credBlock is blank and requestId is blank then generate a new requestId
	// If credBlock is present then requestId need to be use from request input itself as it will be used to generate credblock.
	if credBlock == "" && requestId == "" {
		requestId = idgen.FederalRandomSequence("NEOCDCON", 5)
	}
	// log this vendor call for debugging/audit
	logger.Info(ctx, "Attempt to SUSPEND_ONOFF the card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))

	vgActionFlag, err := getEnableFlag(action)
	if err != nil {
		return card, err, InternalCardResponseStatusCode
	}
	if vgActionFlag {
		cardAction = action_attempt.Action_SUSPEND
	} else {
		cardAction = action_attempt.Action_UNSUSPEND
	}

	deviceBiometricFlag = getDeviceBiometricFlagEnabled()

	deviceId, deviceToken, userProfileId, err := GetDeviceAuthDetails(ctx, card.ActorId, s.authClient)
	if err != nil {
		return card, err, InternalCardResponseStatusCode
	}
	userId, phoneNumber, err := GetEntityDetails(ctx, card.GetActorId(), s.actorClient)
	if err != nil {
		return card, err, InternalCardResponseStatusCode
	}
	customerId, err := GetCustomerId(ctx, userId, card.GetIssuerBank(), s.bcClient)
	if err != nil {
		return card, err, InternalCardResponseStatusCode
	}
	savingsAccount, err := GetSavingsAccountDetails(ctx, card.SavingsAccountId, s.savingsClient)
	if err != nil {
		return card, err, InternalCardResponseStatusCode
	}

	// Invoke the vendor directly to block the card.
	vgResp, err := helper.GetCardVendorgatewayClient(s.dynamicConf, s.cardClient, s.vgPciCardClient).SuspendCard(ctx, &vgPb.SuspendCardRequest{
		Header: &commonvgpb.RequestHeader{Vendor: vendor},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			EncryptedPin:  credBlock,
		},
		RequestId: requestId,
		AccountDetails: &vgPb.AccountDetails{
			CustomerId:    customerId,
			PhoneNumber:   phoneNumber,
			AccountNumber: savingsAccount.AccountNo,
		},
		VendorCardId: card.BankIdentifier,
		// TODO(anand): how does BE know if biometric enabled or not?
		DeviceBiometricEnabled: deviceBiometricFlag,
		Enable:                 vgActionFlag,
	})
	if vgResp != nil {
		vendorResponseCode = vgResp.GetVendorResponseCode()
		vendorResponseReason = vgResp.GetVendorResponseReason()
		internalResponseCode = vgResp.GetInternalStatusCode()
	}
	// If call to vendor failed, return the status as FAILED to the caller. Else update DB.
	switch {
	case err != nil:
		logger.Error(ctx, "error in VG, failed to suspend card",
			zap.String(logger.CARD_ID, card.Id), zap.Error(err))
		s.publishEvent(ctx, cardEvents.Freeze, action, card, cardEvents.FailureStatus)
		return card, err, InternalCardResponseStatusCode
	case vgResp.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "Card already suspended at vendor ", zap.String(logger.CARD_ID, card.Id),
			zap.String(logger.REQUEST_ID, requestId))
	case vgResp.GetStatus().IsSuccess():
		s.publishCardSuspendAutoIdEvents(ctx, rpc.StatusOk(), card)
		logger.Info(ctx, "Successfully suspended card: ", zap.String(logger.CARD_ID, card.Id),
			zap.String(logger.REQUEST_ID, requestId))
	case vgResp.GetStatus().IsTransientFailure():
		s.publishEvent(ctx, cardEvents.Freeze, action, card, cardEvents.FailureStatus)
		return card, fmt.Errorf("transient error state while suspending card: %v request id %v", card.Id, requestId), vgResp.GetInternalStatusCode()
	default:
		s.publishEvent(ctx, cardEvents.Freeze, action, card, cardEvents.FailureStatus)
		s.publishCardSuspendAutoIdEvents(ctx, vgResp.GetStatus(), card)

		return card, fmt.Errorf("non success state while suspending card: %v request id %v", card.Id, requestId), vgResp.GetInternalStatusCode()
	}

	updatedCard, err := s.updateCardSuspendIdempotently(ctx, card, action, vendor)
	if err != nil {
		logger.Error(ctx, "failed to update suspend card state",
			zap.String(logger.CARD_ID, card.Id), zap.String("action", action.String()), zap.Error(err))
		return card, err, InternalCardResponseStatusCode
	}
	actionState = action_attempt.CardActionState_CARD_ACTION_STATE_SUCCESS
	s.publishEvent(ctx, cardEvents.Freeze, action, card, cardEvents.SuccessStatus)
	logger.Info(ctx, "Successfully turned SUSPEND_ONOFF for the card: ", zap.String(logger.CARD_ID, card.Id),
		zap.String("action", action.String()), zap.String(logger.REQUEST_ID, requestId))
	return updatedCard, nil, vgResp.GetInternalStatusCode()
}

// updateCardSuspendIdempotently is an example of `InIdempotentTransaction` (in transaction.go) definition.
// Executes update to card table and updates the card pi state.
// The function is passed on to the idempotent txn wrapper. On ambiguous error, the function is expected to be
// called multiple times with the guarantee that there is no change in the DB state beyond the initial application.
func (s *Service) updateCardSuspendIdempotently(ctx context.Context, card *cardPb.Card,
	action cardPb.CardControlAction, vendor commonvgpb.Vendor) (*cardPb.Card, error) {
	var (
		updatedCard *cardPb.Card
		err         error
	)
	updatedState, _, piState := getUpdateDataForSuspendSuccess(action, vendor)

	txErr := s.txnExecutor.RunIdempotentTxn(ctx, maxTxnRetriesToSuspendCard, func(txnCtx context.Context) error {
		updatedCard, err = s.cardDao.UpdateIfStateMatches(txnCtx, card, nil, updatedState, card.GetState())
		if err != nil {
			logger.Error(ctx, "failed to update DB for card SUSPEND", zap.String(logger.CARD_ID, card.Id),
				zap.String("action", action.String()), zap.Error(err))
			// reset the state because we failed to persist the update
			return err
		}

		err = s.updatePiState(txnCtx, card, piState, cardPb.Provenance_USER_APP, "")
		if err != nil {
			logger.Error(ctx, "error while updating card pi state", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
			return err
		}
		return nil
	})
	if txErr != nil {
		if txn.IsErrorAmbiguous(txErr) {
			// unclear if it committed or not, cannot blindly retry on this error method.
			// its fine to fail this API even in case of ambiguous errors. Even if the card entries were
			// committed, it will never be tracked and can be ignored/cleaned later.
			logger.Error(ctx, logger.TXN_AMBIGUOUS_ERR+" error in suspend card txn", zap.String(logger.CARD_ID, card.Id))
		}
		logger.Error(ctx, logger.TXN_NONRETRYABLE_ERR+" error in suspend card txn", zap.String(logger.CARD_ID, card.Id),
			zap.Error(txErr))
		return nil, txErr
	}
	return updatedCard, nil
}

// updatePiState updates the payment instrument state
func (s *Service) updatePiState(ctx context.Context, card *cardPb.Card, stateToUpdate piPb.PaymentInstrumentState, provenance cardPb.Provenance, reason string) error {
	piRes, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_DEBIT_CARD,
		Identifier: &piPb.GetPiRequest_DebitCardRequestParams_{DebitCardRequestParams: &piPb.GetPiRequest_DebitCardRequestParams{
			TokenizedCardNumber: card.GetBasicInfo().GetCardNumber(),
			Expiry:              card.GetBasicInfo().GetExpiry(),
			Name:                card.GetBasicInfo().GetCustomerName(),
		}},
	})
	if te := epifigrpc.RPCError(piRes, err); te != nil {
		logger.Error(ctx, "error while fetching card pi", zap.String(logger.CARD_ID, card.Id),
			zap.String(logger.ACTOR_ID, card.GetActorId()), zap.Error(te))
		return te
	}
	source, ok := cardProvenanceToPiProvenance[provenance]
	if !ok {
		return fmt.Errorf("invalid block card provenance %s", provenance.String())
	}

	paymentInstrument := piRes.GetPaymentInstrument()
	paymentInstrument.State = stateToUpdate

	updatePiRes, err := s.piClient.UpdatePi(ctx, &piPb.UpdatePiRequest{
		PaymentInstrument: paymentInstrument,
		UpdateFieldMask:   []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
		Source:            source,
		UpdateReason:      reason,
	})
	if te := epifigrpc.RPCError(updatePiRes, err); te != nil {
		logger.Error(ctx, "error while updating pi state", zap.String(logger.CARD_ID, card.Id),
			zap.String(logger.ACTOR_ID, card.GetActorId()), zap.Error(te))
		return te
	}
	return nil
}

// Read the card from DB using the cardId. If found, return. Else, log error and return nil.
// TODO(anand): can move this inside DAO??
func (s *Service) getCard(ctx context.Context, cardId string) (*cardPb.Card, error) {
	card, err := s.cardDao.GetByID(ctx, cardId)
	// the card to be blocked should exist in the DB. If not, flag error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "card not found in DB", zap.String(logger.CARD_ID, cardId), zap.Error(err))
		} else {
			logger.Error(ctx, "can't fetch card from DB", zap.String(logger.CARD_ID, cardId), zap.Error(err))
		}
		return nil, err
	}
	return card, nil
}

// If requested SUSPEND action was ENABLE, after vendor confirmation new card state will be SUSPENDED
// If requested SUSPEND action was DISABLE, after vendor confirmation card will again move back to ACTIVATED state
func getUpdateDataForSuspendSuccess(action cardPb.CardControlAction, vendor commonvgpb.Vendor) (cardPb.CardState, *cardPb.ControlData, piPb.PaymentInstrumentState) {
	switch action {
	case cardPb.CardControlAction_ENABLE:
		return cardPb.CardState_SUSPENDED, generateControlDefinitions(action, vendor, cardPb.CardControlType_SUSPEND), piPb.PaymentInstrumentState_SUSPENDED
	case cardPb.CardControlAction_DISABLE:
		return cardPb.CardState_ACTIVATED, generateControlDefinitions(action, vendor, cardPb.CardControlType_SUSPEND), piPb.PaymentInstrumentState_CREATED
	default:
		return cardPb.CardState_CARD_STATE_UNSPECIFIED, nil, piPb.PaymentInstrumentState_PAYMENT_INSTRUMENT_STATE_UNSPECIFIED
	}
}

// Generate all possible combinations of control definitions with the given list of txnTypes and locTypes.
// A control definition = [locType + txnType]
func generateControlDefinitions(action cardPb.CardControlAction, vendor commonvgpb.Vendor,
	controlType cardPb.CardControlType) *cardPb.ControlData {
	var txnTypes []cardPb.CardTransactionType
	var locTypes []cardPb.CardUsageLocationType

	resp := &cardPb.ControlData{}
	resp.LocStates = make(map[string]cardPb.CardControlAction)
	resp.TxnStates = make(map[string]cardPb.CardControlAction)

	switch controlType {
	case cardPb.CardControlType_DOMESTIC_ON_OFF:
		txnTypes = allTransactionTypes
		locTypes = append(locTypes, cardPb.CardUsageLocationType_DOMESTIC)
		resp.LocStates[cardPb.CardUsageLocationType_DOMESTIC.String()] = action
	case cardPb.CardControlType_INTERNATIONAL_ON_OFF:
		txnTypes = allTransactionTypes
		locTypes = append(locTypes, cardPb.CardUsageLocationType_INTERNATIONAL)
		resp.LocStates[cardPb.CardUsageLocationType_INTERNATIONAL.String()] = action
	case cardPb.CardControlType_ECOMM_ON_OFF:
		txnTypes = append(txnTypes, cardPb.CardTransactionType_ECOMMERCE)
		locTypes = allUsageLocationTypes
		resp.TxnStates[cardPb.CardTransactionType_ECOMMERCE.String()] = action
	case cardPb.CardControlType_ATM_ON_OFF:
		txnTypes = append(txnTypes, cardPb.CardTransactionType_ATM)
		locTypes = allUsageLocationTypes
		resp.TxnStates[cardPb.CardTransactionType_ATM.String()] = action
	case cardPb.CardControlType_NFC_ON_OFF:
		txnTypes = append(txnTypes, cardPb.CardTransactionType_NFC)
		locTypes = allUsageLocationTypes
		resp.TxnStates[cardPb.CardTransactionType_NFC.String()] = action
	case cardPb.CardControlType_POS_ON_OFF:
		txnTypes = append(txnTypes, cardPb.CardTransactionType_POS)
		locTypes = allUsageLocationTypes
		resp.TxnStates[cardPb.CardTransactionType_POS.String()] = action
	}
	/*
		// TODO(anand): Commenting this to wrap up Federal specific development. We might want this later when design is more generic.
		for _, txnType := range txnTypes {
			for _, locType := range locTypes {
				resp.Defs = append(resp.Defs, &cardPb.ControlDefinition{
					TransactionType: txnType,
					LocationType:    locType,
					Action:          action,
					Vendor:          vendor,
				})
			}
		}
	*/
	return resp
}

/*
1. if savedControls is nil, return false
2. if controlsToUpdate is nil, nothing to apply, return true
3. for all the input controlsToUpdate,
  - compare the ControlAction for the pair of {txnType, locType}
  - if not equal, return false

4. return true
*/
func isControlAlreadyApplied(savedControls *cardPb.ControlData,
	controlsToUpdate *cardPb.ControlData) bool {
	if savedControls == nil {
		return false
	}
	if controlsToUpdate == nil {
		return true
	}
	for k, v := range controlsToUpdate.LocStates {
		if savedAction, ok := savedControls.LocStates[k]; !ok || savedAction != v {
			return false
		}
	}
	for k, v := range controlsToUpdate.TxnStates {
		if savedAction, ok := savedControls.TxnStates[k]; !ok || savedAction != v {
			return false
		}
	}
	return true
}

func getControlTypeByLocType(locType cardPb.CardUsageLocationType) (cardPb.CardControlType, error) {
	switch locType {
	case cardPb.CardUsageLocationType_DOMESTIC:
		return cardPb.CardControlType_DOMESTIC_ON_OFF, nil
	case cardPb.CardUsageLocationType_INTERNATIONAL:
		return cardPb.CardControlType_INTERNATIONAL_ON_OFF, nil
	default:
		return 0, fmt.Errorf("can't map location-type control-type for %v", locType.String())
	}
}

func getControlTypeByTxnType(txnType cardPb.CardTransactionType) (cardPb.CardControlType, error) {
	switch txnType {
	case cardPb.CardTransactionType_ECOMMERCE:
		return cardPb.CardControlType_ECOMM_ON_OFF, nil
	case cardPb.CardTransactionType_ATM:
		return cardPb.CardControlType_ATM_ON_OFF, nil
	case cardPb.CardTransactionType_POS:
		return cardPb.CardControlType_POS_ON_OFF, nil
	case cardPb.CardTransactionType_NFC:
		return cardPb.CardControlType_NFC_ON_OFF, nil
	default:
		return 0, fmt.Errorf("can't map txn-type control-type for %v", txnType.String())
	}
}

func getEnableFlag(action cardPb.CardControlAction) (bool, error) {
	switch action {
	case cardPb.CardControlAction_DISABLE:
		return false, nil
	case cardPb.CardControlAction_ENABLE:
		return true, nil
	default:
		return false, fmt.Errorf("invalid card control action %v", action.String())
	}
}

// TODO : Change this when we have setup biometric contract. By default, non-biometric devices
func getDeviceBiometricFlagEnabled() bool {
	return false
}

// publishEvent publishes card control event
func (s *Service) publishEvent(ctx context.Context, cardControl string, action cardPb.CardControlAction, card *cardPb.Card, status string) {
	// record creation attempt time here
	cardControlAttemptedAt := time.Now()
	switch action {
	case cardPb.CardControlAction_ENABLE:
		// nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), cardEvents.NewEnableCardControl(cardEvents.EventEnableCardControl, card.ActorId,
			cardEvents.CardControlFlow, "", "", card.Id, cardControl, "",
			"", status, cardControlAttemptedAt, false))
	case cardPb.CardControlAction_DISABLE:
		// nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), cardEvents.NewDisableCardControl(card.ActorId,
			cardEvents.CardControlFlow, "", "", card.Id, cardControl, "",
			"", status, cardControlAttemptedAt))
	default:
		return
	}
}

func (s *Service) sendCardControlNotification(ctx context.Context, card *cardPb.Card, control cardPb.CardControlType, action cardPb.CardControlAction) {
	if !s.config.Flags.EnableSMSFromEpifi ||
		helper.IsSmsDisabledForSmsType(helper.GetSmsTypeForCardControlTypeAndAction(control, action)) {
		return
	}
	switch control {
	case cardPb.CardControlType_BLOCK:
		switch action {
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			blockCardSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_DebitCardBlockSmsOption{
					DebitCardBlockSmsOption: &commsPb.DebitCardBlockSmsOption{
						SmsType: commsPb.SmsType_DEBIT_CARD_BLOCK,
						Option: &commsPb.DebitCardBlockSmsOption_DebitCardBlockSmsOptionV1{
							DebitCardBlockSmsOptionV1: &commsPb.DebitCardBlockSmsOptionV1{
								TemplateVersion:    commsPb.TemplateVersion_VERSION_V1,
								CardLastFourDigits: cardLastFourDigits,
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, blockCardSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending block card sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent block card sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	case cardPb.CardControlType_SUSPEND:
		switch action {
		// Card suspend operation
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			suspendCardSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_DebitCardFreezeSmsOption{
					DebitCardFreezeSmsOption: &commsPb.DebitCardFreezeSmsOption{
						SmsType: commsPb.SmsType_DEBIT_CARD_FREEZE,
						Option: &commsPb.DebitCardFreezeSmsOption_DebitCardFreezeSmsOptionV1{
							DebitCardFreezeSmsOptionV1: &commsPb.DebitCardFreezeSmsOptionV1{
								TemplateVersion:    commsPb.TemplateVersion_VERSION_V1,
								CardLastFourDigits: cardLastFourDigits,
								Name:               names.ParseString(card.GetBasicInfo().GetCustomerName()),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, suspendCardSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending suspend card sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent suspend card sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
			// Unsuspend card
		case cardPb.CardControlAction_DISABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			unsuspendCardSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_DebitCardUnfreezeSmsOption{
					DebitCardUnfreezeSmsOption: &commsPb.DebitCardUnFreezeSmsOption{
						SmsType: commsPb.SmsType_DEBIT_CARD_UNFREEZE,
						Option: &commsPb.DebitCardUnFreezeSmsOption_DebitCardUnfreezeSmsOptionV1{
							DebitCardUnfreezeSmsOptionV1: &commsPb.DebitCardUnFreezeSmsOptionV1{
								TemplateVersion:    commsPb.TemplateVersion_VERSION_V1,
								CardLastFourDigits: cardLastFourDigits,
								Name:               names.ParseString(card.GetBasicInfo().GetCustomerName()),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, unsuspendCardSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending unsuspend card sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent unsuspend card sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	case cardPb.CardControlType_INTERNATIONAL_ON_OFF:
		switch action {
		// Card suspend operation
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			internationalEnableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_DebitCardInternationalOnSmsOption{
					DebitCardInternationalOnSmsOption: &commsPb.DebitCardInternationalOnSmsOption{
						SmsType: commsPb.SmsType_DEBIT_CARD_INTERNATIONAL_ON,
						Option: &commsPb.DebitCardInternationalOnSmsOption_DebitCardInternationalOnSmsOptionV1{
							DebitCardInternationalOnSmsOptionV1: &commsPb.DebitCardInternationalOnSmsOptionV1{
								TemplateVersion:    commsPb.TemplateVersion_VERSION_V1,
								CardLastFourDigits: cardLastFourDigits,
								Name:               names.ParseString(card.GetBasicInfo().GetCustomerName()),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, internationalEnableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending international enable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent international enable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		case cardPb.CardControlAction_DISABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			internationalDisableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_DebitCardInternationalOffSmsOption{
					DebitCardInternationalOffSmsOption: &commsPb.DebitCardInternationalOffSmsOption{
						SmsType: commsPb.SmsType_DEBIT_CARD_INTERNATIONAL_OFF,
						Option: &commsPb.DebitCardInternationalOffSmsOption_DebitCardInternationalOffSmsOptionV1{
							DebitCardInternationalOffSmsOptionV1: &commsPb.DebitCardInternationalOffSmsOptionV1{
								TemplateVersion:    commsPb.TemplateVersion_VERSION_V1,
								CardLastFourDigits: cardLastFourDigits,
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, internationalDisableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending international disable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent international disable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	case cardPb.CardControlType_ECOMM_ON_OFF:
		switch action {
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			ecommEnableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOnSmsOption{
					CardControlOnSmsOption: &commsPb.CardControlOnSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_ON,
						Option: &commsPb.CardControlOnSmsOption_CardControlOnSmsOptionV1{
							CardControlOnSmsOptionV1: &commsPb.CardControlOnSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                "Online",
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, ecommEnableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending ecomm enable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent ecomm enable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		case cardPb.CardControlAction_DISABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			ecommDisableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOffSmsOption{
					CardControlOffSmsOption: &commsPb.CardControlOffSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_OFF,
						Option: &commsPb.CardControlOffSmsOption_CardControlOffSmsOptionV1{
							CardControlOffSmsOptionV1: &commsPb.CardControlOffSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                "Online",
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, ecommDisableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending ecomm disable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent ecomm disable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	case cardPb.CardControlType_NFC_ON_OFF:
		switch action {
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			nfcEnableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOnSmsOption{
					CardControlOnSmsOption: &commsPb.CardControlOnSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_ON,
						Option: &commsPb.CardControlOnSmsOption_CardControlOnSmsOptionV1{
							CardControlOnSmsOptionV1: &commsPb.CardControlOnSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                "Contactless",
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, nfcEnableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending nfc enable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent nfc enable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		case cardPb.CardControlAction_DISABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			nfcDisableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOffSmsOption{
					CardControlOffSmsOption: &commsPb.CardControlOffSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_OFF,
						Option: &commsPb.CardControlOffSmsOption_CardControlOffSmsOptionV1{
							CardControlOffSmsOptionV1: &commsPb.CardControlOffSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                "Contactless",
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, nfcDisableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending nfc disable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent nfc disable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	case cardPb.CardControlType_ATM_ON_OFF:
		switch action {
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			atmEnableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOnSmsOption{
					CardControlOnSmsOption: &commsPb.CardControlOnSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_ON,
						Option: &commsPb.CardControlOnSmsOption_CardControlOnSmsOptionV1{
							CardControlOnSmsOptionV1: &commsPb.CardControlOnSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                cardPb.CardTransactionType_ATM.String(),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, atmEnableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending atm enable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent atm enable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		case cardPb.CardControlAction_DISABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			atmDisableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOffSmsOption{
					CardControlOffSmsOption: &commsPb.CardControlOffSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_OFF,
						Option: &commsPb.CardControlOffSmsOption_CardControlOffSmsOptionV1{
							CardControlOffSmsOptionV1: &commsPb.CardControlOffSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                cardPb.CardTransactionType_ATM.String(),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, atmDisableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending atm disable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent atm disable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	case cardPb.CardControlType_POS_ON_OFF:
		switch action {
		case cardPb.CardControlAction_ENABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			posEnableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOnSmsOption{
					CardControlOnSmsOption: &commsPb.CardControlOnSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_ON,
						Option: &commsPb.CardControlOnSmsOption_CardControlOnSmsOptionV1{
							CardControlOnSmsOptionV1: &commsPb.CardControlOnSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                cardPb.CardTransactionType_POS.String(),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, posEnableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending pos enable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent pos enable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		case cardPb.CardControlAction_DISABLE:
			cardLastFourDigits, err := MaskFirstNCharacters(card.GetBasicInfo().GetMaskedCardNumber(), 4)
			if err != nil {
				logger.Error(ctx, "error while fetching card last four digits", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			posDisableSmsOption := &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CardControlOffSmsOption{
					CardControlOffSmsOption: &commsPb.CardControlOffSmsOption{
						SmsType: commsPb.SmsType_CARD_CONTROL_OFF,
						Option: &commsPb.CardControlOffSmsOption_CardControlOffSmsOptionV1{
							CardControlOffSmsOptionV1: &commsPb.CardControlOffSmsOptionV1{
								TemplateVersion:         commsPb.TemplateVersion_VERSION_V1,
								DebitCardLastFourDigits: cardLastFourDigits,
								Name:                    names.ParseString(card.GetBasicInfo().GetCustomerName()),
								CardMode:                cardPb.CardTransactionType_POS.String(),
							},
						},
					},
				},
			}
			messageId, err := SendCardSMSNotifications(context.Background(), card.GetActorId(), s.actorClient, s.commsClient, posDisableSmsOption)
			if err != nil {
				logger.Error(ctx, "error while sending pos disable sms", zap.String(logger.CARD_ID, card.Id), zap.Error(err))
				return
			}
			logger.Info(ctx, "sent pos disable sms successfully ", zap.String(logger.CARD_ID, card.Id),
				zap.String("message id", messageId))
		default:
			logger.Error(ctx, "invalid control action type",
				zap.String("action", action.String()), zap.String(logger.CARD_ID, card.GetId()))
		}
	default:
		logger.Error(ctx, "invalid control type", zap.String(logger.CARD_ID, card.Id), zap.String("control type", control.String()))
	}
}

// createActionAttempt creates an action attempt to maintain history of card actions along with the vendor response codes and reason
func (s *Service) createActionAttempt(ctx context.Context, action *action_attempt.Action, state *action_attempt.CardActionState, vendorResponseCode, vendorResponseReason,
	internalResponseCode *string, savedCard *cardPb.Card) {
	if savedCard == nil {
		return
	}
	// nocustomlint:goroutine
	go func() {
		_, err := s.cardActionAttemptDao.Create(context.Background(), &action_attempt.ActionAttempt{
			CardId:               savedCard.GetId(),
			ActorId:              savedCard.GetActorId(),
			Action:               *action,
			State:                *state,
			VendorResponseCode:   *vendorResponseCode,
			VendorResponseReason: *vendorResponseReason,
			InternalResponseCode: *internalResponseCode,
		})
		if err != nil {
			logger.Error(ctx, "failed to create action attempt", zap.String(logger.CARD_ID, savedCard.GetId()), zap.Error(err),
				zap.String(logger.STATE, state.String()))
			return
		}
		logger.Debug(ctx, "created action attempt successfully")
	}()
}
