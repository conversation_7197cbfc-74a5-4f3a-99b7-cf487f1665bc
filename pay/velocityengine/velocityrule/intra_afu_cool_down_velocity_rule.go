package velocityrule

import (
	"context"
	"sync"

	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/velocity_engine"
	"github.com/epifi/gamma/api/pay/velocity_engine/enums"
	config "github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pay/pkg/payerrorcodes"
)

type IntraAfuCoolDownVelocityRule struct {
	// actor id of the user required for calling the rpc
	actorId string
	// pay client required for the calling the rpc
	payClient payPb.PayClient
	// amount user is wanting to make the transaction
	TransactionAmount *moneyPb.Money
	// conf to fetch the thresholds for the given rule
	conf *config.Config
}

func NewIntraAfuCoolDownVelocityRule(actorId string, payClient payPb.PayClient, transactionAmount *moneyPb.Money, conf *config.Config) *IntraAfuCoolDownVelocityRule {
	return &IntraAfuCoolDownVelocityRule{
		actorId:           actorId,
		payClient:         payClient,
		TransactionAmount: transactionAmount,
		conf:              conf,
	}
}

var _ VelocityRule = &IntraAfuCoolDownVelocityRule{}

func (u *IntraAfuCoolDownVelocityRule) GetVelocityRuleName() enums.VelocityRule {
	return enums.VelocityRule_INTRA_AFU_COOL_DOWN_RULE
}

// execute rule for the method, lets check if we want to have both amount and count check or not?
func (u *IntraAfuCoolDownVelocityRule) ExecuteRule(ctx context.Context, velocityParams *velocity_engine.VelocityParams, ruleEvaluationChannel chan<- lo.Tuple4[bool, error, string, enums.VelocityRule], wg *sync.WaitGroup) {
	defer wg.Done()
	// fetch the total transacted amount in the given time range and respective parameters
	isRulePassed, _, err := getTxnAggregatesAndCheckVelocity(ctx, u.payClient, u.conf, u.actorId, u.TransactionAmount, u.GetVelocityRuleName(), velocityParams)
	switch {
	case err != nil:
		ruleEvaluationChannel <- lo.Tuple4[bool, error, string, enums.VelocityRule]{
			B: err,
			D: u.GetVelocityRuleName(),
		}
	case isRulePassed:
		ruleEvaluationChannel <- lo.Tuple4[bool, error, string, enums.VelocityRule]{
			A: true,
			D: u.GetVelocityRuleName(),
		}
	default:
		ruleEvaluationChannel <- lo.Tuple4[bool, error, string, enums.VelocityRule]{
			C: u.GetOnFailPayErrorCode(),
			D: u.GetVelocityRuleName(),
		}

	}
}

func (u *IntraAfuCoolDownVelocityRule) GetOnFailPayErrorCode() string {
	return payerrorcodes.IntraAmountOutOfRangeInAfuCoolDown
}
