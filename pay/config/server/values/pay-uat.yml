Application:
  Environment: "uat"
  Name: "pay"

# Pay service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Pay service to be running on a
# different port in the order server
Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9853

# Pay service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Pay service
EpifiDb:
  DbType: "CRDB"
  AppName: "pay"
  StatementTimeout: 5s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PayDb:
  DbType: "PGDB"
  AppName: "pay"
  StatementTimeout: 5m
  Name: "pay"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "uat/rds/postgres/pay_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "uat-celestial-signal-workflow-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "uat-order-in-payment-order-update-queue"

OrderUpdateEventPublisher:
  TopicName: "uat-order-update-topic"

IFTProcessFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-pay-international-fund-transfer-process-file-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-pay-order-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

InternationalFundTransfer:
  S3Bucket: "epifi-uat-pay-international-fund-transfer"
  PoolInwardAccountPI: "paymentinstrument-us-stock-inward-account"
  UsStocksVendorPI: "paymentinstrument-alpaca-international-account"
  SkipSofDocumentGenerationStage: false
  SkipOrderFulfilmentStage: false
  SherlockHost: "https://sherlock.uat.pointz.in"
  FederalSherlockHost: "https://federal-sherlock.uat.pointz.in"
  GenerateSwiftReportRetry: 5
  GenerateSwiftReportSleep: 25
  MaxPageSize: 15
  NoDataExistForPANCode: "Data does not exist for given pan"
  DailyAggregateLimit:
    CurrencyCode: "INR"
    Units: 100000
  AnnualAggregateLimit:
    CurrencyCode: "INR"
    Units: 1000000
  # 60 days
  AccountVintageCheckDuration: "1440h"
  AccountVintageTxnCount: 5
  ForexRate:
    UpdateAmountInUse:
      EnableIdempotency: true

PayIncidentMgrOrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-order-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

TransactionDetailedStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-txn-detailed-status-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

MinDurationRequiredForUserVintageCheck: "1h" #180 days in hours

VersionSupport:
  MinAndroidAppVersionToSupportSofCheck: 213
  MinIOSAppVersionToSupportSofCheck: 1077
  MinAndroidAppVersionToSupportAddCA: 10000
  MinIOSAppVersionToSupportAddCA: 10000

NonTpapPspHandles: ["fedepsp"]

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 14

IFTRemittanceFileProcessingEventPublisher:
  TopicName: "uat-ift-remittance-file-processing-events-topic"


TxnBackfillBucketName: "epifi-uat-pay-txn-backfill"

Tracing:
  Enable: true

GenerateSofLimitStrategiesValuesSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-generate-sof-limit-strategies-values-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "internationalfundtransfer"

GenerateSofLimitStrategiesValuesPublisher:
  QueueName: "uat-generate-sof-limit-strategies-values-queue"

PennyDropConfig:
  PennyDropPollRetryThreshold: 20
  PennyDropSourceAccountMapping:
    PENNY_DROP_PROVENANCE_SECURED_CREDIT_CARDS_NTB_FLOW:
      # todo(@saurabh): add ids here post pi and actor creation
      FEDERAL_BANK:
        ActorId:
        PaymentInstrumentId:
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_STOCKGUARDIAN_TSP:
      # todo(ayushb): update to correct account_details
      FEDERAL_BANK:
        ActorId: "actor-creditcard-federal-pool-account"
        PaymentInstrumentId: "paymentinstrument-creditcard-federal-pool-account-1"
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_LOANS_LSP:
      FEDERAL_BANK:
        ActorId: "actor-loans-federal-pool-account"
        PaymentInstrumentId: "paymentinstrument-loans-federal-pool-account"
        Amount:
          CurrencyCode: "INR"
          Units: 1

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: false
        CacheTTL: "2m"

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"

ProcessPaymentGatewayWebhookEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-pg-razorpay-inbound-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"
