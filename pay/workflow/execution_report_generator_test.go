package workflow_test

import (
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commspb "github.com/epifi/gamma/api/comms"
	orderPb "github.com/epifi/gamma/api/order"
	payReportGenActivityPb "github.com/epifi/gamma/api/pay/activity/reportgen"
	payFilterPb "github.com/epifi/gamma/api/pay/workflow/filter"
	payWorkflowReportGenPb "github.com/epifi/gamma/api/pay/workflow/reportgen"
	celestialActivity "github.com/epifi/gamma/celestial/activity"
	payActivity "github.com/epifi/gamma/pay/activity"
	payWorkflow "github.com/epifi/gamma/pay/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
)

func TestExecutionReportGenerator(t *testing.T) {
	type mockGetWorkflowCount struct {
		enable bool
		req    *payReportGenActivityPb.GetWorkflowCountRequest
		res    *payReportGenActivityPb.GetWorkflowCountResponse
		err    error
	}
	type mockSendNotification struct {
		enable bool
		req    *notificationPb.SendNotificationRequest
		res    *notificationPb.SendNotificationResponse
		err    error
	}
	tests := []struct {
		name                 string
		req                  *payWorkflowReportGenPb.ExecutionReportGeneratorRequest
		mockGetWorkflowCount mockGetWorkflowCount
		mockSendNotification mockSendNotification
		want                 *payWorkflowReportGenPb.ExecutionReportGeneratorResponse
		wantErr              bool
	}{
		{
			name: "execution report generated successfully",
			req: &payWorkflowReportGenPb.ExecutionReportGeneratorRequest{
				WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
				FilterList: []*payFilterPb.Filter{
					{
						Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
							orderPb.OrderStatus_CREATED,
						},
						},
						},
					},
				},
				ReportSink: &payWorkflowReportGenPb.ReportSink{
					Identifier: &payWorkflowReportGenPb.ReportSink_EmailId{
						EmailId: "<EMAIL>",
					},
				},
			},
			mockGetWorkflowCount: mockGetWorkflowCount{
				enable: true,
				req: &payReportGenActivityPb.GetWorkflowCountRequest{
					WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
					FilterList: []*payFilterPb.Filter{
						{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
					},
					FromTime: &timestampPb.Timestamp{},
					ToTime: &timestampPb.Timestamp{
						Seconds: time.Now().Unix(),
					},
				},
				res: &payReportGenActivityPb.GetWorkflowCountResponse{FilterWithCountList: []*payFilterPb.FilterWithCount{
					{
						Filter: &payFilterPb.Filter{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
						Count: 2,
					},
				}},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_EmailId{EmailId: "<EMAIL>"},
						CommunicationList: []*commspb.Communication{
							{
								Medium: commspb.Medium_EMAIL,
								Message: &commspb.Communication_Email{
									Email: &commspb.EmailMessage{
										FromEmailId:   "<EMAIL>",
										FromEmailName: "",
										ToEmailId:     "<EMAIL>",
										EmailOption: &commspb.EmailOption{Option: &commspb.EmailOption_WorkflowExecutionReportEmailOption{
											WorkflowExecutionReportEmailOption: &commspb.WorkflowExecutionReportEmailOption{
												EmailType: commspb.EmailType_WORKFLOW_EXECUTION_REPORT_EMAIL,
												Option: &commspb.WorkflowExecutionReportEmailOption_WorkflowExecutionReportEmailOptionV1{
													WorkflowExecutionReportEmailOptionV1: &commspb.WorkflowExecutionReportEmailOptionV1{
														Message: generateEmailBody([]*payFilterPb.FilterWithCount{
															{
																Filter: &payFilterPb.Filter{
																	Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
																		orderPb.OrderStatus_CREATED,
																	},
																	},
																	},
																},
																Count: 2,
															},
														}),
														TemplateVersion: commspb.TemplateVersion_VERSION_V1,
													},
												},
											},
										}},
										ReplyToName:    "",
										ReplyToEmailId: "",
									},
								},
							},
						},
						QualityOfService: commspb.QoS_GUARANTEED,
					},
				},
				res: &notificationPb.SendNotificationResponse{
					MessageIdList: []string{
						"abc",
					},
				},
				err: nil,
			},
		},
		{
			name: "should return error in case send notification via comms failed",
			req: &payWorkflowReportGenPb.ExecutionReportGeneratorRequest{
				WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
				FilterList: []*payFilterPb.Filter{
					{
						Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
							orderPb.OrderStatus_CREATED,
						},
						},
						},
					},
				},
				ReportSink: &payWorkflowReportGenPb.ReportSink{
					Identifier: &payWorkflowReportGenPb.ReportSink_EmailId{
						EmailId: "<EMAIL>",
					},
				},
			},
			mockGetWorkflowCount: mockGetWorkflowCount{
				enable: true,
				req: &payReportGenActivityPb.GetWorkflowCountRequest{
					WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
					FilterList: []*payFilterPb.Filter{
						{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
					},
					FromTime: &timestampPb.Timestamp{},
					ToTime: &timestampPb.Timestamp{
						Seconds: time.Now().Unix(),
					},
				},
				res: &payReportGenActivityPb.GetWorkflowCountResponse{FilterWithCountList: []*payFilterPb.FilterWithCount{
					{
						Filter: &payFilterPb.Filter{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
						Count: 2,
					},
				}},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				req: &notificationPb.SendNotificationRequest{
					Notifications: &notificationPb.Notification{
						UserIdentifier: &notificationPb.Notification_EmailId{EmailId: "<EMAIL>"},
						CommunicationList: []*commspb.Communication{
							{
								Medium: commspb.Medium_EMAIL,
								Message: &commspb.Communication_Email{
									Email: &commspb.EmailMessage{
										FromEmailId:   "<EMAIL>",
										FromEmailName: "",
										ToEmailId:     "<EMAIL>",
										EmailOption: &commspb.EmailOption{Option: &commspb.EmailOption_WorkflowExecutionReportEmailOption{
											WorkflowExecutionReportEmailOption: &commspb.WorkflowExecutionReportEmailOption{
												EmailType: commspb.EmailType_WORKFLOW_EXECUTION_REPORT_EMAIL,
												Option: &commspb.WorkflowExecutionReportEmailOption_WorkflowExecutionReportEmailOptionV1{
													WorkflowExecutionReportEmailOptionV1: &commspb.WorkflowExecutionReportEmailOptionV1{
														Message: generateEmailBody([]*payFilterPb.FilterWithCount{
															{
																Filter: &payFilterPb.Filter{
																	Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
																		orderPb.OrderStatus_CREATED,
																	},
																	},
																	},
																},
																Count: 2,
															},
														}),
														TemplateVersion: commspb.TemplateVersion_VERSION_V1,
													},
												},
											},
										}},
										ReplyToName:    "",
										ReplyToEmailId: "",
									},
								},
							},
						},
						QualityOfService: commspb.QoS_GUARANTEED,
					},
				},
				res: nil,
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
		{
			name: "should return error in case of invalid notification template",
			req: &payWorkflowReportGenPb.ExecutionReportGeneratorRequest{
				WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
				FilterList: []*payFilterPb.Filter{
					{
						Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
							orderPb.OrderStatus_CREATED,
						},
						},
						},
					},
				},
				ReportSink: &payWorkflowReportGenPb.ReportSink{
					Identifier: nil,
				},
			},
			mockGetWorkflowCount: mockGetWorkflowCount{
				enable: true,
				req: &payReportGenActivityPb.GetWorkflowCountRequest{
					WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
					FilterList: []*payFilterPb.Filter{
						{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
					},
					FromTime: &timestampPb.Timestamp{},
					ToTime: &timestampPb.Timestamp{
						Seconds: time.Now().Unix(),
					},
				},
				res: &payReportGenActivityPb.GetWorkflowCountResponse{FilterWithCountList: []*payFilterPb.FilterWithCount{
					{
						Filter: &payFilterPb.Filter{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
						Count: 2,
					},
				}},
				err: nil,
			},
			wantErr: true,
		},
		{
			name: "should return error in case get workflow count activity failed",
			req: &payWorkflowReportGenPb.ExecutionReportGeneratorRequest{
				WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
				FilterList: []*payFilterPb.Filter{
					{
						Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
							orderPb.OrderStatus_CREATED,
						},
						},
						},
					},
				},
				ReportSink: &payWorkflowReportGenPb.ReportSink{
					Identifier: &payWorkflowReportGenPb.ReportSink_EmailId{
						EmailId: "<EMAIL>",
					},
				},
			},
			mockGetWorkflowCount: mockGetWorkflowCount{
				enable: true,
				req: &payReportGenActivityPb.GetWorkflowCountRequest{
					WorkflowType: orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
					FilterList: []*payFilterPb.Filter{
						{
							Option: &payFilterPb.Filter_StatusFilterOption{StatusFilterOption: &payFilterPb.StatusOption{StatusList: []orderPb.OrderStatus{
								orderPb.OrderStatus_CREATED,
							},
							},
							},
						},
					},
					FromTime: &timestampPb.Timestamp{},
					ToTime: &timestampPb.Timestamp{
						Seconds: time.Now().Unix(),
					},
				},
				res: nil,
				err: epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&payActivity.Processor{})
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterWorkflow(payWorkflow.ExecutionReportGenerator)

			if tt.mockGetWorkflowCount.enable {
				env.OnActivity(string(payNs.GetWorkflowCount), mock.Anything, tt.mockGetWorkflowCount.req).Return(
					tt.mockGetWorkflowCount.res, tt.mockGetWorkflowCount.err)
			}

			if tt.mockSendNotification.enable {
				env.OnActivity(string(epifitemporal.SendNotification), mock.Anything, tt.mockSendNotification.req).Return(
					tt.mockSendNotification.res, tt.mockSendNotification.err)
			}
			env.ExecuteWorkflow(payNs.ExecutionReportGenerator, tt.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ExecutionReportGenerator() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}

func generateEmailBody(filterList []*payFilterPb.FilterWithCount) string {
	res := ""
	for _, filterCount := range filterList {
		switch filterCount.GetFilter().GetOption().(type) {
		case *payFilterPb.Filter_StageFilterOption:
			statusList := filterCount.GetFilter().GetStageFilterOption().GetStatusFilterOption().GetStatusList()
			var statusLis []string
			for _, status := range statusList {
				statusLis = append(statusLis, status.String())
			}
			stage := filterCount.GetFilter().GetStageFilterOption().GetStage()
			res += "[" + stage.String() + "]" + "(" + strings.Join(statusLis, ",") + ")" + "->" + fmt.Sprintf("%d", filterCount.GetCount())

		case *payFilterPb.Filter_StageWithSlaFilterOption:
			statusList := filterCount.GetFilter().GetStageWithSlaFilterOption().GetStageFilterOption().GetStatusFilterOption().GetStatusList()
			sla := filterCount.GetFilter().GetStageWithSlaFilterOption().GetSla()
			var statusLis []string
			for _, status := range statusList {
				statusLis = append(statusLis, status.String())
			}
			stage := filterCount.GetFilter().GetStageWithSlaFilterOption().GetStageFilterOption().GetStage()
			res += "[" + stage.String() + "]" + "(" + strings.Join(statusLis, ",") + ")" + "{" + sla.String() + "}" + "->" + fmt.Sprintf("%d", filterCount.GetCount())

		case *payFilterPb.Filter_StatusFilterOption:
			statusList := filterCount.GetFilter().GetStatusFilterOption().GetStatusList()
			var statusLis []string
			for _, status := range statusList {
				statusLis = append(statusLis, status.String())
			}
			res += "(" + strings.Join(statusLis, ",") + ")" + "->" + fmt.Sprintf("%d", filterCount.GetCount())
		case *payFilterPb.Filter_StatusWithSlaFilterOption:
			statusList := filterCount.GetFilter().GetStatusWithSlaFilterOption().GetStatusFilterOption().GetStatusList()
			sla := filterCount.GetFilter().GetStatusWithSlaFilterOption().GetSla()
			var statusLis []string
			for _, status := range statusList {
				statusLis = append(statusLis, status.String())
			}
			res += "(" + strings.Join(statusLis, ",") + ")" + "{" + sla.String() + "}" + "->" + fmt.Sprintf("%d", filterCount.GetCount())
		}
	}
	return res
}
