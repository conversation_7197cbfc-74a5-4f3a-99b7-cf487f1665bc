package internationalfundtransfer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/pay/internationalfundtransfer/sof/migrator"

	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	connectedAccPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/consumer"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/pkg/usstocks/deeplinks"
)

const (
	twoMonthDuration = 2 * 30 * 24 * time.Hour
)

func (s *Service) GetSOFDocument(ctx context.Context, req *iftPb.GetSOFDocumentRequest) (*iftPb.GetSOFDocumentResponse, error) {
	var res = &iftPb.GetSOFDocumentResponse{}
	docUrl, err, _ := s.getSofDocUrl(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while finding the sof document", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(docUrl) == 0 {
		res.Status = rpcPb.StatusRecordNotFound()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	res.Url = docUrl
	return res, nil
}

// for recordNotFound getSofDocUrl return empty url without any error
func (s *Service) getSofDocUrl(ctx context.Context, actorId string) (sofUrl string, err error, isSofDocExpired bool) {
	if s.config.InternationalFundTransfer().IsSofBasedRemittanceLimitCheckEnabled() {
		sofUrl, err, isSofDocExpired = s.getSofDocFromSofDetails(ctx, actorId)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return "", fmt.Errorf("failed to get sof doc url from sof details: %w", err), false
		}
		if err == nil {
			return sofUrl, nil, isSofDocExpired
		}
	}

	checks, err := s.checksDao.GetChecksByActorId(ctx, actorId)
	if err != nil {
		if err != nil && (errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound)) {
			return "", nil, false
		}
		logger.Error(ctx, "error while finding the sof document", zap.Error(err))
		return "", err, false
	}

	// if sof document updated at is nil, that means sof document is not available
	if checks.GetSofDocumentUpdatedAt() == nil {
		return "", nil, false
	}

	documentCreationTime := checks.GetSofDocumentUpdatedAt().AsTime()
	expiryTime := documentCreationTime.Add(migrator.SOFValidityDuration)

	isNotExpired := time.Now().Before(expiryTime)

	if isNotExpired {
		url, err := s.createSofDocSignedUrl(ctx, actorId, checks.GetSofDocumentName())
		return url, err, false
	} else {
		return "", nil, !isNotExpired
	}
}

func (s *Service) createSofDocSignedUrl(ctx context.Context, actorId, docName string) (string, error) {
	path := getSOFFilePath(actorId, docName)
	signedUrl, signedUrlErr := s.payInternationalFundTransferS3Client.GetPreSignedUrl(ctx, path, time.Second*3600)
	if signedUrlErr != nil {
		return "", fmt.Errorf("error while get signed url: %w", signedUrlErr)
	}
	return signedUrl, nil
}

func (s *Service) getSofDocFromSofDetails(ctx context.Context, actorId string) (sofUrl string, err error, isSofDocExpired bool) {
	sofDetails, err := s.sofMigrator.GetSofForActor(ctx, actorId)
	if err != nil {
		return "", fmt.Errorf("failed to get latest sof doc: %w", err), false
	}
	isSofDocExpired, err = s.isSofDetailExpired(sofDetails)
	if err != nil {
		return "", fmt.Errorf("failed to check expiry of sof details: %w", err), false
	}
	if !isSofDocExpired {
		url, err := s.createSofDocSignedUrl(ctx, actorId, sofDetails.GetSofDocumentUrl())
		return url, err, false
	} else {
		return "", nil, true
	}
}

func (s *Service) UploadSOFDocument(ctx context.Context, req *iftPb.UploadSOFDocumentRequest) (*iftPb.UploadSOFDocumentResponse, error) {
	var res = &iftPb.UploadSOFDocumentResponse{}

	currentTimeStamp := time.Now().In(datetime.IST)
	currentTimeInString := currentTimeStamp.Format(dateTillSecondFormat)
	documentName := fmt.Sprintf(sofDocumentName, currentTimeInString)

	err := s.saveSOFDocument(ctx, req, documentName)
	if err != nil {
		logger.Error(ctx, "error while save sof document", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	iftCheckExist, err := s.isIFTCheckExists(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in isIFTCheckExists", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if iftCheckExist {
		err = s.updateIFTCheck(ctx, req.GetActorId(), documentName, currentTimeStamp, req.GetSofDocumentType())
		if err != nil {
			logger.Error(ctx, "error while update ift check", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	} else {
		err = s.createIFTCheck(ctx, req.GetActorId(), documentName, currentTimeStamp, req.GetSofDocumentType())
		if err != nil {
			logger.Error(ctx, "error while creating ift check", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	if !s.config.InternationalFundTransfer().IsSofBasedRemittanceLimitCheckEnabled() {
		res.Status = rpcPb.StatusOk()
		return res, nil
	}
	logger.Info(ctx, "processing sof details")

	latestSofForActor, err := s.sofDetailsDao.GetLatestSofByActorId(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to get latest sof_details for actor", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// validate if signed tmp url in req is same as present in latest sof entry for actor
	if isValid := s.validateSofForActor(ctx, req.GetSignedUrl(), latestSofForActor.GetDocumentInfo()); !isValid {
		logger.Error(ctx, "signed url did not match with actors latest sof entry in db")
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// update sof_url, valid_till and sof state in sof_details
	updateSofResp, updateSofErr := s.UpdateSofDetailsWithDocUrl(ctx, &iftPb.UpdateSofDetailsWithDocUrlRequest{
		SofId:  latestSofForActor.GetId(),
		DocUrl: documentName,
	})
	if rpcErr := epifigrpc.RPCError(updateSofResp, updateSofErr); rpcErr != nil {
		logger.Error(ctx, "failed to update sof details document url and valid_till", zap.Error(rpcErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	_, err = s.generateSofLimitStrategiesPublisher.Publish(ctx, &consumer.GenerateSofLimitStrategiesValuesRequest{
		SofId:   latestSofForActor.GetId(),
		ActorId: req.GetActorId(),
	})
	if err != nil {
		logger.Error(ctx, "failed to publish sof limit compute msg", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) validateSofForActor(ctx context.Context, signedUrl string, info *iftPb.SOFDocumentInfo) bool {
	switch info.GetInfo().(type) {
	case *iftPb.SOFDocumentInfo_FiAccountDocInfo:
		return signedUrl == info.GetFiAccountDocInfo().GetTempDocUrl()
	case *iftPb.SOFDocumentInfo_AaAccountDocInfo:
		return signedUrl == info.GetAaAccountDocInfo().GetTempDocUrl()
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled doc info type : %T", info.GetInfo()))
		return false
	}
}

func (s *Service) isIFTCheckExists(ctx context.Context, actorId string) (bool, error) {
	_, err := s.checksDao.GetChecksByActorId(ctx, actorId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("error while GetChecksByActorId: %w", err)
	}
	return true, nil
}

func (s *Service) updateIFTCheck(ctx context.Context, actorId string, documentName string, currentTimeStamp time.Time, documentType iftPb.SOFDocumentType) error {
	filedMask := []iftPb.InternationalFundTransferChecksFieldMask{
		iftPb.InternationalFundTransferChecksFieldMask_INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_SOF_DOCUMENT_NAME,
		iftPb.InternationalFundTransferChecksFieldMask_INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_SOF_DOCUMENT_UPDATED_AT,
		iftPb.InternationalFundTransferChecksFieldMask_INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_SOF_DOC_TYPE,
	}
	err := s.checksDao.Update(ctx, actorId, &iftPb.InternationalFundTransferChecks{
		SofDocumentName:      documentName,
		SofDocumentUpdatedAt: timestampPb.New(currentTimeStamp),
		SofDocType:           documentType,
	}, filedMask)

	if err != nil {
		return fmt.Errorf("unable to update the international fund transfer check: %w", err)
	}
	return nil
}

func (s *Service) createIFTCheck(ctx context.Context, actorId string, documentName string, currentTimeStamp time.Time, documentType iftPb.SOFDocumentType) error {
	// creating the International fund transfer check as no record is found
	_, err := s.checksDao.Create(ctx, &iftPb.InternationalFundTransferChecks{
		ActorId:              actorId,
		LrsLimitConsumed:     nil,
		SofDocumentName:      documentName,
		SofDocumentUpdatedAt: timestampPb.New(currentTimeStamp),
		SofDocType:           documentType,
	})
	if err != nil {
		return fmt.Errorf("unable to create international fund tranfer check: %w", err)
	}
	return nil
}

func getSOFFilePath(actorId string, fileName string) string {
	return fmt.Sprintf(sofDocumentsPathStructure, DirNameForRemittancePartner, sofFolderName, actorId, fileName)
}

func (s *Service) saveSOFDocument(ctx context.Context, req *iftPb.UploadSOFDocumentRequest, fileName string) error {
	fileContent, err := http.NewRequestWithContext(ctx, http.MethodGet, req.GetSignedUrl(), nil)
	if err != nil {
		return fmt.Errorf("unable to download file from %s error %w", req.GetSignedUrl(), err)
	}
	// fetching the generating file from s3 url
	fileResp, err := s.httpClient.Do(fileContent)
	if err != nil {
		return fmt.Errorf("error while creating request %w", err)
	}
	defer func() {
		err = fileResp.Body.Close()
		if err != nil {
			logger.Error(ctx, "failed to close the fileContent in upload sof document", zap.Error(err))
		}
	}()

	body, err := ioutil.ReadAll(fileResp.Body)
	if err != nil {
		return fmt.Errorf("unable to download the SOF document from signed url due to error %w", err)
	}

	// generate the file path and name from request
	path := getSOFFilePath(req.GetActorId(), fileName)

	// upload the file to International fund transfer bucket
	err = s.payInternationalFundTransferS3Client.Write(ctx, path, body, "bucket-owner-full-control")
	if err != nil {
		return fmt.Errorf("upload sof document failed error %w", err)
	}
	return nil
}

func (s *Service) GetNextSOFStep(ctx context.Context, req *iftPb.GetNextSOFStepRequest) (*iftPb.GetNextSOFStepResponse, error) {
	logger.Info(ctx, "triggered GetNextSOFStep", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Uint32(logger.VERSION_ID, req.GetVersion()),
		zap.String(logger.PLATFORM, req.GetPlatform().String()), zap.String(logger.UI_ENTRY_POINT, req.GetEntryPoint().String()))

	// note: here in case of sof_expired the sofDocUrl is returned as empty
	sofDocUrl, err, isSOFExpired := s.getSofDocUrl(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while checking sof doc is present or not", zap.Error(err))
		return &iftPb.GetNextSOFStepResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// if document url is not empty then will not send any deeplink
	if len(sofDocUrl) > 0 {
		return &iftPb.GetNextSOFStepResponse{
			Status:    rpcPb.StatusOk(),
			SofStatus: iftPb.SOFStatus_SOF_STATUS_SOF_STATEMENT_EXIST,
		}, nil
	}

	// case-1 : Fi account is older than 12 months
	isValid, accountErr := s.isFiAccountValid(ctx, req.GetActorId())
	if accountErr != nil {
		logger.Error(ctx, "error while checking account details of user", zap.Error(accountErr))
		return &iftPb.GetNextSOFStepResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// this case hits if the user's account is eligible for fi account statement (irrespective of whether user have a existing SOF or not or SOF is expired)
	// If the SOF does not exist or is expired, the IFT workflow will create a new Fi statement SOF when the user does a Add Fund
	if isValid {
		// will not send any deeplink if 6 month fi statement is already exist
		return &iftPb.GetNextSOFStepResponse{
			Status:    rpcPb.StatusOk(),
			SofStatus: iftPb.SOFStatus_SOF_STATUS_FI_STATEMENT_EXIST,
		}, nil
	}

	if (req.GetPlatform() == commontypes.Platform_IOS && req.GetVersion() < s.config.VersionSupport().MinIOSAppVersionToSupportSofCheck()) ||
		(req.GetPlatform() == commontypes.Platform_ANDROID && req.GetVersion() < s.config.VersionSupport().MinAndroidAppVersionToSupportSofCheck()) {
		return &iftPb.GetNextSOFStepResponse{
			Status:   rpcPb.StatusOk(),
			Deeplink: deeplinks.GetUpdateAppVersionScreen(),
		}, nil
	}

	// case- 2 : if any valid connected account is existed or not
	caResp, err := s.caClient.GetValidAccountDetailsForUSStocks(ctx, &connectedAccPb.GetValidAccountDetailsForUSStocksRequest{
		ActorId:        req.GetActorId(),
		StartTime:      timestampPb.New(time.Now().Add(-oneYearInHours)),
		EndTime:        timestampPb.Now(),
		ValidityParams: nil,
	})

	if err != nil {
		logger.Error(ctx, "error in getting valid account details", zap.Error(err))
		return &iftPb.GetNextSOFStepResponse{Status: rpcPb.StatusInternal()}, nil
	}

	switch caResp.GetStatus().GetCode() {
	case uint32(connectedAccPb.GetValidAccountDetailsForUSStocksResponse_DATA_SYNC_PENDING):
		// case- 3 : if a newly connected account txn fetching process is in progress
		resp := &iftPb.GetNextSOFStepResponse{
			Status:    rpcPb.StatusOk(),
			SofStatus: iftPb.SOFStatus_SOF_STATUS_ACCOUNT_CONNECTION_IN_PROGRESS,
		}
		if req.GetEntryPoint() == iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_BUY_FLOW {
			resp.Deeplink = deeplinks.GetSofWaitingScreen()
		}
		return resp, nil
	case uint32(connectedAccPb.GetValidAccountDetailsForUSStocksResponse_INSUFFICIENT_TRANSACTION),
		uint32(connectedAccPb.GetValidAccountDetailsForUSStocksResponse_NOT_FOUND):
		// case- 4 if no valid account existed, need to connect new external account
		if req.GetEntryPoint() == iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_ONBOARDING_FLOW &&
			(req.GetPlatform() == commontypes.Platform_IOS && req.GetVersion() < s.config.VersionSupport().MinIOSAppVersionToSupportAddCA()) ||
			(req.GetPlatform() == commontypes.Platform_ANDROID && req.GetVersion() < s.config.VersionSupport().MinAndroidAppVersionToSupportAddCA()) {
			return &iftPb.GetNextSOFStepResponse{
				Status:    rpcPb.StatusOk(),
				SofStatus: iftPb.SOFStatus_SOF_STATUS_VALID_ACCOUNT_NOT_CONNECTED,
				Deeplink:  deeplinks.GetUpdateAppVersionScreen(),
			}, nil
		}

		caFlowResp, caFlowErr := s.caClient.GetSdkDeeplinkForCaFlow(ctx, &connectedAccPb.GetSdkDeeplinkForCaFlowRequest{
			ActorId:     req.GetActorId(),
			AppVersion:  req.GetVersion(),
			AppPlatform: req.GetPlatform(),
			CaFlowName:  getConnectedAccountFlowName(req.GetEntryPoint()),
		})
		if te := epifigrpc.RPCError(caFlowResp, caFlowErr); te != nil {
			logger.Error(ctx, "error while getting sdk deeplink for ca flow", zap.Error(te))
			return &iftPb.GetNextSOFStepResponse{Status: rpcPb.StatusInternal()}, nil
		}

		// this link will be used by CA SDK for redirecting user to provided deeplink after connecting external account
		var redirectionDL *deeplink.Deeplink
		if req.GetEntryPoint() == iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_BUY_FLOW {
			redirectionDL = deeplinks.GetSofWaitingScreen()
		}
		if req.GetEntryPoint() == iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_ONBOARDING_FLOW {
			redirectionDL = deeplinks.GetOnBoardingSetupScreen()
		}
		return &iftPb.GetNextSOFStepResponse{
			Status:    rpcPb.StatusOk(),
			SofStatus: iftPb.SOFStatus_SOF_STATUS_VALID_ACCOUNT_NOT_CONNECTED,
			Deeplink:  deeplinks.GetAccountInfoScreenOption(nil, caFlowResp.GetSdkDeeplinkCaFlow(), redirectionDL),
		}, nil
	case uint32(connectedAccPb.GetValidAccountDetailsForUSStocksResponse_OK):
		if isSOFExpired {
			return &iftPb.GetNextSOFStepResponse{
				Status:    rpcPb.StatusOk(),
				SofStatus: iftPb.SOFStatus_SOF_STATUS_CONNECTED_ACCOUNT_STATEMENT_EXPIRED,
				Deeplink:  deeplinks.GetAccountInfoScreenOption(caResp.GetAccountDetails(), nil, req.GetRedirectionDeeplink()),
			}, nil
		}
		return &iftPb.GetNextSOFStepResponse{
			Status:    rpcPb.StatusOk(),
			SofStatus: iftPb.SOFStatus_SOF_STATUS_VALID_ACCOUNT_CONNECTED,
			Deeplink:  deeplinks.GetAccountInfoScreenOption(caResp.GetAccountDetails(), nil, req.GetRedirectionDeeplink()),
		}, nil
	default:
		if te := epifigrpc.RPCError(caResp, err); te != nil {
			logger.Error(ctx, "error while get valid account from connected account", zap.Error(te))
		}
		return &iftPb.GetNextSOFStepResponse{Status: rpcPb.StatusInternal()}, nil
	}
}

// If Fi account is older than 12 month then it would be valid for SOF
func (s *Service) isFiAccountValid(ctx context.Context, actorId string) (bool, error) {
	userResp, err := s.userProcessor.GetUserFromActorId(ctx, actorId)
	if err != nil {
		return false, errors.Wrap(err, "error while getting user from actor id")
	}

	accountRes, accountErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: userResp.GetId()},
	})
	if accountErr != nil {
		return false, errors.Wrap(accountErr, fmt.Sprintf("error in fetching account, user_id: %s", userResp.GetId()))
	}

	if s.config.EnableSOFLogForDebugging() {
		logger.Info(ctx, "account creation at vendor succeeded at", zap.Any("vendor_creation_time", accountRes.GetAccount().GetCreationInfo().GetVendorCreationSucceededAt()))
		logger.Info(ctx, "min duration from config for checking sof status", zap.Any("min_duration_required", s.config.MinDurationRequiredForUserVintageCheck()))
		logger.Info(ctx, "time since account creation", zap.Any("time_since_account_creation", time.Since(accountRes.GetAccount().GetCreationInfo().GetVendorCreationSucceededAt().AsTime())))
	}

	// checking if Fi account is older than 12 month or not
	if time.Since(accountRes.GetAccount().GetCreationInfo().GetVendorCreationSucceededAt().AsTime()) >= s.config.MinDurationRequiredForUserVintageCheck() {
		logger.Info(ctx, "user is eligible via Fi account statement")
		return true, nil
	}
	logger.Info(ctx, "user is not eligible via Fi account statement, need external account statement")
	return false, nil
}

func getConnectedAccountFlowName(entryPoint iftPb.GetNextSOFStepRequest_SofEntryPoint) enums.CAFlowName {
	switch entryPoint {
	case iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_ONBOARDING_FLOW:
		return enums.CAFlowName_CA_FLOW_NAME_USSTOCKS_ONBOARDING_FLOW
	case iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_BUY_FLOW:
		return enums.CAFlowName_CA_FLOW_NAME_USSTOCKS_BUY_FLOW
	default:
		return enums.CAFlowName_CA_FLOW_NAME_UNSPECIFIED
	}
}
