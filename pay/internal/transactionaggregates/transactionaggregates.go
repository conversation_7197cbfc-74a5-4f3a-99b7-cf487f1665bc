package transactionaggregates

import (
	"context"
	"fmt"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
)

type TxnAggregatesProcessor struct {
	txnAggregatesClient txnAggregatesPb.TxnAggregatesClient
}

func NewTxnAggregatesProcessor(txnAggregatesClient txnAggregatesPb.TxnAggregatesClient) *TxnAggregatesProcessor {
	return &TxnAggregatesProcessor{
		txnAggregatesClient: txnAggregatesClient,
	}
}

func (p *TxnAggregatesProcessor) GetTxnAggregatesForActorByTime(ctx context.Context, actorId string, startTimestamp, endTimestamp *timestampPb.Timestamp) (*txnAggregatesPb.TransactionAggregates, error) {
	txnAggregatesReq := &txnAggregatesPb.GetTransactionAggregatesRequest{
		ActorId:          actorId,
		FromExecutedTime: startTimestamp,
		ToExecutedTime:   endTimestamp,
	}
	txnAggregatesRes, err := p.txnAggregatesClient.GetTransactionAggregates(ctx, txnAggregatesReq)
	switch {
	case err != nil:
		return nil, fmt.Errorf("transaction aggregate details can't be fetched due to rpc failure: actorId: %s : %w", actorId, err)
	case !txnAggregatesRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("get transaction aggregates returned non-success status")
	}

	return txnAggregatesRes.GetTransactionAggregates(), nil
}
