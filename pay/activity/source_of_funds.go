package activity

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/accounts/statement"
	iftActivityPb "github.com/epifi/gamma/api/pay/activity/internationalfundtransfer"
	ift "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	"github.com/epifi/be-common/pkg/logger"
)

// nolint: funlen
func (p *Processor) GenerateSOFDocument(ctx context.Context, req *iftActivityPb.GenerateSOFDocumentRequest) (*iftActivityPb.GenerateSOFDocumentResponse, error) {
	lg := activity.GetLogger(ctx)
	var res = &iftActivityPb.GenerateSOFDocumentResponse{}

	// todo: if client req id is in header why are you passing again?
	order, err := p.orderDao.GetByClientReqId(ctx, req.GetClientReqId().GetId())
	if err != nil {
		lg.Error("error while getting the order from client req id during A2 form generation", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	account, err := p.savingsProcessor.GetSavingsAccountForActor(ctx, order.GetFromActorId())
	if err != nil {
		lg.Error("unable to get savings account", zap.String("actor id", order.GetFromActorId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	// checking if valid SOF document is present or not
	sofDocResp, err := p.iftClient.GetSOFDocument(ctx, &ift.GetSOFDocumentRequest{ActorId: order.GetFromActorId()})
	if te := epifigrpc.RPCError(sofDocResp, err); te != nil {
		if sofDocResp.GetStatus().GetCode() != rpcPb.StatusRecordNotFound().GetCode() {
			lg.Error("error while checking sof doc is present or not", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}
	}

	// if document is already present then will send SOF path and sof present flag
	if sofDocResp.GetStatus().GetCode() == rpcPb.StatusOk().GetCode() {
		res.IsSofPresent = true
		res.SofDocumentUrl = sofDocResp.GetUrl()
		return res, nil
	}

	// generate new uuid for generating statement api.
	// Note: in case of activity retries, duplicate statement generation would be triggered with a new clientRequestId.
	// TODO(numan): align with team on how to fix this issue (if we have multiple request send to account statement svc)
	clientReqId := uuid.NewString()

	// time used for to_time will be order created at time since the txn can happen before the wf starts and we need to
	// avoid all txns after the ift txn (incl) in all calculations (remittance so far and loan detection)
	toTime := order.GetCreatedAt()

	fromDate := datetime.TimeToDateInLoc(toTime.AsTime().AddDate(0, -int(req.GetAccountStatementTimeInMonths()), 0), datetime.IST)
	toDate := datetime.TimeToDateInLoc(toTime.AsTime(), datetime.IST)
	genStatementRes, err := p.accountStatementClient.GenerateAccountStatement(ctx, &statement.GenerateAccountStatementRequest{
		AccountId:          account.GetId(),
		FromDate:           fromDate,
		ToDate:             toDate,
		Format:             statement.StatementFormat_PDF,
		IsMonthlyStatement: false,
		AccountType:        accounts.Type_SAVINGS,
		StatementDeliveryOption: &statement.StatementDeliveryOption{
			StatementDeliveryOption: &statement.StatementDeliveryOption_SendToWorkflow{
				SendToWorkflow: &statement.SendToWorkflowUsingSignalDetails{
					WfReqId:    req.GetWfReqId(),
					SignalName: string(payNs.IFTSOFFormGenerationStatusSignal),
					Ownership:  commontypes.Ownership_EPIFI_TECH,
				}}},
		ClientReqId: clientReqId,
		RequestType: statement.RequestType_US_STOCK,
		AddressType: types.AddressType_PERMANENT,
	})

	if rpcErr := epifigrpc.RPCError(genStatementRes, err); rpcErr != nil {
		lg.Error("unable to initiate statement generation", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, rpcErr.Error())
	}

	res.StatementGenRequestId = clientReqId
	res.SofDocInfo = &ift.SOFDocumentInfo{
		Info: &ift.SOFDocumentInfo_FiAccountDocInfo{
			FiAccountDocInfo: &ift.FiAccountDocInfo{
				FromTime:           timestamppb.New(time.Date(int(fromDate.GetYear()), time.Month(fromDate.GetMonth()), int(fromDate.GetDay()), 0, 0, 0, 0, datetime.IST)),
				ToTime:             toTime,
				FiSavingsAccountId: account.GetId(),
				TempDocUrl:         "", // to be populated post getStatement activity which returns the doc url
			},
		},
	}

	return res, nil
}

func (p *Processor) UploadSOFDocument(ctx context.Context, req *iftActivityPb.UploadSOFDocumentRequest) (*iftActivityPb.UploadSOFDocumentResponse, error) {
	var (
		res = &iftActivityPb.UploadSOFDocumentResponse{}
		err error
	)
	lg := activity.GetLogger(ctx)

	order, err := p.orderDao.GetByClientReqId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		lg.Error("error while getting the order from client req id during A2 form generation", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	if p.iftConf.IsSofAnalysisFlowActive && req.GetSofDocInfo() != nil {
		err = p.initSofDetailsWithDocParamsForFiAccStatement(ctx, order.GetFromActorId(), req.GetSofDocInfo())
		if err != nil {
			lg.Error("failed to init sof details with doc params for fi acc statement", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
	}

	resp, err := p.iftClient.UploadSOFDocument(ctx, &ift.UploadSOFDocumentRequest{
		ActorId:         order.GetFromActorId(),
		SignedUrl:       req.GetDocumentUrl(),
		SofDocumentType: ift.SOFDocumentType_SOF_DOCUMENT_TYPE_FI_ACCOUNT_STATEMENT,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		lg.Error("error while uploading sof document", zap.Error(te))
		return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
	}
	return res, nil
}

func (p *Processor) initSofDetailsWithDocParamsForFiAccStatement(ctx context.Context, actorId string, docInfo *ift.SOFDocumentInfo) error {
	// first leg of storing sof. temp url is stored in sof details which is expected to be explicitely submitted by user as sof proof
	// later in the flow. temp url is used to validate that user is submitting the same doc that we have generated here
	initSofDetailsResp, createSofErr := p.iftClient.InitSofDetailsDocumentParams(ctx, &ift.InitSofDetailsDocumentParamsRequest{
		ActorId:         actorId,
		SofDocumentType: ift.SOFDocumentType_SOF_DOCUMENT_TYPE_FI_ACCOUNT_STATEMENT,
		DocumentInfo:    docInfo,
		// valid_till and doc_url will be populated once the document is uploaded in next step
	})
	if rpcErr := epifigrpc.RPCError(initSofDetailsResp, createSofErr); rpcErr != nil {
		return fmt.Errorf("failed to init sof details entry for user : %w", rpcErr)
	}
	return nil
}
