package unitofwork

import (
	"context"
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/protobuf/proto"

	mPb "github.com/epifi/gamma/api/merchant"
)

func TestController_GetAccountMerchantInfo(t *testing.T) {
	c, mockDao, deferFun := getMockDaoService(t, cts.conf)
	defer deferFun()

	type mockGetAccountMerchantInfoDao struct {
		enable bool
		piId   string
		res    *mPb.AccountMerchantInfo
		err    error
	}

	type args struct {
		ctx  context.Context
		piId string
	}
	tests := []struct {
		name                          string
		args                          args
		mockGetAccountMerchantInfoDao mockGetAccountMerchantInfoDao
		want                          *mPb.AccountMerchantInfo
		wantErr                       bool
	}{
		{
			name: "successful fetch for account merchant",
			args: args{
				ctx:  context.Background(),
				piId: "pi-id",
			},
			mockGetAccountMerchantInfoDao: mockGetAccountMerchantInfoDao{
				enable: true,
				piId:   "pi-id",
				res: &mPb.AccountMerchantInfo{
					Id:   "info-id",
					PiId: "pi-id",
					Mcc:  "mcc",
				},
				err: nil,
			},
			want: &mPb.AccountMerchantInfo{
				Id:   "info-id",
				PiId: "pi-id",
				Mcc:  "mcc",
			},
			wantErr: false,
		},
		{
			name: "record not found fetch for account merchant",
			args: args{
				ctx:  context.Background(),
				piId: "pi-id",
			},
			mockGetAccountMerchantInfoDao: mockGetAccountMerchantInfoDao{
				enable: true,
				piId:   "pi-id",
				res:    nil,
				err:    epifierrors.ErrRecordNotFound,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetAccountMerchantInfoDao.enable {
				mockDao.mockAccountMerchantInfoDao.EXPECT().GetByPiId(tt.args.ctx, tt.mockGetAccountMerchantInfoDao.piId).
					Return(tt.mockGetAccountMerchantInfoDao.res, tt.mockGetAccountMerchantInfoDao.err)
			}

			got, err := c.GetAccountMerchantInfo(tt.args.ctx, tt.args.piId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountMerchantInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetAccountMerchantInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}
