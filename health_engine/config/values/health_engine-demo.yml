Application:
  Environment: "demo"
  Name: "healthengine"

Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9991
    HttpPProfPort: 9990

AWS:
  Region: "ap-south-1"

Tracing:
  Enable: true

HealthEngineWebhookPublisher:
  QueueName: "demo-health-state-update-queue"

HealthEngineWebhookSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "demo-health-state-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 3
      MaxAttempts: 10
      TimeUnit: "Second"


RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 2
  ClientName: health-engine
  HystrixCommand:
    CommandName: "health_engine_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80
