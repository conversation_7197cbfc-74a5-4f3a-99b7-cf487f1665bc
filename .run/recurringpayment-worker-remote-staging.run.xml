<component name="ProjectRunConfigurationManager">
	<configuration default="false" name="recurringpayment-worker-remote-staging" type="GoApplicationRunConfiguration" factoryName="Go Application">
		<module name="gamma" />
		<working_directory value="$PROJECT_DIR$" />
		<envs>
			<env name="CONFIG_DIR" value="$PROJECT_DIR$/output/worker/recurringpayment/config" />
			<env name="DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP" value="true" />
			<env name="ENVIRONMENT" value="staging" />
			<env name="REMOTE_DEBUG" value="enable" />
			<env name="TEMPORAL_DEBUG" value="true" />
			<env name="AWS_PROFILE" value="epifi-staging" />
			<env name="AWS_SDK_LOAD_CONFIG" value="true" />
			<env name="RELEASE_BRANCH" value="master" />
			<env name="REMOTE_DEBUG_CONFIG" value="{&quot;consumer&quot;:&quot;disable&quot;}" />
		</envs>
		<kind value="PACKAGE" />
		<package value="github.com/epifi/gamma/cmd/worker/recurringpayment" />
		<directory value="$PROJECT_DIR$" />
		<filePath value="$PROJECT_DIR$/cmd/worker/recurringpayment/worker.go" />
		<output_directory value="$PROJECT_DIR$/output/worker/recurringpayment" />
		<method v="2">
			<option name="RunConfigurationTask" enabled="true" run_configuration_name="init_recurringpayment_worker_remote_debug" run_configuration_type="ShConfigurationType" />
		</method>
	</configuration>
</component>
