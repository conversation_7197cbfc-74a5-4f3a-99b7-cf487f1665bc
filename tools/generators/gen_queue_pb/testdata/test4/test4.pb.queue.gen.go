// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/tools/generators/gen_queue_pb/testdata/test4
package test4

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessRequest1Method = "ProcessRequest1"
	ProcessRequest2Method = "ProcessRequest2"
	ProcessRequest3Method = "ProcessRequest3"
	ProcessRequest4Method = "ProcessRequest4"

	ProcessRequest5Method = "ProcessRequest5"
	ProcessRequest6Method = "ProcessRequest6"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &Request1{}
var _ queue.ConsumerRequest = &Request2{}
var _ queue.ConsumerRequest = &Request3{}
var _ queue.ConsumerRequest = &Request4{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *Request1) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.ReqHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *Request2) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.ReqHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *Request3) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.ReqHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *Request4) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.ReqHeader = header
}

// RegisterProcessRequest1MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRequest1MethodToSubscriber(subscriber queue.Subscriber, srv Test1Server) {
	subscriber.RegisterService(&Test1_ServiceDesc, srv, ProcessRequest1Method)
}

// RegisterProcessRequest2MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRequest2MethodToSubscriber(subscriber queue.Subscriber, srv Test1Server) {
	subscriber.RegisterService(&Test1_ServiceDesc, srv, ProcessRequest2Method)
}

// RegisterProcessRequest3MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRequest3MethodToSubscriber(subscriber queue.Subscriber, srv Test1Server) {
	subscriber.RegisterService(&Test1_ServiceDesc, srv, ProcessRequest3Method)
}

// RegisterProcessRequest4MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRequest4MethodToSubscriber(subscriber queue.Subscriber, srv Test1Server) {
	subscriber.RegisterService(&Test1_ServiceDesc, srv, ProcessRequest4Method)
}

// RegisterProcessRequest5MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRequest5MethodToSubscriber(subscriber queue.Subscriber, srv Test2Server) {
	subscriber.RegisterService(&Test2_ServiceDesc, srv, ProcessRequest5Method)
}

// RegisterProcessRequest6MethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRequest6MethodToSubscriber(subscriber queue.Subscriber, srv Test2Server) {
	subscriber.RegisterService(&Test2_ServiceDesc, srv, ProcessRequest6Method)
}
