// Code generated by MockGen. DO NOT EDIT.
// Source: comms/validator/send_message_validator.go

// Package mock_validator is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	comms "github.com/epifi/gamma/api/comms"
	gomock "github.com/golang/mock/gomock"
)

// MockICommsSendMessageValidator is a mock of ICommsSendMessageValidator interface.
type MockICommsSendMessageValidator struct {
	ctrl     *gomock.Controller
	recorder *MockICommsSendMessageValidatorMockRecorder
}

// MockICommsSendMessageValidatorMockRecorder is the mock recorder for MockICommsSendMessageValidator.
type MockICommsSendMessageValidatorMockRecorder struct {
	mock *MockICommsSendMessageValidator
}

// NewMockICommsSendMessageValidator creates a new mock instance.
func NewMockICommsSendMessageValidator(ctrl *gomock.Controller) *MockICommsSendMessageValidator {
	mock := &MockICommsSendMessageValidator{ctrl: ctrl}
	mock.recorder = &MockICommsSendMessageValidatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICommsSendMessageValidator) EXPECT() *MockICommsSendMessageValidatorMockRecorder {
	return m.recorder
}

// Validate mocks base method.
func (m *MockICommsSendMessageValidator) Validate(ctx context.Context, req *comms.SendMessageRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockICommsSendMessageValidatorMockRecorder) Validate(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockICommsSendMessageValidator)(nil).Validate), ctx, req)
}
