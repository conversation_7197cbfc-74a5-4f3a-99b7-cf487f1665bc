package best_effort

import (
	"fmt"

	pb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/comms/service/best_effort/sender"
	"github.com/epifi/gamma/comms/service/processor"
)

type SenderFactory struct {
	smsSender          *sender.SMSSender
	emailSender        *sender.EmailSender
	notificationSender *sender.NotificationSender
	whatsappSender     *sender.WhatsappSender
}

func NewSenderFactory(smsSender *sender.SMSSender, emailSender *sender.EmailSender, notificationSender *sender.NotificationSender,
	whatsappSender *sender.WhatsappSender) *SenderFactory {
	return &SenderFactory{
		smsSender:          smsSender,
		emailSender:        emailSender,
		notificationSender: notificationSender,
		whatsappSender:     whatsappSender,
	}
}

func (factory *SenderFactory) GetMessageSender(medium pb.Medium) (processor.MessageSender, error) {
	switch medium {
	case pb.Medium_SMS:
		return factory.smsSender, nil
	case pb.Medium_EMAIL:
		return factory.emailSender, nil
	case pb.Medium_NOTIFICATION:
		return factory.notificationSender, nil
	case pb.Medium_WHATSAPP:
		return factory.whatsappSender, nil
	}
	return nil, fmt.Errorf("cannot get sender instance from sender factory, medium: %s", medium)
}
