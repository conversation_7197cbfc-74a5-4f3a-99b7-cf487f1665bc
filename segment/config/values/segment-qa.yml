Application:
  Environment: "qa"
  Name: "segment"

# Segment service is actually initialized on the port defined in user-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Segment service to be running on a
# different port in the user server
Server:
  Ports:
    GrpcPort: 8083
    GrpcSecurePort: 9521
    HttpPort: 9999

SegmentDb:
  AppName: "segment"
  StatementTimeout: 5s
  Name: "segment"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/segment"
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"

AWS:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  HystrixCommand:
    CommandName: "segment_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Tracing:
  Enable: true

PaginationOptions:
  MaxPageSize: 1000

AWSPinpointOptions:
  ApplicationId: "4bc87be6c57f455494fda4bd978670ed"
  S3RoleARN: "arn:aws:iam::************:role/non-prod-to-data-dev-s3-role"
  AssumeRoleARN: "arn:aws:iam::************:role/non-prod-to-data-dev-pinpoint-role"
  S3UrlPrefix: "s3://epifi-data-rewards-dev/segment-exports"

TriggerSegmentExportPublisher:
  QueueName: "qa-segment-trigger-segment-export-queue"

TriggerSegmentExportSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 1
  QueueName: "qa-segment-trigger-segment-export-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 30s
    Namespace: "segment"

PollSegmentExportPublisher:
  QueueName: "qa-segment-poll-segment-export-queue"

PollSegmentExportSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-segment-poll-segment-export-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"

ProcessSegmentExportPartFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-segment-process-segment-export-part-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 16
      TimeUnit: "Second"

ProcessSegmentExportPartFileV2Subscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-segment-process-segment-export-part-file-v2-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 16
      TimeUnit: "Second"

UploadSegmentExportPartFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-segment-upload-segment-export-part-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 16
      TimeUnit: "Second"

UploadSegmentExportPartFilePublisher:
  QueueName: "qa-segment-upload-segment-export-part-file-queue"

CompareSegmentInstancesPublisher:
  QueueName: "qa-segment-compare-segment-instances-queue"

CompareSegmentInstancesSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-segment-compare-segment-instances-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 2
      TimeUnit: "Second"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SegmentExportPartFileConfig:
  # epifi-raw-dev bucket iam access should be given to required prefix path only due to sensitive data on bucket.
  S3BucketName: "epifi-raw-dev"
  S3PathPrefix: "qa/data/vendor/segmentation_service"
