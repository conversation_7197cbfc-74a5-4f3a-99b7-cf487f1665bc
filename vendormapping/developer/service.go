package developer

import (
	"context"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/vendormapping/developer"
	"github.com/epifi/be-common/pkg/logger"
)

type VendorMappingDev struct {
	fac *DevFactory
}

func NewVendorMappingDevService(fac *DevFactory) *VendorMappingDev {
	return &VendorMappingDev{
		fac: fac,
	}
}

func (c *VendorMappingDev) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		// sending only BE mapping entity list because of privacy concerns around exposing DP mapping in db states. Ref - https://monorail.pointz.in/p/fi-app/issues/detail?id=12567
		EntityList: []string{developer.VendorMappingEntity_BE_MAPPING.String()},
	}, nil
}

func (c *VendorMappingDev) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.VendorMappingEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in user"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.VendorMappingEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in vendor mapping")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.VendorMappingEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *VendorMappingDev) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.VendorMappingEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in vendor mapping"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.VendorMappingEntity(ent))
	if err != nil {
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.VendorMappingEntity(ent), req.GetFilters())
	if err != nil {
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
