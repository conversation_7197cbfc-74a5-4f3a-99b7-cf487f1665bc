package consumer

import (
	"context"
	"errors"
	"flag"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	userPb "github.com/epifi/gamma/api/user"
	mocksUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/data"
	"github.com/epifi/gamma/connectedaccount/data/consentorchestrator"
	caError "github.com/epifi/gamma/connectedaccount/error"
	account_processor "github.com/epifi/gamma/connectedaccount/test/mocks/mock_account_processor"
	"github.com/epifi/gamma/connectedaccount/typedef"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/events"
	mock_events "github.com/epifi/be-common/pkg/events/mocks"

	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caNotiPb "github.com/epifi/gamma/api/connected_account/notification"
	vnPb "github.com/epifi/gamma/api/vendornotification/aa"
	"github.com/epifi/gamma/api/vendors/aa"
	"github.com/epifi/gamma/connectedaccount/consent"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/notification"
	"github.com/epifi/gamma/connectedaccount/test"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_account_consent_orchestrator"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_consent_manager"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_dao"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_notification"
)

var (
	acc1 = &caPb.Accounts{AccountList: []*caPb.Account{
		{
			FiType:          caEnumPb.FIType_FI_TYPE_DEPOSIT,
			FipId:           "HDFC-FIP",
			AccType:         "SAVINGS",
			LinkRefNumber:   "test-link-ref-number",
			MaskedAccNumber: "XXXXXXX989",
		},
	}}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

func TestMain(m *testing.M) {
	flag.Parse()
	testServer := test.InitTestServer()
	pgdb = testServer.PgDB
	dynconf = testServer.DynConf
	mockTxnExecutor = storagev2.NewGormTxnExecutor(pgdb)
	exitCode := m.Run()
	testServer.Cleanup()
	os.Exit(exitCode)
}

func TestCaConsumer_ProcessNotification(t *testing.T) {
	var (
		testComm1 = &caNotiPb.CaComms{
			CommsType: caEnumPb.CommsType_COMMS_TYPE_CONSENT_PAUSED_FROM_AA,
			CommsParams: &caNotiPb.CaComms_ConsentPausedFromAaParams{
				ConsentPausedFromAaParams: &caNotiPb.ConsentPausedFromAaParams{
					Consent: &caPb.Consent{
						ConsentId: "consent-id-1",
					},
				},
			},
			ActorId: "actor-id-1",
		}
		testComm2 = &caNotiPb.CaComms{
			CommsType: caEnumPb.CommsType_COMMS_TYPE_CONSENT_PAUSED_FROM_AA,
			CommsParams: &caNotiPb.CaComms_ConsentPausedFromAaParams{
				ConsentPausedFromAaParams: &caNotiPb.ConsentPausedFromAaParams{
					Consent: &caPb.Consent{
						ConsentId: "consent-id-2",
					},
				},
			},
		}
		testComm3 = &caNotiPb.CaComms{
			CommsParams: &caNotiPb.CaComms_ConsentPausedFromAaParams{
				ConsentPausedFromAaParams: &caNotiPb.ConsentPausedFromAaParams{
					Consent: &caPb.Consent{
						ConsentId: "consent-id-3",
					},
				},
			},
			ActorId: "actor-id-3",
		}
	)

	ctr := gomock.NewController(t)
	mockSender := mock_notification.NewMockSender(ctr)
	defer ctr.Finish()

	type fields struct {
		notifier notification.Sender
	}
	type args struct {
		ctx                        context.Context
		processNotificationRequest *caCoPb.ProcessNotificationRequest
		mocks                      []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caCoPb.ProcessNotificationResponse
		wantErr bool
	}{
		{
			name: "successfully processed",
			fields: fields{
				notifier: mockSender,
			},
			args: args{
				ctx: context.Background(),
				processNotificationRequest: &caCoPb.ProcessNotificationRequest{
					CaComms: testComm1,
				},
				mocks: []interface{}{
					mockSender.EXPECT().Send(gomock.Any(), testComm1).Return(nil),
				},
			},
			want:    &caCoPb.ProcessNotificationResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "failure - missing comms",
			fields: fields{
				notifier: mockSender,
			},
			args: args{
				ctx:                        context.Background(),
				processNotificationRequest: &caCoPb.ProcessNotificationRequest{},
				mocks:                      []interface{}{},
			},
			want:    &caCoPb.ProcessNotificationResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "failure - missing actor id",
			fields: fields{
				notifier: mockSender,
			},
			args: args{
				ctx: context.Background(),
				processNotificationRequest: &caCoPb.ProcessNotificationRequest{
					CaComms: testComm2,
				},
				mocks: []interface{}{},
			},
			want:    &caCoPb.ProcessNotificationResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "failure - missing comms type",
			fields: fields{
				notifier: mockSender,
			},
			args: args{
				ctx: context.Background(),
				processNotificationRequest: &caCoPb.ProcessNotificationRequest{
					CaComms: testComm3,
				},
				mocks: []interface{}{},
			},
			want:    &caCoPb.ProcessNotificationResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaConsumer{
				notifier:    tt.fields.notifier,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.ProcessNotification(tt.args.ctx, tt.args.processNotificationRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessNotification() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.GetResponseHeader() != tt.want.GetResponseHeader() {
				t.Errorf("ProcessNotification() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaConsumer_ProcessConsent(t *testing.T) {
	ctr := gomock.NewController(t)
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctr)
	mockCManager := mock_consent_manager.NewMockManager(ctr)
	defer ctr.Finish()
	type fields struct {
		crDao    dao.ConsentRequestDao
		cManager consent.Manager
	}
	type args struct {
		ctx                   context.Context
		processConsentRequest *caCoPb.ProcessConsentRequest
		mocks                 []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caCoPb.ProcessConsentResponse
		wantErr bool
	}{
		{
			name: "failed - no consent request id",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx:                   context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{},
				mocks:                 []interface{}{},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: permanentFailureHeader,
			},
			wantErr: false,
		},
		{
			name: "fail - consent req id not found in DB",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx:                   context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{ConsentRequestId: "consent-req-id-2"},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-2").Return(
						nil, epifierrors.ErrRecordNotFound,
					),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: permanentFailureHeader,
			},
			wantErr: false,
		},
		{
			name: "successfully processed consent",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx:                   context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{ConsentRequestId: "consent-req-id-3"},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-3").Return(
						&caPb.ConsentRequest{
							Id:     "consent-req-id-3",
							Status: caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_SUCCESS,
						}, nil,
					),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: successHeader,
			},
			wantErr: false,
		},
		{
			name: "fail - FetchConsent error",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx:                   context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{ConsentRequestId: "consent-req-id-4"},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-4").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-4",
							ConsentHandle: "consent-handle-4",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-4", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-4", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_UNSPECIFIED, errors.New("error"),
					),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: transientFailureHeader,
			},
			wantErr: false,
		},
		{
			name: "fail - FetchConsent returned CONSENT_HANDLE_STATUS_FAILED",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx: context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{
					ConsentRequestId: "consent-req-id-5",
					RequestHeader:    &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-5").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-5",
							ConsentHandle: "consent-handle-5",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-5", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-5", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED, nil,
					),
					mockCrDao.EXPECT().UpdateById(gomock.Any(), "consent-req-id-5", gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: successHeader,
			},
			wantErr: false,
		},
		{
			name: "fail - FetchConsent returned CONSENT_HANDLE_STATUS_PENDING, non last attempt case",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx: context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{
					ConsentRequestId: "consent-req-id-5",
					RequestHeader:    &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-5").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-5",
							ConsentHandle: "consent-handle-5",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-5", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-5", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING, nil,
					),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: transientFailureHeader,
			},
			wantErr: false,
		},
		{
			name: "fail - FetchConsent returned CONSENT_HANDLE_STATUS_PENDING, last attempt case",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx: context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{
					ConsentRequestId: "consent-req-id-5",
					RequestHeader:    &queuePb.ConsumerRequestHeader{IsLastAttempt: true},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-5").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-5",
							ConsentHandle: "consent-handle-5",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-5", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-5", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING, nil,
					),
					mockCrDao.EXPECT().UpdateById(gomock.Any(), "consent-req-id-5", gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: successHeader,
			},
			wantErr: false,
		},
		{
			name: "success",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx: context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{
					ConsentRequestId: "consent-req-id-6",
					RequestHeader:    &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-6").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-6",
							ConsentHandle: "consent-handle-6",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-6", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-6", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY, nil,
					),
					mockCManager.EXPECT().StoreConsent(context.Background(), "consent-req-id-6", "consent-id-6").Return(
						&caPb.Consent{ConsentId: "consent-id-6"}, nil,
					),
					mockCrDao.EXPECT().UpdateById(gomock.Any(), "consent-req-id-6", gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: successHeader,
			},
			wantErr: false,
		},
		{
			name: "success, error(unique constraint violation) while storing consent",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx: context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{
					ConsentRequestId: "consent-req-id-7",
					RequestHeader:    &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-7").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-7",
							ConsentHandle: "consent-handle-7",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-7", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-7", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY, nil,
					),
					mockCManager.EXPECT().StoreConsent(context.Background(), "consent-req-id-7", "consent-id-7").Return(
						nil, epifierrors.ErrUniqueConstraintViolation,
					),
					mockCrDao.EXPECT().UpdateById(gomock.Any(), "consent-req-id-7", gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: permanentFailureHeader,
			},
			wantErr: false,
		},
		{
			name: "success, error(unique constraint violation) while storing consent",
			fields: fields{
				crDao:    mockCrDao,
				cManager: mockCManager,
			},
			args: args{
				ctx: context.Background(),
				processConsentRequest: &caCoPb.ProcessConsentRequest{
					ConsentRequestId: "consent-req-id-7",
					RequestHeader:    &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().Get(gomock.Any(), "consent-req-id-7").Return(
						&caPb.ConsentRequest{
							Id:            "consent-req-id-7",
							ConsentHandle: "consent-handle-7",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
							AaEntity:      caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						}, nil,
					),
					mockCManager.EXPECT().FetchConsent(context.Background(), "consent-handle-7", caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY).Return(
						"consent-id-7", caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY, nil,
					),
					mockCManager.EXPECT().StoreConsent(context.Background(), "consent-req-id-7", "consent-id-7").Return(
						nil, errors.New("some error"),
					),
					mockCrDao.EXPECT().UpdateById(gomock.Any(), "consent-req-id-7", gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want: &caCoPb.ProcessConsentResponse{
				ResponseHeader: transientFailureHeader,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaConsumer{
				crDao:       tt.fields.crDao,
				cManager:    tt.fields.cManager,
				txnExecutor: mockTxnExecutor,
			}
			got, err := c.ProcessConsent(tt.args.ctx, tt.args.processConsentRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessConsent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.GetResponseHeader().GetStatus() != tt.want.GetResponseHeader().GetStatus() {
				t.Errorf("ProcessConsent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaConsumer_ProcessConsentCallback(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctrl)
	mockAcw := mock_account_consent_orchestrator.NewMockAccountConsentOrchestrator(ctrl)
	mockBroker := mock_events.NewMockBroker(ctrl)
	mockSender := mock_notification.NewMockSender(ctrl)

	defer ctrl.Finish()
	type fields struct {
		crDao                 dao.ConsentRequestDao
		accountConsentWrapper consentorchestrator.AccountConsentOrchestrator
		eventBroker           events.Broker
		notifier              notification.Sender
	}
	type args struct {
		ctx     context.Context
		request *vnPb.ConsentEvent
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caCoPb.ProcessConsentCallbackResponse
		wantErr bool
	}{
		{
			name: "#1 fail - consent notification missing",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx:     context.Background(),
				request: &vnPb.ConsentEvent{},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#2 fail - consent status notification missing",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{},
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#3 fail - consentHandle is empty",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{},
					},
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#4 consentRequest not found in DB",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-4",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-4").Return(
						nil, epifierrors.ErrRecordNotFound,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#5 consentRequest returned internal",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-4",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-4").Return(
						nil, errors.New("internal"),
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#6 consentHandle pending",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-6",
							ConsentId:     "consentId",
							ConsentStatus: caPkg.ConsentHandlePending,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-6").Return(
						&caPb.ConsentRequest{ConsentHandle: "consent-handle-6"}, nil,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#7 consentHandle pending",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-6",
							ConsentId:     "consentId",
							ConsentStatus: caPkg.ConsentHandleFailed,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-6").Return(
						&caPb.ConsentRequest{ConsentHandle: "consent-handle-6"}, nil,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#8 consentStatus is Rejected",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-6",
							ConsentStatus: caPkg.ConsentStatusRejected,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-6").Return(
						&caPb.ConsentRequest{ConsentHandle: "consent-handle-6"}, nil,
					),
					mockCrDao.EXPECT().UpdateByConsentHandle(gomock.Any(), "consent-handle-6", gomock.Any(), gomock.Any()).Return(
						nil,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#9 fail - error updating consent",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-8",
							ConsentId:     "consent-id-8",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-8").Return(
						&caPb.ConsentRequest{
							Id:            "id-8",
							ConsentHandle: "consent-handle-8",
							ConsentId:     "consent-id-8",
						}, nil,
					),
					mockAcw.EXPECT().CreateOrUpdateConsent(gomock.Any(), "id-8", "consent-id-8").Return(
						nil, errors.New("error"),
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#10 fail - error updating consent",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-8",
							ConsentId:     "consent-id-8",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-8").Return(
						&caPb.ConsentRequest{
							Id:            "id-8",
							ConsentHandle: "consent-handle-8",
							ConsentId:     "consent-id-8",
						}, nil,
					),
					mockAcw.EXPECT().CreateOrUpdateConsent(gomock.Any(), "id-8", "consent-id-8").Return(
						nil, epifierrors.ErrDuplicateEntry,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#11 fail - error updating consent",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-8",
							ConsentId:     "consent-id-8",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-8").Return(
						&caPb.ConsentRequest{
							Id:            "id-8",
							ConsentHandle: "consent-handle-8",
							ConsentId:     "consent-id-8",
						}, nil,
					),
					mockAcw.EXPECT().CreateOrUpdateConsent(gomock.Any(), "id-8", "consent-id-8").Return(
						nil, epifierrors.ErrUniqueConstraintViolation,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#12 success, cosent request already in success state",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
				notifier:              mockSender,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-9",
							ConsentId:     "consent-id-9",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-9").Return(
						&caPb.ConsentRequest{
							Id:            "id-9",
							ConsentHandle: "consent-handle-9",
							ConsentId:     "consent-id-9",
							Status:        caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_SUCCESS,
						}, nil,
					),
					mockAcw.EXPECT().CreateOrUpdateConsent(gomock.Any(), "id-9", "consent-id-9").Return(
						&caPb.Consent{
							Id:            "id-9",
							ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
							ActorId:       "actor-id-9",
						}, nil,
					),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#13 success",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
				notifier:              mockSender,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-9",
							ConsentId:     "consent-id-9",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-9").Return(
						&caPb.ConsentRequest{
							Id:            "id-9",
							ConsentHandle: "consent-handle-9",
							ConsentId:     "consent-id-9",
						}, nil,
					),
					mockAcw.EXPECT().CreateOrUpdateConsent(gomock.Any(), "id-9", "consent-id-9").Return(
						&caPb.Consent{
							Id:            "id-9",
							ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
							ActorId:       "actor-id-9",
						}, nil,
					),
					mockCrDao.EXPECT().UpdateByConsentHandle(gomock.Any(), "consent-handle-9", gomock.Any(), gomock.Any()).
						Return(nil),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#14 error updating consent request",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
				notifier:              mockSender,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-11",
							ConsentId:     "consent-id-11",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-11").Return(
						&caPb.ConsentRequest{
							Id:            "id-11",
							ConsentHandle: "consent-handle-11",
							ConsentId:     "consent-id-11",
						}, nil,
					),
					mockAcw.EXPECT().CreateOrUpdateConsent(gomock.Any(), "id-11", "consent-id-11").Return(
						&caPb.Consent{
							Id:            "id-11",
							ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
							ActorId:       "actor-id-11",
						}, nil,
					),
					mockCrDao.EXPECT().UpdateByConsentHandle(gomock.Any(), "consent-handle-11", gomock.Any(), gomock.Any()).
						Return(errors.New("received some DB error")),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#15 success, process auth handle",
			fields: fields{
				crDao:                 mockCrDao,
				accountConsentWrapper: mockAcw,
				eventBroker:           mockBroker,
				notifier:              mockSender,
			},
			args: args{
				ctx: context.Background(),
				request: &vnPb.ConsentEvent{
					ConsentNotification: &aa.ConsentNotification{
						ConsentStatusNotification: &aa.ConsentStatusNotification{
							ConsentHandle: "consent-handle-10",
							ConsentId:     "consent-id-10",
							ConsentStatus: caPkg.ConsentActive,
						},
					},
				},
				mocks: []interface{}{
					mockCrDao.EXPECT().GetByConsentHandle(gomock.Any(), "consent-handle-10").Return(
						&caPb.ConsentRequest{
							Id:                    "id-10",
							ConsentHandle:         "consent-handle-10",
							ConsentId:             "consent-id-10",
							ConsentRequestPurpose: caEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW,
						}, nil,
					),
					mockCrDao.EXPECT().UpdateByConsentHandle(gomock.Any(), "consent-handle-10", gomock.Any(), gomock.Any()).
						Return(nil),
					mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes(),
				},
			},
			want:    &caCoPb.ProcessConsentCallbackResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaConsumer{
				crDao:                 tt.fields.crDao,
				accountConsentWrapper: tt.fields.accountConsentWrapper,
				eventBroker:           tt.fields.eventBroker,
				notifier:              tt.fields.notifier,
				txnExecutor:           mockTxnExecutor,
			}
			got, err := c.ProcessConsentCallback(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessConsentCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.GetResponseHeader().GetStatus() != tt.want.GetResponseHeader().GetStatus() {
				t.Errorf("ProcessNotification() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// func TestCaConsumer_CaptureHeartbeatAndSendNotification(t *testing.T) {
//
// 	ctr := gomock.NewController(t)
// 	mockSender := mock_notification.NewMockSender(ctr)
// 	mockHeartbeatDao := mock_dao.NewMockAaUserHeartbeatDao(ctr)
// 	mockHeartbeatClient := mocks.NewMockHeartbeatClient(ctr)
// 	defer ctr.Finish()
//
// 	type fields struct {
// 		notifier     notification.Sender
// 		heartbeatDao dao.AaUserHeartbeatDao
// 		conf         *genconf.Config
// 	}
// 	type args struct {
// 		ctx                                        context.Context
// 		captureHeartbeatAndSendNotificationRequest *caCoPb.CaptureHeartbeatAndSendNotificationRequest
// 		mocks                                      []interface{}
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		want    *caCoPb.CaptureHeartbeatAndSendNotificationResponse
// 		wantErr bool
// 	}{
// 		{
// 			name: "successfully processed",
// 			fields: fields{
// 				notifier:     mockSender,
// 				heartbeatDao: mockHeartbeatDao,
// 				conf:         dynconf,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					AaEntity:           caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil),
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHeartbeatDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 					mockHeartbeatClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpc.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: successHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure - missing actor id",
// 			fields: fields{
// 				notifier: mockSender,
// 				conf:     dynconf,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{},
// 				mocks: []interface{}{},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: permanentFailureHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure - actor id not present in db",
// 			fields: fields{
// 				notifier:     mockSender,
// 				conf:         dynconf,
// 				heartbeatDao: mockHeartbeatDao,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: permanentFailureHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure - some other error in db",
// 			fields: fields{
// 				notifier:     mockSender,
// 				conf:         dynconf,
// 				heartbeatDao: mockHeartbeatDao,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, errors.New("some other error")),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: transientFailureHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "packet already consumed",
// 			fields: fields{
// 				notifier:     mockSender,
// 				conf:         dynconf,
// 				heartbeatDao: mockHeartbeatDao,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_DONE,
// 					}, nil),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: successHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "failure: send message failed",
// 			fields: fields{
// 				notifier:     mockSender,
// 				conf:         dynconf,
// 				heartbeatDao: mockHeartbeatDao,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					AaEntity:           caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(errors.New("some error")),
// 					mockHeartbeatClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpc.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_UP,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: transientFailureHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "aa still down",
// 			fields: fields{
// 				notifier:     mockSender,
// 				conf:         dynconf,
// 				heartbeatDao: mockHeartbeatDao,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					AaEntity:           caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHeartbeatClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpc.StatusOk(),
// 						HeartbeatStat: &heartbeat.HeartbeatStat{
// 							HeartbeatStatus: heartbeat.HeartbeatStatus_HEARTBEAT_STATUS_DOWN,
// 						},
// 					}, nil),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: transientFailureHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "check if packet is stale",
// 			fields: fields{
// 				notifier:     mockSender,
// 				conf:         dynconf,
// 				heartbeatDao: mockHeartbeatDao,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId: "actor-id-1",
// 					PublishedTimestamp: &timestampPb.Timestamp{
// 						Seconds: 1,
// 					},
// 				},
// 				mocks: []interface{}{},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: permanentFailureHeader},
// 			wantErr: false,
// 		},
// 		{
// 			name: "hearbeat service is down",
// 			fields: fields{
// 				notifier:     mockSender,
// 				heartbeatDao: mockHeartbeatDao,
// 				conf:         dynconf,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				captureHeartbeatAndSendNotificationRequest: &caCoPb.CaptureHeartbeatAndSendNotificationRequest{
// 					ActorId:            "actor-id-1",
// 					AaEntity:           caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
// 					PublishedTimestamp: timestampPb.Now(),
// 				},
// 				mocks: []interface{}{
// 					mockHeartbeatDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&caPb.AaUserHeartbeat{
// 						ActorId:            "actor-id-1",
// 						NotificationStatus: caEnumPb.AaUserHeartbeatNotificationStatus_AA_USER_HEARTBEAT_NOTIFICATION_STATUS_IN_PROGRESS,
// 					}, nil),
// 					mockHeartbeatClient.EXPECT().GetHeartbeatStatus(gomock.Any(), gomock.Any()).Return(&heartbeat.GetHeartbeatStatusResponse{
// 						Status: rpc.StatusInternal(),
// 					}, nil),
// 				},
// 			},
// 			want:    &caCoPb.CaptureHeartbeatAndSendNotificationResponse{ResponseHeader: transientFailureHeader},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			c := &CaConsumer{
// 				notifier:     tt.fields.notifier,
// 				heartbeatDao: tt.fields.heartbeatDao,
// 				conf:         tt.fields.conf,
// 				txnExecutor:  mockTxnExecutor,
// 			}
// 			got, err := c.CaptureHeartbeatAndSendNotification(tt.args.ctx, tt.args.captureHeartbeatAndSendNotificationRequest)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("CaptureHeartbeatAndSendNotification() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if got.GetResponseHeader().GetStatus() != tt.want.GetResponseHeader().GetStatus() {
// 				t.Errorf("CaptureHeartbeatAndSendNotification() got = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

func TestCaConsumer_ProcessConsentDataRefresh(t *testing.T) {
	ctr := gomock.NewController(t)
	mockConsentDao := mock_dao.NewMockConsentDao(ctr)
	mockDataProcessor := account_processor.NewMockProcessor(ctr)
	mockDfaDao := mock_dao.NewMockDataFetchAttemptDao(ctr)
	mockFetchDataDelayPub := mock_queue.NewMockDelayPublisher(ctr)
	mockAcw := mock_account_consent_orchestrator.NewMockAccountConsentOrchestrator(ctr)
	mockUserClient := mocksUser.NewMockUsersClient(ctr)

	defer ctr.Finish()

	type fields struct {
		cDao                  dao.ConsentDao
		dProcessor            data.Processor
		dfaDao                dao.DataFetchAttemptDao
		fetchDataDelayPub     typedef.FetchDataSqsDelayPublisher
		accountConsentWrapper consentorchestrator.AccountConsentOrchestrator
		conf                  *genconf.Config
	}
	type args struct {
		ctx     context.Context
		request *caCoPb.ProcessConsentDataRefreshRequest
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *caCoPb.ProcessConsentDataRefreshResponse
		wantErr bool
	}{
		{
			name: "#1 empty consent reference id",
			fields: fields{
				conf: dynconf,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#2 error in consent dao",
			fields: fields{
				conf: dynconf,
				cDao: mockConsentDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in getting consent")),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#2_1 success in consent dao, return consent status revoked",
			fields: fields{
				conf: dynconf,
				cDao: mockConsentDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-2_1"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-2_1", Accounts: acc1,
						ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_REVOKED,
					}, nil)},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#2_2 success in consent dao, return consent status deleted epifi",
			fields: fields{
				conf: dynconf,
				cDao: mockConsentDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-2_2"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-2_2", Accounts: acc1,
						ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_DELETED_EPIFI,
					}, nil)},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#2_3 data refresh should not happen for user whose account is closed",
			fields: fields{
				conf: dynconf,
				cDao: mockConsentDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-2_2"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-2_2", Accounts: acc1,
						ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
					}, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_CLOSED,
						Status:     rpc.StatusOk(),
					}, nil)},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#3 error in consent dao - update by consent id",
			fields: fields{
				conf: dynconf,
				cDao: mockConsentDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in getting consent")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#4 error creating attempt - invalid argument, refresh error",
			fields: fields{
				conf:                  dynconf,
				cDao:                  mockConsentDao,
				dProcessor:            mockDataProcessor,
				accountConsentWrapper: mockAcw,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, caError.ErrInvalidArgument),
					mockAcw.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("refresh error")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},

		{
			name: "#5 error creating attempt - invalid argument, refreshed",
			fields: fields{
				conf:                  dynconf,
				cDao:                  mockConsentDao,
				dProcessor:            mockDataProcessor,
				accountConsentWrapper: mockAcw,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{
						Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour)),
					}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, caError.ErrInvalidArgument),
					mockAcw.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id: "id-1",
						}, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: permanentFailureHeader},
			wantErr: false,
		},
		{
			name: "#6 error creating attempt - attempt already exists",
			fields: fields{
				conf:       dynconf,
				cDao:       mockConsentDao,
				dProcessor: mockDataProcessor,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1"},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{
						Id: "id-1", Accounts: acc1, Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, caError.ErrAttemptAlreadyExists),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#7 error creating attempt - other error",
			fields: fields{
				conf:       dynconf,
				cDao:       mockConsentDao,
				dProcessor: mockDataProcessor,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, errors.New("error creating attempt")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#8 error publishing - failed to update dao",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
				dfaDao:            mockDfaDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "att-id-1"}, nil),
					mockFetchDataDelayPub.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("publish error")),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("update error")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#9 error publishing - updated dao",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
				dfaDao:            mockDfaDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "att-id-1"}, nil),
					mockFetchDataDelayPub.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("publish error")),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#10 successfully published",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "att-id-1"}, nil),
					mockFetchDataDelayPub.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("sqs-id-1", nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#11 consent data refresh already in progress - attempt already in progress - next fetch at has exceeded past 3(configurable) days - dao does not return error",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
							Accounts:                 acc1,
							Expiry:                   timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#12 consent data refresh already in progress - attempt already in progress - next fetch at has exceeded past 3(configurable) days - dao returned error",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
							Accounts:                 acc1,
							Expiry:                   timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error updating consent")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#13 consent data refresh already in progress - attempt already in progress - next fetch at has not exceeded past 3(configurable) days",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
							Accounts:                 acc1,
							NextFetchAt:              timestampPb.Now(),
							Expiry:                   timestampPb.New(time.Now().Add(1 * time.Hour)),
						}, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#14 error publishing - last attempt - failed to update consent",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
				dfaDao:            mockDfaDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: true}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "att-id-1"}, nil),
					mockFetchDataDelayPub.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("publish error")),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error updating consent dao")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#15 error publishing - last attempt - updated consent",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
				dfaDao:            mockDfaDao,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: true}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1,
						Expiry: timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
						}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(&caPb.DataFetchAttempt{Id: "att-id-1"}, nil),
					mockFetchDataDelayPub.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("publish error")),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#16 consent not eligible for refresh",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_COMPLETED,
							Accounts:                 acc1,
							Expiry:                   timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, caError.ErrConsentIneligibleForDataRefresh),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error in dao")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#17 consent not eligible for refresh",
			fields: fields{
				conf:              dynconf,
				cDao:              mockConsentDao,
				dProcessor:        mockDataProcessor,
				fetchDataDelayPub: mockFetchDataDelayPub,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:                       "id-1",
							ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_COMPLETED,
							Accounts:                 acc1,
							Expiry:                   timestampPb.New(time.Now().Add(1 * time.Hour))}, nil),
					mockDataProcessor.EXPECT().CreateAttempt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), false).Return(nil, caError.ErrConsentIneligibleForDataRefresh),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).MaxTimes(2),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#18 consent expire timestamp reached and account consent entities status updated successfully",
			fields: fields{
				conf:                  dynconf,
				cDao:                  mockConsentDao,
				dProcessor:            mockDataProcessor,
				fetchDataDelayPub:     mockFetchDataDelayPub,
				accountConsentWrapper: mockAcw,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:            "id-1",
							ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_EXPIRED,
						}, nil),
					mockAcw.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: successHeader},
			wantErr: false,
		},
		{
			name: "#19 consent expire timestamp reached and error updating consent status",
			fields: fields{
				conf:                  dynconf,
				cDao:                  mockConsentDao,
				dProcessor:            mockDataProcessor,
				fetchDataDelayPub:     mockFetchDataDelayPub,
				accountConsentWrapper: mockAcw,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						nil, errors.New("error updating consent status")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
		{
			name: "#20 consent expire timestamp reached and error refreshing account consent entities",
			fields: fields{
				conf:                  dynconf,
				cDao:                  mockConsentDao,
				dProcessor:            mockDataProcessor,
				fetchDataDelayPub:     mockFetchDataDelayPub,
				accountConsentWrapper: mockAcw,
			},
			args: args{
				ctx:     context.Background(),
				request: &caCoPb.ProcessConsentDataRefreshRequest{PrimaryConsentId: "id-1", RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false}},
				mocks: []interface{}{
					mockConsentDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&caPb.Consent{Id: "id-1", Accounts: acc1}, nil),
					mockConsentDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&caPb.Consent{
							Id:            "id-1",
							ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_EXPIRED,
						}, nil),
					mockAcw.EXPECT().RefreshByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
						nil, errors.New("error refreshing consent account entities")),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
						User:       &userPb.User{},
						UserStatus: userPb.FiStatus_FI_STATUS_ACTIVE,
						Status:     rpc.StatusOk(),
					}, nil),
				},
			},
			want:    &caCoPb.ProcessConsentDataRefreshResponse{ResponseHeader: transientFailureHeader},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CaConsumer{
				cDao:                  tt.fields.cDao,
				dProcessor:            tt.fields.dProcessor,
				dfaDao:                tt.fields.dfaDao,
				fetchDataDelayPub:     tt.fields.fetchDataDelayPub,
				conf:                  tt.fields.conf,
				accountConsentWrapper: tt.fields.accountConsentWrapper,
				txnExecutor:           mockTxnExecutor,
				userClient:            mockUserClient,
			}
			got, err := c.ProcessConsentDataRefresh(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessConsentDataRefresh() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.GetResponseHeader().GetStatus() != tt.want.GetResponseHeader().GetStatus() {
				t.Errorf("ProcessConsentDataRefresh() got = %v, want %v", got, tt.want)
			}
		})
	}
}
