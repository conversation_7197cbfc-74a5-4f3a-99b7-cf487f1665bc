// nolint:dupl,unparam,funlen,goconst
package fi_types

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/connectedaccount/data/fi_types/xml_models"
	caError "github.com/epifi/gamma/connectedaccount/error"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
)

func (e *NpsProcessor) parseAaAccountFromRawXml(ctx context.Context, dataAttempt *caPb.DataFetchAttempt,
	processAttempt *caPb.DataProcessAttempt, rawNpsXml *xml_models.RawNpsXmlData, acc *caPb.AaAccount) (
	*caPb.AaAccount, error) {
	profile, profileErr := e.getProfileFromNpsXmlData(ctx, rawNpsXml)
	if profileErr != nil {
		return nil, profileErr
	}
	accInstrumentType, accInstrumentTypeErr := caPkg.GetAccountInstrumentType(rawNpsXml.Type)
	if accInstrumentTypeErr != nil {
		return nil, accInstrumentTypeErr
	}

	resultAccount := &caPb.AaAccount{
		Id:                  acc.GetId(),
		ActorId:             acc.GetActorId(),
		MaskedAccountNumber: rawNpsXml.MaskedPranId,
		LinkedAccountRef:    rawNpsXml.LinkedAccRef,
		Version:             rawNpsXml.Version,
		AccInstrumentType:   accInstrumentType,
		FipId:               acc.GetFipId(),
		AccountStatus:       updateAccStatusOnFirstDataPullSuccess(acc.GetAccountStatus()),
		AccountSubStatus:    acc.GetAccountSubStatus(),
		LastSyncedAt:        timestampPb.Now(),
		ConsentReferenceId:  dataAttempt.GetConsentReferenceId(),
		Profile: &caPb.Profile{
			AccountProfile: &caPb.AccountProfile{Profile: &caPb.AccountProfile_NpsHolders{
				NpsHolders: profile,
			}},
		},
	}
	return resultAccount, nil
}

func (e *NpsProcessor) getProfileFromNpsXmlData(ctx context.Context, rawNpsXml *xml_models.RawNpsXmlData) (
	*caPb.NpsHolders, error) {
	holders, holdersErr := e.getHoldersFromNpsXmlData(ctx, rawNpsXml)
	if holdersErr != nil {
		return nil, holdersErr
	}
	return &caPb.NpsHolders{
		NpsHolders: holders,
	}, nil
}

func (e *NpsProcessor) getHoldersFromNpsXmlData(ctx context.Context, rawNpsXml *xml_models.RawNpsXmlData) (
	[]*caPb.NpsHolder, error) {
	profile := rawNpsXml.Profile
	if profile == nil {
		return nil, fmt.Errorf("getProfileFromNpsXmlData: raw profile is nil")
	}
	if profile.Holders == nil {
		return nil, fmt.Errorf("getProfileFromNpsXmlData: holders in raw profile is nil")
	}
	npsHolders := profile.Holders.Holders
	if npsHolders == nil {
		return nil, caError.ErrNilRawHolders
	}
	var holderList []*caPb.NpsHolder
	for _, holder := range npsHolders {
		dob, dobErr := datetime.ParseStringTimeStampProto(caPkg.DateLayout, holder.DOB)
		if dobErr != nil {
			logger.Error(ctx, fmt.Sprintf("getHoldersFromNpsXmlData: error parsing holder dob %v : %v",
				holder.DOB, dobErr))
		}
		pranId := holder.PranId
		if pranId == "" {
			// Fallback to masked pran id if pran id is not present. This issue has been observed with Protean (NPS_PROD)
			pranId = rawNpsXml.MaskedPranId
		}
		if len(pranId) < 4 {
			logger.Error(ctx, fmt.Sprintf("getHoldersFromNpsXmlData: length of pran Id is less than 4 chars %s", pranId))
		}
		nominee, nomineeErr := getNominee(holder.Nominee)
		if nomineeErr != nil {
			logger.Error(ctx, fmt.Sprintf("getHoldersFromNpsXmlData: error parsing nominee value %v", nominee),
				zap.Error(nomineeErr))
		}
		holderList = append(holderList, &caPb.NpsHolder{
			Address:  holder.Address,
			Dob:      dob,
			Email:    holder.Email,
			Landline: holder.Landline,
			Mobile:   holder.Mobile,
			Name:     holder.Name,
			Nominee:  nominee,
			Pan:      holder.PAN,
			PranId:   pranId,
		})
	}
	return holderList, nil
}

func (e *NpsProcessor) checkAaAccountDataValidity(oldAcc, parsedAcc *caPb.AaAccount) error {
	if oldAcc.GetLinkedAccountRef() != parsedAcc.GetLinkedAccountRef() {
		return errors.New(fmt.Sprintf("checkAccountDataValidity: link ref number mismatch in pulled account "+
			"details : %v, %v", oldAcc.GetLinkedAccountRef(), parsedAcc.GetLinkedAccountRef()))
	}
	if oldAcc.GetAccInstrumentType() != parsedAcc.GetAccInstrumentType() {
		return errors.New(fmt.Sprintf("checkAccountDataValidity; acc instrument type mismatch in pulled account "+
			"details : %v, %v", oldAcc.GetAccInstrumentType(), parsedAcc.GetAccInstrumentType()))
	}
	if len(parsedAcc.GetMaskedAccountNumber()) < 4 {
		return errors.New(fmt.Sprintf("checkAccountDataValidity: received invalid masked account number : %s",
			parsedAcc.GetMaskedAccountNumber()))
	}
	oldAccLastFourDigits := oldAcc.GetMaskedAccountNumber()[len(oldAcc.GetMaskedAccountNumber())-4:]
	parsedAccLastFourDigits := parsedAcc.GetMaskedAccountNumber()[len(parsedAcc.GetMaskedAccountNumber())-4:]
	if oldAccLastFourDigits != parsedAccLastFourDigits {
		return errors.New(fmt.Sprintf("checkAccountDataValidity: masked account number mismatch in pulled account "+
			"details : %v, %v", oldAccLastFourDigits, parsedAccLastFourDigits))
	}
	return nil
}

func (e *NpsProcessor) parseSummaryDetailsFromRawXml(ctx context.Context, aaAccount *caPb.AaAccount, rawNpsXml *xml_models.RawNpsXmlData) (
	*caPb.AaNpsAccount, error) {
	if rawNpsXml == nil || rawNpsXml.Summary == nil {
		return nil, fmt.Errorf("parseNpsSummaryDetailsFromRawXml: raw xml or summary is nil")
	}

	currentValue, parseErr := parseNonEmptyMoneyStringToMoney(rawNpsXml.Summary.CurrentValue)
	if parseErr != nil {
		return nil, fmt.Errorf("parseNpsSummaryDetailsFromRawXml: current value could not be parsed to Money")
	}

	accountSummary, err := getNpsAccountSummary(aaAccount.GetFipId(), rawNpsXml.Summary)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting NPS account summary")
	}

	return &caPb.AaNpsAccount{
		AaAccountId:  aaAccount.GetId(),
		CurrentValue: currentValue,
		PranId:       getPranId(rawNpsXml),
		Summary:      accountSummary,
	}, nil
}

func getNpsAccountSummary(fipId string, rawSummary *xml_models.NpsSummary) (*caPb.NpsAccountSummary, error) {
	currentValue, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.CurrentValue)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: current_value could not be parsed to Money")
	}
	debtAssetValue, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.DebtAssetValue)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: debtAssetValue could not be parsed to Money")
	}
	equityAssetValue, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.EquityAssetValue)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: EquityAssetValue could not be parsed to Money")
	}
	otherAssetValue, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.OtherAssetValue)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: OtherAssetValue could not be parsed to Money")
	}

	openingDate, parseErr := parseNonEmptyNpsDateStringToTimestamp(fipId, rawSummary.OpeningDate)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "error in parsing opening date")
	}
	tier1NavDate, parseErr := parseNonEmptyNpsDateStringToTimestamp(fipId, rawSummary.Tier1NAVDate)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "error in parsing tier1NavDate")
	}
	tier2NavDate, parseErr := parseNonEmptyNpsDateStringToTimestamp(fipId, rawSummary.Tier1NAVDate)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "error in parsing tier2NavDate")
	}
	status, parseErr := getNpsAccountStatus(rawSummary.Status)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "error in parsing status")
	}
	tier1Status, parseErr := getNpsTier1AccountStatus(rawSummary.Tier1Status)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "error in parsing tier1Status")
	}
	tier2Status, parseErr := getNpsTier2AccountStatus(rawSummary.Tier2Status)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "error in parsing tier2Status")
	}

	tier1CurrVal, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.Holdings.Tier1Holdings.CurrentValue)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: tier 1 holdings current value could not be parsed to Money")
	}

	tier2CurrVal, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.Holdings.Tier2Holdings.CurrentValue)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: tier 2 holdings current value could not be parsed to Money")
	}

	tier1AmountInTransition, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.Holdings.Tier1Holdings.AmountInTransition)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: tier 1 holdings AmountInTransition could not be parsed to Money")
	}

	tier2AmountInTransition, parseErr := parseNonEmptyMoneyStringToMoney(rawSummary.Holdings.Tier2Holdings.AmountInTransition)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: tier 2 holdings AmountInTransition could not be parsed to Money")
	}

	tier1SchemePrefType, parseErr := getTier1SchemePreferenceType(rawSummary.Holdings.Tier1Holdings.SchemePreferenceType)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: tier 1 scheme preference type could not be parsed")
	}

	tier2SchemePrefType, parseErr := getTier2SchemePreferenceType(rawSummary.Holdings.Tier2Holdings.SchemePreferenceType)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "parseNpsSummaryDetailsFromRawXml: tier 2 scheme preference type could not be parsed")
	}

	return &caPb.NpsAccountSummary{
		OpeningDate:      openingDate,
		CurrentValue:     currentValue,
		Status:           status,
		Tier1Status:      tier1Status,
		Tier1NavDate:     tier1NavDate,
		Tier2Status:      tier2Status,
		Tier2NavDate:     tier2NavDate,
		DebtAssetValue:   debtAssetValue,
		EquityAssetValue: equityAssetValue,
		OtherAssetValue:  otherAssetValue,
		Tier1HoldingSummary: &caPb.NpsAccountSummary_NpsAccountTier1HoldingSummary{
			CurrentValue:         tier1CurrVal,
			AmountInTransition:   tier1AmountInTransition,
			SchemePreferenceType: tier1SchemePrefType,
		},
		Tier2HoldingSummary: &caPb.NpsAccountSummary_NpsAccountTier2HoldingSummary{
			CurrentValue:         tier2CurrVal,
			AmountInTransition:   tier2AmountInTransition,
			SchemePreferenceType: tier2SchemePrefType,
		},
	}, nil
}

func parseNonEmptyNpsDateStringToTimestamp(fipId string, dateString string) (*timestampPb.Timestamp, error) {
	if dateString == "" {
		return nil, nil
	}
	var timeLayout = caPkg.DateLayout
	if fipId == KFinNpsFip {
		timeLayout = caPkg.KFinNpsDateTimeLayout
	}

	return datetime.ParseStringTimeStampProto(timeLayout, dateString)
}

func parseNonEmptyMoneyStringToMoney(moneyString string) (*moneyPb.Money, error) {
	if moneyString == "" {
		return nil, nil
	}
	return money.ParseString(moneyString, "INR")
}

func getTier2SchemePreferenceType(preferenceType string) (caEnumPb.NpsSummaryTier2SchemePreferenceType, error) {
	switch strings.ToLower(preferenceType) {
	case "auto":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_AUTO, nil
	case "active":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_ACTIVE, nil
	case "auto_aggressive":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_AUTO_AGGRESSIVE, nil
	case "auto_moderate":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_AUTO_MODERATE, nil
	case "auto_conservative":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_AUTO_CONSERVATIVE, nil
	case "default_standard":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_DEFAULT_STANDARD, nil
	case "corporate":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_CORPORATE, nil
	case "na":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_NA, nil
	case "":
		return caEnumPb.NpsSummaryTier2SchemePreferenceType_NPS_SUMMARY_TIER2_SCHEME_PREFERENCE_TYPE_UNSPECIFIED, nil
	default:
		return 0, fmt.Errorf("unhandled scheme preference type for tier 2 NPS holding %s", preferenceType)
	}
}

func getTier1SchemePreferenceType(preferenceType string) (caEnumPb.NpsSummaryTier1SchemePreferenceType, error) {
	switch strings.ToLower(preferenceType) {
	case "auto":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_AUTO, nil
	case "active":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_ACTIVE, nil
	case "auto_aggressive":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_AUTO_AGGRESSIVE, nil
	case "auto_moderate":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_AUTO_MODERATE, nil
	case "auto_conservative":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_AUTO_CONSERVATIVE, nil
	case "default_standard":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_DEFAULT_STANDARD, nil
	case "corporate":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_CORPORATE, nil
	case "":
		return caEnumPb.NpsSummaryTier1SchemePreferenceType_NPS_SUMMARY_TIER1_SCHEME_PREFERENCE_TYPE_UNSPECIFIED, nil
	default:
		return 0, fmt.Errorf("unhandled scheme preference type for tier 1 NPS holding %s", preferenceType)
	}
}

func getNpsTier2AccountStatus(status string) (caEnumPb.NpsAccountTier2Status, error) {
	switch strings.ToLower(status) {
	case "active":
		return caEnumPb.NpsAccountTier2Status_NPS_ACCOUNT_TIER2_STATUS_ACTIVE, nil
	case "deactivated":
		return caEnumPb.NpsAccountTier2Status_NPS_ACCOUNT_TIER2_STATUS_DEACTIVATED, nil
	case "frozen":
		return caEnumPb.NpsAccountTier2Status_NPS_ACCOUNT_TIER2_STATUS_FROZEN, nil
	case "na":
		return caEnumPb.NpsAccountTier2Status_NPS_ACCOUNT_TIER2_STATUS_NA, nil
	case "":
		return caEnumPb.NpsAccountTier2Status_NPS_ACCOUNT_TIER2_STATUS_UNSPECIFIED, nil
	default:
		return 0, fmt.Errorf("unhandled nps tier 2 account status %s", status)
	}
}

func getNpsTier1AccountStatus(status string) (caEnumPb.NpsAccountTier1Status, error) {
	switch strings.ToLower(status) {
	case "active":
		return caEnumPb.NpsAccountTier1Status_NPS_ACCOUNT_TIER1_STATUS_ACTIVE, nil
	case "deactivated":
		return caEnumPb.NpsAccountTier1Status_NPS_ACCOUNT_TIER1_STATUS_DEACTIVATED, nil
	case "frozen":
		return caEnumPb.NpsAccountTier1Status_NPS_ACCOUNT_TIER1_STATUS_FROZEN, nil
	case "":
		return caEnumPb.NpsAccountTier1Status_NPS_ACCOUNT_TIER1_STATUS_UNSPECIFIED, nil
	default:
		return 0, fmt.Errorf("unhandled nps tier 1 account status %s", status)
	}
}

func getNpsAccountStatus(status string) (caEnumPb.NpsAccountStatus, error) {
	switch strings.ToLower(status) {
	case "active":
		return caEnumPb.NpsAccountStatus_NPS_ACCOUNT_STATUS_ACTIVE, nil
	case "deactivated":
		return caEnumPb.NpsAccountStatus_NPS_ACCOUNT_STATUS_DEACTIVATED, nil
	default:
		return 0, fmt.Errorf("unhandled nps tier 1 account status %s", status)
	}
}

func getPranId(rawNpsXml *xml_models.RawNpsXmlData) string {
	holders := rawNpsXml.Profile.Holders.Holders
	if len(holders) > 0 && holders[0].PranId != "" {
		return holders[0].PranId
	}
	return rawNpsXml.MaskedPranId
}

func (e *NpsProcessor) parseHoldingDetailsFromRawXml(ctx context.Context, aaAccount *caPb.AaAccount, rawNpsXml *xml_models.RawNpsXmlData) (
	[]*caPb.AaNpsHolding, error) {
	if rawNpsXml == nil || rawNpsXml.Summary == nil || rawNpsXml.Summary.Holdings == nil ||
		rawNpsXml.Summary.Holdings.Tier1Holdings == nil || rawNpsXml.Summary.Holdings.Tier2Holdings == nil {
		return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: holdings can not be parsed from rawNpsXml")
	}

	var npsHoldings []*caPb.AaNpsHolding
	tier1Holdings, err := parseNpsTier1Holdings(aaAccount, rawNpsXml)
	if err != nil {
		return nil, errors.Wrap(err, "error in parsing NPS tier1 holdings")
	}

	if len(tier1Holdings) > 0 {
		npsHoldings = append(npsHoldings, tier1Holdings...)
	}

	tier2Holdings, err := parseNpsTier2Holdings(aaAccount, rawNpsXml)
	if err != nil {
		return nil, errors.Wrap(err, "error in parsing NPS tier2 holdings")
	}

	if len(tier2Holdings) > 0 {
		npsHoldings = append(npsHoldings, tier2Holdings...)
	}

	return npsHoldings, nil
}

func parseNpsTier2Holdings(aaAccount *caPb.AaAccount, rawNpsXml *xml_models.RawNpsXmlData) ([]*caPb.AaNpsHolding, error) {
	var npsTier2Holdings []*caPb.AaNpsHolding
	for _, rawHoldingData := range rawNpsXml.Summary.Holdings.Tier2Holdings.Tier2Holdings {
		if rawHoldingData.SchemeId == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: SchemeId is nil in raw holding data")
		}
		if rawHoldingData.SchemeName == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: SchemeName is nil in raw holding data")
		}
		if rawHoldingData.TotalUnits == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: total units is nil in raw holding data")
		}
		if rawHoldingData.NAV == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: NAV is nil in raw holding data")
		}

		totalUnits, unitsParseErr := strconv.ParseFloat(rawHoldingData.TotalUnits, 32)
		if unitsParseErr != nil {
			return nil, errors.Wrap(unitsParseErr, "parseHoldingDetailsFromRawXml: units could not be parsed to float32")
		}
		blockedUnits, blockedUnitsParseErr := parseStringToFloat(rawHoldingData.BlockedUnits, 32)
		if blockedUnitsParseErr != nil {
			return nil, errors.Wrap(blockedUnitsParseErr, "parseHoldingDetailsFromRawXml: BlockedUnits could not be parsed to float32")
		}
		freeUnits, freeUnitsParseErr := parseStringToFloat(rawHoldingData.FreeUnits, 32)
		if freeUnitsParseErr != nil {
			return nil, errors.Wrap(freeUnitsParseErr, "parseHoldingDetailsFromRawXml: units could not be parsed to float32")
		}
		allocationPercentage, allocationPercentageParseErr := parseStringToFloat(rawHoldingData.AllocationPercentage, 32)
		if allocationPercentageParseErr != nil {
			return nil, errors.Wrap(allocationPercentageParseErr, "parseHoldingDetailsFromRawXml: allocationPercentageParseErr could not be parsed to float32")
		}
		currentValue, parseErr := money.ParseString(rawHoldingData.TotalValueOfScheme, "INR")
		if parseErr != nil {
			return nil, errors.Wrap(parseErr, "parseInvitSummaryDetailsFromRawXml: current value could not be parsed to Money")
		}
		nav, parseErr := money.ParseString(rawHoldingData.NAV, "INR")
		if parseErr != nil {
			return nil, errors.Wrap(parseErr, "parseInvitSummaryDetailsFromRawXml: NAV could not be parsed to Money")
		}

		npsTier2Holdings = append(npsTier2Holdings, &caPb.AaNpsHolding{
			AaAccountId: aaAccount.GetId(),
			SchemeId:    rawHoldingData.SchemeId,
			TotalUnits:  float32(totalUnits),
			NpsHoldingMetadata: &caPb.NpsHoldingMetadata{
				Holding: &caPb.NpsHoldingMetadata_Tier2Holding{
					Tier2Holding: &caPb.NpsTier2Holding{
						BlockedUnits:         float32(blockedUnits),
						FreeUnits:            float32(freeUnits),
						Nav:                  nav,
						SchemeName:           rawHoldingData.SchemeName,
						SchemeId:             rawHoldingData.SchemeId,
						AllocationPercentage: float32(allocationPercentage),
						TotalUnits:           float32(totalUnits),
						TotalValueOfScheme:   currentValue,
					},
				},
			},
		})
	}
	return npsTier2Holdings, nil
}

func parseNpsTier1Holdings(aaAccount *caPb.AaAccount, rawNpsXml *xml_models.RawNpsXmlData) ([]*caPb.AaNpsHolding, error) {
	var npsTier1Holdings []*caPb.AaNpsHolding
	for _, rawHoldingData := range rawNpsXml.Summary.Holdings.Tier1Holdings.Tier1Holdings {
		if rawHoldingData.SchemeId == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: SchemeId is nil in raw holding data")
		}
		if rawHoldingData.SchemeName == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: SchemeName is nil in raw holding data")
		}
		if rawHoldingData.TotalUnits == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: total units is nil in raw holding data")
		}
		if rawHoldingData.NAV == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: NAV is nil in raw holding data")
		}

		totalUnits, unitsParseErr := strconv.ParseFloat(rawHoldingData.TotalUnits, 32)
		if unitsParseErr != nil {
			return nil, errors.Wrap(unitsParseErr, "parseHoldingDetailsFromRawXml: units could not be parsed to float32")
		}
		blockedUnits, blockedUnitsParseErr := parseStringToFloat(rawHoldingData.BlockedUnits, 32)
		if blockedUnitsParseErr != nil {
			return nil, errors.Wrap(blockedUnitsParseErr, "parseHoldingDetailsFromRawXml: BlockedUnits could not be parsed to float32")
		}
		freeUnits, freeUnitsParseErr := parseStringToFloat(rawHoldingData.FreeUnits, 32)
		if freeUnitsParseErr != nil {
			return nil, errors.Wrap(freeUnitsParseErr, "parseHoldingDetailsFromRawXml: units could not be parsed to float32")
		}
		allocationPercentage, allocationPercentageParseErr := parseStringToFloat(rawHoldingData.AllocationPercentage, 32)
		if allocationPercentageParseErr != nil {
			return nil, errors.Wrap(allocationPercentageParseErr, "parseHoldingDetailsFromRawXml: allocationPercentageParseErr could not be parsed to float32")
		}
		currentValue, parseErr := money.ParseString(rawHoldingData.TotalValueOfScheme, "INR")
		if parseErr != nil {
			return nil, errors.Wrap(parseErr, "parseInvitSummaryDetailsFromRawXml: current value could not be parsed to Money")
		}
		nav, parseErr := money.ParseString(rawHoldingData.NAV, "INR")
		if parseErr != nil {
			return nil, errors.Wrap(parseErr, "parseInvitSummaryDetailsFromRawXml: NAV could not be parsed to Money")
		}

		npsTier1Holdings = append(npsTier1Holdings, &caPb.AaNpsHolding{
			AaAccountId: aaAccount.GetId(),
			SchemeId:    rawHoldingData.SchemeId,
			TotalUnits:  float32(totalUnits),
			NpsHoldingMetadata: &caPb.NpsHoldingMetadata{
				Holding: &caPb.NpsHoldingMetadata_Tier1Holding{
					Tier1Holding: &caPb.NpsTier1Holding{
						BlockedUnits:         float32(blockedUnits),
						FreeUnits:            float32(freeUnits),
						Nav:                  nav,
						SchemeName:           rawHoldingData.SchemeName,
						SchemeId:             rawHoldingData.SchemeId,
						AllocationPercentage: float32(allocationPercentage),
						TotalUnits:           float32(totalUnits),
						TotalValueOfScheme:   currentValue,
					},
				},
			},
		})
	}
	return npsTier1Holdings, nil
}

func parseStringToFloat(value string, bitSize int) (float64, error) {
	if value == "" {
		return 0, nil
	}
	floatValue, parseErr := strconv.ParseFloat(value, bitSize)
	if parseErr != nil {
		return 0, parseErr
	}
	return floatValue, nil
}

func (e *NpsProcessor) checkSummaryDataAndRecordColumnUpdate(ctx context.Context, oldAcc, parsedAcc *caPb.AaNpsAccount) error {
	parsedAcc.Id = oldAcc.GetId()
	if oldAcc.GetCurrentValue() != parsedAcc.GetCurrentValue() {
		// assuming the column update timestamp as the current time of data processing.
		captureColumnUpdate(ctx, parsedAcc.GetAaAccountId(), caEnumPb.AaAccountMutableColumn_AA_ACCOUNT_MUTABLE_COLUMN_BALANCE,
			money.ToDisplayString(parsedAcc.GetCurrentValue()), timestampPb.New(time.Now()), e.captureColumnUpdatePub)
	}
	return nil
}

func getNpsHoldingSchemeId(holding *caPb.AaNpsHolding) string {
	if holding.GetNpsHoldingMetadata().GetTier1Holding() != nil {
		return holding.GetNpsHoldingMetadata().GetTier1Holding().GetSchemeId()
	}
	return holding.GetNpsHoldingMetadata().GetTier2Holding().GetSchemeId()
}

func (e *NpsProcessor) categorizeOperationsOnHoldings(oldHoldings, newHoldings []*caPb.AaNpsHolding) (updatedAndAddedHoldings, deletedHoldings []*caPb.AaNpsHolding, err error) {
	if len(oldHoldings) == 0 {
		return newHoldings, nil, nil
	}

	deletedHoldings = []*caPb.AaNpsHolding{}
	updatedAndAddedHoldings = []*caPb.AaNpsHolding{}

	oldHoldingsMap := make(map[string]*caPb.AaNpsHolding)
	newHoldingsMap := make(map[string]*caPb.AaNpsHolding)

	// Create maps for both oldNpsHoldings and newNpsHoldings for faster lookups.
	for _, oldHolding := range oldHoldings {
		oldHoldingsMap[getNpsHoldingSchemeId(oldHolding)] = oldHolding
	}
	for _, newHolding := range newHoldings {
		newHoldingsMap[getNpsHoldingSchemeId(newHolding)] = newHolding
	}

	// Iterate through oldNpsHoldings to find deleted and updated/added holdings.
	for _, oldHolding := range oldHoldings {
		if newHolding, found := newHoldingsMap[getNpsHoldingSchemeId(oldHolding)]; found {
			// If the nps holding exists in both lists, compare each field.
			updatedHolding, updHoldingErr := e.compareAndEnrichNpsHolding(oldHolding, newHolding)
			if updHoldingErr != nil {
				return nil, nil, fmt.Errorf("error in compareAndEnrichNpsHolding updated holding, oldHoldingISIN: %s",
					getNpsHoldingSchemeId(oldHolding))
			}
			updatedAndAddedHoldings = append(updatedAndAddedHoldings, updatedHolding)
		} else {
			// If the nps holding doesn't exist in newNpsHoldings, mark it for deletion.
			deletedHoldings = append(deletedHoldings, oldHolding)
		}
	}

	// Iterate through newNpsHoldings to find added holdings.
	for _, newHolding := range newHoldings {
		if _, found := oldHoldingsMap[getNpsHoldingSchemeId(newHolding)]; !found {
			// If the nps holding exists in newNpsHoldings but not in oldNpsHoldings, mark it as added.
			updatedAndAddedHoldings = append(updatedAndAddedHoldings, newHolding)
		}
	}

	// At this point, deletedHoldings contains deleted nps holdings,
	// and updatedAndAddedHoldings contains both updated and new added nps holdings.
	return updatedAndAddedHoldings, deletedHoldings, nil
}

func (e *NpsProcessor) compareAndEnrichNpsHolding(oldHolding, newHolding *caPb.AaNpsHolding) (*caPb.AaNpsHolding, error) {
	updatedNpsHolding := oldHolding
	if getNpsHoldingSchemeId(oldHolding) != getNpsHoldingSchemeId(newHolding) {
		return nil, fmt.Errorf("ISIN is different in old and new holding: oldISIN:%s ; newISIN:%s ",
			getNpsHoldingSchemeId(oldHolding), getNpsHoldingSchemeId(newHolding))
	}

	updatedNpsHolding.TotalUnits = newHolding.GetTotalUnits()
	updatedNpsHolding.NpsHoldingMetadata = newHolding.GetNpsHoldingMetadata()
	return updatedNpsHolding, nil
}

func (e *NpsProcessor) convertToExtNpsSummary(ctx context.Context, npsAccount *caPb.AaNpsAccount, npsHoldings []*caPb.AaNpsHolding) (*caExtPb.NpsSummary, error) {
	var tier1Holdings []*caExtPb.NpsSummary_Tier1Holding
	var tier2Holdings []*caExtPb.NpsSummary_Tier2Holding
	for _, holding := range npsHoldings {
		if holding.GetNpsHoldingMetadata().GetTier1Holding() != nil {
			npsHolding := holding.GetNpsHoldingMetadata().GetTier1Holding()
			tier1Holdings = append(tier1Holdings, &caExtPb.NpsSummary_Tier1Holding{
				BlockedUnits:         npsHolding.GetBlockedUnits(),
				FreeUnits:            npsHolding.GetFreeUnits(),
				Nav:                  npsHolding.GetNav(),
				SchemeName:           npsHolding.GetSchemeName(),
				SchemeId:             npsHolding.GetSchemeId(),
				AllocationPercentage: npsHolding.GetAllocationPercentage(),
				TotalUnits:           npsHolding.GetTotalUnits(),
				TotalValueOfScheme:   npsHolding.GetTotalValueOfScheme(),
			})
		}

		if holding.GetNpsHoldingMetadata().GetTier2Holding() != nil {
			npsHolding := holding.GetNpsHoldingMetadata().GetTier2Holding()
			tier2Holdings = append(tier2Holdings, &caExtPb.NpsSummary_Tier2Holding{
				BlockedUnits:         npsHolding.GetBlockedUnits(),
				FreeUnits:            npsHolding.GetFreeUnits(),
				Nav:                  npsHolding.GetNav(),
				SchemeName:           npsHolding.GetSchemeName(),
				SchemeId:             npsHolding.GetSchemeId(),
				AllocationPercentage: npsHolding.GetAllocationPercentage(),
				TotalUnits:           npsHolding.GetTotalUnits(),
				TotalValueOfScheme:   npsHolding.GetTotalValueOfScheme(),
			})
		}
	}

	holdingsInfo := &caExtPb.NpsSummary_Holdings{
		Tier1Holdings: &caExtPb.NpsSummary_Tier1Holdings{
			SchemePreferenceType: npsAccount.GetSummary().GetTier1HoldingSummary().GetSchemePreferenceType(),
			AmountInTransition:   npsAccount.GetSummary().GetTier1HoldingSummary().GetAmountInTransition(),
			CurrentValue:         npsAccount.GetSummary().GetTier1HoldingSummary().GetCurrentValue(),
			Holdings:             tier1Holdings,
		},
		Tier2Holdings: &caExtPb.NpsSummary_Tier2Holdings{
			SchemePreferenceType: npsAccount.GetSummary().GetTier2HoldingSummary().GetSchemePreferenceType(),
			AmountInTransition:   npsAccount.GetSummary().GetTier2HoldingSummary().GetAmountInTransition(),
			CurrentValue:         npsAccount.GetSummary().GetTier2HoldingSummary().GetCurrentValue(),
			Holdings:             tier2Holdings,
		},
	}

	return &caExtPb.NpsSummary{
		OpeningDate:      npsAccount.GetSummary().GetOpeningDate(),
		CurrentValue:     npsAccount.GetCurrentValue(),
		Status:           npsAccount.GetSummary().GetStatus(),
		Tier1Status:      npsAccount.GetSummary().GetTier1Status(),
		Tier1NavDate:     npsAccount.GetSummary().GetTier1NavDate(),
		Tier2Status:      npsAccount.GetSummary().GetTier2Status(),
		Tier2NavDate:     npsAccount.GetSummary().GetTier2NavDate(),
		DebtAssetValue:   npsAccount.GetSummary().GetDebtAssetValue(),
		EquityAssetValue: npsAccount.GetSummary().GetEquityAssetValue(),
		OtherAssetValue:  npsAccount.GetSummary().GetOtherAssetValue(),
		Holdings:         holdingsInfo,
	}, nil
}

// convertToExtProfileDetails converts the raw profile of an account data in DB to external proto message
func (e *NpsProcessor) convertToExtProfileDetails(profile *caPb.Profile) (*caExtPb.NpsProfileDetails, error) {
	if profile == nil || profile.GetAccountProfile() == nil || profile.GetAccountProfile().GetNpsHolders() == nil ||
		profile.GetAccountProfile().GetNpsHolders().GetNpsHolders() == nil {
		return nil, fmt.Errorf("error converting to external profile due to nil nps account profile")
	}

	var holderDetailsList []*caExtPb.NpsHolderDetails
	for _, holder := range profile.GetAccountProfile().GetNpsHolders().GetNpsHolders() {
		phNum, phErr := strconv.ParseUint(holder.GetMobile(), 10, 64)
		if phErr != nil {
			return nil, fmt.Errorf("error while parsing phone number in nps holders list: %v", holder.GetMobile())
		}
		holderDetailsList = append(holderDetailsList, &caExtPb.NpsHolderDetails{
			FullName: &commontypes.Name{
				FirstName: holder.GetName(),
			},
			Dob: holder.GetDob(),
			Mobile: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: phNum,
			},
			Nominee: holder.GetNominee(),
			PranId:  holder.GetPranId(),
			Landline: &commontypes.Landline{
				StdCode: Unknown,
				Number:  holder.GetLandline(),
			},
			Address:       holder.GetAddress(),
			KycCompliance: holder.GetKycCompliance(),
			Email:         holder.GetEmail(),
			Pan:           holder.GetPan(),
		})
	}

	return &caExtPb.NpsProfileDetails{
		NpsHolderDetails: holderDetailsList,
	}, nil
}
