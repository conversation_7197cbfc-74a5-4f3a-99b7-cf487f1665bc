package txn_monitoring

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/contact"
	"github.com/epifi/gamma/api/user/contact/mocks"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/risk/config"
)

type mockUserContactService struct {
	enable  bool
	req     *contact.GetContactsByBatchRequest
	want    *contact.GetContactsByBatchResponse
	wantErr error
}
type mockActorService struct {
	enable  bool
	req     *actor.GetEntityDetailsRequest
	want    *actor.GetEntityDetailsResponse
	wantErr error
	times   int
}
type mockUserService struct {
	enable  bool
	req     *user.GetUsersRequest
	want    *user.GetUsersResponse
	wantErr error
	times   int
}

var (
	pc1 = &rpc.PageContextRequest{
		PageSize: 1000,
		Token: &rpc.PageContextRequest_AfterToken{
			AfterToken: "",
		},
	}
	uc1 = &contact.UserContact{
		Id:              "someId",
		ActorId:         "someActor",
		PhoneNumberHash: "someUserHashedPhoneNumber",
	}
	fuc1 = &contact.UserContact{
		PhoneNumberHash: "fraudUserHashedPhoneNumber",
	}
	fraudUser1 = &user.User{
		Id: "fraudUser",
		AccessRevokeDetails: &user.AccessRevokeDetails{
			AccessRevokeStatus: user.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED,
		},
	}
	User1 = &user.User{
		Id: "someUserId",
	}
)

func TestCollector_getIngressContactSignals(t *testing.T) {

	ctrl := gomock.NewController(t)
	mockContactClient := mocks.NewMockContactClient(ctrl)
	mockActorClient := mockActor.NewMockActorClient(ctrl)
	mockUserClient := mocks2.NewMockUsersClient(ctrl)
	ctx := context.Background()
	phoneNumberHash1 := "somePhoneNumberHash"
	id1 := &contact.ContactsQueryIdentifier{
		Identifier: &contact.ContactsQueryIdentifier_HashedPhoneNumber{
			HashedPhoneNumber: phoneNumberHash1,
		},
	}
	ed1 := &actor.GetEntityDetailsResponse_EntityDetail{
		EntityId:   "someUserId",
		EntityType: types.ActorType_USER,
	}
	fed1 := &actor.GetEntityDetailsResponse_EntityDetail{
		EntityId:   "fraudUser",
		EntityType: types.ActorType_USER,
	}
	ed2 := &actor.GetEntityDetailsResponse_EntityDetail{
		EntityId:   "someMerchantId",
		EntityType: types.ActorType_MERCHANT,
	}
	uid1 := &user.GetUsersRequest_GetUsersIdentifier_Id{
		Id: "someUserId",
	}
	fuid1 := &user.GetUsersRequest_GetUsersIdentifier_Id{
		Id: "fraudUser",
	}
	tests := []struct {
		name                   string
		hashedPhoneNumber      string
		mockUserContactService []*mockUserContactService
		mockActorService       mockActorService
		mockUserService        mockUserService
		want                   int64
		wantErr                bool
	}{
		{
			name:              "get error while getting the contacts",
			hashedPhoneNumber: phoneNumberHash1,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want:    nil,
					wantErr: errors.New("some error"),
				},
			},
			want:    0,
			wantErr: true,
		},
		{
			name:              "get error while getting the actor info",
			hashedPhoneNumber: phoneNumberHash1,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							uc1,
						},
					},
					wantErr: nil,
				},
			},
			mockActorService: mockActorService{
				enable: true,
				req: &actor.GetEntityDetailsRequest{
					ActorIds: []string{"someActor"},
				},
				want:    nil,
				wantErr: errors.New("some error"),
				times:   1,
			},
			wantErr: true,
			want:    0,
		},
		{
			name:              "get error while getting the users info",
			hashedPhoneNumber: phoneNumberHash1,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							uc1,
						},
					},
					wantErr: nil,
				},
			},
			mockActorService: mockActorService{
				enable: true,
				req: &actor.GetEntityDetailsRequest{
					ActorIds: []string{"someActor"},
				},
				want: &actor.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
						ed1,
					},
				},
				wantErr: nil,
				times:   1,
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: uid1,
						},
					}},
				want:    nil,
				wantErr: errors.New("some error"),
				times:   1,
			},
			wantErr: true,
			want:    0,
		},
		{
			name:              "get 0 risky contacts when we don't find any users in the system for a actor",
			hashedPhoneNumber: phoneNumberHash1,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							uc1,
						},
						PageContextResponse: &rpc.PageContextResponse{HasAfter: false},
					},
					wantErr: nil,
				},
			},
			mockActorService: mockActorService{
				enable: true,
				req: &actor.GetEntityDetailsRequest{
					ActorIds: []string{"someActor"},
				},
				want: &actor.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
						ed2,
					},
				},
				wantErr: nil,
				times:   1,
			},
			mockUserService: mockUserService{
				enable:  false,
				req:     nil,
				want:    nil,
				wantErr: nil,
			},
			wantErr: false,
			want:    0,
		},
		{
			name:              "get 0 risky contacts when we don't find any fraud users in the system for a actor",
			hashedPhoneNumber: phoneNumberHash1,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							uc1,
						},
						PageContextResponse: &rpc.PageContextResponse{HasAfter: false},
					},
					wantErr: nil,
				},
			},
			mockActorService: mockActorService{
				enable: true,
				req: &actor.GetEntityDetailsRequest{
					ActorIds: []string{"someActor"},
				},
				want: &actor.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
						ed1,
					},
				},
				wantErr: nil,
				times:   1,
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: uid1,
						},
					}},
				want: &user.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*user.User{
						User1,
					},
				},
				times:   1,
				wantErr: nil,
			},
			wantErr: false,
			want:    0,
		},
		{
			name:              "get risky contacts when we have risky contacts in the system",
			hashedPhoneNumber: phoneNumberHash1,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							uc1,
						},
						PageContextResponse: &rpc.PageContextResponse{HasAfter: false},
					},
					wantErr: nil,
				},
			},
			mockActorService: mockActorService{
				enable: true,
				req: &actor.GetEntityDetailsRequest{
					ActorIds: []string{"someActor"},
				},
				want: &actor.GetEntityDetailsResponse{
					Status: rpc.StatusOk(),
					EntityDetails: []*actor.GetEntityDetailsResponse_EntityDetail{
						fed1,
					},
				},
				wantErr: nil,
				times:   1,
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: fuid1,
						},
					}},
				want: &user.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*user.User{
						fraudUser1,
					},
				},
				times:   1,
				wantErr: nil,
			},
			wantErr: false,
			want:    1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Collector{
				actorClient:       mockActorClient,
				userClient:        mockUserClient,
				userContactClient: mockContactClient,
				config: &config.Config{
					TxnMonitoringConfig: &config.TxnMonitoringConfig{
						ContactSignalBatchSize:     1000,
						ContactSignalsMaxIteration: 500,
					},
				},
			}
			for _, mockContact := range tt.mockUserContactService {
				if mockContact.enable {
					mockContactClient.EXPECT().GetContactsByBatch(ctx, mockContact.req).
						Return(mockContact.want, mockContact.wantErr)
				}
			}
			if tt.mockActorService.enable {
				mockActorClient.EXPECT().GetEntityDetails(ctx, tt.mockActorService.req).
					Return(tt.mockActorService.want, tt.mockActorService.wantErr).Times(tt.mockActorService.times)
			}

			if tt.mockUserService.enable {
				mockUserClient.EXPECT().GetUsers(ctx, tt.mockUserService.req).
					Return(tt.mockUserService.want, tt.mockUserService.wantErr).Times(tt.mockUserService.times)
			}
			got, err := c.getIngressContactSignals(ctx, tt.hashedPhoneNumber)
			if (err != nil) != tt.wantErr {
				t.Errorf("getIngressContactSignals() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getIngressContactSignals() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCollector_getEgressContactSignals(t *testing.T) {

	ctrl := gomock.NewController(t)
	mockContactClient := mocks.NewMockContactClient(ctrl)
	mockUserClient := mocks2.NewMockUsersClient(ctrl)
	ctx := context.Background()
	actorId := "someActorId"
	id1 := &contact.ContactsQueryIdentifier{
		Identifier: &contact.ContactsQueryIdentifier_ActorId{
			ActorId: actorId,
		},
	}
	hp1 := &user.GetUsersRequest_GetUsersIdentifier_HashedPhoneNumber{
		HashedPhoneNumber: "someUserHashedPhoneNumber",
	}
	fhp1 := &user.GetUsersRequest_GetUsersIdentifier_HashedPhoneNumber{
		HashedPhoneNumber: "fraudUserHashedPhoneNumber",
	}
	tests := []struct {
		name                   string
		actorId                string
		mockUserContactService []*mockUserContactService
		mockUserService        mockUserService
		want                   int64
		want1                  int64
		wantErr                bool
	}{
		{
			name:    "get error while getting the contacts",
			actorId: actorId,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want:    nil,
					wantErr: errors.New("some error"),
				},
			},
			want:    0,
			want1:   0,
			wantErr: true,
		},
		{
			name:    "should not give error when no contact founds",
			actorId: actorId,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusRecordNotFound(),
					},
					wantErr: nil,
				},
			},
			want:    0,
			want1:   0,
			wantErr: false,
		},
		{
			name:    "get error while getting the users info",
			actorId: actorId,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							uc1,
						},
					},
					wantErr: nil,
				},
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: hp1,
						},
					}},
				want:    nil,
				wantErr: errors.New("some error"),
				times:   1,
			},
			wantErr: true,
			want:    0,
			want1:   0,
		},
		{
			name:    "get 0 risky contacts when we don't find any fraud users in the system for a actor",
			actorId: actorId,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							fuc1,
						},
						PageContextResponse: &rpc.PageContextResponse{HasAfter: false},
					},
					wantErr: nil,
				},
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: fhp1,
						},
					}},
				want: &user.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*user.User{
						User1,
					},
				},
				times:   1,
				wantErr: nil,
			},
			wantErr: false,
			want:    1,
			want1:   0,
		},
		{
			name:    "get no error when it could not find the users",
			actorId: actorId,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							fuc1,
						},
						PageContextResponse: &rpc.PageContextResponse{HasAfter: false},
					},
					wantErr: nil,
				},
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: fhp1,
						},
					}},
				want: &user.GetUsersResponse{
					Status: rpc.StatusRecordNotFound(),
					Users:  nil,
				},
				times:   1,
				wantErr: nil,
			},
			wantErr: false,
			want:    1,
			want1:   0,
		},
		{
			name:    "get risky contacts when we have risky contacts in the system",
			actorId: actorId,
			mockUserContactService: []*mockUserContactService{
				&mockUserContactService{
					enable: true,
					req: &contact.GetContactsByBatchRequest{
						Identifier:         id1,
						PageContextRequest: pc1,
					},
					want: &contact.GetContactsByBatchResponse{
						Status: rpc.StatusOk(),
						Contacts: []*contact.UserContact{
							fuc1,
						},
						PageContextResponse: &rpc.PageContextResponse{HasAfter: false},
					},
					wantErr: nil,
				},
			},
			mockUserService: mockUserService{
				enable: true,
				req: &user.GetUsersRequest{
					Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: fhp1,
						},
					}},
				want: &user.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*user.User{
						fraudUser1,
					},
				},
				times:   1,
				wantErr: nil,
			},
			wantErr: false,
			want:    1,
			want1:   1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Collector{
				userClient:        mockUserClient,
				userContactClient: mockContactClient,
				config: &config.Config{
					TxnMonitoringConfig: &config.TxnMonitoringConfig{
						ContactSignalBatchSize:     1000,
						ContactSignalsMaxIteration: 500,
					},
				},
			}
			for _, mockContact := range tt.mockUserContactService {
				if mockContact.enable {
					mockContactClient.EXPECT().GetContactsByBatch(ctx, mockContact.req).
						Return(mockContact.want, mockContact.wantErr)
				}
			}
			if tt.mockUserService.enable {
				mockUserClient.EXPECT().GetUsers(ctx, tt.mockUserService.req).
					Return(tt.mockUserService.want, tt.mockUserService.wantErr).Times(tt.mockUserService.times)
			}
			got, got1, err := c.getEgressContactSignals(ctx, tt.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("getEgressContactSignals() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getEgressContactSignals() got = %v, want %v", got, tt.want)
			}

			if got1 != tt.want1 {
				t.Errorf("getEgressContactSignals() got1 = %v, want1 %v", got1, tt.want1)
			}
		})
	}
}
