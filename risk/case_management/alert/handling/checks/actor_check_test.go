package checks

import (
	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/api/typesv2"

	actorPb "github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
)

const testActorId = "actorId"

var respExtUsrGetEntityDetailsByActorId = &actorPb.GetEntityDetailsByActorIdResponse{
	Type:   typesv2.ActorType_EXTERNAL_USER,
	Status: rpc.StatusOk(),
}
var respUsrGetEntityDetailsByActorId = &actorPb.GetEntityDetailsByActorIdResponse{
	Type:   typesv2.ActorType_USER,
	Status: rpc.StatusOk(),
}

type ActorCheckMockedDependencies struct {
	actorClient *mockActor.MockActorClient
}

func NewActorCheckWithMockedDependencies(t *testing.T) (*ActorCheck, *ActorCheckMockedDependencies, func()) {
	ctrl := gomock.NewController(t)
	mockActorClient := mockActor.NewMockActorClient(ctrl)
	return NewActorCheck(mockActorClient), &ActorCheckMockedDependencies{
		actorClient: mockActorClient,
	}, ctrl.Finish
}

func TestActorCheck_GetAlertHandling(t *testing.T) {
	ctx := context.Background()
	tests := []struct {
		name    string
		mocks   func(mocks *ActorCheckMockedDependencies)
		want    *cmPb.AlertHandlingParams
		wantErr bool
	}{
		{
			name: "skipping external actor from review",
			mocks: func(mocks *ActorCheckMockedDependencies) {
				mocks.actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: testActorId,
				}).Return(respExtUsrGetEntityDetailsByActorId, nil)
			},
			want: &cmPb.AlertHandlingParams{
				HandlingType: cmEnumsPb.AlertHandlingType_ALERT_HANDLING_TYPE_SKIP_REVIEW,
				HandlingReasons: []cmEnumsPb.AlertHandlingReason{
					cmEnumsPb.AlertHandlingReason_ALERT_HANDLING_REASON_EXTERNAL_ACTOR,
				},
			},
			wantErr: false,
		},
		{
			name: "sending other actors for review",
			mocks: func(mocks *ActorCheckMockedDependencies) {
				mocks.actorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: testActorId,
				}).Return(respUsrGetEntityDetailsByActorId, nil)
			},
			want: &cmPb.AlertHandlingParams{
				HandlingType: cmEnumsPb.AlertHandlingType_ALERT_HANDLING_TYPE_SEND_FOR_REVIEW,
				HandlingReasons: []cmEnumsPb.AlertHandlingReason{
					cmEnumsPb.AlertHandlingReason_ALERT_HANDLING_REASON_ELIGIBLE_FOR_REVIEW,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a, mockedDeps, assertTest := NewActorCheckWithMockedDependencies(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps)
			}
			got, err := a.GetAlertHandling(ctx, &Input{
				AlertWithRule: &cmPb.AlertWithRuleDetails{
					Alert: &cmPb.Alert{
						ActorId: testActorId,
					},
				},
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAlertHandling() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAlertHandling() got = %v, want %v", got, tt.want)
			}
			assertTest()
		})
	}
}
