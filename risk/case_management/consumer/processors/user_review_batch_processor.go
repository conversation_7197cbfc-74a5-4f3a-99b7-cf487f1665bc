package processors

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	persistentQueuePb "github.com/epifi/gamma/api/persistentqueue"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	enumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/risk/case_management/helper"
	"github.com/epifi/gamma/risk/case_management/metrics"
	"github.com/epifi/gamma/risk/config/genconf"
)

type UserReviewBatchProcessor struct {
	caseManagementClient caseManagementPb.CaseManagementClient
	pqHelper             helper.IPersistentQueueHelper
	cfg                  *genconf.Config
}

func NewUserReviewBatchProcessor(caseManagementClient caseManagementPb.CaseManagementClient, pqHelper helper.IPersistentQueueHelper, cfg *genconf.Config) *UserReviewBatchProcessor {
	return &UserReviewBatchProcessor{
		caseManagementClient: caseManagementClient,
		pqHelper:             pqHelper,
		cfg:                  cfg,
	}
}

func (p *UserReviewBatchProcessor) ProcessPayload(ctx context.Context, payloadType caseManagementPb.PayloadType, inputs []*Input) []*Failure {
	var failures []*Failure
	if payloadType != caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW {
		for _, input := range inputs {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("invalid payload type:%s %w", payloadType, epifierrors.ErrInvalidArgument),
				BatchId: input.GetBatchId(),
			})
		}
		return failures
	}

	var createAlertsBatch, persistentQueueBatch []*Input
	for _, input := range inputs {
		userReviewPayload, ok := input.GetPayload().(*caseManagementPb.RiskCase_UserReview)
		if !ok || userReviewPayload == nil {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("invalid payload passed for payload type: %s %w", payloadType, epifierrors.ErrInvalidArgument),
				BatchId: input.GetBatchId(),
			})
			continue
		}
		isCaseManagementFlow := EvaluateStickinessForRolloutPercentage(ctx, userReviewPayload.UserReview.GetActorId(), input.GetBatchId(), p.cfg)
		if isCaseManagementFlow {
			createAlertsBatch = append(createAlertsBatch, input)
			continue
		}
		persistentQueueBatch = append(persistentQueueBatch, input)
	}
	failures = append(failures, p.processPayloadToCaseManagementFlow(ctx, createAlertsBatch)...)
	failures = append(failures, p.processPayloadToPersistentQueue(ctx, persistentQueueBatch)...)
	return failures
}

func (p *UserReviewBatchProcessor) processPayloadToCaseManagementFlow(ctx context.Context, inputs []*Input) []*Failure {
	var currBatch []*Input
	var failures []*Failure
	payloadType := caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW

	for _, input := range inputs {
		userReviewPayload, ok := input.GetPayload().(*caseManagementPb.RiskCase_UserReview)
		if !ok || userReviewPayload == nil {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("invalid payload passed for payload type: %s %w", payloadType, epifierrors.ErrInvalidArgument),
				BatchId: input.GetBatchId(),
			})
			continue
		}
		currBatch = append(currBatch, input)

		// Keep adding to createAlertsBatch till processor capacity is not utilized
		if len(currBatch) < int(p.cfg.CMConsumerConfig().CreateAlertsBatchSize()) {
			continue
		}
		failures = append(failures, p.createAlerts(ctx, currBatch)...)
		currBatch = []*Input{}
	}
	failures = append(failures, p.createAlerts(ctx, currBatch)...)
	return failures
}

func (p *UserReviewBatchProcessor) createAlerts(ctx context.Context, batch []*Input) []*Failure {
	payloadType := caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW
	var failures []*Failure
	if len(batch) == 0 {
		return failures
	}
	rawAlerts, createRawAlertsFailures := userReviewPayloadsToRawAlerts(batch)
	failures = append(failures, createRawAlertsFailures...)
	resp, err := p.caseManagementClient.CreateAlerts(ctx,
		&caseManagementPb.CreateAlertsRequest{
			Alerts: rawAlerts,
		})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		for _, input := range batch {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("failed to create alert %w", err),
				BatchId: input.GetBatchId(),
			})
		}
		return failures
	}
	for _, input := range batch {
		metrics.RecordIngestionSuccess(input.GetBatchId(), payloadType)
	}
	return failures
}

func userReviewPayloadsToRawAlerts(inputs []*Input) ([]*caseManagementPb.RawAlert, []*Failure) {
	var rawAlerts []*caseManagementPb.RawAlert
	var failures []*Failure
	for _, input := range inputs {
		payload, ok := input.GetPayload().(*caseManagementPb.RiskCase_UserReview)
		if !ok {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				BatchId: input.GetBatchId(),
				Err:     fmt.Errorf("invalid payload passed %w", epifierrors.ErrInvalidArgument),
			})
			continue
		}
		rawAlert := &caseManagementPb.RawAlert{}
		rawAlert.Identifier = input.GetRuleIdentifier()
		rawAlert.ActorId = payload.UserReview.GetActorId()
		rawAlert.EntityType = enumsPb.EntityType_ENTITY_TYPE_USER
		rawAlert.EntityId = payload.UserReview.GetActorId()
		rawAlert.BatchName = input.GetBatchId()
		rawAlert.InitiatedAt = input.GetInitiatedAt()
		rawAlerts = append(rawAlerts, rawAlert)
	}
	return rawAlerts, failures
}

func (p *UserReviewBatchProcessor) processPayloadToPersistentQueue(ctx context.Context, inputs []*Input) []*Failure {
	payloadType := caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW
	var failures []*Failure
	for _, input := range inputs {
		userReviewPayload, ok := input.GetPayload().(*caseManagementPb.RiskCase_UserReview)
		if !ok || userReviewPayload == nil {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("invalid payload passed for payload type: %s %w", payloadType, epifierrors.ErrInvalidArgument),
				BatchId: input.GetBatchId(),
			})
			continue
		}
		pqPayloadType, err := p.pqHelper.ConvertToPQPayloadType(ctx, payloadType)
		if err != nil {
			failures = append(failures, &Failure{
				Payload: userReviewPayload,
				Err:     fmt.Errorf("error while converting to persistent queue payload type: %s %w", payloadType, err),
				BatchId: input.GetBatchId(),
			})
			continue
		}

		pqUserReviewPayload := buildUserReviewPQPayload(userReviewPayload.UserReview)
		err = p.pqHelper.PushToQueue(ctx, userReviewPayload.UserReview.GetActorId(), pqPayloadType, &persistentQueuePb.Payload{UserReviewPayload: pqUserReviewPayload})
		if err != nil {
			failures = append(failures, &Failure{
				Payload: userReviewPayload,
				Err:     fmt.Errorf("error while pushing payload to persistent queue payload type: %s %w", payloadType, err),
				BatchId: input.GetBatchId(),
			})
			continue
		}
		metrics.RecordIngestionSuccess(input.GetBatchId(), payloadType)
	}
	return failures
}

func buildUserReviewPQPayload(payload *caseManagementPb.UserReview) *persistentQueuePb.UserReview {
	return &persistentQueuePb.UserReview{ActorId: payload.GetActorId()}
}

func EvaluateStickinessForRolloutPercentage(ctx context.Context, actorId, batchId string, cfg *genconf.Config) bool {
	actorAndBatchId := fmt.Sprintf("%v%v", batchId, actorId)
	num, err := release.GetHashNum(actorAndBatchId)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor+batchID string", zap.Error(err))
		return false
	}
	return num%100 < cfg.CMConsumerConfig().CaseManagementRollOutPercentage().
		Get(caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW.String())
}
