package middleware

import (
	"net/http"
)

// Middleware defines a function type that can wrap an HTTP handler
type Middleware func(http.HandlerFunc) http.HandlerFunc

// ApplyMiddleware chains multiple middleware functions together
func ApplyMiddleware(handler http.HandlerFunc, middlewares ...Middleware) http.HandlerFunc {
	// Apply middlewares in reverse order so they execute in the order they are passed
	for i := len(middlewares) - 1; i >= 0; i-- {
		if middlewares[i] != nil {
			handler = middlewares[i](handler)
		}
	}
	return handler
}
