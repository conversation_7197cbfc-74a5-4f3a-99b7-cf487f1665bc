// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=DataVerificationDetail,DataVerificationDetails,AttributionDetails
syntax = "proto3";

package user;

import "api/employment/employment_data.proto";
import "api/kyc/kyc.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/address.proto";
import "api/typesv2/category.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/community.proto";
import "api/typesv2/designation.proto";
import "api/typesv2/disability_type.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/qualification.proto";
import "api/typesv2/religion.proto";
import "api/typesv2/shipping_items.proto";
import "api/typesv2/user.proto";
import "api/user/enums.proto";
import "api/user/profile.proto";
import "api/vendorgateway/vendor.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/user";
option java_package = "com.github.epifi.gamma.api.user";

// Message representing a user.
message User {
  string id = 1;
  Profile profile = 2;
  // contains information regarding bank customers for a particular user
  // a user can have multiple customer id as we on-board more vendor banks
  // for now we have only one partner vendor i.e. FEDERAL it might have only
  // one entry going initially.
  // Deprecated: Use bank customer service
  repeated BankCustomerInfo customer_infos = 3 [deprecated = true];
  // Deprecated: use AccessRevokeInfo
  AccessRevokeState access_revoke_state = 4 [deprecated = true];
  // deprecated: use AccessRevokeDetails
  // contains all access related detail status, reason for a user
  AccessRevokeInfo access_revoke_info = 5 [deprecated = true];
  // contains all access revoke details like status, reason, remarks, updated by
  AccessRevokeDetails access_revoke_details = 6;
  // contains all acquisition related details like acquisition source, platform, url through which acquisition happened
  AcquisitionInfo acquisition_info = 7;
  // contains all deletion details like reason, new user ID if created
  DeletionDetails deletion_details = 8;
  // stores the verification data from different flows of various entities like PAN,DOB etc
  DataVerificationDetails data_verification_details = 9;
  // stores reference to the id of actor with entityId as user id
  string actor_id = 10;
  // deleted_at maps to deleted_at_unix in user table
  google.protobuf.Timestamp deleted_at = 11;
}

message DataVerificationDetail {
  DataType data_type = 1;
  oneof data_value {
    string pan_number = 2;
    google.type.Date d_o_b = 3;
    api.typesv2.common.Name pan_name = 4;
    EmploymentDetail employment_detail = 5;
    api.typesv2.MaritalStatus marital_status = 6;
    ResidenceDetails address_details = 7;
  }
  vendorgateway.Vendor verification_entity = 9;
  VerificationMethod verification_method = 10;
  google.protobuf.Timestamp verified_time = 11;
  message EmploymentDetail {
    api.typesv2.EmploymentType employment_type = 1;
    string organization_name = 2;
    // applicable for salaried individuals
    google.type.Money monthly_income = 3;
    string work_email = 4;
    employment.OccupationType occupation_type = 5;
    // applicable for business owners
    string GSTIN = 6;
    // applicable for business owners
    google.type.Money annual_revenue = 7;
  }
  message ResidenceDetails {
    api.typesv2.ResidentialAddress residential_address = 1;
    // applicable only for ResidenceTypes RENTED and PAYING_GUEST
    google.type.Money monthly_rent = 2;
  }
}

message DataVerificationDetails {
  repeated DataVerificationDetail data_verification_details = 1;
}

message DeletionDetails {
  // deletion reason
  enum DeletionReason {
    DELETION_REASON_UNSPECIFIED = 0;
    DELETION_REASON_AFU_BEFORE_DEV_REG = 1;
    // user is deleted as user's phone number is conflicting with the user trying to perform AFU
    DELETION_REASON_AFU_CONFLICT_PHONE = 2;
    // user is deleted as user's email is conflicting with the user trying to perform AFU
    DELETION_REASON_AFU_CONFLICT_EMAIL = 3;
    // user is deleted if user attempts afu before customer creation
    DELETION_REASON_AFU_BEFORE_CUSTOMER_CREATION = 4;
    DELETION_REASON_DEDUPE_MISMATCH = 5;
    DELETION_REASON_SCRIPT = 6;
    DELETION_REASON_OLD_USER_ONB_JOURNEY_RESET = 7;
    DELETION_REASON_CLOSED_SAVINGS_ACCOUNT = 8;
    DELETION_REASON_DEV_ACTION = 9;
    DELETION_REASON_CX_ADMIN = 10;
    // User journey reset in case mobile number does not match with the number linked to aadhar
    DELETION_REASON_AADHAR_MOBILE_MISMATCH = 11;
    // User journey reset in case NR user onboard with indian number
    DELETION_REASON_NRI_WITH_INDIAN_PH_NUM = 12;
  }

  DeletionReason deletion_reason = 1;
}

message AccessRevokeInfo {
  // deprecated in favour of AccessRevokeDetails
  option deprecated = true;
  // denotes if user is blocked or not
  AccessRevokedStatus is_access_revoked = 1;
  // Specifies reason for which user is blocked
  AccessRevokeReason reason = 2;
  // remarks should be non empty if reason is other
  string remarks = 3;
  // time at which user blocked
  google.protobuf.Timestamp updated_at = 4;
  // it should be email
  string updated_by = 5;
}

message AccessRevokeDetails {
  // denotes the user access revoke status
  AccessRevokeStatus access_revoke_status = 1;
  // Specifies reason for which user's access is revoked
  AccessRevokeReason reason = 2;
  // specifies reason when app access is restored
  AccessRestoreReason restore_reason = 6;
  // remarks should be non empty if reason is other
  string remarks = 3;
  // time at which user blocked
  google.protobuf.Timestamp updated_at = 4;
  // indicates who updated the details
  // it should be email
  string updated_by = 5;
}

enum AccessRevokeStatus {
  // denotes unspecified revoke status
  ACCESS_REVOKE_STATUS_UNSPECIFIED = 0;
  // denotes user is blocked
  ACCESS_REVOKE_STATUS_BLOCKED = 1;
  // denotes user is unblocked
  ACCESS_REVOKE_STATUS_UNBLOCKED = 2;
}

enum AccessRevokedStatus {
  // deprecated in favour of AccessRevokeStatus
  option deprecated = true;
  // denotes detail not available
  ACCESS_REVOKED_STATUS_UNSPECIFIED = 0;
  // denotes user is blocked
  ACCESS_REVOKED_STATUS_BLOCKED = 1;
  // denotes user is unblocked
  ACCESS_REVOKED_STATUS_UNBLOCKED = 2;
}

enum AccessRevokeReason {
  ACCESS_REVOKE_REASON_UNSPECIFIED = 0;
  // denotes user's account is deleted at fi as it is requested by user
  ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST = 1;
  // denotes user's account is deleted by fi
  // possible reason could be liveness or facematch issue
  // deprecated in favour of ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT
  ACCESS_REVOKE_REASON_SPOOF = 2 [deprecated = true];
  // For other remarks will contain detailed remark
  ACCESS_REVOKE_REASON_OTHER = 3;
  // denotes user's account is closed due to min kyc expiry
  ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY = 4;
  // denotes user's account is deleted by fi
  // possible reason could be liveness or facematch issue
  ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT = 5;
  // kyc issues like face match, liveness, name match
  ACCESS_REVOKE_REASON_CORE_KYC_ISSUE = 6;
  // suspicious email patterns, AA info mismatch, location based issues
  // and also if data analytics has pointed a user as risky
  ACCESS_REVOKE_REASON_PROFILE_INDICATORS = 7;
  // penny drop abuse as name suggests
  ACCESS_REVOKE_REASON_PENNY_DROP_ABUSE = 8;
  // received funds from known fraudsters
  ACCESS_REVOKE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS = 9;
  // high number of withdrawals from atms
  ACCESS_REVOKE_REASON_HIGH_ATM_WITHDRAWAL_COUNT = 10;
  // intersection rule in transaction monitoring
  ACCESS_REVOKE_REASON_TM_ALERT = 11;
  // transaction monitoring profile mismatch
  ACCESS_REVOKE_REASON_TM_PROFILE_MISMATCH = 12;
  // account frozen under LEA
  ACCESS_REVOKE_REASON_LEA_COMPLAINT = 13;
  // account inquired under LEA
  ACCESS_REVOKE_REASON_LEA_ENQUIRY = 14;
  // account frozen under NPCI
  ACCESS_REVOKE_REASON_NPCI_COMPLAINT = 15;
  // account frozen under FEDERAL rules
  ACCESS_REVOKE_REASON_FEDERAL_RULES = 16;
  // LSO users haven't complete their vkyc
  ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING = 17;
  // reason to stop onboarding journey for users (no comms triggered)
  ACCESS_REVOKE_REASON_BLOCK_ONBOARDING = 18;
  // UPI PIN retries exceeded post successful AFU
  ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU = 19;
  // users who were part of revkyc queue and did not complete re vkyc
  ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED = 20;
}

// AccessRestoreReason denotes the reason for which app access was unblocked
enum AccessRestoreReason {
  ACCESS_RESTORE_REASON_UNSCPECIFIED = 0;
  // unblock reason: after due dilligence
  ACCESS_RESTORE_REASON_DUE_DILIGENCE = 1;
  // unblock reason: after customer outcall
  ACCESS_RESTORE_REASON_CUSTOMER_OUTCALL = 2;
  // unblock reason: credit freeze imposed on savings account,
  // app access need not be revoked for this
  ACCESS_RESTORE_REASON_CREDIT_FREEZE = 3;
  ACCESS_RESTORE_REASON_OTHERS = 4;
  // unfreeze reason: LEA
  ACCESS_RESTORE_REASON_LEA_UNFREEZE = 5;
  // unblock reason: debit freeze imposed on savings account
  ACCESS_RESTORE_REASON_DEBIT_FREEZE = 6;
  // unblock to use cc or pl
  ACCESS_RESTORE_REASON_REASON_CC_OR_PL_USER = 7;
  ACCESS_RESTORE_REASON_COOLDOWN_LIFTED = 8;
  // unblock reason: total freeze imposed on savings account
  ACCESS_RESTORE_REASON_TOTAL_FREEZE = 9;
}

enum AccessRevokeState {
  option deprecated = true;
  // default value used to denote unblocked user. This will also be used to update and unblock an user.
  ACCESS_REVOKE_STATE_UNSPECIFIED = 0;
  // denotes user's account is deleted only at Fi as requested by user
  ACCESS_REVOKE_STATE_SOFT_BLOCK = 1;
  // denotes user's account is blacklisted by Fi
  ACCESS_REVOKE_STATE_BLACKLISTED = 2;
}

// User's personally identifying details.
message Profile {
  // Customer's Display name.
  api.typesv2.common.Name name = 1 [deprecated = true];

  google.type.Date date_of_birth = 3;
  api.typesv2.common.PhoneNumber phone_number = 5;
  string email = 4;
  // This facilitates to save multiple addresses for a user. Each address must belong to either of the types defined
  // in api.typesv2.AddressType enum.
  // Represents a map from api.typesv2.AddressType (converted to string) to Address.
  map<string, google.type.PostalAddress> addresses = 6;

  // PAN Number of the user
  string PAN = 7;

  // profile image of user. This will be the image location in S3
  string profile_image_url = 8 [deprecated = true];
  // Mother name
  api.typesv2.common.Name mother_name = 9;
  // Father name
  api.typesv2.common.Name father_name = 10;
  // privacy settings
  PrivacySettings privacy_settings = 11;
  // deprecated in favour of kyc_name
  api.typesv2.common.Name legal_name = 12 [deprecated = true];

  // photo of the user that will be permanently stored with us.
  api.typesv2.common.Image photo = 13;

  // customer name as per KYC record
  api.typesv2.common.Name kyc_name = 14;

  // customer name on PAN card
  api.typesv2.common.Name pan_name = 15;

  // customer entered legal name
  api.typesv2.common.Name legal_name_by_user = 16;

  // name to be printed on the user's Debit card.
  api.typesv2.common.Name debit_card_name = 17;

  // Salary range
  SalaryRange salary_range = 18;

  // name recorded in user's gmail account
  api.typesv2.common.Name gmail_name = 19;

  string profile_image_s3_file_path = 20;

  // to store the hashed phone number from DB
  string hashed_phone_number = 21;

  // Gender of user based on KYC data
  api.typesv2.Gender kyc_gender = 22;

  // Name given by user, will be unverified ... use cautiously!
  api.typesv2.common.Name given_name = 23;

  // Gender given by user
  api.typesv2.Gender given_gender = 24;

  // User Qualification data
  api.typesv2.Qualification qualification = 25;

  // User Designation data
  api.typesv2.Designation designation = 26;

  // User Community data
  api.typesv2.Community community = 27;

  // User Religion data
  api.typesv2.Religion religion = 28;

  // User Category data
  api.typesv2.Category category = 29;

  // User Disability type
  api.typesv2.DisabilityType disability_type = 30;
}

enum CustomerCreationStatus {
  CUSTOMER_CREATION_STATUS_UNSPECIFIED = 0;
  // INITIATED-  req is registered on epifi server and message is enqueued
  INITIATED = 1;
  // IN_PROGRESS- at least one attempt was made at calling the vendor for creating a customer. The status needs to be
  //	polled. Requests can be in this state -
  //	1. after successful acknowledgement of receiving the request by a vendor in case the APIs are async.
  //	In this case status will be polled till we get a terminal status back.
  //	2. after seeing transient errors e.g. vendor systems are down or the vendor call timed out.
  //	In this case status will be polled and appropriate retries will be done if required.
  IN_PROGRESS = 2;
  // CREATED- customer has been created for this user at vendor's end
  CREATED = 3;
  // FAILED- customer creation has failed due to some permanent failure at vendor's end.
  // an example of permanent failure would be missing documents or unable to verify user details, etc.
  FAILED = 4;
  // MANUAL_INTERVENTION- System has exhausted all the retries post transient errors so this needs attention from a human.
  MANUAL_INTERVENTION = 5 [deprecated = true];
  // Dedupe customer
  DEDUPE_CUSTOMER = 6;
  // Max retry
  MAX_RETRY_CREATION_STEP = 7;
  // Max retry reached in check status api
  MAX_RETRY_STATUS_CHECK_STEP = 8;
}

// User Field mask specifies which fields need to be considered for a particular operation.
// For example, when updating user information.
enum UserFieldMask {
  FIELD_MASK_UNSPECIFIED = 0;

  // User profile fields such as name, address, phone number, etc. excluding PAN
  USER_PROFILE = 1;

  // Bank customer information such as customer id, request id for
  // customer creation, etc.
  BANK_CUSTOMER_INFO = 2;

  // mask to update user's ACCESS_REVOKE_STATE
  ACCESS_REVOKE_STATE = 3;

  PHONE_NUMBER = 4;

  EMAIL = 5;

  PAN = 6;

  DOB = 7;

  KYC_NAME = 8;

  PAN_NAME = 15;

  DEBIT_CARD_NAME = 9;

  USER_LIVENESS_PHOTO = 10;

  PROFILE_IMAGE_URL = 11 [deprecated = true];

  MOTHER_NAME = 12;

  FATHER_NAME = 13;

  PRIVACY_SETTINGS = 14;

  PHOTO = 16;

  SALARY_RANGE = 17;

  ADDRESSES = 18;

  LEGAL_NAME_BY_USER = 19;

  ACCESS_REVOKE_INFO = 20;

  PROFILE_IMAGE_S3_FILE_PATH = 21;

  ACCESS_REVOKE_DETAILS = 22;

  ACQUISITION_INFO = 23;

  KYC_GENDER = 24;

  GIVEN_NAME = 25;

  GIVEN_GENDER = 26;

  DATA_VERIFICATION_DETAILS = 27;

  ACTOR_ID = 28;

  QUALIFICATION = 29;

  DESIGNATION = 30;

  COMMUNITY = 31;

  RELIGION = 32;

  CATEGORY = 33;

  DISABILITY_TYPE = 34;
}

// Message representing information regarding the customer created at vendor's end
message BankCustomerInfo {
  // customer id as provided by vendor
  // will be empty if customer creation is in progress/ failed / initiated state
  string id = 1;

  vendorgateway.Vendor vendor = 2;

  // Status of customer creation
  CustomerCreationStatus customer_creation_status = 3;

  // vendor specific customer name
  api.typesv2.common.Name name = 4;
  // This denotes if customer was dedupe customer or not
  enum CustomerType {
    CUSTOMER_TYPE_UNSPECIFIED = 0;
    DEDUPE_CUSTOMER = 1;
  }
  CustomerType customer_type = 5;
  // This parameter is only for dedupe customer, and denotes if the used has done partial/full kyc with vendor.
  // This kyc level is in regard to the kyc done by the user with federal/other vendor outside of Epifi
  kyc.KYCLevel original_kyc_level_with_vendor = 6;

  // Kyc level of user is dependent on the original kyc level with vendor, and the kyc done by user with epifi.
  // This parameter represents the consolidated kyc level of both the parameters.
  kyc.KYCLevel kyc_level = 7;

  // time at which bank customer creation was triggered at vendor's end
  google.protobuf.Timestamp creation_started_at = 8;

  // time at which bank customer was created at partner bank's end
  google.protobuf.Timestamp vendor_creation_succeeded_at = 9;

  // time at which bank customer was marked created at epifi's end
  google.protobuf.Timestamp fi_creation_succeeded_at = 10;

  // ekyc rrn number used during customer creation
  string ekyc_rrn_no = 11;

  // flow via which kyc level was updated
  // Ideally, this is to be populated only when kyc level is updated to full kyc
  KycLevelUpdateFlow kyc_level_update_flow = 12;
}

// Message representing information regarding the customer creation retry
message QueueRetryInfo {

  // id of queue msg for retry debugging
  string queue_msg_id = 1;

  // epifi generated id customer creation
  string req_id = 2;

  // number of attempts that has been made to create customer
  int32 attempts = 3;
}

message ShippingPreference {
  // ID for the record in the database, if the preference info is persisted in the database
  string id = 1;

  string actor_id = 2 [(validate.rules).string.min_len = 1];
  api.typesv2.ShippingItem shipping_item = 3;
  api.typesv2.AddressType address_type = 4;
}

message UserUpdateEvent {

  // Unique id to represent an event uniquely(uuid for now)
  string event_id = 1;

  // time at which the event was published
  google.protobuf.Timestamp event_timestamp = 2;

  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 3;

  // Mandatory
  string actor_id = 4;

  // user id
  string user_id = 5;

  // customer_id created at federal's end
  string fed_customer_id = 6;

  // account type - min account/full account depends on kyc level
  kyc.KYCLevel kyc_level = 7;

  vendorgateway.VendorStatus vendor_status = 8;

  CustomerCreationStatus customer_creation_status = 9;

}

// flow via which kyc level was updated
// Ideally, this is to be populated only when kyc level is updated to full kyc
enum KycLevelUpdateFlow {
  KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED = 0;
  KYC_LEVEL_UPDATE_FLOW_DEDUPE_CUSTOMER = 1;
  KYC_LEVEL_UPDATE_FLOW_VKYC_POST_ONBOARDING = 2;
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LSO = 3;
  // CKYC Type O user
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O = 4;
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT = 5;
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC = 6;
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH = 7;
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS = 8;
  KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS = 9;
  KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING = 10;
  KYC_LEVEL_UPDATE_FLOW_BKYC_POST_ONBOARDING = 11;
  KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING = 12;
  KYC_LEVEL_UPDATE_FLOW_FEDERAL_LOANS = 13;
  KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC_LATEST_NO_DEDUPE = 14;
  KYC_LEVEL_UPDATE_FLOW_BKYC_PRE_ONBOARDING = 15;
}


message UserDeviceProperty {

  // unique identifier of type uuid
  string id = 1;

  // foreign key to actor for whom we are storing the device properties
  string actor_id = 2;

  // phone number associated with the user
  string phone_number = 3;

  // enum for type of property that we are storing. e.g device_language, device location_token etc.
  api.typesv2.DeviceProperty device_property = 4;

  // value of the property that we are storing, value in the PropertyValue message
  // could be one of various types of device properties
  api.typesv2.PropertyValue property_value = 5;

  // timestamp corresponding to  deletion of entry
  int64 deleted_at_unix = 6;

  // timestamp corresponding to creation of entry
  google.protobuf.Timestamp created_at = 7;
}

// contains acquisition related details
message AcquisitionInfo {
  // Platform used to register with fi
  api.typesv2.common.Platform platform = 1;
  // source from which acquisition happened, can be like B2B,Facebook,etc.
  // Note: this stores the raw string of source in case the enum field `AcquisitionChannel` doesn't support
  // the identified source.
  string acquisition_source = 2;
  // url used to register with fi when platform is web
  string web_url = 3;
  // source/channel of acquiring the user from.
  // Note: it's an enum entry for `acquisition_source` field.
  AcquisitionChannel acquisition_channel = 4;
  // Intent of the user coming on Fi app.
  // If intent can't be identified, it shall be considered as BANKING.
  // Note: it's an enum entry for `acquisition_intent_raw` field.
  AcquisitionIntent acquisition_intent = 5;
  // Intent of the user coming on Fi app.
  // Note: this stores the raw string of intent in case the enum field `AcquisitionIntent` doesn't support
  // the identified intent.
  string acquisition_intent_raw = 6;
  // Raw attribution details which helps in determining the source and intent of the user
  AttributionDetails attribution_details = 7;
}

message AttributionDetails {
  // Appsflyer callback with attribution details
  google.protobuf.Struct appsflyer_attribution_data = 1;
  // Attribution details of the user retrieved from Google's Install Referrer API
  // Ref - https://developer.android.com/google/play/installreferrer
  google.protobuf.Struct install_referrer_data = 2;
}

// Source of the user coming to Fi from, i.e. paid marketing, organic etc.
enum AcquisitionChannel {
  ACQUISITION_CHANNEL_UNSPECIFIED = 0;
  STUDENT_PROGRAM_VIT = 1;
  // user coming via B2B salary program flow
  B2B_SALARY_PROGRAM = 2;
  // user coming from ads shown via affiliates
  ACQUISITION_CHANNEL_AFFILIATES = 3;
  // user coming from ads shown via GoogleAds
  ACQUISITION_CHANNEL_GOOGLE = 4;
  // user coming from ads shown via Facebook
  ACQUISITION_CHANNEL_FACEBOOK = 5;
  // user coming from ads shown during search in App Store
  ACQUISITION_CHANNEL_APPLE_SEARCH_ADS = 6;
  // user coming organically
  ACQUISITION_CHANNEL_ORGANIC = 7;
  // user coming via referrals
  ACQUISITION_CHANNEL_REFERRALS = 8;
  // user coming via web channel
  ACQUISITION_CHANNEL_WEB = 9;

  // user coming from a source not from the above list
  ACQUISITION_CHANNEL_OTHERS = 15;
}

// Intent of the user coming on Fi app.
// This can depend upon the type of Ad shown to the user, offline campaign, non-Ad acquisitions etc.
enum AcquisitionIntent {
  ACQUISITION_INTENT_UNSPECIFIED = 0;
  // Ad conveyed the intent of Banking (Savings Account)
  ACQUISITION_INTENT_BANKING = 1;
  // Ad conveyed the intent of Personal Loans
  ACQUISITION_INTENT_PERSONAL_LOANS = 2;
  // Ad conveyed the intent of Credit Cards
  ACQUISITION_INTENT_CREDIT_CARDS = 3;
  // Ad conveyed the intent of Networth
  ACQUISITION_INTENT_NET_WORTH = 4;
  // Ad conveyed the intent of Debit card
  ACQUISITION_INTENT_DEBIT_CARD = 5;
  // Ad conveyed the intent of Wealth Analyser
  ACQUISITION_INTENT_WEALTH_ANALYSER = 6;
}
