// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/simulator/openbanking/chequebook/federal/service.proto

package federal

import (
	context "context"
	federal "github.com/epifi/gamma/api/vendors/federal"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Chequebook_OrderChequebook_FullMethodName             = "/simulator.openbanking.chequebook.federal.Chequebook/OrderChequebook"
	Chequebook_TrackChequebook_FullMethodName             = "/simulator.openbanking.chequebook.federal.Chequebook/TrackChequebook"
	Chequebook_IssueDigitalCancelledCheque_FullMethodName = "/simulator.openbanking.chequebook.federal.Chequebook/IssueDigitalCancelledCheque"
	Chequebook_UpdateProfileAtBank_FullMethodName         = "/simulator.openbanking.chequebook.federal.Chequebook/UpdateProfileAtBank"
	Chequebook_CheckProfileUpdateStatus_FullMethodName    = "/simulator.openbanking.chequebook.federal.Chequebook/CheckProfileUpdateStatus"
)

// ChequebookClient is the client API for Chequebook service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChequebookClient interface {
	// OrderChequebook RPC is used to place the request for chequebook
	OrderChequebook(ctx context.Context, in *federal.OrderChequebookRequest, opts ...grpc.CallOption) (*federal.OrderChequebookResponse, error)
	// TrackChequebook RPC is used to track the chequebook request
	TrackChequebook(ctx context.Context, in *federal.TrackChequebookRequest, opts ...grpc.CallOption) (*federal.TrackChequebookResponse, error)
	// IssueDigitalCancelledCheque RPC is used to issue digital cancelled cheque
	IssueDigitalCancelledCheque(ctx context.Context, in *federal.IssueDigitalCancelledChequeRequest, opts ...grpc.CallOption) (*federal.IssueDigitalCancelledChequeResponse, error)
	UpdateProfileAtBank(ctx context.Context, in *federal.UpdateProfileAtBankRequest, opts ...grpc.CallOption) (*federal.UpdateProfileAtBankResponse, error)
	CheckProfileUpdateStatus(ctx context.Context, in *federal.CheckProfileUpdateStatusRequest, opts ...grpc.CallOption) (*federal.CheckProfileUpdateStatusResponse, error)
}

type chequebookClient struct {
	cc grpc.ClientConnInterface
}

func NewChequebookClient(cc grpc.ClientConnInterface) ChequebookClient {
	return &chequebookClient{cc}
}

func (c *chequebookClient) OrderChequebook(ctx context.Context, in *federal.OrderChequebookRequest, opts ...grpc.CallOption) (*federal.OrderChequebookResponse, error) {
	out := new(federal.OrderChequebookResponse)
	err := c.cc.Invoke(ctx, Chequebook_OrderChequebook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chequebookClient) TrackChequebook(ctx context.Context, in *federal.TrackChequebookRequest, opts ...grpc.CallOption) (*federal.TrackChequebookResponse, error) {
	out := new(federal.TrackChequebookResponse)
	err := c.cc.Invoke(ctx, Chequebook_TrackChequebook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chequebookClient) IssueDigitalCancelledCheque(ctx context.Context, in *federal.IssueDigitalCancelledChequeRequest, opts ...grpc.CallOption) (*federal.IssueDigitalCancelledChequeResponse, error) {
	out := new(federal.IssueDigitalCancelledChequeResponse)
	err := c.cc.Invoke(ctx, Chequebook_IssueDigitalCancelledCheque_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chequebookClient) UpdateProfileAtBank(ctx context.Context, in *federal.UpdateProfileAtBankRequest, opts ...grpc.CallOption) (*federal.UpdateProfileAtBankResponse, error) {
	out := new(federal.UpdateProfileAtBankResponse)
	err := c.cc.Invoke(ctx, Chequebook_UpdateProfileAtBank_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chequebookClient) CheckProfileUpdateStatus(ctx context.Context, in *federal.CheckProfileUpdateStatusRequest, opts ...grpc.CallOption) (*federal.CheckProfileUpdateStatusResponse, error) {
	out := new(federal.CheckProfileUpdateStatusResponse)
	err := c.cc.Invoke(ctx, Chequebook_CheckProfileUpdateStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChequebookServer is the server API for Chequebook service.
// All implementations should embed UnimplementedChequebookServer
// for forward compatibility
type ChequebookServer interface {
	// OrderChequebook RPC is used to place the request for chequebook
	OrderChequebook(context.Context, *federal.OrderChequebookRequest) (*federal.OrderChequebookResponse, error)
	// TrackChequebook RPC is used to track the chequebook request
	TrackChequebook(context.Context, *federal.TrackChequebookRequest) (*federal.TrackChequebookResponse, error)
	// IssueDigitalCancelledCheque RPC is used to issue digital cancelled cheque
	IssueDigitalCancelledCheque(context.Context, *federal.IssueDigitalCancelledChequeRequest) (*federal.IssueDigitalCancelledChequeResponse, error)
	UpdateProfileAtBank(context.Context, *federal.UpdateProfileAtBankRequest) (*federal.UpdateProfileAtBankResponse, error)
	CheckProfileUpdateStatus(context.Context, *federal.CheckProfileUpdateStatusRequest) (*federal.CheckProfileUpdateStatusResponse, error)
}

// UnimplementedChequebookServer should be embedded to have forward compatible implementations.
type UnimplementedChequebookServer struct {
}

func (UnimplementedChequebookServer) OrderChequebook(context.Context, *federal.OrderChequebookRequest) (*federal.OrderChequebookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderChequebook not implemented")
}
func (UnimplementedChequebookServer) TrackChequebook(context.Context, *federal.TrackChequebookRequest) (*federal.TrackChequebookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackChequebook not implemented")
}
func (UnimplementedChequebookServer) IssueDigitalCancelledCheque(context.Context, *federal.IssueDigitalCancelledChequeRequest) (*federal.IssueDigitalCancelledChequeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IssueDigitalCancelledCheque not implemented")
}
func (UnimplementedChequebookServer) UpdateProfileAtBank(context.Context, *federal.UpdateProfileAtBankRequest) (*federal.UpdateProfileAtBankResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProfileAtBank not implemented")
}
func (UnimplementedChequebookServer) CheckProfileUpdateStatus(context.Context, *federal.CheckProfileUpdateStatusRequest) (*federal.CheckProfileUpdateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckProfileUpdateStatus not implemented")
}

// UnsafeChequebookServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChequebookServer will
// result in compilation errors.
type UnsafeChequebookServer interface {
	mustEmbedUnimplementedChequebookServer()
}

func RegisterChequebookServer(s grpc.ServiceRegistrar, srv ChequebookServer) {
	s.RegisterService(&Chequebook_ServiceDesc, srv)
}

func _Chequebook_OrderChequebook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(federal.OrderChequebookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChequebookServer).OrderChequebook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chequebook_OrderChequebook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChequebookServer).OrderChequebook(ctx, req.(*federal.OrderChequebookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chequebook_TrackChequebook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(federal.TrackChequebookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChequebookServer).TrackChequebook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chequebook_TrackChequebook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChequebookServer).TrackChequebook(ctx, req.(*federal.TrackChequebookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chequebook_IssueDigitalCancelledCheque_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(federal.IssueDigitalCancelledChequeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChequebookServer).IssueDigitalCancelledCheque(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chequebook_IssueDigitalCancelledCheque_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChequebookServer).IssueDigitalCancelledCheque(ctx, req.(*federal.IssueDigitalCancelledChequeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chequebook_UpdateProfileAtBank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(federal.UpdateProfileAtBankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChequebookServer).UpdateProfileAtBank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chequebook_UpdateProfileAtBank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChequebookServer).UpdateProfileAtBank(ctx, req.(*federal.UpdateProfileAtBankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chequebook_CheckProfileUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(federal.CheckProfileUpdateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChequebookServer).CheckProfileUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chequebook_CheckProfileUpdateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChequebookServer).CheckProfileUpdateStatus(ctx, req.(*federal.CheckProfileUpdateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Chequebook_ServiceDesc is the grpc.ServiceDesc for Chequebook service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Chequebook_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "simulator.openbanking.chequebook.federal.Chequebook",
	HandlerType: (*ChequebookServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OrderChequebook",
			Handler:    _Chequebook_OrderChequebook_Handler,
		},
		{
			MethodName: "TrackChequebook",
			Handler:    _Chequebook_TrackChequebook_Handler,
		},
		{
			MethodName: "IssueDigitalCancelledCheque",
			Handler:    _Chequebook_IssueDigitalCancelledCheque_Handler,
		},
		{
			MethodName: "UpdateProfileAtBank",
			Handler:    _Chequebook_UpdateProfileAtBank_Handler,
		},
		{
			MethodName: "CheckProfileUpdateStatus",
			Handler:    _Chequebook_CheckProfileUpdateStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/simulator/openbanking/chequebook/federal/service.proto",
}
