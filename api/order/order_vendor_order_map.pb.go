// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/order_vendor_order_map.proto

package order

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/order/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OrderVendorOrderMap stores mappings b/w internal order id and vendor's order id
type OrderVendorOrderMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// identifier of the order which has been created on pay's end. This need not be
	// necessarily the same as the pay's side order id but can also be the primary
	// identifier of any other payment related orchestration where the pay's order is
	//
	//	not  created
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// identifier of the corresponding order that has been created on vendor's end
	VendorOrderId string `protobuf:"bytes,3,opt,name=vendor_order_id,json=vendorOrderId,proto3" json:"vendor_order_id,omitempty"`
	// the tech provider who is processing the payment . It can be a
	// payment gateway or a bank.
	Vendor vendorgateway.Vendor `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// to determine if the order is a forward direction payment or
	// a reversal/refund . This will be useful in cases where the vendor
	// side order id is the same as the reversal order id i.e the txn
	// refund are a part of the same order. In such cases, we can use this
	// to differentiate between the two
	OrderDirection enums.OrderDirection `protobuf:"varint,5,opt,name=order_direction,json=orderDirection,proto3,enum=enums.OrderDirection" json:"order_direction,omitempty"`
	// Entity to which the order belongs. This will be used to identify the DB from that the data for a
	// respective id has to be queried.
	EntityOwnership common.Ownership `protobuf:"varint,6,opt,name=entity_ownership,json=entityOwnership,proto3,enum=api.typesv2.common.Ownership" json:"entity_ownership,omitempty"`
	// Identifier of order provided by the domain service. This
	// will be shared between the domain service, pay service and the vendor.
	// Since this is generated in the domain service's end, the domain service itself
	// can reconcile its orders with the vendor's orders
	DomainReferenceId string `protobuf:"bytes,7,opt,name=domain_reference_id,json=domainReferenceId,proto3" json:"domain_reference_id,omitempty"`
	// order creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// order last updated time stamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// order deleted time stamp
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *OrderVendorOrderMap) Reset() {
	*x = OrderVendorOrderMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_vendor_order_map_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderVendorOrderMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderVendorOrderMap) ProtoMessage() {}

func (x *OrderVendorOrderMap) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_vendor_order_map_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderVendorOrderMap.ProtoReflect.Descriptor instead.
func (*OrderVendorOrderMap) Descriptor() ([]byte, []int) {
	return file_api_order_order_vendor_order_map_proto_rawDescGZIP(), []int{0}
}

func (x *OrderVendorOrderMap) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrderVendorOrderMap) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *OrderVendorOrderMap) GetVendorOrderId() string {
	if x != nil {
		return x.VendorOrderId
	}
	return ""
}

func (x *OrderVendorOrderMap) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *OrderVendorOrderMap) GetOrderDirection() enums.OrderDirection {
	if x != nil {
		return x.OrderDirection
	}
	return enums.OrderDirection(0)
}

func (x *OrderVendorOrderMap) GetEntityOwnership() common.Ownership {
	if x != nil {
		return x.EntityOwnership
	}
	return common.Ownership(0)
}

func (x *OrderVendorOrderMap) GetDomainReferenceId() string {
	if x != nil {
		return x.DomainReferenceId
	}
	return ""
}

func (x *OrderVendorOrderMap) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrderVendorOrderMap) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *OrderVendorOrderMap) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_order_order_vendor_order_map_proto protoreflect.FileDescriptor

var file_api_order_order_vendor_order_map_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d,
	0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x1a,
	0x1b, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x82, 0x04, 0x0a, 0x13, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x0f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x10, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x52, 0x0f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_order_order_vendor_order_map_proto_rawDescOnce sync.Once
	file_api_order_order_vendor_order_map_proto_rawDescData = file_api_order_order_vendor_order_map_proto_rawDesc
)

func file_api_order_order_vendor_order_map_proto_rawDescGZIP() []byte {
	file_api_order_order_vendor_order_map_proto_rawDescOnce.Do(func() {
		file_api_order_order_vendor_order_map_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_order_vendor_order_map_proto_rawDescData)
	})
	return file_api_order_order_vendor_order_map_proto_rawDescData
}

var file_api_order_order_vendor_order_map_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_order_order_vendor_order_map_proto_goTypes = []interface{}{
	(*OrderVendorOrderMap)(nil),   // 0: order.OrderVendorOrderMap
	(vendorgateway.Vendor)(0),     // 1: vendorgateway.Vendor
	(enums.OrderDirection)(0),     // 2: enums.OrderDirection
	(common.Ownership)(0),         // 3: api.typesv2.common.Ownership
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_api_order_order_vendor_order_map_proto_depIdxs = []int32{
	1, // 0: order.OrderVendorOrderMap.vendor:type_name -> vendorgateway.Vendor
	2, // 1: order.OrderVendorOrderMap.order_direction:type_name -> enums.OrderDirection
	3, // 2: order.OrderVendorOrderMap.entity_ownership:type_name -> api.typesv2.common.Ownership
	4, // 3: order.OrderVendorOrderMap.created_at:type_name -> google.protobuf.Timestamp
	4, // 4: order.OrderVendorOrderMap.updated_at:type_name -> google.protobuf.Timestamp
	4, // 5: order.OrderVendorOrderMap.deleted_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_order_order_vendor_order_map_proto_init() }
func file_api_order_order_vendor_order_map_proto_init() {
	if File_api_order_order_vendor_order_map_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_order_order_vendor_order_map_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderVendorOrderMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_order_vendor_order_map_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_order_order_vendor_order_map_proto_goTypes,
		DependencyIndexes: file_api_order_order_vendor_order_map_proto_depIdxs,
		MessageInfos:      file_api_order_order_vendor_order_map_proto_msgTypes,
	}.Build()
	File_api_order_order_vendor_order_map_proto = out.File
	file_api_order_order_vendor_order_map_proto_rawDesc = nil
	file_api_order_order_vendor_order_map_proto_goTypes = nil
	file_api_order_order_vendor_order_map_proto_depIdxs = nil
}
