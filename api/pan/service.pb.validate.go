// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pan/service.proto

package pan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	epan "github.com/epifi/gamma/api/pan/epan"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"

	wealth "github.com/epifi/gamma/api/vendors/wealth"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = epan.UpdateEPANInfoParamMask(0)

	_ = vendorgateway.Vendor(0)

	_ = wealth.NsdlPanStatus(0)
)

// Validate checks the field values on FetchProfileFromPanRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchProfileFromPanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchProfileFromPanRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchProfileFromPanRequestMultiError, or nil if none found.
func (m *FetchProfileFromPanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchProfileFromPanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for ActorId

	// no validation rules for Pan

	if len(errors) > 0 {
		return FetchProfileFromPanRequestMultiError(errors)
	}

	return nil
}

// FetchProfileFromPanRequestMultiError is an error wrapping multiple
// validation errors returned by FetchProfileFromPanRequest.ValidateAll() if
// the designated constraints aren't met.
type FetchProfileFromPanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchProfileFromPanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchProfileFromPanRequestMultiError) AllErrors() []error { return m }

// FetchProfileFromPanRequestValidationError is the validation error returned
// by FetchProfileFromPanRequest.Validate if the designated constraints aren't met.
type FetchProfileFromPanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchProfileFromPanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchProfileFromPanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchProfileFromPanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchProfileFromPanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchProfileFromPanRequestValidationError) ErrorName() string {
	return "FetchProfileFromPanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchProfileFromPanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchProfileFromPanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchProfileFromPanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchProfileFromPanRequestValidationError{}

// Validate checks the field values on FetchProfileFromPanResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchProfileFromPanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchProfileFromPanResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchProfileFromPanResponseMultiError, or nil if none found.
func (m *FetchProfileFromPanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchProfileFromPanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchProfileFromPanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchProfileFromPanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchProfileFromPanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchProfileFromPanResponseValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchProfileFromPanResponseValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchProfileFromPanResponseValidationError{
				field:  "PanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchProfileFromPanResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchProfileFromPanResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchProfileFromPanResponseValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchProfileFromPanResponseMultiError(errors)
	}

	return nil
}

// FetchProfileFromPanResponseMultiError is an error wrapping multiple
// validation errors returned by FetchProfileFromPanResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchProfileFromPanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchProfileFromPanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchProfileFromPanResponseMultiError) AllErrors() []error { return m }

// FetchProfileFromPanResponseValidationError is the validation error returned
// by FetchProfileFromPanResponse.Validate if the designated constraints
// aren't met.
type FetchProfileFromPanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchProfileFromPanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchProfileFromPanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchProfileFromPanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchProfileFromPanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchProfileFromPanResponseValidationError) ErrorName() string {
	return "FetchProfileFromPanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchProfileFromPanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchProfileFromPanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchProfileFromPanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchProfileFromPanResponseValidationError{}

// Validate checks the field values on GetPanUpdateNextActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPanUpdateNextActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPanUpdateNextActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPanUpdateNextActionRequestMultiError, or nil if none found.
func (m *GetPanUpdateNextActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPanUpdateNextActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientLastState

	if all {
		switch v := interface{}(m.GetPollingRequestInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPanUpdateNextActionRequestValidationError{
					field:  "PollingRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPanUpdateNextActionRequestValidationError{
					field:  "PollingRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingRequestInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPanUpdateNextActionRequestValidationError{
				field:  "PollingRequestInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPanUpdateNextActionRequestMultiError(errors)
	}

	return nil
}

// GetPanUpdateNextActionRequestMultiError is an error wrapping multiple
// validation errors returned by GetPanUpdateNextActionRequest.ValidateAll()
// if the designated constraints aren't met.
type GetPanUpdateNextActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPanUpdateNextActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPanUpdateNextActionRequestMultiError) AllErrors() []error { return m }

// GetPanUpdateNextActionRequestValidationError is the validation error
// returned by GetPanUpdateNextActionRequest.Validate if the designated
// constraints aren't met.
type GetPanUpdateNextActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPanUpdateNextActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPanUpdateNextActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPanUpdateNextActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPanUpdateNextActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPanUpdateNextActionRequestValidationError) ErrorName() string {
	return "GetPanUpdateNextActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPanUpdateNextActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPanUpdateNextActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPanUpdateNextActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPanUpdateNextActionRequestValidationError{}

// Validate checks the field values on GetPanUpdateNextActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPanUpdateNextActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPanUpdateNextActionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPanUpdateNextActionResponseMultiError, or nil if none found.
func (m *GetPanUpdateNextActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPanUpdateNextActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPanUpdateNextActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPanUpdateNextActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPanUpdateNextActionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPanUpdateNextActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPanUpdateNextActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPanUpdateNextActionResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPollingResponseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPanUpdateNextActionResponseValidationError{
					field:  "PollingResponseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPanUpdateNextActionResponseValidationError{
					field:  "PollingResponseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingResponseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPanUpdateNextActionResponseValidationError{
				field:  "PollingResponseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPanUpdateNextActionResponseMultiError(errors)
	}

	return nil
}

// GetPanUpdateNextActionResponseMultiError is an error wrapping multiple
// validation errors returned by GetPanUpdateNextActionResponse.ValidateAll()
// if the designated constraints aren't met.
type GetPanUpdateNextActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPanUpdateNextActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPanUpdateNextActionResponseMultiError) AllErrors() []error { return m }

// GetPanUpdateNextActionResponseValidationError is the validation error
// returned by GetPanUpdateNextActionResponse.Validate if the designated
// constraints aren't met.
type GetPanUpdateNextActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPanUpdateNextActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPanUpdateNextActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPanUpdateNextActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPanUpdateNextActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPanUpdateNextActionResponseValidationError) ErrorName() string {
	return "GetPanUpdateNextActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPanUpdateNextActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPanUpdateNextActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPanUpdateNextActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPanUpdateNextActionResponseValidationError{}

// Validate checks the field values on GetNudgesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetNudgesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNudgesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNudgesRequestMultiError, or nil if none found.
func (m *GetNudgesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNudgesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetNudgesRequestMultiError(errors)
	}

	return nil
}

// GetNudgesRequestMultiError is an error wrapping multiple validation errors
// returned by GetNudgesRequest.ValidateAll() if the designated constraints
// aren't met.
type GetNudgesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNudgesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNudgesRequestMultiError) AllErrors() []error { return m }

// GetNudgesRequestValidationError is the validation error returned by
// GetNudgesRequest.Validate if the designated constraints aren't met.
type GetNudgesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNudgesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNudgesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNudgesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNudgesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNudgesRequestValidationError) ErrorName() string { return "GetNudgesRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetNudgesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNudgesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNudgesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNudgesRequestValidationError{}

// Validate checks the field values on GetNudgesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetNudgesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNudgesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNudgesResponseMultiError, or nil if none found.
func (m *GetNudgesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNudgesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNudgesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNudgesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNudgesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetNudges()))
		i := 0
		for key := range m.GetNudges() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNudges()[key]
			_ = val

			// no validation rules for Nudges[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetNudgesResponseValidationError{
							field:  fmt.Sprintf("Nudges[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetNudgesResponseValidationError{
							field:  fmt.Sprintf("Nudges[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetNudgesResponseValidationError{
						field:  fmt.Sprintf("Nudges[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetNudgesResponseMultiError(errors)
	}

	return nil
}

// GetNudgesResponseMultiError is an error wrapping multiple validation errors
// returned by GetNudgesResponse.ValidateAll() if the designated constraints
// aren't met.
type GetNudgesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNudgesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNudgesResponseMultiError) AllErrors() []error { return m }

// GetNudgesResponseValidationError is the validation error returned by
// GetNudgesResponse.Validate if the designated constraints aren't met.
type GetNudgesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNudgesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNudgesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNudgesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNudgesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNudgesResponseValidationError) ErrorName() string {
	return "GetNudgesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNudgesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNudgesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNudgesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNudgesResponseValidationError{}

// Validate checks the field values on UploadEPANRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UploadEPANRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadEPANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadEPANRequestMultiError, or nil if none found.
func (m *UploadEPANRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadEPANRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	// no validation rules for Password

	// no validation rules for ClientRequestId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return UploadEPANRequestMultiError(errors)
	}

	return nil
}

// UploadEPANRequestMultiError is an error wrapping multiple validation errors
// returned by UploadEPANRequest.ValidateAll() if the designated constraints
// aren't met.
type UploadEPANRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadEPANRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadEPANRequestMultiError) AllErrors() []error { return m }

// UploadEPANRequestValidationError is the validation error returned by
// UploadEPANRequest.Validate if the designated constraints aren't met.
type UploadEPANRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadEPANRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadEPANRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadEPANRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadEPANRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadEPANRequestValidationError) ErrorName() string {
	return "UploadEPANRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadEPANRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadEPANRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadEPANRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadEPANRequestValidationError{}

// Validate checks the field values on UploadEPANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadEPANResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadEPANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadEPANResponseMultiError, or nil if none found.
func (m *UploadEPANResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadEPANResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEPANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEPANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEPANResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadEPANResponseMultiError(errors)
	}

	return nil
}

// UploadEPANResponseMultiError is an error wrapping multiple validation errors
// returned by UploadEPANResponse.ValidateAll() if the designated constraints
// aren't met.
type UploadEPANResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadEPANResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadEPANResponseMultiError) AllErrors() []error { return m }

// UploadEPANResponseValidationError is the validation error returned by
// UploadEPANResponse.Validate if the designated constraints aren't met.
type UploadEPANResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadEPANResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadEPANResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadEPANResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadEPANResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadEPANResponseValidationError) ErrorName() string {
	return "UploadEPANResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadEPANResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadEPANResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadEPANResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadEPANResponseValidationError{}

// Validate checks the field values on ValidatePANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidatePANRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidatePANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidatePANRequestMultiError, or nil if none found.
func (m *ValidatePANRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidatePANRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if utf8.RuneCountInString(m.GetPanNumber()) != 10 {
		err := ValidatePANRequestValidationError{
			field:  "PanNumber",
			reason: "value length must be 10 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return ValidatePANRequestMultiError(errors)
	}

	return nil
}

// ValidatePANRequestMultiError is an error wrapping multiple validation errors
// returned by ValidatePANRequest.ValidateAll() if the designated constraints
// aren't met.
type ValidatePANRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidatePANRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidatePANRequestMultiError) AllErrors() []error { return m }

// ValidatePANRequestValidationError is the validation error returned by
// ValidatePANRequest.Validate if the designated constraints aren't met.
type ValidatePANRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidatePANRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidatePANRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidatePANRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidatePANRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidatePANRequestValidationError) ErrorName() string {
	return "ValidatePANRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidatePANRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidatePANRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidatePANRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidatePANRequestValidationError{}

// Validate checks the field values on ValidatePANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidatePANResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidatePANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidatePANResponseMultiError, or nil if none found.
func (m *ValidatePANResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidatePANResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidatePANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidatePANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidatePANResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidatePANResponseValidationError{
					field:  "Record",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidatePANResponseValidationError{
					field:  "Record",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidatePANResponseValidationError{
				field:  "Record",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidatePANResponseMultiError(errors)
	}

	return nil
}

// ValidatePANResponseMultiError is an error wrapping multiple validation
// errors returned by ValidatePANResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidatePANResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidatePANResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidatePANResponseMultiError) AllErrors() []error { return m }

// ValidatePANResponseValidationError is the validation error returned by
// ValidatePANResponse.Validate if the designated constraints aren't met.
type ValidatePANResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidatePANResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidatePANResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidatePANResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidatePANResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidatePANResponseValidationError) ErrorName() string {
	return "ValidatePANResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidatePANResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidatePANResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidatePANResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidatePANResponseValidationError{}

// Validate checks the field values on NsdlPanInquiryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NsdlPanInquiryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NsdlPanInquiryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NsdlPanInquiryRequestMultiError, or nil if none found.
func (m *NsdlPanInquiryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *NsdlPanInquiryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPanNumber()) < 1 {
		err := NsdlPanInquiryRequestValidationError{
			field:  "PanNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return NsdlPanInquiryRequestMultiError(errors)
	}

	return nil
}

// NsdlPanInquiryRequestMultiError is an error wrapping multiple validation
// errors returned by NsdlPanInquiryRequest.ValidateAll() if the designated
// constraints aren't met.
type NsdlPanInquiryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NsdlPanInquiryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NsdlPanInquiryRequestMultiError) AllErrors() []error { return m }

// NsdlPanInquiryRequestValidationError is the validation error returned by
// NsdlPanInquiryRequest.Validate if the designated constraints aren't met.
type NsdlPanInquiryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NsdlPanInquiryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NsdlPanInquiryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NsdlPanInquiryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NsdlPanInquiryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NsdlPanInquiryRequestValidationError) ErrorName() string {
	return "NsdlPanInquiryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e NsdlPanInquiryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNsdlPanInquiryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NsdlPanInquiryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NsdlPanInquiryRequestValidationError{}

// Validate checks the field values on NsdlPanInquiryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NsdlPanInquiryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NsdlPanInquiryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NsdlPanInquiryResponseMultiError, or nil if none found.
func (m *NsdlPanInquiryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *NsdlPanInquiryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NsdlPanInquiryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NsdlPanInquiryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NsdlPanInquiryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanStatus

	// no validation rules for AadhaarStatus

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NsdlPanInquiryResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NsdlPanInquiryResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NsdlPanInquiryResponseValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LastUpdatedDate

	// no validation rules for PanCardName

	// no validation rules for ErrMsg

	if len(errors) > 0 {
		return NsdlPanInquiryResponseMultiError(errors)
	}

	return nil
}

// NsdlPanInquiryResponseMultiError is an error wrapping multiple validation
// errors returned by NsdlPanInquiryResponse.ValidateAll() if the designated
// constraints aren't met.
type NsdlPanInquiryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NsdlPanInquiryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NsdlPanInquiryResponseMultiError) AllErrors() []error { return m }

// NsdlPanInquiryResponseValidationError is the validation error returned by
// NsdlPanInquiryResponse.Validate if the designated constraints aren't met.
type NsdlPanInquiryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NsdlPanInquiryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NsdlPanInquiryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NsdlPanInquiryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NsdlPanInquiryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NsdlPanInquiryResponseValidationError) ErrorName() string {
	return "NsdlPanInquiryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e NsdlPanInquiryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNsdlPanInquiryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NsdlPanInquiryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NsdlPanInquiryResponseValidationError{}

// Validate checks the field values on InitiateEPANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateEPANRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateEPANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateEPANRequestMultiError, or nil if none found.
func (m *InitiateEPANRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateEPANRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return InitiateEPANRequestMultiError(errors)
	}

	return nil
}

// InitiateEPANRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateEPANRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateEPANRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateEPANRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateEPANRequestMultiError) AllErrors() []error { return m }

// InitiateEPANRequestValidationError is the validation error returned by
// InitiateEPANRequest.Validate if the designated constraints aren't met.
type InitiateEPANRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateEPANRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateEPANRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateEPANRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateEPANRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateEPANRequestValidationError) ErrorName() string {
	return "InitiateEPANRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateEPANRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateEPANRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateEPANRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateEPANRequestValidationError{}

// Validate checks the field values on InitiateEPANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateEPANResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateEPANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateEPANResponseMultiError, or nil if none found.
func (m *InitiateEPANResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateEPANResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEPANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEPANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEPANResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for KarzaToken

	// no validation rules for PanNumber

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEPANResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEPANResponseValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEPANResponseValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateEPANResponseMultiError(errors)
	}

	return nil
}

// InitiateEPANResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateEPANResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateEPANResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateEPANResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateEPANResponseMultiError) AllErrors() []error { return m }

// InitiateEPANResponseValidationError is the validation error returned by
// InitiateEPANResponse.Validate if the designated constraints aren't met.
type InitiateEPANResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateEPANResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateEPANResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateEPANResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateEPANResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateEPANResponseValidationError) ErrorName() string {
	return "InitiateEPANResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateEPANResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateEPANResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateEPANResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateEPANResponseValidationError{}

// Validate checks the field values on UpdateEPANInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEPANInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEPANInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEPANInfoRequestMultiError, or nil if none found.
func (m *UpdateEPANInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEPANInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetEPanData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEPANInfoRequestValidationError{
					field:  "EPanData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEPANInfoRequestValidationError{
					field:  "EPanData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEPanData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEPANInfoRequestValidationError{
				field:  "EPanData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventName

	// no validation rules for ActorId

	// no validation rules for UpdateSource

	if len(errors) > 0 {
		return UpdateEPANInfoRequestMultiError(errors)
	}

	return nil
}

// UpdateEPANInfoRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateEPANInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateEPANInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEPANInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEPANInfoRequestMultiError) AllErrors() []error { return m }

// UpdateEPANInfoRequestValidationError is the validation error returned by
// UpdateEPANInfoRequest.Validate if the designated constraints aren't met.
type UpdateEPANInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEPANInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEPANInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEPANInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEPANInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEPANInfoRequestValidationError) ErrorName() string {
	return "UpdateEPANInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEPANInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEPANInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEPANInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEPANInfoRequestValidationError{}

// Validate checks the field values on UpdateEPANInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEPANInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEPANInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEPANInfoResponseMultiError, or nil if none found.
func (m *UpdateEPANInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEPANInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEPANInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEPANInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEPANInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateEPANInfoResponseMultiError(errors)
	}

	return nil
}

// UpdateEPANInfoResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateEPANInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateEPANInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEPANInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEPANInfoResponseMultiError) AllErrors() []error { return m }

// UpdateEPANInfoResponseValidationError is the validation error returned by
// UpdateEPANInfoResponse.Validate if the designated constraints aren't met.
type UpdateEPANInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEPANInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEPANInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEPANInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEPANInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEPANInfoResponseValidationError) ErrorName() string {
	return "UpdateEPANInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEPANInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEPANInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEPANInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEPANInfoResponseValidationError{}

// Validate checks the field values on GetEPANAttemptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEPANAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEPANAttemptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEPANAttemptRequestMultiError, or nil if none found.
func (m *GetEPANAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEPANAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return GetEPANAttemptRequestMultiError(errors)
	}

	return nil
}

// GetEPANAttemptRequestMultiError is an error wrapping multiple validation
// errors returned by GetEPANAttemptRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEPANAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEPANAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEPANAttemptRequestMultiError) AllErrors() []error { return m }

// GetEPANAttemptRequestValidationError is the validation error returned by
// GetEPANAttemptRequest.Validate if the designated constraints aren't met.
type GetEPANAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEPANAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEPANAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEPANAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEPANAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEPANAttemptRequestValidationError) ErrorName() string {
	return "GetEPANAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEPANAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEPANAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEPANAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEPANAttemptRequestValidationError{}

// Validate checks the field values on GetEPANAttemptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEPANAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEPANAttemptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEPANAttemptResponseMultiError, or nil if none found.
func (m *GetEPANAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEPANAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEPANAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEPANAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEPANAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEPanAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEPANAttemptResponseValidationError{
					field:  "EPanAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEPANAttemptResponseValidationError{
					field:  "EPanAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEPanAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEPANAttemptResponseValidationError{
				field:  "EPanAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEPANAttemptResponseMultiError(errors)
	}

	return nil
}

// GetEPANAttemptResponseMultiError is an error wrapping multiple validation
// errors returned by GetEPANAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEPANAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEPANAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEPANAttemptResponseMultiError) AllErrors() []error { return m }

// GetEPANAttemptResponseValidationError is the validation error returned by
// GetEPANAttemptResponse.Validate if the designated constraints aren't met.
type GetEPANAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEPANAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEPANAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEPANAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEPANAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEPANAttemptResponseValidationError) ErrorName() string {
	return "GetEPANAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEPANAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEPANAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEPANAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEPANAttemptResponseValidationError{}

// Validate checks the field values on GetPANAadharLinkStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPANAadharLinkStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPANAadharLinkStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPANAadharLinkStatusRequestMultiError, or nil if none found.
func (m *GetPANAadharLinkStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPANAadharLinkStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ForceCache

	if len(errors) > 0 {
		return GetPANAadharLinkStatusRequestMultiError(errors)
	}

	return nil
}

// GetPANAadharLinkStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetPANAadharLinkStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetPANAadharLinkStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPANAadharLinkStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPANAadharLinkStatusRequestMultiError) AllErrors() []error { return m }

// GetPANAadharLinkStatusRequestValidationError is the validation error
// returned by GetPANAadharLinkStatusRequest.Validate if the designated
// constraints aren't met.
type GetPANAadharLinkStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPANAadharLinkStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPANAadharLinkStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPANAadharLinkStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPANAadharLinkStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPANAadharLinkStatusRequestValidationError) ErrorName() string {
	return "GetPANAadharLinkStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPANAadharLinkStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPANAadharLinkStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPANAadharLinkStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPANAadharLinkStatusRequestValidationError{}

// Validate checks the field values on GetPANAadharLinkStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPANAadharLinkStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPANAadharLinkStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPANAadharLinkStatusResponseMultiError, or nil if none found.
func (m *GetPANAadharLinkStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPANAadharLinkStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPANAadharLinkStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPANAadharLinkStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPANAadharLinkStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanAadharLinkStatus

	if all {
		switch v := interface{}(m.GetSyncedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPANAadharLinkStatusResponseValidationError{
					field:  "SyncedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPANAadharLinkStatusResponseValidationError{
					field:  "SyncedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSyncedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPANAadharLinkStatusResponseValidationError{
				field:  "SyncedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPANAadharLinkStatusResponseMultiError(errors)
	}

	return nil
}

// GetPANAadharLinkStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetPANAadharLinkStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetPANAadharLinkStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPANAadharLinkStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPANAadharLinkStatusResponseMultiError) AllErrors() []error { return m }

// GetPANAadharLinkStatusResponseValidationError is the validation error
// returned by GetPANAadharLinkStatusResponse.Validate if the designated
// constraints aren't met.
type GetPANAadharLinkStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPANAadharLinkStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPANAadharLinkStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPANAadharLinkStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPANAadharLinkStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPANAadharLinkStatusResponseValidationError) ErrorName() string {
	return "GetPANAadharLinkStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPANAadharLinkStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPANAadharLinkStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPANAadharLinkStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPANAadharLinkStatusResponseValidationError{}

// Validate checks the field values on RegisterEPANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterEPANRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterEPANRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterEPANRequestMultiError, or nil if none found.
func (m *RegisterEPANRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterEPANRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return RegisterEPANRequestMultiError(errors)
	}

	return nil
}

// RegisterEPANRequestMultiError is an error wrapping multiple validation
// errors returned by RegisterEPANRequest.ValidateAll() if the designated
// constraints aren't met.
type RegisterEPANRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterEPANRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterEPANRequestMultiError) AllErrors() []error { return m }

// RegisterEPANRequestValidationError is the validation error returned by
// RegisterEPANRequest.Validate if the designated constraints aren't met.
type RegisterEPANRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterEPANRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterEPANRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterEPANRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterEPANRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterEPANRequestValidationError) ErrorName() string {
	return "RegisterEPANRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterEPANRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterEPANRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterEPANRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterEPANRequestValidationError{}

// Validate checks the field values on RegisterEPANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterEPANResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterEPANResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterEPANResponseMultiError, or nil if none found.
func (m *RegisterEPANResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterEPANResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterEPANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterEPANResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterEPANResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegisterEPANResponseMultiError(errors)
	}

	return nil
}

// RegisterEPANResponseMultiError is an error wrapping multiple validation
// errors returned by RegisterEPANResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterEPANResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterEPANResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterEPANResponseMultiError) AllErrors() []error { return m }

// RegisterEPANResponseValidationError is the validation error returned by
// RegisterEPANResponse.Validate if the designated constraints aren't met.
type RegisterEPANResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterEPANResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterEPANResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterEPANResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterEPANResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterEPANResponseValidationError) ErrorName() string {
	return "RegisterEPANResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterEPANResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterEPANResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterEPANResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterEPANResponseValidationError{}

// Validate checks the field values on PurgeEPANDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PurgeEPANDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurgeEPANDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurgeEPANDataRequestMultiError, or nil if none found.
func (m *PurgeEPANDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PurgeEPANDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurgeEPANDataRequestValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurgeEPANDataRequestValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurgeEPANDataRequestValidationError{
				field:  "ExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurgeEPANDataRequestMultiError(errors)
	}

	return nil
}

// PurgeEPANDataRequestMultiError is an error wrapping multiple validation
// errors returned by PurgeEPANDataRequest.ValidateAll() if the designated
// constraints aren't met.
type PurgeEPANDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurgeEPANDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurgeEPANDataRequestMultiError) AllErrors() []error { return m }

// PurgeEPANDataRequestValidationError is the validation error returned by
// PurgeEPANDataRequest.Validate if the designated constraints aren't met.
type PurgeEPANDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurgeEPANDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurgeEPANDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurgeEPANDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurgeEPANDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurgeEPANDataRequestValidationError) ErrorName() string {
	return "PurgeEPANDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PurgeEPANDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurgeEPANDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurgeEPANDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurgeEPANDataRequestValidationError{}

// Validate checks the field values on PurgeEPANDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PurgeEPANDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurgeEPANDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurgeEPANDataResponseMultiError, or nil if none found.
func (m *PurgeEPANDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PurgeEPANDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurgeEPANDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurgeEPANDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurgeEPANDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurgeEPANDataResponseMultiError(errors)
	}

	return nil
}

// PurgeEPANDataResponseMultiError is an error wrapping multiple validation
// errors returned by PurgeEPANDataResponse.ValidateAll() if the designated
// constraints aren't met.
type PurgeEPANDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurgeEPANDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurgeEPANDataResponseMultiError) AllErrors() []error { return m }

// PurgeEPANDataResponseValidationError is the validation error returned by
// PurgeEPANDataResponse.Validate if the designated constraints aren't met.
type PurgeEPANDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurgeEPANDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurgeEPANDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurgeEPANDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurgeEPANDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurgeEPANDataResponseValidationError) ErrorName() string {
	return "PurgeEPANDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PurgeEPANDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurgeEPANDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurgeEPANDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurgeEPANDataResponseValidationError{}

// Validate checks the field values on GetAllEPANAttemptsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllEPANAttemptsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllEPANAttemptsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllEPANAttemptsRequestMultiError, or nil if none found.
func (m *GetAllEPANAttemptsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllEPANAttemptsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Limit

	if len(errors) > 0 {
		return GetAllEPANAttemptsRequestMultiError(errors)
	}

	return nil
}

// GetAllEPANAttemptsRequestMultiError is an error wrapping multiple validation
// errors returned by GetAllEPANAttemptsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetAllEPANAttemptsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllEPANAttemptsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllEPANAttemptsRequestMultiError) AllErrors() []error { return m }

// GetAllEPANAttemptsRequestValidationError is the validation error returned by
// GetAllEPANAttemptsRequest.Validate if the designated constraints aren't met.
type GetAllEPANAttemptsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllEPANAttemptsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllEPANAttemptsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllEPANAttemptsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllEPANAttemptsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllEPANAttemptsRequestValidationError) ErrorName() string {
	return "GetAllEPANAttemptsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllEPANAttemptsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllEPANAttemptsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllEPANAttemptsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllEPANAttemptsRequestValidationError{}

// Validate checks the field values on GetAllEPANAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllEPANAttemptsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllEPANAttemptsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllEPANAttemptsResponseMultiError, or nil if none found.
func (m *GetAllEPANAttemptsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllEPANAttemptsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllEPANAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllEPANAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllEPANAttemptsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEPanAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllEPANAttemptsResponseValidationError{
						field:  fmt.Sprintf("EPanAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllEPANAttemptsResponseValidationError{
						field:  fmt.Sprintf("EPanAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllEPANAttemptsResponseValidationError{
					field:  fmt.Sprintf("EPanAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllEPANAttemptsResponseMultiError(errors)
	}

	return nil
}

// GetAllEPANAttemptsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllEPANAttemptsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAllEPANAttemptsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllEPANAttemptsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllEPANAttemptsResponseMultiError) AllErrors() []error { return m }

// GetAllEPANAttemptsResponseValidationError is the validation error returned
// by GetAllEPANAttemptsResponse.Validate if the designated constraints aren't met.
type GetAllEPANAttemptsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllEPANAttemptsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllEPANAttemptsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllEPANAttemptsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllEPANAttemptsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllEPANAttemptsResponseValidationError) ErrorName() string {
	return "GetAllEPANAttemptsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllEPANAttemptsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllEPANAttemptsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllEPANAttemptsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllEPANAttemptsResponseValidationError{}

// Validate checks the field values on UploadPANToS3Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadPANToS3Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadPANToS3Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadPANToS3RequestMultiError, or nil if none found.
func (m *UploadPANToS3Request) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadPANToS3Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PanType

	// no validation rules for PanData

	// no validation rules for FileName

	// no validation rules for Flow

	if len(errors) > 0 {
		return UploadPANToS3RequestMultiError(errors)
	}

	return nil
}

// UploadPANToS3RequestMultiError is an error wrapping multiple validation
// errors returned by UploadPANToS3Request.ValidateAll() if the designated
// constraints aren't met.
type UploadPANToS3RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadPANToS3RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadPANToS3RequestMultiError) AllErrors() []error { return m }

// UploadPANToS3RequestValidationError is the validation error returned by
// UploadPANToS3Request.Validate if the designated constraints aren't met.
type UploadPANToS3RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadPANToS3RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadPANToS3RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadPANToS3RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadPANToS3RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadPANToS3RequestValidationError) ErrorName() string {
	return "UploadPANToS3RequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadPANToS3RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadPANToS3Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadPANToS3RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadPANToS3RequestValidationError{}

// Validate checks the field values on UploadPANToS3Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadPANToS3Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadPANToS3Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadPANToS3ResponseMultiError, or nil if none found.
func (m *UploadPANToS3Response) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadPANToS3Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadPANToS3ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadPANToS3ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadPANToS3ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3PathUrl

	if len(errors) > 0 {
		return UploadPANToS3ResponseMultiError(errors)
	}

	return nil
}

// UploadPANToS3ResponseMultiError is an error wrapping multiple validation
// errors returned by UploadPANToS3Response.ValidateAll() if the designated
// constraints aren't met.
type UploadPANToS3ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadPANToS3ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadPANToS3ResponseMultiError) AllErrors() []error { return m }

// UploadPANToS3ResponseValidationError is the validation error returned by
// UploadPANToS3Response.Validate if the designated constraints aren't met.
type UploadPANToS3ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadPANToS3ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadPANToS3ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadPANToS3ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadPANToS3ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadPANToS3ResponseValidationError) ErrorName() string {
	return "UploadPANToS3ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadPANToS3ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadPANToS3Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadPANToS3ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadPANToS3ResponseValidationError{}

// Validate checks the field values on GetEPANNextActionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEPANNextActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEPANNextActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEPANNextActionRequestMultiError, or nil if none found.
func (m *GetEPANNextActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEPANNextActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	// no validation rules for Blob

	if len(errors) > 0 {
		return GetEPANNextActionRequestMultiError(errors)
	}

	return nil
}

// GetEPANNextActionRequestMultiError is an error wrapping multiple validation
// errors returned by GetEPANNextActionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEPANNextActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEPANNextActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEPANNextActionRequestMultiError) AllErrors() []error { return m }

// GetEPANNextActionRequestValidationError is the validation error returned by
// GetEPANNextActionRequest.Validate if the designated constraints aren't met.
type GetEPANNextActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEPANNextActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEPANNextActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEPANNextActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEPANNextActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEPANNextActionRequestValidationError) ErrorName() string {
	return "GetEPANNextActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEPANNextActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEPANNextActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEPANNextActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEPANNextActionRequestValidationError{}

// Validate checks the field values on GetEPANNextActionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEPANNextActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEPANNextActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEPANNextActionResponseMultiError, or nil if none found.
func (m *GetEPANNextActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEPANNextActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEPANNextActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEPANNextActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEPANNextActionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEPANNextActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEPANNextActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEPANNextActionResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEPANNextActionResponseMultiError(errors)
	}

	return nil
}

// GetEPANNextActionResponseMultiError is an error wrapping multiple validation
// errors returned by GetEPANNextActionResponse.ValidateAll() if the
// designated constraints aren't met.
type GetEPANNextActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEPANNextActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEPANNextActionResponseMultiError) AllErrors() []error { return m }

// GetEPANNextActionResponseValidationError is the validation error returned by
// GetEPANNextActionResponse.Validate if the designated constraints aren't met.
type GetEPANNextActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEPANNextActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEPANNextActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEPANNextActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEPANNextActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEPANNextActionResponseValidationError) ErrorName() string {
	return "GetEPANNextActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEPANNextActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEPANNextActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEPANNextActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEPANNextActionResponseValidationError{}

// Validate checks the field values on FetchPANFromS3Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchPANFromS3Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchPANFromS3Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchPANFromS3RequestMultiError, or nil if none found.
func (m *FetchPANFromS3Request) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchPANFromS3Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filepath

	if len(errors) > 0 {
		return FetchPANFromS3RequestMultiError(errors)
	}

	return nil
}

// FetchPANFromS3RequestMultiError is an error wrapping multiple validation
// errors returned by FetchPANFromS3Request.ValidateAll() if the designated
// constraints aren't met.
type FetchPANFromS3RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchPANFromS3RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchPANFromS3RequestMultiError) AllErrors() []error { return m }

// FetchPANFromS3RequestValidationError is the validation error returned by
// FetchPANFromS3Request.Validate if the designated constraints aren't met.
type FetchPANFromS3RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchPANFromS3RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchPANFromS3RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchPANFromS3RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchPANFromS3RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchPANFromS3RequestValidationError) ErrorName() string {
	return "FetchPANFromS3RequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchPANFromS3RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchPANFromS3Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchPANFromS3RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchPANFromS3RequestValidationError{}

// Validate checks the field values on FetchPANFromS3Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchPANFromS3Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchPANFromS3Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchPANFromS3ResponseMultiError, or nil if none found.
func (m *FetchPANFromS3Response) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchPANFromS3Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchPANFromS3ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchPANFromS3ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchPANFromS3ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanData

	if len(errors) > 0 {
		return FetchPANFromS3ResponseMultiError(errors)
	}

	return nil
}

// FetchPANFromS3ResponseMultiError is an error wrapping multiple validation
// errors returned by FetchPANFromS3Response.ValidateAll() if the designated
// constraints aren't met.
type FetchPANFromS3ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchPANFromS3ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchPANFromS3ResponseMultiError) AllErrors() []error { return m }

// FetchPANFromS3ResponseValidationError is the validation error returned by
// FetchPANFromS3Response.Validate if the designated constraints aren't met.
type FetchPANFromS3ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchPANFromS3ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchPANFromS3ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchPANFromS3ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchPANFromS3ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchPANFromS3ResponseValidationError) ErrorName() string {
	return "FetchPANFromS3ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchPANFromS3ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchPANFromS3Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchPANFromS3ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchPANFromS3ResponseValidationError{}

// Validate checks the field values on UploadScannedPanDocumentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadScannedPanDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadScannedPanDocumentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadScannedPanDocumentRequestMultiError, or nil if none found.
func (m *UploadScannedPanDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadScannedPanDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for PanImage

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return UploadScannedPanDocumentRequestMultiError(errors)
	}

	return nil
}

// UploadScannedPanDocumentRequestMultiError is an error wrapping multiple
// validation errors returned by UploadScannedPanDocumentRequest.ValidateAll()
// if the designated constraints aren't met.
type UploadScannedPanDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadScannedPanDocumentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadScannedPanDocumentRequestMultiError) AllErrors() []error { return m }

// UploadScannedPanDocumentRequestValidationError is the validation error
// returned by UploadScannedPanDocumentRequest.Validate if the designated
// constraints aren't met.
type UploadScannedPanDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadScannedPanDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadScannedPanDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadScannedPanDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadScannedPanDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadScannedPanDocumentRequestValidationError) ErrorName() string {
	return "UploadScannedPanDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadScannedPanDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadScannedPanDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadScannedPanDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadScannedPanDocumentRequestValidationError{}

// Validate checks the field values on UploadScannedPanDocumentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadScannedPanDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadScannedPanDocumentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadScannedPanDocumentResponseMultiError, or nil if none found.
func (m *UploadScannedPanDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadScannedPanDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadScannedPanDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadScannedPanDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadScannedPanDocumentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadScannedPanDocumentResponseMultiError(errors)
	}

	return nil
}

// UploadScannedPanDocumentResponseMultiError is an error wrapping multiple
// validation errors returned by
// UploadScannedPanDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadScannedPanDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadScannedPanDocumentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadScannedPanDocumentResponseMultiError) AllErrors() []error { return m }

// UploadScannedPanDocumentResponseValidationError is the validation error
// returned by UploadScannedPanDocumentResponse.Validate if the designated
// constraints aren't met.
type UploadScannedPanDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadScannedPanDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadScannedPanDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadScannedPanDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadScannedPanDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadScannedPanDocumentResponseValidationError) ErrorName() string {
	return "UploadScannedPanDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadScannedPanDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadScannedPanDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadScannedPanDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadScannedPanDocumentResponseValidationError{}

// Validate checks the field values on GetScannedPanDocumentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScannedPanDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScannedPanDocumentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScannedPanDocumentRequestMultiError, or nil if none found.
func (m *GetScannedPanDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScannedPanDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WantImageInBase64

	switch v := m.Identifier.(type) {
	case *GetScannedPanDocumentRequest_ActorId:
		if v == nil {
			err := GetScannedPanDocumentRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *GetScannedPanDocumentRequest_ClientReqId:
		if v == nil {
			err := GetScannedPanDocumentRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ClientReqId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetScannedPanDocumentRequestMultiError(errors)
	}

	return nil
}

// GetScannedPanDocumentRequestMultiError is an error wrapping multiple
// validation errors returned by GetScannedPanDocumentRequest.ValidateAll() if
// the designated constraints aren't met.
type GetScannedPanDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScannedPanDocumentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScannedPanDocumentRequestMultiError) AllErrors() []error { return m }

// GetScannedPanDocumentRequestValidationError is the validation error returned
// by GetScannedPanDocumentRequest.Validate if the designated constraints
// aren't met.
type GetScannedPanDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScannedPanDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScannedPanDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScannedPanDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScannedPanDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScannedPanDocumentRequestValidationError) ErrorName() string {
	return "GetScannedPanDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetScannedPanDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScannedPanDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScannedPanDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScannedPanDocumentRequestValidationError{}

// Validate checks the field values on GetScannedPanDocumentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScannedPanDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScannedPanDocumentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetScannedPanDocumentResponseMultiError, or nil if none found.
func (m *GetScannedPanDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScannedPanDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScannedPanDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScannedPanDocumentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScannedPanDocumentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScannedPanAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScannedPanDocumentResponseValidationError{
					field:  "ScannedPanAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScannedPanDocumentResponseValidationError{
					field:  "ScannedPanAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScannedPanAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScannedPanDocumentResponseValidationError{
				field:  "ScannedPanAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScannedPanDocumentResponseMultiError(errors)
	}

	return nil
}

// GetScannedPanDocumentResponseMultiError is an error wrapping multiple
// validation errors returned by GetScannedPanDocumentResponse.ValidateAll()
// if the designated constraints aren't met.
type GetScannedPanDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScannedPanDocumentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScannedPanDocumentResponseMultiError) AllErrors() []error { return m }

// GetScannedPanDocumentResponseValidationError is the validation error
// returned by GetScannedPanDocumentResponse.Validate if the designated
// constraints aren't met.
type GetScannedPanDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScannedPanDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScannedPanDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScannedPanDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScannedPanDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScannedPanDocumentResponseValidationError) ErrorName() string {
	return "GetScannedPanDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetScannedPanDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScannedPanDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScannedPanDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScannedPanDocumentResponseValidationError{}

// Validate checks the field values on ValidateV2Request with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ValidateV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateV2Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateV2RequestMultiError, or nil if none found.
func (m *ValidateV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for ActorId

	// no validation rules for Pan

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateV2RequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateV2RequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateV2RequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateV2RequestMultiError(errors)
	}

	return nil
}

// ValidateV2RequestMultiError is an error wrapping multiple validation errors
// returned by ValidateV2Request.ValidateAll() if the designated constraints
// aren't met.
type ValidateV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateV2RequestMultiError) AllErrors() []error { return m }

// ValidateV2RequestValidationError is the validation error returned by
// ValidateV2Request.Validate if the designated constraints aren't met.
type ValidateV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateV2RequestValidationError) ErrorName() string {
	return "ValidateV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateV2RequestValidationError{}

// Validate checks the field values on ValidateV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateV2ResponseMultiError, or nil if none found.
func (m *ValidateV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateV2ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanValid

	// no validation rules for NameMatch

	// no validation rules for DobMatch

	// no validation rules for PanAadharLinkStatus

	if len(errors) > 0 {
		return ValidateV2ResponseMultiError(errors)
	}

	return nil
}

// ValidateV2ResponseMultiError is an error wrapping multiple validation errors
// returned by ValidateV2Response.ValidateAll() if the designated constraints
// aren't met.
type ValidateV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateV2ResponseMultiError) AllErrors() []error { return m }

// ValidateV2ResponseValidationError is the validation error returned by
// ValidateV2Response.Validate if the designated constraints aren't met.
type ValidateV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateV2ResponseValidationError) ErrorName() string {
	return "ValidateV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateV2ResponseValidationError{}
