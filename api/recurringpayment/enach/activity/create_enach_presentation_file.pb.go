// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/recurringpayment/enach/activity/create_enach_presentation_file.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateEnachPresentationFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *CreateEnachPresentationFileRequest) Reset() {
	*x = CreateEnachPresentationFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnachPresentationFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnachPresentationFileRequest) ProtoMessage() {}

func (x *CreateEnachPresentationFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnachPresentationFileRequest.ProtoReflect.Descriptor instead.
func (*CreateEnachPresentationFileRequest) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescGZIP(), []int{0}
}

func (x *CreateEnachPresentationFileRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type CreateEnachPresentationFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// list of action ids for which the file generation was successful
	PresentedActionExecutionIds []string `protobuf:"bytes,2,rep,name=presented_action_execution_ids,json=presentedActionExecutionIds,proto3" json:"presented_action_execution_ids,omitempty"`
	// s3 path of the created presentation file.
	PresentationFileS3Path string `protobuf:"bytes,3,opt,name=presentation_file_s3_path,json=presentationFileS3Path,proto3" json:"presentation_file_s3_path,omitempty"`
	// name of the file to be used when uploading at vendor.
	PresentationFileName string `protobuf:"bytes,4,opt,name=presentation_file_name,json=presentationFileName,proto3" json:"presentation_file_name,omitempty"`
	// execution id of the batch in which the execution was presented.
	PresentationBatchExecutionId string `protobuf:"bytes,5,opt,name=presentation_batch_execution_id,json=presentationBatchExecutionId,proto3" json:"presentation_batch_execution_id,omitempty"`
}

func (x *CreateEnachPresentationFileResponse) Reset() {
	*x = CreateEnachPresentationFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnachPresentationFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnachPresentationFileResponse) ProtoMessage() {}

func (x *CreateEnachPresentationFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnachPresentationFileResponse.ProtoReflect.Descriptor instead.
func (*CreateEnachPresentationFileResponse) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescGZIP(), []int{1}
}

func (x *CreateEnachPresentationFileResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CreateEnachPresentationFileResponse) GetPresentedActionExecutionIds() []string {
	if x != nil {
		return x.PresentedActionExecutionIds
	}
	return nil
}

func (x *CreateEnachPresentationFileResponse) GetPresentationFileS3Path() string {
	if x != nil {
		return x.PresentationFileS3Path
	}
	return ""
}

func (x *CreateEnachPresentationFileResponse) GetPresentationFileName() string {
	if x != nil {
		return x.PresentationFileName
	}
	return ""
}

func (x *CreateEnachPresentationFileResponse) GetPresentationBatchExecutionId() string {
	if x != nil {
		return x.PresentationBatchExecutionId
	}
	return ""
}

var File_api_recurringpayment_enach_activity_create_enach_presentation_file_proto protoreflect.FileDescriptor

var file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDesc = []byte{
	0x0a, 0x48, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x61, 0x63, 0x68, 0x2f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x61,
	0x63, 0x68, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x61,
	0x63, 0x68, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x23, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x6e, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x61, 0x63, 0x68, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0xef, 0x02, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x61, 0x63, 0x68,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x1e, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x73, 0x33, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x33, 0x50, 0x61, 0x74, 0x68, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x1f, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x42, 0x78, 0x0a, 0x3a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x65, 0x6e, 0x61, 0x63, 0x68, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5a, 0x3a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e,
	0x61, 0x63, 0x68, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescOnce sync.Once
	file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescData = file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDesc
)

func file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescGZIP() []byte {
	file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescOnce.Do(func() {
		file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescData)
	})
	return file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDescData
}

var file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_goTypes = []interface{}{
	(*CreateEnachPresentationFileRequest)(nil),  // 0: recurringpayment.enach.activity.CreateEnachPresentationFileRequest
	(*CreateEnachPresentationFileResponse)(nil), // 1: recurringpayment.enach.activity.CreateEnachPresentationFileResponse
	(*activity.RequestHeader)(nil),              // 2: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),             // 3: celestial.activity.ResponseHeader
}
var file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_depIdxs = []int32{
	2, // 0: recurringpayment.enach.activity.CreateEnachPresentationFileRequest.request_header:type_name -> celestial.activity.RequestHeader
	3, // 1: recurringpayment.enach.activity.CreateEnachPresentationFileResponse.response_header:type_name -> celestial.activity.ResponseHeader
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_init() }
func file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_init() {
	if File_api_recurringpayment_enach_activity_create_enach_presentation_file_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnachPresentationFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnachPresentationFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_goTypes,
		DependencyIndexes: file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_depIdxs,
		MessageInfos:      file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_msgTypes,
	}.Build()
	File_api_recurringpayment_enach_activity_create_enach_presentation_file_proto = out.File
	file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_rawDesc = nil
	file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_goTypes = nil
	file_api_recurringpayment_enach_activity_create_enach_presentation_file_proto_depIdxs = nil
}
