// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/recurringpayment/enach/enach_mandate_action.pb.go

package enach

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing ActionMetadata while reading from DB
func (a *ActionMetadata) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ActionMetadata in string format in DB
func (a *ActionMetadata) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ActionMetadata
func (a *ActionMetadata) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ActionMetadata
func (a *ActionMetadata) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing ActionDetailedStatus while reading from DB
func (a *ActionDetailedStatus) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the ActionDetailedStatus in string format in DB
func (a *ActionDetailedStatus) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for ActionDetailedStatus
func (a *ActionDetailedStatus) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for ActionDetailedStatus
func (a *ActionDetailedStatus) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
