// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/dynamic_ui_element/service.proto

package dynamic_ui_element

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateOrUpdateVariantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VariantName string       `protobuf:"bytes,1,opt,name=variant_name,json=variantName,proto3" json:"variant_name,omitempty"`
	ContentJson *ContentJson `protobuf:"bytes,2,opt,name=content_json,json=contentJson,proto3" json:"content_json,omitempty"`
}

func (x *CreateOrUpdateVariantRequest) Reset() {
	*x = CreateOrUpdateVariantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateVariantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateVariantRequest) ProtoMessage() {}

func (x *CreateOrUpdateVariantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateVariantRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateVariantRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOrUpdateVariantRequest) GetVariantName() string {
	if x != nil {
		return x.VariantName
	}
	return ""
}

func (x *CreateOrUpdateVariantRequest) GetContentJson() *ContentJson {
	if x != nil {
		return x.ContentJson
	}
	return nil
}

type CreateOrUpdateVariantResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DynamicUiElement *DynamicUIElement `protobuf:"bytes,2,opt,name=dynamic_ui_element,json=dynamicUiElement,proto3" json:"dynamic_ui_element,omitempty"`
}

func (x *CreateOrUpdateVariantResponse) Reset() {
	*x = CreateOrUpdateVariantResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateVariantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateVariantResponse) ProtoMessage() {}

func (x *CreateOrUpdateVariantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateVariantResponse.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateVariantResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateOrUpdateVariantResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateOrUpdateVariantResponse) GetDynamicUiElement() *DynamicUIElement {
	if x != nil {
		return x.DynamicUiElement
	}
	return nil
}

type UpdateDynamicUIElementEvaluatorConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DynamicUiElementEvaluatorConfig *DynamicUIElementEvaluatorConfig `protobuf:"bytes,1,opt,name=dynamic_ui_element_evaluator_config,json=dynamicUiElementEvaluatorConfig,proto3" json:"dynamic_ui_element_evaluator_config,omitempty"`
}

func (x *UpdateDynamicUIElementEvaluatorConfigRequest) Reset() {
	*x = UpdateDynamicUIElementEvaluatorConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDynamicUIElementEvaluatorConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDynamicUIElementEvaluatorConfigRequest) ProtoMessage() {}

func (x *UpdateDynamicUIElementEvaluatorConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDynamicUIElementEvaluatorConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateDynamicUIElementEvaluatorConfigRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateDynamicUIElementEvaluatorConfigRequest) GetDynamicUiElementEvaluatorConfig() *DynamicUIElementEvaluatorConfig {
	if x != nil {
		return x.DynamicUiElementEvaluatorConfig
	}
	return nil
}

type UpdateDynamicUIElementEvaluatorConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                          *rpc.Status                      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DynamicUiElementEvaluatorConfig *DynamicUIElementEvaluatorConfig `protobuf:"bytes,2,opt,name=dynamic_ui_element_evaluator_config,json=dynamicUiElementEvaluatorConfig,proto3" json:"dynamic_ui_element_evaluator_config,omitempty"`
}

func (x *UpdateDynamicUIElementEvaluatorConfigResponse) Reset() {
	*x = UpdateDynamicUIElementEvaluatorConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDynamicUIElementEvaluatorConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDynamicUIElementEvaluatorConfigResponse) ProtoMessage() {}

func (x *UpdateDynamicUIElementEvaluatorConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDynamicUIElementEvaluatorConfigResponse.ProtoReflect.Descriptor instead.
func (*UpdateDynamicUIElementEvaluatorConfigResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDynamicUIElementEvaluatorConfigResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateDynamicUIElementEvaluatorConfigResponse) GetDynamicUiElementEvaluatorConfig() *DynamicUIElementEvaluatorConfig {
	if x != nil {
		return x.DynamicUiElementEvaluatorConfig
	}
	return nil
}

type EvaluateAndFetchDynamicUIElementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Screen  DynamicUIScreen  `protobuf:"varint,1,opt,name=screen,proto3,enum=api.investment.dynamic_ui_element.DynamicUIScreen" json:"screen,omitempty"`
	Usecase DynamicUIUsecase `protobuf:"varint,2,opt,name=usecase,proto3,enum=api.investment.dynamic_ui_element.DynamicUIUsecase" json:"usecase,omitempty"`
	ActorId string           `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *EvaluateAndFetchDynamicUIElementRequest) Reset() {
	*x = EvaluateAndFetchDynamicUIElementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateAndFetchDynamicUIElementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateAndFetchDynamicUIElementRequest) ProtoMessage() {}

func (x *EvaluateAndFetchDynamicUIElementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateAndFetchDynamicUIElementRequest.ProtoReflect.Descriptor instead.
func (*EvaluateAndFetchDynamicUIElementRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP(), []int{4}
}

func (x *EvaluateAndFetchDynamicUIElementRequest) GetScreen() DynamicUIScreen {
	if x != nil {
		return x.Screen
	}
	return DynamicUIScreen_DYNAMIC_UI_SCREEN_UNSPECIFIED
}

func (x *EvaluateAndFetchDynamicUIElementRequest) GetUsecase() DynamicUIUsecase {
	if x != nil {
		return x.Usecase
	}
	return DynamicUIUsecase_DYNAMIC_UI_USECASE_UNSPECIFIED
}

func (x *EvaluateAndFetchDynamicUIElementRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type EvaluateAndFetchDynamicUIElementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DynamicUiElement *DynamicUIElement `protobuf:"bytes,2,opt,name=dynamic_ui_element,json=dynamicUiElement,proto3" json:"dynamic_ui_element,omitempty"`
}

func (x *EvaluateAndFetchDynamicUIElementResponse) Reset() {
	*x = EvaluateAndFetchDynamicUIElementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateAndFetchDynamicUIElementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateAndFetchDynamicUIElementResponse) ProtoMessage() {}

func (x *EvaluateAndFetchDynamicUIElementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_dynamic_ui_element_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateAndFetchDynamicUIElementResponse.ProtoReflect.Descriptor instead.
func (*EvaluateAndFetchDynamicUIElementResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP(), []int{5}
}

func (x *EvaluateAndFetchDynamicUIElementResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *EvaluateAndFetchDynamicUIElementResponse) GetDynamicUiElement() *DynamicUIElement {
	if x != nil {
		return x.DynamicUiElement
	}
	return nil
}

var File_api_investment_dynamic_ui_element_service_proto protoreflect.FileDescriptor

var file_api_investment_dynamic_ui_element_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x21, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x4b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x94, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x0b, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0xa7, 0x01, 0x0a, 0x1d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72,
	0x69, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x61, 0x0a, 0x12, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x10, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x69, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x22, 0xc1, 0x01, 0x0a, 0x2c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x23, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55,
	0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x1f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x55, 0x69, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xe7, 0x01, 0x0a, 0x2d, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x90, 0x01, 0x0a, 0x23, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x1f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x69, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0xdf, 0x01, 0x0a, 0x27, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41,
	0x6e, 0x64, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a,
	0x0a, 0x06, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x52, 0x06, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x4d, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x63, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x55, 0x73, 0x65, 0x63, 0x61, 0x73, 0x65,
	0x52, 0x07, 0x75, 0x73, 0x65, 0x63, 0x61, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0xb2, 0x01, 0x0a, 0x28, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x64, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x12, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x55, 0x69, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x32, 0xc1, 0x04, 0x0a, 0x17, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x12,
	0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xca, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x50, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55,
	0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xbb, 0x01, 0x0a, 0x20, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x64, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x55, 0x49, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x4b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x49, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x74, 0x0a,
	0x38, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75,
	0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x75, 0x69, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_dynamic_ui_element_service_proto_rawDescOnce sync.Once
	file_api_investment_dynamic_ui_element_service_proto_rawDescData = file_api_investment_dynamic_ui_element_service_proto_rawDesc
)

func file_api_investment_dynamic_ui_element_service_proto_rawDescGZIP() []byte {
	file_api_investment_dynamic_ui_element_service_proto_rawDescOnce.Do(func() {
		file_api_investment_dynamic_ui_element_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_dynamic_ui_element_service_proto_rawDescData)
	})
	return file_api_investment_dynamic_ui_element_service_proto_rawDescData
}

var file_api_investment_dynamic_ui_element_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_investment_dynamic_ui_element_service_proto_goTypes = []interface{}{
	(*CreateOrUpdateVariantRequest)(nil),                  // 0: api.investment.dynamic_ui_element.CreateOrUpdateVariantRequest
	(*CreateOrUpdateVariantResponse)(nil),                 // 1: api.investment.dynamic_ui_element.CreateOrUpdateVariantResponse
	(*UpdateDynamicUIElementEvaluatorConfigRequest)(nil),  // 2: api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigRequest
	(*UpdateDynamicUIElementEvaluatorConfigResponse)(nil), // 3: api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigResponse
	(*EvaluateAndFetchDynamicUIElementRequest)(nil),       // 4: api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementRequest
	(*EvaluateAndFetchDynamicUIElementResponse)(nil),      // 5: api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementResponse
	(*ContentJson)(nil),                                   // 6: api.investment.dynamic_ui_element.ContentJson
	(*rpc.Status)(nil),                                    // 7: rpc.Status
	(*DynamicUIElement)(nil),                              // 8: api.investment.dynamic_ui_element.DynamicUIElement
	(*DynamicUIElementEvaluatorConfig)(nil),               // 9: api.investment.dynamic_ui_element.DynamicUIElementEvaluatorConfig
	(DynamicUIScreen)(0),                                  // 10: api.investment.dynamic_ui_element.DynamicUIScreen
	(DynamicUIUsecase)(0),                                 // 11: api.investment.dynamic_ui_element.DynamicUIUsecase
}
var file_api_investment_dynamic_ui_element_service_proto_depIdxs = []int32{
	6,  // 0: api.investment.dynamic_ui_element.CreateOrUpdateVariantRequest.content_json:type_name -> api.investment.dynamic_ui_element.ContentJson
	7,  // 1: api.investment.dynamic_ui_element.CreateOrUpdateVariantResponse.status:type_name -> rpc.Status
	8,  // 2: api.investment.dynamic_ui_element.CreateOrUpdateVariantResponse.dynamic_ui_element:type_name -> api.investment.dynamic_ui_element.DynamicUIElement
	9,  // 3: api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigRequest.dynamic_ui_element_evaluator_config:type_name -> api.investment.dynamic_ui_element.DynamicUIElementEvaluatorConfig
	7,  // 4: api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigResponse.status:type_name -> rpc.Status
	9,  // 5: api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigResponse.dynamic_ui_element_evaluator_config:type_name -> api.investment.dynamic_ui_element.DynamicUIElementEvaluatorConfig
	10, // 6: api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementRequest.screen:type_name -> api.investment.dynamic_ui_element.DynamicUIScreen
	11, // 7: api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementRequest.usecase:type_name -> api.investment.dynamic_ui_element.DynamicUIUsecase
	7,  // 8: api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementResponse.status:type_name -> rpc.Status
	8,  // 9: api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementResponse.dynamic_ui_element:type_name -> api.investment.dynamic_ui_element.DynamicUIElement
	0,  // 10: api.investment.dynamic_ui_element.DynamicUIElementService.CreateOrUpdateVariant:input_type -> api.investment.dynamic_ui_element.CreateOrUpdateVariantRequest
	2,  // 11: api.investment.dynamic_ui_element.DynamicUIElementService.UpdateDynamicUIElementEvaluatorConfig:input_type -> api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigRequest
	4,  // 12: api.investment.dynamic_ui_element.DynamicUIElementService.EvaluateAndFetchDynamicUIElement:input_type -> api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementRequest
	1,  // 13: api.investment.dynamic_ui_element.DynamicUIElementService.CreateOrUpdateVariant:output_type -> api.investment.dynamic_ui_element.CreateOrUpdateVariantResponse
	3,  // 14: api.investment.dynamic_ui_element.DynamicUIElementService.UpdateDynamicUIElementEvaluatorConfig:output_type -> api.investment.dynamic_ui_element.UpdateDynamicUIElementEvaluatorConfigResponse
	5,  // 15: api.investment.dynamic_ui_element.DynamicUIElementService.EvaluateAndFetchDynamicUIElement:output_type -> api.investment.dynamic_ui_element.EvaluateAndFetchDynamicUIElementResponse
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_investment_dynamic_ui_element_service_proto_init() }
func file_api_investment_dynamic_ui_element_service_proto_init() {
	if File_api_investment_dynamic_ui_element_service_proto != nil {
		return
	}
	file_api_investment_dynamic_ui_element_dynamic_ui_element_proto_init()
	file_api_investment_dynamic_ui_element_dynamic_ui_element_evaluator_config_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_investment_dynamic_ui_element_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrUpdateVariantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_dynamic_ui_element_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrUpdateVariantResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_dynamic_ui_element_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDynamicUIElementEvaluatorConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_dynamic_ui_element_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDynamicUIElementEvaluatorConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_dynamic_ui_element_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateAndFetchDynamicUIElementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_dynamic_ui_element_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateAndFetchDynamicUIElementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_dynamic_ui_element_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_investment_dynamic_ui_element_service_proto_goTypes,
		DependencyIndexes: file_api_investment_dynamic_ui_element_service_proto_depIdxs,
		MessageInfos:      file_api_investment_dynamic_ui_element_service_proto_msgTypes,
	}.Build()
	File_api_investment_dynamic_ui_element_service_proto = out.File
	file_api_investment_dynamic_ui_element_service_proto_rawDesc = nil
	file_api_investment_dynamic_ui_element_service_proto_goTypes = nil
	file_api_investment_dynamic_ui_element_service_proto_depIdxs = nil
}
