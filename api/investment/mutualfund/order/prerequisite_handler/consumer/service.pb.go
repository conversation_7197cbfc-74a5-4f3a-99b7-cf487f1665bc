// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/mutualfund/order/prerequisite_handler/consumer/service.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	prerequisite_handler "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessPrerequisiteFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader   *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Type            prerequisite_handler.Type    `protobuf:"varint,2,opt,name=type,proto3,enum=api.investment.mutualfund.order.prerequisite_handler.Type" json:"type,omitempty"`
	FilePath        string                       `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	Vendor          vendorgateway.Vendor         `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	ActorIds        []string                     `protobuf:"bytes,5,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
	ClientRequestId string                       `protobuf:"bytes,6,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *ProcessPrerequisiteFileRequest) Reset() {
	*x = ProcessPrerequisiteFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPrerequisiteFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPrerequisiteFileRequest) ProtoMessage() {}

func (x *ProcessPrerequisiteFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPrerequisiteFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessPrerequisiteFileRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessPrerequisiteFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessPrerequisiteFileRequest) GetType() prerequisite_handler.Type {
	if x != nil {
		return x.Type
	}
	return prerequisite_handler.Type(0)
}

func (x *ProcessPrerequisiteFileRequest) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ProcessPrerequisiteFileRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessPrerequisiteFileRequest) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

func (x *ProcessPrerequisiteFileRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type ProcessPrerequisiteFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessPrerequisiteFileResponse) Reset() {
	*x = ProcessPrerequisiteFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPrerequisiteFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPrerequisiteFileResponse) ProtoMessage() {}

func (x *ProcessPrerequisiteFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPrerequisiteFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessPrerequisiteFileResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessPrerequisiteFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type GetPreRequisiteStatusRetryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader                       `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Request       *prerequisite_handler.GetPreRequisiteStatusRequest `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
}

func (x *GetPreRequisiteStatusRetryRequest) Reset() {
	*x = GetPreRequisiteStatusRetryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreRequisiteStatusRetryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreRequisiteStatusRetryRequest) ProtoMessage() {}

func (x *GetPreRequisiteStatusRetryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreRequisiteStatusRetryRequest.ProtoReflect.Descriptor instead.
func (*GetPreRequisiteStatusRetryRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetPreRequisiteStatusRetryRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetPreRequisiteStatusRetryRequest) GetRequest() *prerequisite_handler.GetPreRequisiteStatusRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type GetPreRequisiteStatusRetryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *GetPreRequisiteStatusRetryResponse) Reset() {
	*x = GetPreRequisiteStatusRetryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreRequisiteStatusRetryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreRequisiteStatusRetryResponse) ProtoMessage() {}

func (x *GetPreRequisiteStatusRetryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreRequisiteStatusRetryResponse.ProtoReflect.Descriptor instead.
func (*GetPreRequisiteStatusRetryResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPreRequisiteStatusRetryResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto protoreflect.FileDescriptor

var file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x4b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x37, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xca, 0x02, 0x0a,
	0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x73, 0x69, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74,
	0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a,
	0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0xd6, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x6c, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x52, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6c, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xbd, 0x03, 0x0a, 0x14,
	0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x12, 0xcc, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x57, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x65,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x72, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x58, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xd5, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x12, 0x5a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70,
	0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x72, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x5b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x65, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x56, 0x5a, 0x54, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescOnce sync.Once
	file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescData = file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDesc
)

func file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescGZIP() []byte {
	file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescData)
	})
	return file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDescData
}

var file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_goTypes = []interface{}{
	(*ProcessPrerequisiteFileRequest)(nil),                    // 0: api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileRequest
	(*ProcessPrerequisiteFileResponse)(nil),                   // 1: api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileResponse
	(*GetPreRequisiteStatusRetryRequest)(nil),                 // 2: api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryRequest
	(*GetPreRequisiteStatusRetryResponse)(nil),                // 3: api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryResponse
	(*queue.ConsumerRequestHeader)(nil),                       // 4: queue.ConsumerRequestHeader
	(prerequisite_handler.Type)(0),                            // 5: api.investment.mutualfund.order.prerequisite_handler.Type
	(vendorgateway.Vendor)(0),                                 // 6: vendorgateway.Vendor
	(*queue.ConsumerResponseHeader)(nil),                      // 7: queue.ConsumerResponseHeader
	(*prerequisite_handler.GetPreRequisiteStatusRequest)(nil), // 8: api.investment.mutualfund.order.prerequisite_handler.GetPreRequisiteStatusRequest
}
var file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_depIdxs = []int32{
	4, // 0: api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	5, // 1: api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileRequest.type:type_name -> api.investment.mutualfund.order.prerequisite_handler.Type
	6, // 2: api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileRequest.vendor:type_name -> vendorgateway.Vendor
	7, // 3: api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	4, // 4: api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryRequest.request_header:type_name -> queue.ConsumerRequestHeader
	8, // 5: api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryRequest.request:type_name -> api.investment.mutualfund.order.prerequisite_handler.GetPreRequisiteStatusRequest
	7, // 6: api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 7: api.investment.mutualfund.prerequisite_handler.consumer.PrerequisiteConsumer.ProcessPrerequisiteFile:input_type -> api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileRequest
	2, // 8: api.investment.mutualfund.prerequisite_handler.consumer.PrerequisiteConsumer.GetPreRequisiteStatusRetry:input_type -> api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryRequest
	1, // 9: api.investment.mutualfund.prerequisite_handler.consumer.PrerequisiteConsumer.ProcessPrerequisiteFile:output_type -> api.investment.mutualfund.prerequisite_handler.consumer.ProcessPrerequisiteFileResponse
	3, // 10: api.investment.mutualfund.prerequisite_handler.consumer.PrerequisiteConsumer.GetPreRequisiteStatusRetry:output_type -> api.investment.mutualfund.prerequisite_handler.consumer.GetPreRequisiteStatusRetryResponse
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_init() }
func file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_init() {
	if File_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPrerequisiteFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPrerequisiteFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreRequisiteStatusRetryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreRequisiteStatusRetryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_msgTypes,
	}.Build()
	File_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto = out.File
	file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_rawDesc = nil
	file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_goTypes = nil
	file_api_investment_mutualfund_order_prerequisite_handler_consumer_service_proto_depIdxs = nil
}
