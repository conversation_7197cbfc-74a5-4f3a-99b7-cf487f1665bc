// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/nudge/journey/service.proto

package journey

import (
	rpc "github.com/epifi/be-common/api/rpc"
	nudge "github.com/epifi/gamma/api/nudge"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateJourneyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// internal only description of journey
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// logical expression of segment ids or any other expressions to which the journey will be displayed e.g. IsMember(S1) && !IsMember(S2) && IsTPAPUser()
	// true would mean the journey will be displayed to all users
	EntryExpression string `protobuf:"bytes,2,opt,name=entry_expression,json=entryExpression,proto3" json:"entry_expression,omitempty"`
	// entry event for the journey
	EntryEvent JourneyEntryEvent `protobuf:"varint,9,opt,name=entry_event,json=entryEvent,proto3,enum=journey.JourneyEntryEvent" json:"entry_event,omitempty"`
	// entry event expression for the journey
	EntryEventExpression string `protobuf:"bytes,10,opt,name=entry_event_expression,json=entryEventExpression,proto3" json:"entry_event_expression,omitempty"`
	// list of nudge ids which acts as actions for journeys
	NudgeIds []string `protobuf:"bytes,3,rep,name=nudge_ids,json=nudgeIds,proto3" json:"nudge_ids,omitempty"`
	// display properties of journey
	DisplayConfig *JourneyDisplay `protobuf:"bytes,4,opt,name=display_config,json=displayConfig,proto3" json:"display_config,omitempty"`
	// meta data of the journey for prioritization or for analytics purpose
	MetaData *JourneyMetaData `protobuf:"bytes,5,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// status of the journey
	Status JourneyStatus `protobuf:"varint,6,opt,name=status,proto3,enum=journey.JourneyStatus" json:"status,omitempty"`
	// timestamp since when the journey will be active
	ActiveSince *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// timestamp till when the journey will be active
	ActiveTill *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
}

func (x *CreateJourneyRequest) Reset() {
	*x = CreateJourneyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJourneyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJourneyRequest) ProtoMessage() {}

func (x *CreateJourneyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJourneyRequest.ProtoReflect.Descriptor instead.
func (*CreateJourneyRequest) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateJourneyRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateJourneyRequest) GetEntryExpression() string {
	if x != nil {
		return x.EntryExpression
	}
	return ""
}

func (x *CreateJourneyRequest) GetEntryEvent() JourneyEntryEvent {
	if x != nil {
		return x.EntryEvent
	}
	return JourneyEntryEvent_JOURNEY_ENTRY_EVENT_UNSPECIFIED
}

func (x *CreateJourneyRequest) GetEntryEventExpression() string {
	if x != nil {
		return x.EntryEventExpression
	}
	return ""
}

func (x *CreateJourneyRequest) GetNudgeIds() []string {
	if x != nil {
		return x.NudgeIds
	}
	return nil
}

func (x *CreateJourneyRequest) GetDisplayConfig() *JourneyDisplay {
	if x != nil {
		return x.DisplayConfig
	}
	return nil
}

func (x *CreateJourneyRequest) GetMetaData() *JourneyMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *CreateJourneyRequest) GetStatus() JourneyStatus {
	if x != nil {
		return x.Status
	}
	return JourneyStatus_JOURNEY_STATUS_UNSPECIFIED
}

func (x *CreateJourneyRequest) GetActiveSince() *timestamppb.Timestamp {
	if x != nil {
		return x.ActiveSince
	}
	return nil
}

func (x *CreateJourneyRequest) GetActiveTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ActiveTill
	}
	return nil
}

type CreateJourneyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// journey details
	Journey *Journey `protobuf:"bytes,2,opt,name=journey,proto3" json:"journey,omitempty"`
}

func (x *CreateJourneyResponse) Reset() {
	*x = CreateJourneyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJourneyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJourneyResponse) ProtoMessage() {}

func (x *CreateJourneyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJourneyResponse.ProtoReflect.Descriptor instead.
func (*CreateJourneyResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateJourneyResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateJourneyResponse) GetJourney() *Journey {
	if x != nil {
		return x.Journey
	}
	return nil
}

type UpdateJourneyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the journey
	JourneyId string `protobuf:"bytes,1,opt,name=journey_id,json=journeyId,proto3" json:"journey_id,omitempty"`
	// internal only description of journey
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// logical expression of segment ids or any other expressions to which the journey will be displayed e.g. IsMember(S1) && !IsMember(S2) && IsTPAPUser()
	// true would mean the journey will be displayed to all users
	EntryExpression string `protobuf:"bytes,3,opt,name=entry_expression,json=entryExpression,proto3" json:"entry_expression,omitempty"`
	// entry event for the journey
	EntryEvent JourneyEntryEvent `protobuf:"varint,11,opt,name=entry_event,json=entryEvent,proto3,enum=journey.JourneyEntryEvent" json:"entry_event,omitempty"`
	// entry event expression for the journey
	EntryEventExpression string `protobuf:"bytes,12,opt,name=entry_event_expression,json=entryEventExpression,proto3" json:"entry_event_expression,omitempty"`
	// list of nudge ids which acts as actions for journeys
	NudgeIds []string `protobuf:"bytes,4,rep,name=nudge_ids,json=nudgeIds,proto3" json:"nudge_ids,omitempty"`
	// display properties of journey
	DisplayConfig *JourneyDisplay `protobuf:"bytes,5,opt,name=display_config,json=displayConfig,proto3" json:"display_config,omitempty"`
	// meta data of the journey for prioritization or for analytics purpose
	MetaData *JourneyMetaData `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// status of the journey
	Status JourneyStatus `protobuf:"varint,7,opt,name=status,proto3,enum=journey.JourneyStatus" json:"status,omitempty"`
	// timestamp since when the journey will be active
	ActiveSince *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// timestamp till when the journey will be active
	ActiveTill *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// field mask to indicate which fields to update
	Mask []JourneyFieldMask `protobuf:"varint,10,rep,packed,name=mask,proto3,enum=journey.JourneyFieldMask" json:"mask,omitempty"`
}

func (x *UpdateJourneyRequest) Reset() {
	*x = UpdateJourneyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJourneyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJourneyRequest) ProtoMessage() {}

func (x *UpdateJourneyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJourneyRequest.ProtoReflect.Descriptor instead.
func (*UpdateJourneyRequest) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateJourneyRequest) GetJourneyId() string {
	if x != nil {
		return x.JourneyId
	}
	return ""
}

func (x *UpdateJourneyRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateJourneyRequest) GetEntryExpression() string {
	if x != nil {
		return x.EntryExpression
	}
	return ""
}

func (x *UpdateJourneyRequest) GetEntryEvent() JourneyEntryEvent {
	if x != nil {
		return x.EntryEvent
	}
	return JourneyEntryEvent_JOURNEY_ENTRY_EVENT_UNSPECIFIED
}

func (x *UpdateJourneyRequest) GetEntryEventExpression() string {
	if x != nil {
		return x.EntryEventExpression
	}
	return ""
}

func (x *UpdateJourneyRequest) GetNudgeIds() []string {
	if x != nil {
		return x.NudgeIds
	}
	return nil
}

func (x *UpdateJourneyRequest) GetDisplayConfig() *JourneyDisplay {
	if x != nil {
		return x.DisplayConfig
	}
	return nil
}

func (x *UpdateJourneyRequest) GetMetaData() *JourneyMetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *UpdateJourneyRequest) GetStatus() JourneyStatus {
	if x != nil {
		return x.Status
	}
	return JourneyStatus_JOURNEY_STATUS_UNSPECIFIED
}

func (x *UpdateJourneyRequest) GetActiveSince() *timestamppb.Timestamp {
	if x != nil {
		return x.ActiveSince
	}
	return nil
}

func (x *UpdateJourneyRequest) GetActiveTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ActiveTill
	}
	return nil
}

func (x *UpdateJourneyRequest) GetMask() []JourneyFieldMask {
	if x != nil {
		return x.Mask
	}
	return nil
}

type UpdateJourneyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// journey details
	Journey *Journey `protobuf:"bytes,2,opt,name=journey,proto3" json:"journey,omitempty"`
}

func (x *UpdateJourneyResponse) Reset() {
	*x = UpdateJourneyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJourneyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJourneyResponse) ProtoMessage() {}

func (x *UpdateJourneyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJourneyResponse.ProtoReflect.Descriptor instead.
func (*UpdateJourneyResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateJourneyResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateJourneyResponse) GetJourney() *Journey {
	if x != nil {
		return x.Journey
	}
	return nil
}

type FetchDisplayableJourneysRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the actor to fetch journeys
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *FetchDisplayableJourneysRequest) Reset() {
	*x = FetchDisplayableJourneysRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDisplayableJourneysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDisplayableJourneysRequest) ProtoMessage() {}

func (x *FetchDisplayableJourneysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDisplayableJourneysRequest.ProtoReflect.Descriptor instead.
func (*FetchDisplayableJourneysRequest) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{4}
}

func (x *FetchDisplayableJourneysRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type FetchDisplayableJourneysResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of journeys eligible for user
	JourneyInstances []*JourneyInstance `protobuf:"bytes,2,rep,name=journey_instances,json=journeyInstances,proto3" json:"journey_instances,omitempty"`
	// id of the last completed actor journey
	LastCompletedActorJourneyId string `protobuf:"bytes,3,opt,name=last_completed_actor_journey_id,json=lastCompletedActorJourneyId,proto3" json:"last_completed_actor_journey_id,omitempty"`
}

func (x *FetchDisplayableJourneysResponse) Reset() {
	*x = FetchDisplayableJourneysResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDisplayableJourneysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDisplayableJourneysResponse) ProtoMessage() {}

func (x *FetchDisplayableJourneysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDisplayableJourneysResponse.ProtoReflect.Descriptor instead.
func (*FetchDisplayableJourneysResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{5}
}

func (x *FetchDisplayableJourneysResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchDisplayableJourneysResponse) GetJourneyInstances() []*JourneyInstance {
	if x != nil {
		return x.JourneyInstances
	}
	return nil
}

func (x *FetchDisplayableJourneysResponse) GetLastCompletedActorJourneyId() string {
	if x != nil {
		return x.LastCompletedActorJourneyId
	}
	return ""
}

type FetchJourneyByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the actor
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// journey identifier
	//
	// Types that are assignable to Identifier:
	//	*FetchJourneyByIdRequest_JourneyId
	//	*FetchJourneyByIdRequest_ActorJourneyId
	Identifier isFetchJourneyByIdRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *FetchJourneyByIdRequest) Reset() {
	*x = FetchJourneyByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchJourneyByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchJourneyByIdRequest) ProtoMessage() {}

func (x *FetchJourneyByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchJourneyByIdRequest.ProtoReflect.Descriptor instead.
func (*FetchJourneyByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{6}
}

func (x *FetchJourneyByIdRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (m *FetchJourneyByIdRequest) GetIdentifier() isFetchJourneyByIdRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *FetchJourneyByIdRequest) GetJourneyId() string {
	if x, ok := x.GetIdentifier().(*FetchJourneyByIdRequest_JourneyId); ok {
		return x.JourneyId
	}
	return ""
}

func (x *FetchJourneyByIdRequest) GetActorJourneyId() string {
	if x, ok := x.GetIdentifier().(*FetchJourneyByIdRequest_ActorJourneyId); ok {
		return x.ActorJourneyId
	}
	return ""
}

type isFetchJourneyByIdRequest_Identifier interface {
	isFetchJourneyByIdRequest_Identifier()
}

type FetchJourneyByIdRequest_JourneyId struct {
	JourneyId string `protobuf:"bytes,2,opt,name=journey_id,json=journeyId,proto3,oneof"`
}

type FetchJourneyByIdRequest_ActorJourneyId struct {
	ActorJourneyId string `protobuf:"bytes,3,opt,name=actor_journey_id,json=actorJourneyId,proto3,oneof"`
}

func (*FetchJourneyByIdRequest_JourneyId) isFetchJourneyByIdRequest_Identifier() {}

func (*FetchJourneyByIdRequest_ActorJourneyId) isFetchJourneyByIdRequest_Identifier() {}

type FetchJourneyByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// journey instance details
	JourneyInstance *JourneyInstance `protobuf:"bytes,2,opt,name=journey_instance,json=journeyInstance,proto3" json:"journey_instance,omitempty"`
	// details of the nudges part of the journey
	NudgeInstances []*nudge.NudgeInstance `protobuf:"bytes,3,rep,name=nudge_instances,json=nudgeInstances,proto3" json:"nudge_instances,omitempty"`
}

func (x *FetchJourneyByIdResponse) Reset() {
	*x = FetchJourneyByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchJourneyByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchJourneyByIdResponse) ProtoMessage() {}

func (x *FetchJourneyByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchJourneyByIdResponse.ProtoReflect.Descriptor instead.
func (*FetchJourneyByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{7}
}

func (x *FetchJourneyByIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchJourneyByIdResponse) GetJourneyInstance() *JourneyInstance {
	if x != nil {
		return x.JourneyInstance
	}
	return nil
}

func (x *FetchJourneyByIdResponse) GetNudgeInstances() []*nudge.NudgeInstance {
	if x != nil {
		return x.NudgeInstances
	}
	return nil
}

type FetchActivationJourneyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the actor
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *FetchActivationJourneyRequest) Reset() {
	*x = FetchActivationJourneyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchActivationJourneyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchActivationJourneyRequest) ProtoMessage() {}

func (x *FetchActivationJourneyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchActivationJourneyRequest.ProtoReflect.Descriptor instead.
func (*FetchActivationJourneyRequest) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{8}
}

func (x *FetchActivationJourneyRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type FetchActivationJourneyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// journey instance details
	JourneyInstance *JourneyInstance `protobuf:"bytes,2,opt,name=journey_instance,json=journeyInstance,proto3" json:"journey_instance,omitempty"`
	// details of the nudges part of the journey
	NudgeInstances []*nudge.NudgeInstance `protobuf:"bytes,3,rep,name=nudge_instances,json=nudgeInstances,proto3" json:"nudge_instances,omitempty"`
}

func (x *FetchActivationJourneyResponse) Reset() {
	*x = FetchActivationJourneyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchActivationJourneyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchActivationJourneyResponse) ProtoMessage() {}

func (x *FetchActivationJourneyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchActivationJourneyResponse.ProtoReflect.Descriptor instead.
func (*FetchActivationJourneyResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{9}
}

func (x *FetchActivationJourneyResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchActivationJourneyResponse) GetJourneyInstance() *JourneyInstance {
	if x != nil {
		return x.JourneyInstance
	}
	return nil
}

func (x *FetchActivationJourneyResponse) GetNudgeInstances() []*nudge.NudgeInstance {
	if x != nil {
		return x.NudgeInstances
	}
	return nil
}

type UpdateActorJourneyStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the actor journey to be updated
	ActorJourneyId string `protobuf:"bytes,1,opt,name=actor_journey_id,json=actorJourneyId,proto3" json:"actor_journey_id,omitempty"`
	// status of the actor journey
	ActorJourneyStatus ActorJourneyStatus `protobuf:"varint,2,opt,name=actor_journey_status,json=actorJourneyStatus,proto3,enum=journey.ActorJourneyStatus" json:"actor_journey_status,omitempty"`
}

func (x *UpdateActorJourneyStatusRequest) Reset() {
	*x = UpdateActorJourneyStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateActorJourneyStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActorJourneyStatusRequest) ProtoMessage() {}

func (x *UpdateActorJourneyStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActorJourneyStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateActorJourneyStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateActorJourneyStatusRequest) GetActorJourneyId() string {
	if x != nil {
		return x.ActorJourneyId
	}
	return ""
}

func (x *UpdateActorJourneyStatusRequest) GetActorJourneyStatus() ActorJourneyStatus {
	if x != nil {
		return x.ActorJourneyStatus
	}
	return ActorJourneyStatus_ACTOR_JOURNEY_STATUS_UNSPECIFIED
}

type UpdateActorJourneyStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateActorJourneyStatusResponse) Reset() {
	*x = UpdateActorJourneyStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_journey_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateActorJourneyStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActorJourneyStatusResponse) ProtoMessage() {}

func (x *UpdateActorJourneyStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_journey_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActorJourneyStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateActorJourneyStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_journey_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateActorJourneyStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_nudge_journey_service_proto protoreflect.FileDescriptor

var file_api_nudge_journey_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x6a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x07, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f,
	0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2f, 0x6a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f,
	0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x96, 0x04, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3b,
	0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x3e,
	0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79,
	0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52,
	0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35,
	0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e,
	0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53,
	0x69, 0x6e, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74,
	0x69, 0x6c, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6c,
	0x6c, 0x22, 0x68, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2a, 0x0a, 0x07, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x52, 0x07, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x22, 0xe4, 0x04, 0x0a, 0x14,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65,
	0x79, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e,
	0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a,
	0x16, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x3e, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x35, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65,
	0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x69, 0x6c, 0x6c, 0x12, 0x2d, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x6d, 0x61,
	0x73, 0x6b, 0x22, 0x68, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2a, 0x0a, 0x07, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x52, 0x07, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x22, 0x3c, 0x0a, 0x1f,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd4, 0x01, 0x0a, 0x20, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4a,
	0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x45, 0x0a, 0x11, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65,
	0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x10, 0x6a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x1f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x49,
	0x64, 0x22, 0x8f, 0x01, 0x0a, 0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x6a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09,
	0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0xc3, 0x01, 0x0a, 0x18, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x75,
	0x72, 0x6e, 0x65, 0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65,
	0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0f, 0x6a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x6e, 0x75,
	0x64, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x4e, 0x75, 0x64, 0x67,
	0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x6e, 0x75, 0x64, 0x67, 0x65,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x3a, 0x0a, 0x1d, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xc9, 0x01, 0x0a, 0x1e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a,
	0x10, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65,
	0x79, 0x2e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x0f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6e, 0x75,
	0x64, 0x67, 0x65, 0x2e, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x0e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x22, 0x9a, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6a,
	0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x49, 0x64, 0x12,
	0x4d, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f, 0x75,
	0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x47,
	0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f, 0x75,
	0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xd6, 0x04, 0x0a, 0x0e, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4e, 0x0a, 0x0d, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x12, 0x1d, 0x2e, 0x6a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6a, 0x6f, 0x75,
	0x72, 0x6e, 0x65, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x12, 0x1d, 0x2e, 0x6a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72,
	0x6e, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6a, 0x6f, 0x75,
	0x72, 0x6e, 0x65, 0x79, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x18, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x73, 0x12, 0x28, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c,
	0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x10, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x42, 0x79, 0x49, 0x64, 0x12,
	0x20, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4a,
	0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x21, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x12, 0x26,
	0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6f, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x6a, 0x6f,
	0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x4a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4a, 0x6f, 0x75, 0x72, 0x6e,
	0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e,
	0x75, 0x64, 0x67, 0x65, 0x2e, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5a, 0x28, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x6a,
	0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_nudge_journey_service_proto_rawDescOnce sync.Once
	file_api_nudge_journey_service_proto_rawDescData = file_api_nudge_journey_service_proto_rawDesc
)

func file_api_nudge_journey_service_proto_rawDescGZIP() []byte {
	file_api_nudge_journey_service_proto_rawDescOnce.Do(func() {
		file_api_nudge_journey_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_nudge_journey_service_proto_rawDescData)
	})
	return file_api_nudge_journey_service_proto_rawDescData
}

var file_api_nudge_journey_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_nudge_journey_service_proto_goTypes = []interface{}{
	(*CreateJourneyRequest)(nil),             // 0: journey.CreateJourneyRequest
	(*CreateJourneyResponse)(nil),            // 1: journey.CreateJourneyResponse
	(*UpdateJourneyRequest)(nil),             // 2: journey.UpdateJourneyRequest
	(*UpdateJourneyResponse)(nil),            // 3: journey.UpdateJourneyResponse
	(*FetchDisplayableJourneysRequest)(nil),  // 4: journey.FetchDisplayableJourneysRequest
	(*FetchDisplayableJourneysResponse)(nil), // 5: journey.FetchDisplayableJourneysResponse
	(*FetchJourneyByIdRequest)(nil),          // 6: journey.FetchJourneyByIdRequest
	(*FetchJourneyByIdResponse)(nil),         // 7: journey.FetchJourneyByIdResponse
	(*FetchActivationJourneyRequest)(nil),    // 8: journey.FetchActivationJourneyRequest
	(*FetchActivationJourneyResponse)(nil),   // 9: journey.FetchActivationJourneyResponse
	(*UpdateActorJourneyStatusRequest)(nil),  // 10: journey.UpdateActorJourneyStatusRequest
	(*UpdateActorJourneyStatusResponse)(nil), // 11: journey.UpdateActorJourneyStatusResponse
	(JourneyEntryEvent)(0),                   // 12: journey.JourneyEntryEvent
	(*JourneyDisplay)(nil),                   // 13: journey.JourneyDisplay
	(*JourneyMetaData)(nil),                  // 14: journey.JourneyMetaData
	(JourneyStatus)(0),                       // 15: journey.JourneyStatus
	(*timestamppb.Timestamp)(nil),            // 16: google.protobuf.Timestamp
	(*rpc.Status)(nil),                       // 17: rpc.Status
	(*Journey)(nil),                          // 18: journey.Journey
	(JourneyFieldMask)(0),                    // 19: journey.JourneyFieldMask
	(*JourneyInstance)(nil),                  // 20: journey.JourneyInstance
	(*nudge.NudgeInstance)(nil),              // 21: nudge.NudgeInstance
	(ActorJourneyStatus)(0),                  // 22: journey.ActorJourneyStatus
}
var file_api_nudge_journey_service_proto_depIdxs = []int32{
	12, // 0: journey.CreateJourneyRequest.entry_event:type_name -> journey.JourneyEntryEvent
	13, // 1: journey.CreateJourneyRequest.display_config:type_name -> journey.JourneyDisplay
	14, // 2: journey.CreateJourneyRequest.meta_data:type_name -> journey.JourneyMetaData
	15, // 3: journey.CreateJourneyRequest.status:type_name -> journey.JourneyStatus
	16, // 4: journey.CreateJourneyRequest.active_since:type_name -> google.protobuf.Timestamp
	16, // 5: journey.CreateJourneyRequest.active_till:type_name -> google.protobuf.Timestamp
	17, // 6: journey.CreateJourneyResponse.status:type_name -> rpc.Status
	18, // 7: journey.CreateJourneyResponse.journey:type_name -> journey.Journey
	12, // 8: journey.UpdateJourneyRequest.entry_event:type_name -> journey.JourneyEntryEvent
	13, // 9: journey.UpdateJourneyRequest.display_config:type_name -> journey.JourneyDisplay
	14, // 10: journey.UpdateJourneyRequest.meta_data:type_name -> journey.JourneyMetaData
	15, // 11: journey.UpdateJourneyRequest.status:type_name -> journey.JourneyStatus
	16, // 12: journey.UpdateJourneyRequest.active_since:type_name -> google.protobuf.Timestamp
	16, // 13: journey.UpdateJourneyRequest.active_till:type_name -> google.protobuf.Timestamp
	19, // 14: journey.UpdateJourneyRequest.mask:type_name -> journey.JourneyFieldMask
	17, // 15: journey.UpdateJourneyResponse.status:type_name -> rpc.Status
	18, // 16: journey.UpdateJourneyResponse.journey:type_name -> journey.Journey
	17, // 17: journey.FetchDisplayableJourneysResponse.status:type_name -> rpc.Status
	20, // 18: journey.FetchDisplayableJourneysResponse.journey_instances:type_name -> journey.JourneyInstance
	17, // 19: journey.FetchJourneyByIdResponse.status:type_name -> rpc.Status
	20, // 20: journey.FetchJourneyByIdResponse.journey_instance:type_name -> journey.JourneyInstance
	21, // 21: journey.FetchJourneyByIdResponse.nudge_instances:type_name -> nudge.NudgeInstance
	17, // 22: journey.FetchActivationJourneyResponse.status:type_name -> rpc.Status
	20, // 23: journey.FetchActivationJourneyResponse.journey_instance:type_name -> journey.JourneyInstance
	21, // 24: journey.FetchActivationJourneyResponse.nudge_instances:type_name -> nudge.NudgeInstance
	22, // 25: journey.UpdateActorJourneyStatusRequest.actor_journey_status:type_name -> journey.ActorJourneyStatus
	17, // 26: journey.UpdateActorJourneyStatusResponse.status:type_name -> rpc.Status
	0,  // 27: journey.JourneyService.CreateJourney:input_type -> journey.CreateJourneyRequest
	2,  // 28: journey.JourneyService.UpdateJourney:input_type -> journey.UpdateJourneyRequest
	4,  // 29: journey.JourneyService.FetchDisplayableJourneys:input_type -> journey.FetchDisplayableJourneysRequest
	6,  // 30: journey.JourneyService.FetchJourneyById:input_type -> journey.FetchJourneyByIdRequest
	8,  // 31: journey.JourneyService.FetchActivationJourney:input_type -> journey.FetchActivationJourneyRequest
	10, // 32: journey.JourneyService.UpdateActorJourneyStatus:input_type -> journey.UpdateActorJourneyStatusRequest
	1,  // 33: journey.JourneyService.CreateJourney:output_type -> journey.CreateJourneyResponse
	3,  // 34: journey.JourneyService.UpdateJourney:output_type -> journey.UpdateJourneyResponse
	5,  // 35: journey.JourneyService.FetchDisplayableJourneys:output_type -> journey.FetchDisplayableJourneysResponse
	7,  // 36: journey.JourneyService.FetchJourneyById:output_type -> journey.FetchJourneyByIdResponse
	9,  // 37: journey.JourneyService.FetchActivationJourney:output_type -> journey.FetchActivationJourneyResponse
	11, // 38: journey.JourneyService.UpdateActorJourneyStatus:output_type -> journey.UpdateActorJourneyStatusResponse
	33, // [33:39] is the sub-list for method output_type
	27, // [27:33] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_nudge_journey_service_proto_init() }
func file_api_nudge_journey_service_proto_init() {
	if File_api_nudge_journey_service_proto != nil {
		return
	}
	file_api_nudge_journey_journey_proto_init()
	file_api_nudge_journey_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_nudge_journey_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJourneyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJourneyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJourneyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJourneyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDisplayableJourneysRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDisplayableJourneysResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchJourneyByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchJourneyByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchActivationJourneyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchActivationJourneyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateActorJourneyStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_nudge_journey_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateActorJourneyStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_nudge_journey_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*FetchJourneyByIdRequest_JourneyId)(nil),
		(*FetchJourneyByIdRequest_ActorJourneyId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_nudge_journey_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_nudge_journey_service_proto_goTypes,
		DependencyIndexes: file_api_nudge_journey_service_proto_depIdxs,
		MessageInfos:      file_api_nudge_journey_service_proto_msgTypes,
	}.Build()
	File_api_nudge_journey_service_proto = out.File
	file_api_nudge_journey_service_proto_rawDesc = nil
	file_api_nudge_journey_service_proto_goTypes = nil
	file_api_nudge_journey_service_proto_depIdxs = nil
}
