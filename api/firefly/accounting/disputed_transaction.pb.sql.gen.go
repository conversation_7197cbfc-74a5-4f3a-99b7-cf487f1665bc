// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/firefly/accounting/disputed_transaction.pb.go

package accounting

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing DisputeDetails while reading from DB
func (a *DisputeDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the DisputeDetails in string format in DB
func (a *DisputeDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for DisputeDetails
func (a *DisputeDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for DisputeDetails
func (a *DisputeDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
