// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/p2pinvestment/service.proto

package p2pinvestment

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment/common"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"

	jump_client_states "github.com/epifi/gamma/api/frontend/p2pinvestment/jump_client_states"

	transaction "github.com/epifi/gamma/api/frontend/pay/transaction"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.MaturityConsentType(0)

	_ = deeplink.Screen(0)

	_ = jump_client_states.RenewalType(0)

	_ = transaction.PinRequiredType(0)
)

// Validate checks the field values on
// GetInvestPageFromPromotionalUseCaseRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetInvestPageFromPromotionalUseCaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInvestPageFromPromotionalUseCaseRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetInvestPageFromPromotionalUseCaseRequestMultiError, or nil if none found.
func (m *GetInvestPageFromPromotionalUseCaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestPageFromPromotionalUseCaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestPageFromPromotionalUseCaseRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPromotionLoadingScreenOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseRequestValidationError{
					field:  "PromotionLoadingScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseRequestValidationError{
					field:  "PromotionLoadingScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPromotionLoadingScreenOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestPageFromPromotionalUseCaseRequestValidationError{
				field:  "PromotionLoadingScreenOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestPageFromPromotionalUseCaseRequestMultiError(errors)
	}

	return nil
}

// GetInvestPageFromPromotionalUseCaseRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestPageFromPromotionalUseCaseRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestPageFromPromotionalUseCaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestPageFromPromotionalUseCaseRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestPageFromPromotionalUseCaseRequestMultiError) AllErrors() []error { return m }

// GetInvestPageFromPromotionalUseCaseRequestValidationError is the validation
// error returned by GetInvestPageFromPromotionalUseCaseRequest.Validate if
// the designated constraints aren't met.
type GetInvestPageFromPromotionalUseCaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestPageFromPromotionalUseCaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestPageFromPromotionalUseCaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestPageFromPromotionalUseCaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestPageFromPromotionalUseCaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestPageFromPromotionalUseCaseRequestValidationError) ErrorName() string {
	return "GetInvestPageFromPromotionalUseCaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestPageFromPromotionalUseCaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestPageFromPromotionalUseCaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestPageFromPromotionalUseCaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestPageFromPromotionalUseCaseRequestValidationError{}

// Validate checks the field values on
// GetInvestPageFromPromotionalUseCaseResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetInvestPageFromPromotionalUseCaseResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetInvestPageFromPromotionalUseCaseResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetInvestPageFromPromotionalUseCaseResponseMultiError, or nil if none found.
func (m *GetInvestPageFromPromotionalUseCaseResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestPageFromPromotionalUseCaseResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestPageFromPromotionalUseCaseResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestPageFromPromotionalUseCaseResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestPageFromPromotionalUseCaseResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestPageFromPromotionalUseCaseResponseMultiError(errors)
	}

	return nil
}

// GetInvestPageFromPromotionalUseCaseResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetInvestPageFromPromotionalUseCaseResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestPageFromPromotionalUseCaseResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestPageFromPromotionalUseCaseResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestPageFromPromotionalUseCaseResponseMultiError) AllErrors() []error { return m }

// GetInvestPageFromPromotionalUseCaseResponseValidationError is the validation
// error returned by GetInvestPageFromPromotionalUseCaseResponse.Validate if
// the designated constraints aren't met.
type GetInvestPageFromPromotionalUseCaseResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestPageFromPromotionalUseCaseResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestPageFromPromotionalUseCaseResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestPageFromPromotionalUseCaseResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestPageFromPromotionalUseCaseResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestPageFromPromotionalUseCaseResponseValidationError) ErrorName() string {
	return "GetInvestPageFromPromotionalUseCaseResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestPageFromPromotionalUseCaseResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestPageFromPromotionalUseCaseResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestPageFromPromotionalUseCaseResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestPageFromPromotionalUseCaseResponseValidationError{}

// Validate checks the field values on GetInvestNowScreenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestNowScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestNowScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestNowScreenRequestMultiError, or nil if none found.
func (m *GetInvestNowScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestNowScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestNowScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestNowScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestNowScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	if all {
		switch v := interface{}(m.GetSelectedOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestNowScreenRequestValidationError{
					field:  "SelectedOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestNowScreenRequestValidationError{
					field:  "SelectedOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestNowScreenRequestValidationError{
				field:  "SelectedOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestNowScreenRequestMultiError(errors)
	}

	return nil
}

// GetInvestNowScreenRequestMultiError is an error wrapping multiple validation
// errors returned by GetInvestNowScreenRequest.ValidateAll() if the
// designated constraints aren't met.
type GetInvestNowScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestNowScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestNowScreenRequestMultiError) AllErrors() []error { return m }

// GetInvestNowScreenRequestValidationError is the validation error returned by
// GetInvestNowScreenRequest.Validate if the designated constraints aren't met.
type GetInvestNowScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestNowScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestNowScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestNowScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestNowScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestNowScreenRequestValidationError) ErrorName() string {
	return "GetInvestNowScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestNowScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestNowScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestNowScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestNowScreenRequestValidationError{}

// Validate checks the field values on InvestScreenSelectedOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestScreenSelectedOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestScreenSelectedOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestScreenSelectedOptionsMultiError, or nil if none found.
func (m *InvestScreenSelectedOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestScreenSelectedOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestScreenSelectedOptionsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestScreenSelectedOptionsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestScreenSelectedOptionsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaturityConsentType

	if len(errors) > 0 {
		return InvestScreenSelectedOptionsMultiError(errors)
	}

	return nil
}

// InvestScreenSelectedOptionsMultiError is an error wrapping multiple
// validation errors returned by InvestScreenSelectedOptions.ValidateAll() if
// the designated constraints aren't met.
type InvestScreenSelectedOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestScreenSelectedOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestScreenSelectedOptionsMultiError) AllErrors() []error { return m }

// InvestScreenSelectedOptionsValidationError is the validation error returned
// by InvestScreenSelectedOptions.Validate if the designated constraints
// aren't met.
type InvestScreenSelectedOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestScreenSelectedOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestScreenSelectedOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestScreenSelectedOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestScreenSelectedOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestScreenSelectedOptionsValidationError) ErrorName() string {
	return "InvestScreenSelectedOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e InvestScreenSelectedOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestScreenSelectedOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestScreenSelectedOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestScreenSelectedOptionsValidationError{}

// Validate checks the field values on GetInvestNowScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestNowScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestNowScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInvestNowScreenResponseMultiError, or nil if none found.
func (m *GetInvestNowScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestNowScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestNowScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestNowScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestNowScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestNowScreenResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestNowScreenResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestNowScreenResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestNowScreenResponseMultiError(errors)
	}

	return nil
}

// GetInvestNowScreenResponseMultiError is an error wrapping multiple
// validation errors returned by GetInvestNowScreenResponse.ValidateAll() if
// the designated constraints aren't met.
type GetInvestNowScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestNowScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestNowScreenResponseMultiError) AllErrors() []error { return m }

// GetInvestNowScreenResponseValidationError is the validation error returned
// by GetInvestNowScreenResponse.Validate if the designated constraints aren't met.
type GetInvestNowScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestNowScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestNowScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestNowScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestNowScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestNowScreenResponseValidationError) ErrorName() string {
	return "GetInvestNowScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestNowScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestNowScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestNowScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestNowScreenResponseValidationError{}

// Validate checks the field values on GetInvestmentSummaryScreenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentSummaryScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryScreenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryScreenRequestMultiError, or nil if none found.
func (m *GetInvestmentSummaryScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentSummaryScreenRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryScreenRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentSummaryScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentSummaryScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryScreenRequestMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryScreenRequestValidationError is the validation error
// returned by GetInvestmentSummaryScreenRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentSummaryScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryScreenRequestValidationError) ErrorName() string {
	return "GetInvestmentSummaryScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryScreenRequestValidationError{}

// Validate checks the field values on GetInvestmentSummaryScreenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentSummaryScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentSummaryScreenResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentSummaryScreenResponseMultiError, or nil if none found.
func (m *GetInvestmentSummaryScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentSummaryScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentSummaryScreenResponseValidationError{
					field:  "Screen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentSummaryScreenResponseValidationError{
					field:  "Screen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentSummaryScreenResponseValidationError{
				field:  "Screen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentSummaryScreenResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentSummaryScreenResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentSummaryScreenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentSummaryScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentSummaryScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentSummaryScreenResponseMultiError) AllErrors() []error { return m }

// GetInvestmentSummaryScreenResponseValidationError is the validation error
// returned by GetInvestmentSummaryScreenResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentSummaryScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentSummaryScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentSummaryScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentSummaryScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentSummaryScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentSummaryScreenResponseValidationError) ErrorName() string {
	return "GetInvestmentSummaryScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentSummaryScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentSummaryScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentSummaryScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentSummaryScreenResponseValidationError{}

// Validate checks the field values on GetLandingInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLandingInfoRequestMultiError, or nil if none found.
func (m *GetLandingInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingInfoRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingInfoRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingInfoRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingInfoRequestMultiError(errors)
	}

	return nil
}

// GetLandingInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetLandingInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLandingInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingInfoRequestMultiError) AllErrors() []error { return m }

// GetLandingInfoRequestValidationError is the validation error returned by
// GetLandingInfoRequest.Validate if the designated constraints aren't met.
type GetLandingInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingInfoRequestValidationError) ErrorName() string {
	return "GetLandingInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingInfoRequestValidationError{}

// Validate checks the field values on GetLandingInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLandingInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLandingInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLandingInfoResponseMultiError, or nil if none found.
func (m *GetLandingInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLandingInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingInfoResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLandingInfoResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLandingInfoResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLandingInfoResponseMultiError(errors)
	}

	return nil
}

// GetLandingInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetLandingInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLandingInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLandingInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLandingInfoResponseMultiError) AllErrors() []error { return m }

// GetLandingInfoResponseValidationError is the validation error returned by
// GetLandingInfoResponse.Validate if the designated constraints aren't met.
type GetLandingInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLandingInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLandingInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLandingInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLandingInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLandingInfoResponseValidationError) ErrorName() string {
	return "GetLandingInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLandingInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLandingInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLandingInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLandingInfoResponseValidationError{}

// Validate checks the field values on CheckEligibilityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEligibilityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckEligibilityRequestMultiError, or nil if none found.
func (m *CheckEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEligibilityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEligibilityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEligibilityRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckEligibilityRequestMultiError(errors)
	}

	return nil
}

// CheckEligibilityRequestMultiError is an error wrapping multiple validation
// errors returned by CheckEligibilityRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEligibilityRequestMultiError) AllErrors() []error { return m }

// CheckEligibilityRequestValidationError is the validation error returned by
// CheckEligibilityRequest.Validate if the designated constraints aren't met.
type CheckEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEligibilityRequestValidationError) ErrorName() string {
	return "CheckEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEligibilityRequestValidationError{}

// Validate checks the field values on CheckEligibilityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEligibilityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckEligibilityResponseMultiError, or nil if none found.
func (m *CheckEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEligibilityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEligibilityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEligibilityResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEligibilityResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEligibilityResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEligibilityResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckEligibilityResponseMultiError(errors)
	}

	return nil
}

// CheckEligibilityResponseMultiError is an error wrapping multiple validation
// errors returned by CheckEligibilityResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEligibilityResponseMultiError) AllErrors() []error { return m }

// CheckEligibilityResponseValidationError is the validation error returned by
// CheckEligibilityResponse.Validate if the designated constraints aren't met.
type CheckEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEligibilityResponseValidationError) ErrorName() string {
	return "CheckEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEligibilityResponseValidationError{}

// Validate checks the field values on InvestRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InvestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InvestRequestMultiError, or
// nil if none found.
func (m *InvestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	// no validation rules for MaturityConsentType

	if len(errors) > 0 {
		return InvestRequestMultiError(errors)
	}

	return nil
}

// InvestRequestMultiError is an error wrapping multiple validation errors
// returned by InvestRequest.ValidateAll() if the designated constraints
// aren't met.
type InvestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestRequestMultiError) AllErrors() []error { return m }

// InvestRequestValidationError is the validation error returned by
// InvestRequest.Validate if the designated constraints aren't met.
type InvestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestRequestValidationError) ErrorName() string { return "InvestRequestValidationError" }

// Error satisfies the builtin error interface
func (e InvestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestRequestValidationError{}

// Validate checks the field values on InvestResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InvestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InvestResponseMultiError,
// or nil if none found.
func (m *InvestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestResponseValidationError{
					field:  "Eta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestResponseValidationError{
					field:  "Eta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestResponseValidationError{
				field:  "Eta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderClientReqId

	if all {
		switch v := interface{}(m.GetTransactionAttribute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestResponseValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestResponseValidationError{
					field:  "TransactionAttribute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAttribute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestResponseValidationError{
				field:  "TransactionAttribute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PinRequired

	if len(errors) > 0 {
		return InvestResponseMultiError(errors)
	}

	return nil
}

// InvestResponseMultiError is an error wrapping multiple validation errors
// returned by InvestResponse.ValidateAll() if the designated constraints
// aren't met.
type InvestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestResponseMultiError) AllErrors() []error { return m }

// InvestResponseValidationError is the validation error returned by
// InvestResponse.Validate if the designated constraints aren't met.
type InvestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestResponseValidationError) ErrorName() string { return "InvestResponseValidationError" }

// Error satisfies the builtin error interface
func (e InvestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestResponseValidationError{}

// Validate checks the field values on GetAllActivitiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllActivitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllActivitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllActivitiesRequestMultiError, or nil if none found.
func (m *GetAllActivitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllActivitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllActivitiesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllActivitiesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllActivitiesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterId

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllActivitiesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllActivitiesRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllActivitiesRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllActivitiesRequestMultiError(errors)
	}

	return nil
}

// GetAllActivitiesRequestMultiError is an error wrapping multiple validation
// errors returned by GetAllActivitiesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAllActivitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllActivitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllActivitiesRequestMultiError) AllErrors() []error { return m }

// GetAllActivitiesRequestValidationError is the validation error returned by
// GetAllActivitiesRequest.Validate if the designated constraints aren't met.
type GetAllActivitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllActivitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllActivitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllActivitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllActivitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllActivitiesRequestValidationError) ErrorName() string {
	return "GetAllActivitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllActivitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllActivitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllActivitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllActivitiesRequestValidationError{}

// Validate checks the field values on GetAllActivitiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllActivitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllActivitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllActivitiesResponseMultiError, or nil if none found.
func (m *GetAllActivitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllActivitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllActivitiesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllActivitiesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllActivitiesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSortedActivities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllActivitiesResponseValidationError{
						field:  fmt.Sprintf("SortedActivities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllActivitiesResponseValidationError{
						field:  fmt.Sprintf("SortedActivities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllActivitiesResponseValidationError{
					field:  fmt.Sprintf("SortedActivities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFilterChips() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllActivitiesResponseValidationError{
						field:  fmt.Sprintf("FilterChips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllActivitiesResponseValidationError{
						field:  fmt.Sprintf("FilterChips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllActivitiesResponseValidationError{
					field:  fmt.Sprintf("FilterChips[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllActivitiesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllActivitiesResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllActivitiesResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActivitySummaries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllActivitiesResponseValidationError{
						field:  fmt.Sprintf("ActivitySummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllActivitiesResponseValidationError{
						field:  fmt.Sprintf("ActivitySummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllActivitiesResponseValidationError{
					field:  fmt.Sprintf("ActivitySummaries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllActivitiesResponseMultiError(errors)
	}

	return nil
}

// GetAllActivitiesResponseMultiError is an error wrapping multiple validation
// errors returned by GetAllActivitiesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAllActivitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllActivitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllActivitiesResponseMultiError) AllErrors() []error { return m }

// GetAllActivitiesResponseValidationError is the validation error returned by
// GetAllActivitiesResponse.Validate if the designated constraints aren't met.
type GetAllActivitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllActivitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllActivitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllActivitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllActivitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllActivitiesResponseValidationError) ErrorName() string {
	return "GetAllActivitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllActivitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllActivitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllActivitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllActivitiesResponseValidationError{}

// Validate checks the field values on GetRecentActivitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecentActivitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecentActivitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRecentActivitiesRequestMultiError, or nil if none found.
func (m *GetRecentActivitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecentActivitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecentActivitiesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecentActivitiesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecentActivitiesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecentActivitiesRequestMultiError(errors)
	}

	return nil
}

// GetRecentActivitiesRequestMultiError is an error wrapping multiple
// validation errors returned by GetRecentActivitiesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRecentActivitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecentActivitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecentActivitiesRequestMultiError) AllErrors() []error { return m }

// GetRecentActivitiesRequestValidationError is the validation error returned
// by GetRecentActivitiesRequest.Validate if the designated constraints aren't met.
type GetRecentActivitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecentActivitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecentActivitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecentActivitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecentActivitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecentActivitiesRequestValidationError) ErrorName() string {
	return "GetRecentActivitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecentActivitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecentActivitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecentActivitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecentActivitiesRequestValidationError{}

// Validate checks the field values on GetRecentActivitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecentActivitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecentActivitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRecentActivitiesResponseMultiError, or nil if none found.
func (m *GetRecentActivitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecentActivitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecentActivitiesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNudgeInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "NudgeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "NudgeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNudgeInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecentActivitiesResponseValidationError{
				field:  "NudgeInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecentActivities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecentActivitiesResponseValidationError{
						field:  fmt.Sprintf("RecentActivities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecentActivitiesResponseValidationError{
						field:  fmt.Sprintf("RecentActivities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecentActivitiesResponseValidationError{
					field:  fmt.Sprintf("RecentActivities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetActivitySummaries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecentActivitiesResponseValidationError{
						field:  fmt.Sprintf("ActivitySummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecentActivitiesResponseValidationError{
						field:  fmt.Sprintf("ActivitySummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecentActivitiesResponseValidationError{
					field:  fmt.Sprintf("ActivitySummaries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ShowRecentActivity

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecentActivitiesResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetViewAllCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "ViewAllCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecentActivitiesResponseValidationError{
					field:  "ViewAllCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAllCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecentActivitiesResponseValidationError{
				field:  "ViewAllCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecentActivitiesResponseMultiError(errors)
	}

	return nil
}

// GetRecentActivitiesResponseMultiError is an error wrapping multiple
// validation errors returned by GetRecentActivitiesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRecentActivitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecentActivitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecentActivitiesResponseMultiError) AllErrors() []error { return m }

// GetRecentActivitiesResponseValidationError is the validation error returned
// by GetRecentActivitiesResponse.Validate if the designated constraints
// aren't met.
type GetRecentActivitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecentActivitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecentActivitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecentActivitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecentActivitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecentActivitiesResponseValidationError) ErrorName() string {
	return "GetRecentActivitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecentActivitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecentActivitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecentActivitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecentActivitiesResponseValidationError{}

// Validate checks the field values on GetP2POrderStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetP2POrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetP2POrderStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetP2POrderStatusRequestMultiError, or nil if none found.
func (m *GetP2POrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetP2POrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetP2POrderStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetP2POrderStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetP2POrderStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderClientReqId

	if len(errors) > 0 {
		return GetP2POrderStatusRequestMultiError(errors)
	}

	return nil
}

// GetP2POrderStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetP2POrderStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetP2POrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetP2POrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetP2POrderStatusRequestMultiError) AllErrors() []error { return m }

// GetP2POrderStatusRequestValidationError is the validation error returned by
// GetP2POrderStatusRequest.Validate if the designated constraints aren't met.
type GetP2POrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetP2POrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetP2POrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetP2POrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetP2POrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetP2POrderStatusRequestValidationError) ErrorName() string {
	return "GetP2POrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetP2POrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetP2POrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetP2POrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetP2POrderStatusRequestValidationError{}

// Validate checks the field values on GetP2POrderStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetP2POrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetP2POrderStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetP2POrderStatusResponseMultiError, or nil if none found.
func (m *GetP2POrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetP2POrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetP2POrderStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetP2POrderStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetP2POrderStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderStatusInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetP2POrderStatusResponseValidationError{
					field:  "OrderStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetP2POrderStatusResponseValidationError{
					field:  "OrderStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderStatusInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetP2POrderStatusResponseValidationError{
				field:  "OrderStatusInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOrderTimeline() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetP2POrderStatusResponseValidationError{
						field:  fmt.Sprintf("OrderTimeline[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetP2POrderStatusResponseValidationError{
						field:  fmt.Sprintf("OrderTimeline[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetP2POrderStatusResponseValidationError{
					field:  fmt.Sprintf("OrderTimeline[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetP2POrderStatusResponseValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetP2POrderStatusResponseValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetP2POrderStatusResponseValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowConfetti

	// no validation rules for BottomInfo

	if len(errors) > 0 {
		return GetP2POrderStatusResponseMultiError(errors)
	}

	return nil
}

// GetP2POrderStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetP2POrderStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type GetP2POrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetP2POrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetP2POrderStatusResponseMultiError) AllErrors() []error { return m }

// GetP2POrderStatusResponseValidationError is the validation error returned by
// GetP2POrderStatusResponse.Validate if the designated constraints aren't met.
type GetP2POrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetP2POrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetP2POrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetP2POrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetP2POrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetP2POrderStatusResponseValidationError) ErrorName() string {
	return "GetP2POrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetP2POrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetP2POrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetP2POrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetP2POrderStatusResponseValidationError{}

// Validate checks the field values on GetDashboardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDashboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDashboardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDashboardRequestMultiError, or nil if none found.
func (m *GetDashboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDashboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDashboardRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDashboardRequestMultiError(errors)
	}

	return nil
}

// GetDashboardRequestMultiError is an error wrapping multiple validation
// errors returned by GetDashboardRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDashboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDashboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDashboardRequestMultiError) AllErrors() []error { return m }

// GetDashboardRequestValidationError is the validation error returned by
// GetDashboardRequest.Validate if the designated constraints aren't met.
type GetDashboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDashboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDashboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDashboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDashboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDashboardRequestValidationError) ErrorName() string {
	return "GetDashboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDashboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDashboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDashboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDashboardRequestValidationError{}

// Validate checks the field values on GetDashboardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDashboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDashboardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDashboardResponseMultiError, or nil if none found.
func (m *GetDashboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDashboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDashboardResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDashboardResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDashboardResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDashboardResponseValidationError{
				field:  "DashboardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NegativeReturnValue

	// no validation rules for EventProperties

	if len(errors) > 0 {
		return GetDashboardResponseMultiError(errors)
	}

	return nil
}

// GetDashboardResponseMultiError is an error wrapping multiple validation
// errors returned by GetDashboardResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDashboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDashboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDashboardResponseMultiError) AllErrors() []error { return m }

// GetDashboardResponseValidationError is the validation error returned by
// GetDashboardResponse.Validate if the designated constraints aren't met.
type GetDashboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDashboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDashboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDashboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDashboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDashboardResponseValidationError) ErrorName() string {
	return "GetDashboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDashboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDashboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDashboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDashboardResponseValidationError{}

// Validate checks the field values on GetDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeeplinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeeplinkRequestMultiError, or nil if none found.
func (m *GetDeeplinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeeplinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeeplinkRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Screen

	if len(errors) > 0 {
		return GetDeeplinkRequestMultiError(errors)
	}

	return nil
}

// GetDeeplinkRequestMultiError is an error wrapping multiple validation errors
// returned by GetDeeplinkRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDeeplinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeeplinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeeplinkRequestMultiError) AllErrors() []error { return m }

// GetDeeplinkRequestValidationError is the validation error returned by
// GetDeeplinkRequest.Validate if the designated constraints aren't met.
type GetDeeplinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeeplinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeeplinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeeplinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeeplinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeeplinkRequestValidationError) ErrorName() string {
	return "GetDeeplinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeeplinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeeplinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeeplinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeeplinkRequestValidationError{}

// Validate checks the field values on GetDeeplinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeeplinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeeplinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeeplinkResponseMultiError, or nil if none found.
func (m *GetDeeplinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeeplinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeeplinkResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeeplinkResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeeplinkResponseMultiError(errors)
	}

	return nil
}

// GetDeeplinkResponseMultiError is an error wrapping multiple validation
// errors returned by GetDeeplinkResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDeeplinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeeplinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeeplinkResponseMultiError) AllErrors() []error { return m }

// GetDeeplinkResponseValidationError is the validation error returned by
// GetDeeplinkResponse.Validate if the designated constraints aren't met.
type GetDeeplinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeeplinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeeplinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeeplinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeeplinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeeplinkResponseValidationError) ErrorName() string {
	return "GetDeeplinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeeplinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeeplinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeeplinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeeplinkResponseValidationError{}

// Validate checks the field values on WithdrawRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WithdrawRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WithdrawRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WithdrawRequestMultiError, or nil if none found.
func (m *WithdrawRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *WithdrawRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WithdrawRequestMultiError(errors)
	}

	return nil
}

// WithdrawRequestMultiError is an error wrapping multiple validation errors
// returned by WithdrawRequest.ValidateAll() if the designated constraints
// aren't met.
type WithdrawRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WithdrawRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WithdrawRequestMultiError) AllErrors() []error { return m }

// WithdrawRequestValidationError is the validation error returned by
// WithdrawRequest.Validate if the designated constraints aren't met.
type WithdrawRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WithdrawRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WithdrawRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WithdrawRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WithdrawRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WithdrawRequestValidationError) ErrorName() string { return "WithdrawRequestValidationError" }

// Error satisfies the builtin error interface
func (e WithdrawRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWithdrawRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WithdrawRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WithdrawRequestValidationError{}

// Validate checks the field values on WithdrawResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WithdrawResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WithdrawResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WithdrawResponseMultiError, or nil if none found.
func (m *WithdrawResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *WithdrawResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderClientReqId

	if len(errors) > 0 {
		return WithdrawResponseMultiError(errors)
	}

	return nil
}

// WithdrawResponseMultiError is an error wrapping multiple validation errors
// returned by WithdrawResponse.ValidateAll() if the designated constraints
// aren't met.
type WithdrawResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WithdrawResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WithdrawResponseMultiError) AllErrors() []error { return m }

// WithdrawResponseValidationError is the validation error returned by
// WithdrawResponse.Validate if the designated constraints aren't met.
type WithdrawResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WithdrawResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WithdrawResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WithdrawResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WithdrawResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WithdrawResponseValidationError) ErrorName() string { return "WithdrawResponseValidationError" }

// Error satisfies the builtin error interface
func (e WithdrawResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWithdrawResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WithdrawResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WithdrawResponseValidationError{}

// Validate checks the field values on GetEarlyWithdrawalChargesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetEarlyWithdrawalChargesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEarlyWithdrawalChargesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEarlyWithdrawalChargesRequestMultiError, or nil if none found.
func (m *GetEarlyWithdrawalChargesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarlyWithdrawalChargesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarlyWithdrawalChargesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarlyWithdrawalChargesRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEarlyWithdrawalChargesRequestMultiError(errors)
	}

	return nil
}

// GetEarlyWithdrawalChargesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetEarlyWithdrawalChargesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEarlyWithdrawalChargesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarlyWithdrawalChargesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarlyWithdrawalChargesRequestMultiError) AllErrors() []error { return m }

// GetEarlyWithdrawalChargesRequestValidationError is the validation error
// returned by GetEarlyWithdrawalChargesRequest.Validate if the designated
// constraints aren't met.
type GetEarlyWithdrawalChargesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarlyWithdrawalChargesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarlyWithdrawalChargesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarlyWithdrawalChargesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarlyWithdrawalChargesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarlyWithdrawalChargesRequestValidationError) ErrorName() string {
	return "GetEarlyWithdrawalChargesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarlyWithdrawalChargesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarlyWithdrawalChargesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarlyWithdrawalChargesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarlyWithdrawalChargesRequestValidationError{}

// Validate checks the field values on GetEarlyWithdrawalChargesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetEarlyWithdrawalChargesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEarlyWithdrawalChargesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEarlyWithdrawalChargesResponseMultiError, or nil if none found.
func (m *GetEarlyWithdrawalChargesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarlyWithdrawalChargesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarlyWithdrawalChargesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EarlyWithdrawalMessage

	if all {
		switch v := interface{}(m.GetPenaltyCharge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesResponseValidationError{
					field:  "PenaltyCharge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarlyWithdrawalChargesResponseValidationError{
					field:  "PenaltyCharge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyCharge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarlyWithdrawalChargesResponseValidationError{
				field:  "PenaltyCharge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEarlyWithdrawalChargesResponseMultiError(errors)
	}

	return nil
}

// GetEarlyWithdrawalChargesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetEarlyWithdrawalChargesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEarlyWithdrawalChargesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarlyWithdrawalChargesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarlyWithdrawalChargesResponseMultiError) AllErrors() []error { return m }

// GetEarlyWithdrawalChargesResponseValidationError is the validation error
// returned by GetEarlyWithdrawalChargesResponse.Validate if the designated
// constraints aren't met.
type GetEarlyWithdrawalChargesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarlyWithdrawalChargesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarlyWithdrawalChargesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarlyWithdrawalChargesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarlyWithdrawalChargesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarlyWithdrawalChargesResponseValidationError) ErrorName() string {
	return "GetEarlyWithdrawalChargesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarlyWithdrawalChargesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarlyWithdrawalChargesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarlyWithdrawalChargesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarlyWithdrawalChargesResponseValidationError{}

// Validate checks the field values on GetActivityDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivityDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivityDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActivityDetailsRequestMultiError, or nil if none found.
func (m *GetActivityDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivityDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivityDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivityDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivityDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderExternalId

	// no validation rules for ShowMaturityConsentForm

	if len(errors) > 0 {
		return GetActivityDetailsRequestMultiError(errors)
	}

	return nil
}

// GetActivityDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetActivityDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetActivityDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivityDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivityDetailsRequestMultiError) AllErrors() []error { return m }

// GetActivityDetailsRequestValidationError is the validation error returned by
// GetActivityDetailsRequest.Validate if the designated constraints aren't met.
type GetActivityDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivityDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivityDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivityDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivityDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivityDetailsRequestValidationError) ErrorName() string {
	return "GetActivityDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivityDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivityDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivityDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivityDetailsRequestValidationError{}

// Validate checks the field values on GetActivityDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivityDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivityDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActivityDetailsResponseMultiError, or nil if none found.
func (m *GetActivityDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivityDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivityDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivityDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivityDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivityDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivityDetailsResponseValidationError{
					field:  "ActivityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivityDetailsResponseValidationError{
					field:  "ActivityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivityDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivityDetailsResponseValidationError{
				field:  "ActivityDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivityDetailsV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActivityDetailsResponseValidationError{
					field:  "ActivityDetailsV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActivityDetailsResponseValidationError{
					field:  "ActivityDetailsV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivityDetailsV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActivityDetailsResponseValidationError{
				field:  "ActivityDetailsV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActivityDetailsResponseMultiError(errors)
	}

	return nil
}

// GetActivityDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetActivityDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetActivityDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivityDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivityDetailsResponseMultiError) AllErrors() []error { return m }

// GetActivityDetailsResponseValidationError is the validation error returned
// by GetActivityDetailsResponse.Validate if the designated constraints aren't met.
type GetActivityDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivityDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivityDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivityDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivityDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivityDetailsResponseValidationError) ErrorName() string {
	return "GetActivityDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivityDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivityDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivityDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivityDetailsResponseValidationError{}

// Validate checks the field values on GetWithdrawMoneyAttributesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWithdrawMoneyAttributesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWithdrawMoneyAttributesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetWithdrawMoneyAttributesRequestMultiError, or nil if none found.
func (m *GetWithdrawMoneyAttributesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWithdrawMoneyAttributesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WithdrawalMethod

	if len(errors) > 0 {
		return GetWithdrawMoneyAttributesRequestMultiError(errors)
	}

	return nil
}

// GetWithdrawMoneyAttributesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetWithdrawMoneyAttributesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetWithdrawMoneyAttributesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWithdrawMoneyAttributesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWithdrawMoneyAttributesRequestMultiError) AllErrors() []error { return m }

// GetWithdrawMoneyAttributesRequestValidationError is the validation error
// returned by GetWithdrawMoneyAttributesRequest.Validate if the designated
// constraints aren't met.
type GetWithdrawMoneyAttributesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWithdrawMoneyAttributesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWithdrawMoneyAttributesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWithdrawMoneyAttributesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWithdrawMoneyAttributesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWithdrawMoneyAttributesRequestValidationError) ErrorName() string {
	return "GetWithdrawMoneyAttributesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWithdrawMoneyAttributesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWithdrawMoneyAttributesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWithdrawMoneyAttributesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWithdrawMoneyAttributesRequestValidationError{}

// Validate checks the field values on GetWithdrawMoneyAttributesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWithdrawMoneyAttributesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWithdrawMoneyAttributesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetWithdrawMoneyAttributesResponseMultiError, or nil if none found.
func (m *GetWithdrawMoneyAttributesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWithdrawMoneyAttributesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsNudgePresent

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOperativeAccountMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "OperativeAccountMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "OperativeAccountMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOperativeAccountMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponseValidationError{
				field:  "OperativeAccountMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "RequestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "RequestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponseValidationError{
				field:  "RequestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountBreakUpDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "AmountBreakUpDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "AmountBreakUpDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountBreakUpDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponseValidationError{
				field:  "AmountBreakUpDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNudgeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "NudgeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
					field:  "NudgeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNudgeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponseValidationError{
				field:  "NudgeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInfoItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
						field:  fmt.Sprintf("InfoItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetWithdrawMoneyAttributesResponseValidationError{
						field:  fmt.Sprintf("InfoItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetWithdrawMoneyAttributesResponseValidationError{
					field:  fmt.Sprintf("InfoItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetWithdrawMoneyAttributesResponseMultiError(errors)
	}

	return nil
}

// GetWithdrawMoneyAttributesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetWithdrawMoneyAttributesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetWithdrawMoneyAttributesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWithdrawMoneyAttributesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWithdrawMoneyAttributesResponseMultiError) AllErrors() []error { return m }

// GetWithdrawMoneyAttributesResponseValidationError is the validation error
// returned by GetWithdrawMoneyAttributesResponse.Validate if the designated
// constraints aren't met.
type GetWithdrawMoneyAttributesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWithdrawMoneyAttributesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWithdrawMoneyAttributesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWithdrawMoneyAttributesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWithdrawMoneyAttributesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWithdrawMoneyAttributesResponseValidationError) ErrorName() string {
	return "GetWithdrawMoneyAttributesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWithdrawMoneyAttributesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWithdrawMoneyAttributesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWithdrawMoneyAttributesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWithdrawMoneyAttributesResponseValidationError{}

// Validate checks the field values on WithdrawV2Request with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WithdrawV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WithdrawV2Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WithdrawV2RequestMultiError, or nil if none found.
func (m *WithdrawV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *WithdrawV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawV2RequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawV2RequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawV2RequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawV2RequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAmountBreakUp() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WithdrawV2RequestValidationError{
						field:  fmt.Sprintf("AmountBreakUp[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WithdrawV2RequestValidationError{
						field:  fmt.Sprintf("AmountBreakUp[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WithdrawV2RequestValidationError{
					field:  fmt.Sprintf("AmountBreakUp[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WithdrawV2RequestMultiError(errors)
	}

	return nil
}

// WithdrawV2RequestMultiError is an error wrapping multiple validation errors
// returned by WithdrawV2Request.ValidateAll() if the designated constraints
// aren't met.
type WithdrawV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WithdrawV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WithdrawV2RequestMultiError) AllErrors() []error { return m }

// WithdrawV2RequestValidationError is the validation error returned by
// WithdrawV2Request.Validate if the designated constraints aren't met.
type WithdrawV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WithdrawV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WithdrawV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WithdrawV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WithdrawV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WithdrawV2RequestValidationError) ErrorName() string {
	return "WithdrawV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e WithdrawV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWithdrawV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WithdrawV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WithdrawV2RequestValidationError{}

// Validate checks the field values on WithdrawV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WithdrawV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WithdrawV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WithdrawV2ResponseMultiError, or nil if none found.
func (m *WithdrawV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *WithdrawV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawV2ResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderClientReqId

	if len(errors) > 0 {
		return WithdrawV2ResponseMultiError(errors)
	}

	return nil
}

// WithdrawV2ResponseMultiError is an error wrapping multiple validation errors
// returned by WithdrawV2Response.ValidateAll() if the designated constraints
// aren't met.
type WithdrawV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WithdrawV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WithdrawV2ResponseMultiError) AllErrors() []error { return m }

// WithdrawV2ResponseValidationError is the validation error returned by
// WithdrawV2Response.Validate if the designated constraints aren't met.
type WithdrawV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WithdrawV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WithdrawV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WithdrawV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WithdrawV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WithdrawV2ResponseValidationError) ErrorName() string {
	return "WithdrawV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e WithdrawV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWithdrawV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WithdrawV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WithdrawV2ResponseValidationError{}

// Validate checks the field values on GetBannersRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetBannersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBannersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBannersRequestMultiError, or nil if none found.
func (m *GetBannersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBannersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBannersRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBannersRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBannersRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBannerReqPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBannersRequestValidationError{
					field:  "BannerReqPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBannersRequestValidationError{
					field:  "BannerReqPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBannerReqPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBannersRequestValidationError{
				field:  "BannerReqPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBannersRequestMultiError(errors)
	}

	return nil
}

// GetBannersRequestMultiError is an error wrapping multiple validation errors
// returned by GetBannersRequest.ValidateAll() if the designated constraints
// aren't met.
type GetBannersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBannersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBannersRequestMultiError) AllErrors() []error { return m }

// GetBannersRequestValidationError is the validation error returned by
// GetBannersRequest.Validate if the designated constraints aren't met.
type GetBannersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBannersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBannersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBannersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBannersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBannersRequestValidationError) ErrorName() string {
	return "GetBannersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBannersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBannersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBannersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBannersRequestValidationError{}

// Validate checks the field values on GetBannersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBannersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBannersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBannersResponseMultiError, or nil if none found.
func (m *GetBannersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBannersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBannersResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBannersResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBannersResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBanners() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBannersResponseValidationError{
						field:  fmt.Sprintf("Banners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBannersResponseValidationError{
						field:  fmt.Sprintf("Banners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBannersResponseValidationError{
					field:  fmt.Sprintf("Banners[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBannersResponseMultiError(errors)
	}

	return nil
}

// GetBannersResponseMultiError is an error wrapping multiple validation errors
// returned by GetBannersResponse.ValidateAll() if the designated constraints
// aren't met.
type GetBannersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBannersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBannersResponseMultiError) AllErrors() []error { return m }

// GetBannersResponseValidationError is the validation error returned by
// GetBannersResponse.Validate if the designated constraints aren't met.
type GetBannersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBannersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBannersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBannersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBannersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBannersResponseValidationError) ErrorName() string {
	return "GetBannersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBannersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBannersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBannersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBannersResponseValidationError{}

// Validate checks the field values on GenerateRenewInvestmentOtpRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GenerateRenewInvestmentOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateRenewInvestmentOtpRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateRenewInvestmentOtpRequestMultiError, or nil if none found.
func (m *GenerateRenewInvestmentOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateRenewInvestmentOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateRenewInvestmentOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateRenewInvestmentOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateRenewInvestmentOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RenewalInvestmentRequestId

	if len(errors) > 0 {
		return GenerateRenewInvestmentOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateRenewInvestmentOtpRequestMultiError is an error wrapping multiple
// validation errors returned by
// GenerateRenewInvestmentOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateRenewInvestmentOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateRenewInvestmentOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateRenewInvestmentOtpRequestMultiError) AllErrors() []error { return m }

// GenerateRenewInvestmentOtpRequestValidationError is the validation error
// returned by GenerateRenewInvestmentOtpRequest.Validate if the designated
// constraints aren't met.
type GenerateRenewInvestmentOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateRenewInvestmentOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateRenewInvestmentOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateRenewInvestmentOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateRenewInvestmentOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateRenewInvestmentOtpRequestValidationError) ErrorName() string {
	return "GenerateRenewInvestmentOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateRenewInvestmentOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateRenewInvestmentOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateRenewInvestmentOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateRenewInvestmentOtpRequestValidationError{}

// Validate checks the field values on GenerateRenewInvestmentOtpResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GenerateRenewInvestmentOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateRenewInvestmentOtpResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateRenewInvestmentOtpResponseMultiError, or nil if none found.
func (m *GenerateRenewInvestmentOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateRenewInvestmentOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateRenewInvestmentOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateRenewInvestmentOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateRenewInvestmentOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateRenewInvestmentOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateRenewInvestmentOtpResponseMultiError is an error wrapping multiple
// validation errors returned by
// GenerateRenewInvestmentOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateRenewInvestmentOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateRenewInvestmentOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateRenewInvestmentOtpResponseMultiError) AllErrors() []error { return m }

// GenerateRenewInvestmentOtpResponseValidationError is the validation error
// returned by GenerateRenewInvestmentOtpResponse.Validate if the designated
// constraints aren't met.
type GenerateRenewInvestmentOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateRenewInvestmentOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateRenewInvestmentOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateRenewInvestmentOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateRenewInvestmentOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateRenewInvestmentOtpResponseValidationError) ErrorName() string {
	return "GenerateRenewInvestmentOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateRenewInvestmentOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateRenewInvestmentOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateRenewInvestmentOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateRenewInvestmentOtpResponseValidationError{}

// Validate checks the field values on RenewInvestmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewInvestmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewInvestmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewInvestmentRequestMultiError, or nil if none found.
func (m *RenewInvestmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewInvestmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewInvestmentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewInvestmentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewInvestmentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Otp

	// no validation rules for RenewalInvestmentRequestId

	if len(errors) > 0 {
		return RenewInvestmentRequestMultiError(errors)
	}

	return nil
}

// RenewInvestmentRequestMultiError is an error wrapping multiple validation
// errors returned by RenewInvestmentRequest.ValidateAll() if the designated
// constraints aren't met.
type RenewInvestmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewInvestmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewInvestmentRequestMultiError) AllErrors() []error { return m }

// RenewInvestmentRequestValidationError is the validation error returned by
// RenewInvestmentRequest.Validate if the designated constraints aren't met.
type RenewInvestmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewInvestmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewInvestmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewInvestmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewInvestmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewInvestmentRequestValidationError) ErrorName() string {
	return "RenewInvestmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RenewInvestmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewInvestmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewInvestmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewInvestmentRequestValidationError{}

// Validate checks the field values on RenewInvestmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewInvestmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewInvestmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewInvestmentResponseMultiError, or nil if none found.
func (m *RenewInvestmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewInvestmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewInvestmentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewInvestmentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewInvestmentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RenewInvestmentResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RenewInvestmentResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RenewInvestmentResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	// no validation rules for FlowType

	if len(errors) > 0 {
		return RenewInvestmentResponseMultiError(errors)
	}

	return nil
}

// RenewInvestmentResponseMultiError is an error wrapping multiple validation
// errors returned by RenewInvestmentResponse.ValidateAll() if the designated
// constraints aren't met.
type RenewInvestmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewInvestmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewInvestmentResponseMultiError) AllErrors() []error { return m }

// RenewInvestmentResponseValidationError is the validation error returned by
// RenewInvestmentResponse.Validate if the designated constraints aren't met.
type RenewInvestmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewInvestmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewInvestmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewInvestmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewInvestmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewInvestmentResponseValidationError) ErrorName() string {
	return "RenewInvestmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RenewInvestmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewInvestmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewInvestmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewInvestmentResponseValidationError{}

// Validate checks the field values on GetGenerateOTPScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGenerateOTPScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGenerateOTPScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGenerateOTPScreenRequestMultiError, or nil if none found.
func (m *GetGenerateOTPScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGenerateOTPScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetGenerateOTPScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetGenerateOTPScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetGenerateOTPScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RenewalType

	// no validation rules for OrderExternalId

	if len(errors) > 0 {
		return GetGenerateOTPScreenRequestMultiError(errors)
	}

	return nil
}

// GetGenerateOTPScreenRequestMultiError is an error wrapping multiple
// validation errors returned by GetGenerateOTPScreenRequest.ValidateAll() if
// the designated constraints aren't met.
type GetGenerateOTPScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGenerateOTPScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGenerateOTPScreenRequestMultiError) AllErrors() []error { return m }

// GetGenerateOTPScreenRequestValidationError is the validation error returned
// by GetGenerateOTPScreenRequest.Validate if the designated constraints
// aren't met.
type GetGenerateOTPScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGenerateOTPScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGenerateOTPScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGenerateOTPScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGenerateOTPScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGenerateOTPScreenRequestValidationError) ErrorName() string {
	return "GetGenerateOTPScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetGenerateOTPScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGenerateOTPScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGenerateOTPScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGenerateOTPScreenRequestValidationError{}

// Validate checks the field values on GetGenerateOTPScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGenerateOTPScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGenerateOTPScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGenerateOTPScreenResponseMultiError, or nil if none found.
func (m *GetGenerateOTPScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGenerateOTPScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetGenerateOTPScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetGenerateOTPScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetGenerateOTPScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetGenerateOTPScreenResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetGenerateOTPScreenResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetGenerateOTPScreenResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetGenerateOTPScreenResponseValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetGenerateOTPScreenResponseValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetGenerateOTPScreenResponseValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WaitingTimeInSecs

	// no validation rules for RenewalInvestmentRequestId

	// no validation rules for SkipAutoRead

	// no validation rules for SchemeName

	// no validation rules for FlowType

	if len(errors) > 0 {
		return GetGenerateOTPScreenResponseMultiError(errors)
	}

	return nil
}

// GetGenerateOTPScreenResponseMultiError is an error wrapping multiple
// validation errors returned by GetGenerateOTPScreenResponse.ValidateAll() if
// the designated constraints aren't met.
type GetGenerateOTPScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGenerateOTPScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGenerateOTPScreenResponseMultiError) AllErrors() []error { return m }

// GetGenerateOTPScreenResponseValidationError is the validation error returned
// by GetGenerateOTPScreenResponse.Validate if the designated constraints
// aren't met.
type GetGenerateOTPScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGenerateOTPScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGenerateOTPScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGenerateOTPScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGenerateOTPScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGenerateOTPScreenResponseValidationError) ErrorName() string {
	return "GetGenerateOTPScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetGenerateOTPScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGenerateOTPScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGenerateOTPScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGenerateOTPScreenResponseValidationError{}

// Validate checks the field values on GetRenewInvestmentAttributesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRenewInvestmentAttributesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRenewInvestmentAttributesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRenewInvestmentAttributesRequestMultiError, or nil if none found.
func (m *GetRenewInvestmentAttributesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRenewInvestmentAttributesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderExternalId

	if len(errors) > 0 {
		return GetRenewInvestmentAttributesRequestMultiError(errors)
	}

	return nil
}

// GetRenewInvestmentAttributesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetRenewInvestmentAttributesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRenewInvestmentAttributesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRenewInvestmentAttributesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRenewInvestmentAttributesRequestMultiError) AllErrors() []error { return m }

// GetRenewInvestmentAttributesRequestValidationError is the validation error
// returned by GetRenewInvestmentAttributesRequest.Validate if the designated
// constraints aren't met.
type GetRenewInvestmentAttributesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRenewInvestmentAttributesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRenewInvestmentAttributesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRenewInvestmentAttributesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRenewInvestmentAttributesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRenewInvestmentAttributesRequestValidationError) ErrorName() string {
	return "GetRenewInvestmentAttributesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRenewInvestmentAttributesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRenewInvestmentAttributesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRenewInvestmentAttributesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRenewInvestmentAttributesRequestValidationError{}

// Validate checks the field values on GetRenewInvestmentAttributesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRenewInvestmentAttributesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRenewInvestmentAttributesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRenewInvestmentAttributesResponseMultiError, or nil if none found.
func (m *GetRenewInvestmentAttributesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRenewInvestmentAttributesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRenewInvestmentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponseValidationError{
					field:  "RenewInvestmentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponseValidationError{
					field:  "RenewInvestmentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRenewInvestmentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponseValidationError{
				field:  "RenewInvestmentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	// no validation rules for FlowType

	if len(errors) > 0 {
		return GetRenewInvestmentAttributesResponseMultiError(errors)
	}

	return nil
}

// GetRenewInvestmentAttributesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRenewInvestmentAttributesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRenewInvestmentAttributesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRenewInvestmentAttributesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRenewInvestmentAttributesResponseMultiError) AllErrors() []error { return m }

// GetRenewInvestmentAttributesResponseValidationError is the validation error
// returned by GetRenewInvestmentAttributesResponse.Validate if the designated
// constraints aren't met.
type GetRenewInvestmentAttributesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRenewInvestmentAttributesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRenewInvestmentAttributesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRenewInvestmentAttributesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRenewInvestmentAttributesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRenewInvestmentAttributesResponseValidationError) ErrorName() string {
	return "GetRenewInvestmentAttributesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRenewInvestmentAttributesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRenewInvestmentAttributesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRenewInvestmentAttributesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRenewInvestmentAttributesResponseValidationError{}

// Validate checks the field values on GetAllUpcomingRenewalsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllUpcomingRenewalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllUpcomingRenewalsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllUpcomingRenewalsRequestMultiError, or nil if none found.
func (m *GetAllUpcomingRenewalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllUpcomingRenewalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllUpcomingRenewalsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestType

	if len(errors) > 0 {
		return GetAllUpcomingRenewalsRequestMultiError(errors)
	}

	return nil
}

// GetAllUpcomingRenewalsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllUpcomingRenewalsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAllUpcomingRenewalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllUpcomingRenewalsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllUpcomingRenewalsRequestMultiError) AllErrors() []error { return m }

// GetAllUpcomingRenewalsRequestValidationError is the validation error
// returned by GetAllUpcomingRenewalsRequest.Validate if the designated
// constraints aren't met.
type GetAllUpcomingRenewalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllUpcomingRenewalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllUpcomingRenewalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllUpcomingRenewalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllUpcomingRenewalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllUpcomingRenewalsRequestValidationError) ErrorName() string {
	return "GetAllUpcomingRenewalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllUpcomingRenewalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllUpcomingRenewalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllUpcomingRenewalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllUpcomingRenewalsRequestValidationError{}

// Validate checks the field values on GetAllUpcomingRenewalsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllUpcomingRenewalsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllUpcomingRenewalsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllUpcomingRenewalsResponseMultiError, or nil if none found.
func (m *GetAllUpcomingRenewalsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllUpcomingRenewalsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllUpcomingRenewalsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllUpcomingRenewalsResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllUpcomingRenewalsResponseValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetViewAll()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAll()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllUpcomingRenewalsResponseValidationError{
				field:  "ViewAll",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRenewalInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
						field:  fmt.Sprintf("RenewalInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllUpcomingRenewalsResponseValidationError{
						field:  fmt.Sprintf("RenewalInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllUpcomingRenewalsResponseValidationError{
					field:  fmt.Sprintf("RenewalInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllUpcomingRenewalsResponseMultiError(errors)
	}

	return nil
}

// GetAllUpcomingRenewalsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllUpcomingRenewalsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAllUpcomingRenewalsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllUpcomingRenewalsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllUpcomingRenewalsResponseMultiError) AllErrors() []error { return m }

// GetAllUpcomingRenewalsResponseValidationError is the validation error
// returned by GetAllUpcomingRenewalsResponse.Validate if the designated
// constraints aren't met.
type GetAllUpcomingRenewalsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllUpcomingRenewalsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllUpcomingRenewalsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllUpcomingRenewalsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllUpcomingRenewalsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllUpcomingRenewalsResponseValidationError) ErrorName() string {
	return "GetAllUpcomingRenewalsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllUpcomingRenewalsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllUpcomingRenewalsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllUpcomingRenewalsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllUpcomingRenewalsResponseValidationError{}

// Validate checks the field values on
// UpdateMaturityConsentForInvestmentRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateMaturityConsentForInvestmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateMaturityConsentForInvestmentRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateMaturityConsentForInvestmentRequestMultiError, or nil if none found.
func (m *UpdateMaturityConsentForInvestmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMaturityConsentForInvestmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMaturityConsentForInvestmentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMaturityConsentForInvestmentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMaturityConsentForInvestmentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderExternalId

	// no validation rules for MaturityConsentType

	if len(errors) > 0 {
		return UpdateMaturityConsentForInvestmentRequestMultiError(errors)
	}

	return nil
}

// UpdateMaturityConsentForInvestmentRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateMaturityConsentForInvestmentRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateMaturityConsentForInvestmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMaturityConsentForInvestmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMaturityConsentForInvestmentRequestMultiError) AllErrors() []error { return m }

// UpdateMaturityConsentForInvestmentRequestValidationError is the validation
// error returned by UpdateMaturityConsentForInvestmentRequest.Validate if the
// designated constraints aren't met.
type UpdateMaturityConsentForInvestmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMaturityConsentForInvestmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMaturityConsentForInvestmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMaturityConsentForInvestmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMaturityConsentForInvestmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMaturityConsentForInvestmentRequestValidationError) ErrorName() string {
	return "UpdateMaturityConsentForInvestmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMaturityConsentForInvestmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMaturityConsentForInvestmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMaturityConsentForInvestmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMaturityConsentForInvestmentRequestValidationError{}

// Validate checks the field values on
// UpdateMaturityConsentForInvestmentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateMaturityConsentForInvestmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateMaturityConsentForInvestmentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateMaturityConsentForInvestmentResponseMultiError, or nil if none found.
func (m *UpdateMaturityConsentForInvestmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMaturityConsentForInvestmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMaturityConsentForInvestmentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMaturityConsentForInvestmentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMaturityConsentForInvestmentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ToastMessage

	if len(errors) > 0 {
		return UpdateMaturityConsentForInvestmentResponseMultiError(errors)
	}

	return nil
}

// UpdateMaturityConsentForInvestmentResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateMaturityConsentForInvestmentResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateMaturityConsentForInvestmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMaturityConsentForInvestmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMaturityConsentForInvestmentResponseMultiError) AllErrors() []error { return m }

// UpdateMaturityConsentForInvestmentResponseValidationError is the validation
// error returned by UpdateMaturityConsentForInvestmentResponse.Validate if
// the designated constraints aren't met.
type UpdateMaturityConsentForInvestmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMaturityConsentForInvestmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMaturityConsentForInvestmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMaturityConsentForInvestmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMaturityConsentForInvestmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMaturityConsentForInvestmentResponseValidationError) ErrorName() string {
	return "UpdateMaturityConsentForInvestmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMaturityConsentForInvestmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMaturityConsentForInvestmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMaturityConsentForInvestmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMaturityConsentForInvestmentResponseValidationError{}

// Validate checks the field values on
// GetMaturityAmountAndConsentFormForInvestmentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMaturityAmountAndConsentFormForInvestmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetMaturityAmountAndConsentFormForInvestmentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetMaturityAmountAndConsentFormForInvestmentRequestMultiError, or nil if
// none found.
func (m *GetMaturityAmountAndConsentFormForInvestmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaturityAmountAndConsentFormForInvestmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{
					field:  "InvestmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{
					field:  "InvestmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{
				field:  "InvestmentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	// no validation rules for MaturityConsentType

	if len(errors) > 0 {
		return GetMaturityAmountAndConsentFormForInvestmentRequestMultiError(errors)
	}

	return nil
}

// GetMaturityAmountAndConsentFormForInvestmentRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetMaturityAmountAndConsentFormForInvestmentRequest.ValidateAll() if the
// designated constraints aren't met.
type GetMaturityAmountAndConsentFormForInvestmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaturityAmountAndConsentFormForInvestmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaturityAmountAndConsentFormForInvestmentRequestMultiError) AllErrors() []error { return m }

// GetMaturityAmountAndConsentFormForInvestmentRequestValidationError is the
// validation error returned by
// GetMaturityAmountAndConsentFormForInvestmentRequest.Validate if the
// designated constraints aren't met.
type GetMaturityAmountAndConsentFormForInvestmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaturityAmountAndConsentFormForInvestmentRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetMaturityAmountAndConsentFormForInvestmentRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetMaturityAmountAndConsentFormForInvestmentRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetMaturityAmountAndConsentFormForInvestmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaturityAmountAndConsentFormForInvestmentRequestValidationError) ErrorName() string {
	return "GetMaturityAmountAndConsentFormForInvestmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaturityAmountAndConsentFormForInvestmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaturityAmountAndConsentFormForInvestmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaturityAmountAndConsentFormForInvestmentRequestValidationError{}

// Validate checks the field values on
// GetMaturityAmountAndConsentFormForInvestmentResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMaturityAmountAndConsentFormForInvestmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetMaturityAmountAndConsentFormForInvestmentResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetMaturityAmountAndConsentFormForInvestmentResponseMultiError, or nil if
// none found.
func (m *GetMaturityAmountAndConsentFormForInvestmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaturityAmountAndConsentFormForInvestmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
					field:  "MaturityAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
					field:  "MaturityAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
				field:  "MaturityAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
					field:  "ConsentCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
					field:  "ConsentCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{
				field:  "ConsentCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMaturityAmountAndConsentFormForInvestmentResponseMultiError(errors)
	}

	return nil
}

// GetMaturityAmountAndConsentFormForInvestmentResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetMaturityAmountAndConsentFormForInvestmentResponse.ValidateAll() if the
// designated constraints aren't met.
type GetMaturityAmountAndConsentFormForInvestmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaturityAmountAndConsentFormForInvestmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaturityAmountAndConsentFormForInvestmentResponseMultiError) AllErrors() []error { return m }

// GetMaturityAmountAndConsentFormForInvestmentResponseValidationError is the
// validation error returned by
// GetMaturityAmountAndConsentFormForInvestmentResponse.Validate if the
// designated constraints aren't met.
type GetMaturityAmountAndConsentFormForInvestmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaturityAmountAndConsentFormForInvestmentResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetMaturityAmountAndConsentFormForInvestmentResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetMaturityAmountAndConsentFormForInvestmentResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetMaturityAmountAndConsentFormForInvestmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaturityAmountAndConsentFormForInvestmentResponseValidationError) ErrorName() string {
	return "GetMaturityAmountAndConsentFormForInvestmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaturityAmountAndConsentFormForInvestmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaturityAmountAndConsentFormForInvestmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaturityAmountAndConsentFormForInvestmentResponseValidationError{}

// Validate checks the field values on GetAllActivitiesResponse_FilterChip with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAllActivitiesResponse_FilterChip) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllActivitiesResponse_FilterChip
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAllActivitiesResponse_FilterChipMultiError, or nil if none found.
func (m *GetAllActivitiesResponse_FilterChip) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllActivitiesResponse_FilterChip) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayName

	// no validation rules for IsSelected

	// no validation rules for FilterId

	if len(errors) > 0 {
		return GetAllActivitiesResponse_FilterChipMultiError(errors)
	}

	return nil
}

// GetAllActivitiesResponse_FilterChipMultiError is an error wrapping multiple
// validation errors returned by
// GetAllActivitiesResponse_FilterChip.ValidateAll() if the designated
// constraints aren't met.
type GetAllActivitiesResponse_FilterChipMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllActivitiesResponse_FilterChipMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllActivitiesResponse_FilterChipMultiError) AllErrors() []error { return m }

// GetAllActivitiesResponse_FilterChipValidationError is the validation error
// returned by GetAllActivitiesResponse_FilterChip.Validate if the designated
// constraints aren't met.
type GetAllActivitiesResponse_FilterChipValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllActivitiesResponse_FilterChipValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllActivitiesResponse_FilterChipValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllActivitiesResponse_FilterChipValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllActivitiesResponse_FilterChipValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllActivitiesResponse_FilterChipValidationError) ErrorName() string {
	return "GetAllActivitiesResponse_FilterChipValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllActivitiesResponse_FilterChipValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllActivitiesResponse_FilterChip.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllActivitiesResponse_FilterChipValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllActivitiesResponse_FilterChipValidationError{}

// Validate checks the field values on GetRecentActivitiesResponse_NudgeInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecentActivitiesResponse_NudgeInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecentActivitiesResponse_NudgeInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecentActivitiesResponse_NudgeInfoMultiError, or nil if none found.
func (m *GetRecentActivitiesResponse_NudgeInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecentActivitiesResponse_NudgeInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	// no validation rules for Title

	// no validation rules for SubTitle

	// no validation rules for BgColor

	if len(errors) > 0 {
		return GetRecentActivitiesResponse_NudgeInfoMultiError(errors)
	}

	return nil
}

// GetRecentActivitiesResponse_NudgeInfoMultiError is an error wrapping
// multiple validation errors returned by
// GetRecentActivitiesResponse_NudgeInfo.ValidateAll() if the designated
// constraints aren't met.
type GetRecentActivitiesResponse_NudgeInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecentActivitiesResponse_NudgeInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecentActivitiesResponse_NudgeInfoMultiError) AllErrors() []error { return m }

// GetRecentActivitiesResponse_NudgeInfoValidationError is the validation error
// returned by GetRecentActivitiesResponse_NudgeInfo.Validate if the
// designated constraints aren't met.
type GetRecentActivitiesResponse_NudgeInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecentActivitiesResponse_NudgeInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecentActivitiesResponse_NudgeInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecentActivitiesResponse_NudgeInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecentActivitiesResponse_NudgeInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecentActivitiesResponse_NudgeInfoValidationError) ErrorName() string {
	return "GetRecentActivitiesResponse_NudgeInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecentActivitiesResponse_NudgeInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecentActivitiesResponse_NudgeInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecentActivitiesResponse_NudgeInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecentActivitiesResponse_NudgeInfoValidationError{}

// Validate checks the field values on
// GetWithdrawMoneyAttributesResponse_RequestedAmountTile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWithdrawMoneyAttributesResponse_RequestedAmountTile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetWithdrawMoneyAttributesResponse_RequestedAmountTile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWithdrawMoneyAttributesResponse_RequestedAmountTileMultiError, or nil if
// none found.
func (m *GetWithdrawMoneyAttributesResponse_RequestedAmountTile) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWithdrawMoneyAttributesResponse_RequestedAmountTile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestedAmountTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{
					field:  "RequestedAmountTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{
					field:  "RequestedAmountTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestedAmountTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{
				field:  "RequestedAmountTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWithdrawMoneyAttributesResponse_RequestedAmountTileMultiError(errors)
	}

	return nil
}

// GetWithdrawMoneyAttributesResponse_RequestedAmountTileMultiError is an error
// wrapping multiple validation errors returned by
// GetWithdrawMoneyAttributesResponse_RequestedAmountTile.ValidateAll() if the
// designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_RequestedAmountTileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWithdrawMoneyAttributesResponse_RequestedAmountTileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWithdrawMoneyAttributesResponse_RequestedAmountTileMultiError) AllErrors() []error {
	return m
}

// GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError is the
// validation error returned by
// GetWithdrawMoneyAttributesResponse_RequestedAmountTile.Validate if the
// designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError) ErrorName() string {
	return "GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError"
}

// Error satisfies the builtin error interface
func (e GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWithdrawMoneyAttributesResponse_RequestedAmountTile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWithdrawMoneyAttributesResponse_RequestedAmountTileValidationError{}

// Validate checks the field values on
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsMultiError, or nil
// if none found.
func (m *GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAmountBreakUp() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
						field:  fmt.Sprintf("AmountBreakUp[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
						field:  fmt.Sprintf("AmountBreakUp[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
					field:  fmt.Sprintf("AmountBreakUp[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Icon

	if all {
		switch v := interface{}(m.GetPenaltyMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
					field:  "PenaltyMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
					field:  "PenaltyMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{
				field:  "PenaltyMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsMultiError(errors)
	}

	return nil
}

// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsMultiError is an
// error wrapping multiple validation errors returned by
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails.ValidateAll() if
// the designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsMultiError) AllErrors() []error {
	return m
}

// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError is
// the validation error returned by
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails.Validate if the
// designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError) ErrorName() string {
	return "GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWithdrawMoneyAttributesResponse_AmountBreakUpDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWithdrawMoneyAttributesResponse_AmountBreakUpDetailsValidationError{}

// Validate checks the field values on
// GetWithdrawMoneyAttributesResponse_NudgeDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetWithdrawMoneyAttributesResponse_NudgeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetWithdrawMoneyAttributesResponse_NudgeDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetWithdrawMoneyAttributesResponse_NudgeDetailsMultiError, or nil if none found.
func (m *GetWithdrawMoneyAttributesResponse_NudgeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWithdrawMoneyAttributesResponse_NudgeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TitleBgColor

	if all {
		switch v := interface{}(m.GetDesc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "Desc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "Desc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
				field:  "Desc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "Img",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "Img",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
				field:  "Img",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWithdrawLaterCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "WithdrawLaterCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "WithdrawLaterCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWithdrawLaterCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
				field:  "WithdrawLaterCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProceedCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "ProceedCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
					field:  "ProceedCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProceedCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{
				field:  "ProceedCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWithdrawMoneyAttributesResponse_NudgeDetailsMultiError(errors)
	}

	return nil
}

// GetWithdrawMoneyAttributesResponse_NudgeDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetWithdrawMoneyAttributesResponse_NudgeDetails.ValidateAll() if the
// designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_NudgeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWithdrawMoneyAttributesResponse_NudgeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWithdrawMoneyAttributesResponse_NudgeDetailsMultiError) AllErrors() []error { return m }

// GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError is the
// validation error returned by
// GetWithdrawMoneyAttributesResponse_NudgeDetails.Validate if the designated
// constraints aren't met.
type GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError) ErrorName() string {
	return "GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWithdrawMoneyAttributesResponse_NudgeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWithdrawMoneyAttributesResponse_NudgeDetailsValidationError{}

// Validate checks the field values on
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpMultiError,
// or nil if none found.
func (m *GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	if len(errors) > 0 {
		return GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpMultiError(errors)
	}

	return nil
}

// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpMultiError
// is an error wrapping multiple validation errors returned by
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp.ValidateAll()
// if the designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpMultiError) AllErrors() []error {
	return m
}

// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError
// is the validation error returned by
// GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp.Validate
// if the designated constraints aren't met.
type GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError) ErrorName() string {
	return "GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError"
}

// Error satisfies the builtin error interface
func (e GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUpValidationError{}

// Validate checks the field values on WithdrawV2Request_AmountBreakUp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WithdrawV2Request_AmountBreakUp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WithdrawV2Request_AmountBreakUp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WithdrawV2Request_AmountBreakUpMultiError, or nil if none found.
func (m *WithdrawV2Request_AmountBreakUp) ValidateAll() error {
	return m.validate(true)
}

func (m *WithdrawV2Request_AmountBreakUp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WithdrawV2Request_AmountBreakUpValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WithdrawV2Request_AmountBreakUpValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WithdrawV2Request_AmountBreakUpValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeName

	if len(errors) > 0 {
		return WithdrawV2Request_AmountBreakUpMultiError(errors)
	}

	return nil
}

// WithdrawV2Request_AmountBreakUpMultiError is an error wrapping multiple
// validation errors returned by WithdrawV2Request_AmountBreakUp.ValidateAll()
// if the designated constraints aren't met.
type WithdrawV2Request_AmountBreakUpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WithdrawV2Request_AmountBreakUpMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WithdrawV2Request_AmountBreakUpMultiError) AllErrors() []error { return m }

// WithdrawV2Request_AmountBreakUpValidationError is the validation error
// returned by WithdrawV2Request_AmountBreakUp.Validate if the designated
// constraints aren't met.
type WithdrawV2Request_AmountBreakUpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WithdrawV2Request_AmountBreakUpValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WithdrawV2Request_AmountBreakUpValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WithdrawV2Request_AmountBreakUpValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WithdrawV2Request_AmountBreakUpValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WithdrawV2Request_AmountBreakUpValidationError) ErrorName() string {
	return "WithdrawV2Request_AmountBreakUpValidationError"
}

// Error satisfies the builtin error interface
func (e WithdrawV2Request_AmountBreakUpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWithdrawV2Request_AmountBreakUp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WithdrawV2Request_AmountBreakUpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WithdrawV2Request_AmountBreakUpValidationError{}

// Validate checks the field values on
// GetRenewInvestmentAttributesResponse_RenewInvestmentData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRenewInvestmentAttributesResponse_RenewInvestmentData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRenewInvestmentAttributesResponse_RenewInvestmentData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRenewInvestmentAttributesResponse_RenewInvestmentDataMultiError, or nil
// if none found.
func (m *GetRenewInvestmentAttributesResponse_RenewInvestmentData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRenewInvestmentAttributesResponse_RenewInvestmentData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBadgeImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "BadgeImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "BadgeImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBadgeImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "BadgeImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityAmountTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "MaturityAmountTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "MaturityAmountTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityAmountTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "MaturityAmountTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "MaturityAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "MaturityAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "MaturityAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInfoChips() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
						field:  fmt.Sprintf("InfoChips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
						field:  fmt.Sprintf("InfoChips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  fmt.Sprintf("InfoChips[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReinvestmentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "ReinvestmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "ReinvestmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReinvestmentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "ReinvestmentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "InvestmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "InvestmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "InvestmentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReinvestmentButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "ReinvestmentButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "ReinvestmentButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReinvestmentButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "ReinvestmentButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRenewalCancelButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "RenewalCancelButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
					field:  "RenewalCancelButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRenewalCancelButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{
				field:  "RenewalCancelButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRenewInvestmentAttributesResponse_RenewInvestmentDataMultiError(errors)
	}

	return nil
}

// GetRenewInvestmentAttributesResponse_RenewInvestmentDataMultiError is an
// error wrapping multiple validation errors returned by
// GetRenewInvestmentAttributesResponse_RenewInvestmentData.ValidateAll() if
// the designated constraints aren't met.
type GetRenewInvestmentAttributesResponse_RenewInvestmentDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRenewInvestmentAttributesResponse_RenewInvestmentDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRenewInvestmentAttributesResponse_RenewInvestmentDataMultiError) AllErrors() []error {
	return m
}

// GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError is
// the validation error returned by
// GetRenewInvestmentAttributesResponse_RenewInvestmentData.Validate if the
// designated constraints aren't met.
type GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError) ErrorName() string {
	return "GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRenewInvestmentAttributesResponse_RenewInvestmentData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRenewInvestmentAttributesResponse_RenewInvestmentDataValidationError{}
