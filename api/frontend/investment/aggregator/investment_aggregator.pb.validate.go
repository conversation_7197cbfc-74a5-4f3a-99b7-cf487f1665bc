// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/investment/aggregator/investment_aggregator.proto

package aggregator

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = deeplink.Screen(0)
)

// Validate checks the field values on InvestmentLandingComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentLandingComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentLandingComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentLandingComponentMultiError, or nil if none found.
func (m *InvestmentLandingComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentLandingComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Component.(type) {
	case *InvestmentLandingComponent_Summary:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSummary()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Summary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Summary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_Nudges:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNudges()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Nudges",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Nudges",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNudges()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Nudges",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_Banner:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBanner()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Banner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Banner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBanner()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_Instruments:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInstruments()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Instruments",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Instruments",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInstruments()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Instruments",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_Recommendations:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRecommendations()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Recommendations",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Recommendations",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRecommendations()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Recommendations",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_QuickLinks:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetQuickLinks()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "QuickLinks",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "QuickLinks",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetQuickLinks()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "QuickLinks",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_FitttComp:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFitttComp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "FitttComp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "FitttComp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFitttComp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "FitttComp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_Story:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStory()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Story",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Story",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStory()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_Alert:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAlert()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Alert",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "Alert",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAlert()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_AutoInvestment:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAutoInvestment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "AutoInvestment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "AutoInvestment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAutoInvestment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "AutoInvestment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_PartnersComponent:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPartnersComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "PartnersComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "PartnersComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPartnersComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "PartnersComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_SearchComponent:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSearchComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "SearchComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "SearchComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSearchComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "SearchComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_VisualElementHeader:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVisualElementHeader()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "VisualElementHeader",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "VisualElementHeader",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVisualElementHeader()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "VisualElementHeader",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_InvestDashboard:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInvestDashboard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "InvestDashboard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "InvestDashboard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInvestDashboard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "InvestDashboard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_CollectiveInstrumentSummary:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCollectiveInstrumentSummary()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "CollectiveInstrumentSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "CollectiveInstrumentSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCollectiveInstrumentSummary()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "CollectiveInstrumentSummary",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *InvestmentLandingComponent_SipProjectionWidget:
		if v == nil {
			err := InvestmentLandingComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSipProjectionWidget()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "SipProjectionWidget",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentLandingComponentValidationError{
						field:  "SipProjectionWidget",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSipProjectionWidget()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentLandingComponentValidationError{
					field:  "SipProjectionWidget",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return InvestmentLandingComponentMultiError(errors)
	}

	return nil
}

// InvestmentLandingComponentMultiError is an error wrapping multiple
// validation errors returned by InvestmentLandingComponent.ValidateAll() if
// the designated constraints aren't met.
type InvestmentLandingComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentLandingComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentLandingComponentMultiError) AllErrors() []error { return m }

// InvestmentLandingComponentValidationError is the validation error returned
// by InvestmentLandingComponent.Validate if the designated constraints aren't met.
type InvestmentLandingComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentLandingComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentLandingComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentLandingComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentLandingComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentLandingComponentValidationError) ErrorName() string {
	return "InvestmentLandingComponentValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentLandingComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentLandingComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentLandingComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentLandingComponentValidationError{}

// Validate checks the field values on InvestmentNudges with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InvestmentNudges) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentNudges with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentNudgesMultiError, or nil if none found.
func (m *InvestmentNudges) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentNudges) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentNudgesValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentNudgesValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentNudgesValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentNudgesValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentNudgesValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentNudgesValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Screen

	if all {
		switch v := interface{}(m.GetTitles()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentNudgesValidationError{
					field:  "Titles",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentNudgesValidationError{
					field:  "Titles",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitles()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentNudgesValidationError{
				field:  "Titles",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentNudgesValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentNudgesValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentNudgesValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InvestmentNudgesMultiError(errors)
	}

	return nil
}

// InvestmentNudgesMultiError is an error wrapping multiple validation errors
// returned by InvestmentNudges.ValidateAll() if the designated constraints
// aren't met.
type InvestmentNudgesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentNudgesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentNudgesMultiError) AllErrors() []error { return m }

// InvestmentNudgesValidationError is the validation error returned by
// InvestmentNudges.Validate if the designated constraints aren't met.
type InvestmentNudgesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentNudgesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentNudgesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentNudgesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentNudgesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentNudgesValidationError) ErrorName() string { return "InvestmentNudgesValidationError" }

// Error satisfies the builtin error interface
func (e InvestmentNudgesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentNudges.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentNudgesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentNudgesValidationError{}

// Validate checks the field values on InvestmentDynamicBanner with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentDynamicBanner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentDynamicBanner with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentDynamicBannerMultiError, or nil if none found.
func (m *InvestmentDynamicBanner) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentDynamicBanner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return InvestmentDynamicBannerMultiError(errors)
	}

	return nil
}

// InvestmentDynamicBannerMultiError is an error wrapping multiple validation
// errors returned by InvestmentDynamicBanner.ValidateAll() if the designated
// constraints aren't met.
type InvestmentDynamicBannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentDynamicBannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentDynamicBannerMultiError) AllErrors() []error { return m }

// InvestmentDynamicBannerValidationError is the validation error returned by
// InvestmentDynamicBanner.Validate if the designated constraints aren't met.
type InvestmentDynamicBannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentDynamicBannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentDynamicBannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentDynamicBannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentDynamicBannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentDynamicBannerValidationError) ErrorName() string {
	return "InvestmentDynamicBannerValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentDynamicBannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentDynamicBanner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentDynamicBannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentDynamicBannerValidationError{}

// Validate checks the field values on InvestmentStoryComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentStoryComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentStoryComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentStoryComponentMultiError, or nil if none found.
func (m *InvestmentStoryComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentStoryComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentStoryComponentValidationError{
					field:  "StoryTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentStoryComponentValidationError{
					field:  "StoryTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentStoryComponentValidationError{
				field:  "StoryTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetViewAll()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentStoryComponentValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentStoryComponentValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAll()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentStoryComponentValidationError{
				field:  "ViewAll",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InvestmentStoryComponentMultiError(errors)
	}

	return nil
}

// InvestmentStoryComponentMultiError is an error wrapping multiple validation
// errors returned by InvestmentStoryComponent.ValidateAll() if the designated
// constraints aren't met.
type InvestmentStoryComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentStoryComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentStoryComponentMultiError) AllErrors() []error { return m }

// InvestmentStoryComponentValidationError is the validation error returned by
// InvestmentStoryComponent.Validate if the designated constraints aren't met.
type InvestmentStoryComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentStoryComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentStoryComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentStoryComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentStoryComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentStoryComponentValidationError) ErrorName() string {
	return "InvestmentStoryComponentValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentStoryComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentStoryComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentStoryComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentStoryComponentValidationError{}

// Validate checks the field values on InvestmentInstruments with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentInstruments) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentInstruments with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentInstrumentsMultiError, or nil if none found.
func (m *InvestmentInstruments) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentInstruments) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentInstrumentsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentInstrumentsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentInstrumentsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentInstrumentsValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentInstrumentsValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentInstrumentsValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCompare()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentInstrumentsValidationError{
					field:  "Compare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentInstrumentsValidationError{
					field:  "Compare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompare()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentInstrumentsValidationError{
				field:  "Compare",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentInstrumentsValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentInstrumentsValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentInstrumentsValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InvestmentInstrumentsMultiError(errors)
	}

	return nil
}

// InvestmentInstrumentsMultiError is an error wrapping multiple validation
// errors returned by InvestmentInstruments.ValidateAll() if the designated
// constraints aren't met.
type InvestmentInstrumentsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentInstrumentsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentInstrumentsMultiError) AllErrors() []error { return m }

// InvestmentInstrumentsValidationError is the validation error returned by
// InvestmentInstruments.Validate if the designated constraints aren't met.
type InvestmentInstrumentsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentInstrumentsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentInstrumentsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentInstrumentsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentInstrumentsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentInstrumentsValidationError) ErrorName() string {
	return "InvestmentInstrumentsValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentInstrumentsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentInstruments.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentInstrumentsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentInstrumentsValidationError{}

// Validate checks the field values on InstrumentCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InstrumentCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstrumentCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InstrumentCardMultiError,
// or nil if none found.
func (m *InstrumentCard) ValidateAll() error {
	return m.validate(true)
}

func (m *InstrumentCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	if all {
		switch v := interface{}(m.GetVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentCardValidationError{
				field:  "VisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentCardValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentCardValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentCardValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InstrumentCardValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InstrumentCardValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InstrumentCardValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetStoryDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "StoryDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentCardValidationError{
					field:  "StoryDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentCardValidationError{
				field:  "StoryDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InstrumentType

	if len(errors) > 0 {
		return InstrumentCardMultiError(errors)
	}

	return nil
}

// InstrumentCardMultiError is an error wrapping multiple validation errors
// returned by InstrumentCard.ValidateAll() if the designated constraints
// aren't met.
type InstrumentCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstrumentCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstrumentCardMultiError) AllErrors() []error { return m }

// InstrumentCardValidationError is the validation error returned by
// InstrumentCard.Validate if the designated constraints aren't met.
type InstrumentCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstrumentCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstrumentCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstrumentCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstrumentCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstrumentCardValidationError) ErrorName() string { return "InstrumentCardValidationError" }

// Error satisfies the builtin error interface
func (e InstrumentCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstrumentCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstrumentCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstrumentCardValidationError{}

// Validate checks the field values on InvestmentRecommendations with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentRecommendations) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentRecommendations with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentRecommendationsMultiError, or nil if none found.
func (m *InvestmentRecommendations) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentRecommendations) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentRecommendationsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentRecommendationsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentRecommendationsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecommendationCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentRecommendationsValidationError{
						field:  fmt.Sprintf("RecommendationCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentRecommendationsValidationError{
						field:  fmt.Sprintf("RecommendationCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentRecommendationsValidationError{
					field:  fmt.Sprintf("RecommendationCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentRecommendationsValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentRecommendationsValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentRecommendationsValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentRecommendationsValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentRecommendationsValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentRecommendationsValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentRecommendationsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentRecommendationsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentRecommendationsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecommendationSegmentExpression

	if len(errors) > 0 {
		return InvestmentRecommendationsMultiError(errors)
	}

	return nil
}

// InvestmentRecommendationsMultiError is an error wrapping multiple validation
// errors returned by InvestmentRecommendations.ValidateAll() if the
// designated constraints aren't met.
type InvestmentRecommendationsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentRecommendationsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentRecommendationsMultiError) AllErrors() []error { return m }

// InvestmentRecommendationsValidationError is the validation error returned by
// InvestmentRecommendations.Validate if the designated constraints aren't met.
type InvestmentRecommendationsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentRecommendationsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentRecommendationsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentRecommendationsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentRecommendationsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentRecommendationsValidationError) ErrorName() string {
	return "InvestmentRecommendationsValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentRecommendationsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentRecommendations.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentRecommendationsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentRecommendationsValidationError{}

// Validate checks the field values on RecommendationTitle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendationTitle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendationTitle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendationTitleMultiError, or nil if none found.
func (m *RecommendationTitle) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendationTitle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationTitleValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationTitleValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationTitleValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationTitleValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationTitleValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecommendationTitleMultiError(errors)
	}

	return nil
}

// RecommendationTitleMultiError is an error wrapping multiple validation
// errors returned by RecommendationTitle.ValidateAll() if the designated
// constraints aren't met.
type RecommendationTitleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendationTitleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendationTitleMultiError) AllErrors() []error { return m }

// RecommendationTitleValidationError is the validation error returned by
// RecommendationTitle.Validate if the designated constraints aren't met.
type RecommendationTitleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendationTitleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendationTitleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendationTitleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendationTitleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendationTitleValidationError) ErrorName() string {
	return "RecommendationTitleValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendationTitleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendationTitle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendationTitleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendationTitleValidationError{}

// Validate checks the field values on RecommendationHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendationHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendationHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendationHeaderMultiError, or nil if none found.
func (m *RecommendationHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendationHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	if all {
		switch v := interface{}(m.GetIconSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationHeaderValidationError{
					field:  "IconSubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationHeaderValidationError{
					field:  "IconSubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationHeaderValidationError{
				field:  "IconSubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetViewAll()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationHeaderValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationHeaderValidationError{
					field:  "ViewAll",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewAll()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationHeaderValidationError{
				field:  "ViewAll",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecommendationHeaderValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecommendationHeaderValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecommendationHeaderValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecommendationHeaderMultiError(errors)
	}

	return nil
}

// RecommendationHeaderMultiError is an error wrapping multiple validation
// errors returned by RecommendationHeader.ValidateAll() if the designated
// constraints aren't met.
type RecommendationHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendationHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendationHeaderMultiError) AllErrors() []error { return m }

// RecommendationHeaderValidationError is the validation error returned by
// RecommendationHeader.Validate if the designated constraints aren't met.
type RecommendationHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendationHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendationHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendationHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendationHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendationHeaderValidationError) ErrorName() string {
	return "RecommendationHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendationHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendationHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendationHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendationHeaderValidationError{}

// Validate checks the field values on RecommendationCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendationCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendationCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendationCardMultiError, or nil if none found.
func (m *RecommendationCard) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendationCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecommendationCardValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecommendationCardValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecommendationCardValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTagV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "TagV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "TagV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTagV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "TagV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InstrumentId

	// no validation rules for InstrumentType

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationCardValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationCardValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldUpdateRealTime

	for idx, item := range m.GetInfoV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecommendationCardValidationError{
						field:  fmt.Sprintf("InfoV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecommendationCardValidationError{
						field:  fmt.Sprintf("InfoV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecommendationCardValidationError{
					field:  fmt.Sprintf("InfoV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecommendationCardMultiError(errors)
	}

	return nil
}

// RecommendationCardMultiError is an error wrapping multiple validation errors
// returned by RecommendationCard.ValidateAll() if the designated constraints
// aren't met.
type RecommendationCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendationCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendationCardMultiError) AllErrors() []error { return m }

// RecommendationCardValidationError is the validation error returned by
// RecommendationCard.Validate if the designated constraints aren't met.
type RecommendationCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendationCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendationCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendationCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendationCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendationCardValidationError) ErrorName() string {
	return "RecommendationCardValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendationCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendationCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendationCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendationCardValidationError{}

// Validate checks the field values on RecommendationInfoV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendationInfoV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendationInfoV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendationInfoV2MultiError, or nil if none found.
func (m *RecommendationInfoV2) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendationInfoV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationInfoV2ValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationInfoV2ValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationInfoV2ValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StreamVariableType

	if len(errors) > 0 {
		return RecommendationInfoV2MultiError(errors)
	}

	return nil
}

// RecommendationInfoV2MultiError is an error wrapping multiple validation
// errors returned by RecommendationInfoV2.ValidateAll() if the designated
// constraints aren't met.
type RecommendationInfoV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendationInfoV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendationInfoV2MultiError) AllErrors() []error { return m }

// RecommendationInfoV2ValidationError is the validation error returned by
// RecommendationInfoV2.Validate if the designated constraints aren't met.
type RecommendationInfoV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendationInfoV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendationInfoV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendationInfoV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendationInfoV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendationInfoV2ValidationError) ErrorName() string {
	return "RecommendationInfoV2ValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendationInfoV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendationInfoV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendationInfoV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendationInfoV2ValidationError{}

// Validate checks the field values on RecommendationFooter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendationFooter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendationFooter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendationFooterMultiError, or nil if none found.
func (m *RecommendationFooter) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendationFooter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationFooterValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationFooterValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationFooterValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IconUrl

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendationFooterValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendationFooterValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendationFooterValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetShadows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecommendationFooterValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecommendationFooterValidationError{
						field:  fmt.Sprintf("Shadows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecommendationFooterValidationError{
					field:  fmt.Sprintf("Shadows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecommendationFooterMultiError(errors)
	}

	return nil
}

// RecommendationFooterMultiError is an error wrapping multiple validation
// errors returned by RecommendationFooter.ValidateAll() if the designated
// constraints aren't met.
type RecommendationFooterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendationFooterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendationFooterMultiError) AllErrors() []error { return m }

// RecommendationFooterValidationError is the validation error returned by
// RecommendationFooter.Validate if the designated constraints aren't met.
type RecommendationFooterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendationFooterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendationFooterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendationFooterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendationFooterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendationFooterValidationError) ErrorName() string {
	return "RecommendationFooterValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendationFooterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendationFooter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendationFooterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendationFooterValidationError{}

// Validate checks the field values on QuickLinks with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QuickLinks) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuickLinks with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QuickLinksMultiError, or
// nil if none found.
func (m *QuickLinks) ValidateAll() error {
	return m.validate(true)
}

func (m *QuickLinks) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuickLinksValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuickLinksValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuickLinksValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuickLink() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuickLinksValidationError{
						field:  fmt.Sprintf("QuickLink[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuickLinksValidationError{
						field:  fmt.Sprintf("QuickLink[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuickLinksValidationError{
					field:  fmt.Sprintf("QuickLink[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QuickLinksMultiError(errors)
	}

	return nil
}

// QuickLinksMultiError is an error wrapping multiple validation errors
// returned by QuickLinks.ValidateAll() if the designated constraints aren't met.
type QuickLinksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuickLinksMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuickLinksMultiError) AllErrors() []error { return m }

// QuickLinksValidationError is the validation error returned by
// QuickLinks.Validate if the designated constraints aren't met.
type QuickLinksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuickLinksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuickLinksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuickLinksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuickLinksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuickLinksValidationError) ErrorName() string { return "QuickLinksValidationError" }

// Error satisfies the builtin error interface
func (e QuickLinksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuickLinks.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuickLinksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuickLinksValidationError{}

// Validate checks the field values on FitttRulesComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FitttRulesComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FitttRulesComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FitttRulesComponentMultiError, or nil if none found.
func (m *FitttRulesComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *FitttRulesComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FitttRulesComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FitttRulesComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FitttRulesComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRuleInfoCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FitttRulesComponentValidationError{
						field:  fmt.Sprintf("RuleInfoCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FitttRulesComponentValidationError{
						field:  fmt.Sprintf("RuleInfoCards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FitttRulesComponentValidationError{
					field:  fmt.Sprintf("RuleInfoCards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExploreText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FitttRulesComponentValidationError{
					field:  "ExploreText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FitttRulesComponentValidationError{
					field:  "ExploreText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExploreText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FitttRulesComponentValidationError{
				field:  "ExploreText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExplore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FitttRulesComponentValidationError{
					field:  "Explore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FitttRulesComponentValidationError{
					field:  "Explore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExplore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FitttRulesComponentValidationError{
				field:  "Explore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FitttRulesComponentMultiError(errors)
	}

	return nil
}

// FitttRulesComponentMultiError is an error wrapping multiple validation
// errors returned by FitttRulesComponent.ValidateAll() if the designated
// constraints aren't met.
type FitttRulesComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FitttRulesComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FitttRulesComponentMultiError) AllErrors() []error { return m }

// FitttRulesComponentValidationError is the validation error returned by
// FitttRulesComponent.Validate if the designated constraints aren't met.
type FitttRulesComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FitttRulesComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FitttRulesComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FitttRulesComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FitttRulesComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FitttRulesComponentValidationError) ErrorName() string {
	return "FitttRulesComponentValidationError"
}

// Error satisfies the builtin error interface
func (e FitttRulesComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFitttRulesComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FitttRulesComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FitttRulesComponentValidationError{}

// Validate checks the field values on RuleInfoCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RuleInfoCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleInfoCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RuleInfoCardMultiError, or
// nil if none found.
func (m *RuleInfoCard) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleInfoCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalCount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleInfoCardValidationError{
					field:  "TotalCount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleInfoCardValidationError{
					field:  "TotalCount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalCount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleInfoCardValidationError{
				field:  "TotalCount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleInfoCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleInfoCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleInfoCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleInfoCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleInfoCardValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleInfoCardValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return RuleInfoCardMultiError(errors)
	}

	return nil
}

// RuleInfoCardMultiError is an error wrapping multiple validation errors
// returned by RuleInfoCard.ValidateAll() if the designated constraints aren't met.
type RuleInfoCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleInfoCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleInfoCardMultiError) AllErrors() []error { return m }

// RuleInfoCardValidationError is the validation error returned by
// RuleInfoCard.Validate if the designated constraints aren't met.
type RuleInfoCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleInfoCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleInfoCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleInfoCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleInfoCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleInfoCardValidationError) ErrorName() string { return "RuleInfoCardValidationError" }

// Error satisfies the builtin error interface
func (e RuleInfoCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleInfoCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleInfoCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleInfoCardValidationError{}

// Validate checks the field values on AlertNotificationComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AlertNotificationComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AlertNotificationComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AlertNotificationComponentMultiError, or nil if none found.
func (m *AlertNotificationComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *AlertNotificationComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AlertNotificationComponentValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AlertNotificationComponentValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AlertNotificationComponentValidationError{
				field:  "RightIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AlertNotificationComponentValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AlertNotificationComponentValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AlertNotificationComponentMultiError(errors)
	}

	return nil
}

// AlertNotificationComponentMultiError is an error wrapping multiple
// validation errors returned by AlertNotificationComponent.ValidateAll() if
// the designated constraints aren't met.
type AlertNotificationComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AlertNotificationComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AlertNotificationComponentMultiError) AllErrors() []error { return m }

// AlertNotificationComponentValidationError is the validation error returned
// by AlertNotificationComponent.Validate if the designated constraints aren't met.
type AlertNotificationComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AlertNotificationComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AlertNotificationComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AlertNotificationComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AlertNotificationComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AlertNotificationComponentValidationError) ErrorName() string {
	return "AlertNotificationComponentValidationError"
}

// Error satisfies the builtin error interface
func (e AlertNotificationComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAlertNotificationComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AlertNotificationComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AlertNotificationComponentValidationError{}

// Validate checks the field values on PartnersComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PartnersComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PartnersComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PartnersComponentMultiError, or nil if none found.
func (m *PartnersComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PartnersComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnersComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnersComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnersComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPartners() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PartnersComponentValidationError{
						field:  fmt.Sprintf("Partners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PartnersComponentValidationError{
						field:  fmt.Sprintf("Partners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PartnersComponentValidationError{
					field:  fmt.Sprintf("Partners[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PartnersComponentMultiError(errors)
	}

	return nil
}

// PartnersComponentMultiError is an error wrapping multiple validation errors
// returned by PartnersComponent.ValidateAll() if the designated constraints
// aren't met.
type PartnersComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PartnersComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PartnersComponentMultiError) AllErrors() []error { return m }

// PartnersComponentValidationError is the validation error returned by
// PartnersComponent.Validate if the designated constraints aren't met.
type PartnersComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PartnersComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PartnersComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PartnersComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PartnersComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PartnersComponentValidationError) ErrorName() string {
	return "PartnersComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PartnersComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPartnersComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PartnersComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PartnersComponentValidationError{}

// Validate checks the field values on Partner with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Partner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Partner with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PartnerMultiError, or nil if none found.
func (m *Partner) ValidateAll() error {
	return m.validate(true)
}

func (m *Partner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnerValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnerValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnerValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPartnerName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnerValidationError{
					field:  "PartnerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnerValidationError{
					field:  "PartnerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnerValidationError{
				field:  "PartnerName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PartnerValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PartnerValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PartnerValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PartnerMultiError(errors)
	}

	return nil
}

// PartnerMultiError is an error wrapping multiple validation errors returned
// by Partner.ValidateAll() if the designated constraints aren't met.
type PartnerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PartnerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PartnerMultiError) AllErrors() []error { return m }

// PartnerValidationError is the validation error returned by Partner.Validate
// if the designated constraints aren't met.
type PartnerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PartnerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PartnerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PartnerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PartnerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PartnerValidationError) ErrorName() string { return "PartnerValidationError" }

// Error satisfies the builtin error interface
func (e PartnerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPartner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PartnerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PartnerValidationError{}
