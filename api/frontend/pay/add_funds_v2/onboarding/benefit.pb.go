// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/pay/add_funds_v2/onboarding/benefit.proto

package onboarding

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Benefit component to be shown
// Example - "200 Joining Bonus", "2x Rewards on Spends", "Debit card with 0 forex markup", "2% cashback on spends"
// These 4 benefits consist of a benefit component
type BenefitsComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the benefits component
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// info details to be shown when user clicks on info icon
	InfoPopup *InfoPopUpDetails `protobuf:"bytes,3,opt,name=info_popup,json=infoPopup,proto3" json:"info_popup,omitempty"`
	// A range based benefit list is the benefits to be shown for a certain amount range
	// Example - Amount 0-10000 will be shown benefits Ben_1, Ben_2, Ben_3
	// Similarly, Amount 10001-20000 will be shown benefits Ben_2, Ben_4, Ben_5
	// This list of benefit range list make up a benefit component
	// Client can use the entered amount to search within the list of benefit range list and find out -
	// - the applicable benefits for this entered amount
	RangeBasedBenefits []*RangeBasedBenefitsInfo `protobuf:"bytes,4,rep,name=range_based_benefits,json=rangeBasedBenefits,proto3" json:"range_based_benefits,omitempty"`
	// background colour
	BgColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *BenefitsComponent) Reset() {
	*x = BenefitsComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitsComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitsComponent) ProtoMessage() {}

func (x *BenefitsComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitsComponent.ProtoReflect.Descriptor instead.
func (*BenefitsComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescGZIP(), []int{0}
}

func (x *BenefitsComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BenefitsComponent) GetInfoPopup() *InfoPopUpDetails {
	if x != nil {
		return x.InfoPopup
	}
	return nil
}

func (x *BenefitsComponent) GetRangeBasedBenefits() []*RangeBasedBenefitsInfo {
	if x != nil {
		return x.RangeBasedBenefits
	}
	return nil
}

func (x *BenefitsComponent) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

type InfoPopUpDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title image
	TitleImage *common.Image `protobuf:"bytes,1,opt,name=title_image,json=titleImage,proto3" json:"title_image,omitempty"`
	// title text, example - "Benefits details"
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// subtitle text, example - "How do I activate these benefits?"
	Subtitle *common.Text `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// description text
	Description *common.Text `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// background colour
	BgColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *InfoPopUpDetails) Reset() {
	*x = InfoPopUpDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoPopUpDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoPopUpDetails) ProtoMessage() {}

func (x *InfoPopUpDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoPopUpDetails.ProtoReflect.Descriptor instead.
func (*InfoPopUpDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescGZIP(), []int{1}
}

func (x *InfoPopUpDetails) GetTitleImage() *common.Image {
	if x != nil {
		return x.TitleImage
	}
	return nil
}

func (x *InfoPopUpDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *InfoPopUpDetails) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *InfoPopUpDetails) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *InfoPopUpDetails) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

// List of benefits applicable for a given amount range
// Example - lets say for the amount range 0-10000, a particular set of benefits will be applicable
// Based on the entered amount, client will fetch the applicable benefits list and display that
type RangeBasedBenefitsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Min Amount for which the benefit list is to be displayed - ex for the range 0-10000, this is 0 [inclusive]
	MinAmount *typesv2.Money `protobuf:"bytes,1,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	// Max Amount for which the benefit list is to be displayed - ex for the range 0-10000, this is 10000 [inclusive]
	MaxAmount *typesv2.Money `protobuf:"bytes,2,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	// List of benefits applicable for this range
	BenefitInfos []*BenefitInfo `protobuf:"bytes,3,rep,name=benefit_infos,json=benefitInfos,proto3" json:"benefit_infos,omitempty"`
}

func (x *RangeBasedBenefitsInfo) Reset() {
	*x = RangeBasedBenefitsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RangeBasedBenefitsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeBasedBenefitsInfo) ProtoMessage() {}

func (x *RangeBasedBenefitsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeBasedBenefitsInfo.ProtoReflect.Descriptor instead.
func (*RangeBasedBenefitsInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescGZIP(), []int{2}
}

func (x *RangeBasedBenefitsInfo) GetMinAmount() *typesv2.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *RangeBasedBenefitsInfo) GetMaxAmount() *typesv2.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *RangeBasedBenefitsInfo) GetBenefitInfos() []*BenefitInfo {
	if x != nil {
		return x.BenefitInfos
	}
	return nil
}

type BenefitInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Min amount for which the benefit line item is enabled [inclusive]
	MinAmount *typesv2.Money `protobuf:"bytes,1,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	// Max amount for which the benefit line item is enabled [inclusive]
	MaxAmount *typesv2.Money `protobuf:"bytes,2,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	// icon to be shown to the left
	LeftIcon *common.Image `protobuf:"bytes,3,opt,name=left_icon,json=leftIcon,proto3" json:"left_icon,omitempty"`
	// icon to be shown to the left when the benefit is disabled
	LeftIconDisabled *common.Image `protobuf:"bytes,4,opt,name=left_icon_disabled,json=leftIconDisabled,proto3" json:"left_icon_disabled,omitempty"`
	// title to be shown - "4x Rewards on Spends"
	Title *common.Text `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// title to be shown when benefit is disabled
	TitleDisabled *common.Text `protobuf:"bytes,6,opt,name=title_disabled,json=titleDisabled,proto3" json:"title_disabled,omitempty"`
	// icon/text to be shown to the right when the amount being entered is eligible for this benefit
	RightIconText *ui.IconTextComponent `protobuf:"bytes,7,opt,name=right_icon_text,json=rightIconText,proto3" json:"right_icon_text,omitempty"`
	// icon/text to be shown to the right when the amount being entered is not eligible for this benefit - "Add 10,000"
	RightIconTextDisabled *ui.IconTextComponent `protobuf:"bytes,8,opt,name=right_icon_text_disabled,json=rightIconTextDisabled,proto3" json:"right_icon_text_disabled,omitempty"`
}

func (x *BenefitInfo) Reset() {
	*x = BenefitInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitInfo) ProtoMessage() {}

func (x *BenefitInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitInfo.ProtoReflect.Descriptor instead.
func (*BenefitInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescGZIP(), []int{3}
}

func (x *BenefitInfo) GetMinAmount() *typesv2.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *BenefitInfo) GetMaxAmount() *typesv2.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *BenefitInfo) GetLeftIcon() *common.Image {
	if x != nil {
		return x.LeftIcon
	}
	return nil
}

func (x *BenefitInfo) GetLeftIconDisabled() *common.Image {
	if x != nil {
		return x.LeftIconDisabled
	}
	return nil
}

func (x *BenefitInfo) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BenefitInfo) GetTitleDisabled() *common.Text {
	if x != nil {
		return x.TitleDisabled
	}
	return nil
}

func (x *BenefitInfo) GetRightIconText() *ui.IconTextComponent {
	if x != nil {
		return x.RightIconText
	}
	return nil
}

func (x *BenefitInfo) GetRightIconTextDisabled() *ui.IconTextComponent {
	if x != nil {
		return x.RightIconTextDisabled
	}
	return nil
}

var File_api_frontend_pay_add_funds_v2_onboarding_benefit_proto protoreflect.FileDescriptor

var file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDesc = []byte{
	0x0a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70,
	0x61, 0x79, 0x2f, 0x61, 0x64, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x76, 0x32, 0x2f,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x61, 0x64, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73,
	0x5f, 0x76, 0x32, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xe0, 0x02, 0x0a, 0x11, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x55, 0x0a, 0x0a, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x61, 0x79, 0x2e, 0x61, 0x64, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x76,
	0x32, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x09, 0x69,
	0x6e, 0x66, 0x6f, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x6e, 0x0a, 0x14, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x61, 0x64, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f,
	0x76, 0x32, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0xbd, 0x02, 0x0a, 0x10, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x6f,
	0x70, 0x55, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0xd6, 0x01, 0x0a, 0x16, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x78,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x56, 0x0a, 0x0d, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x61, 0x64, 0x64,
	0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x76, 0x32, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x8c,
	0x04, 0x0a, 0x0b, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31,
	0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x09, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x12,
	0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x10, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x49, 0x0a, 0x0f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0d, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x5a, 0x0a, 0x18, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x15, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x82, 0x01,
	0x0a, 0x3f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x61, 0x64, 0x64, 0x5f, 0x66, 0x75,
	0x6e, 0x64, 0x73, 0x5f, 0x76, 0x32, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x61, 0x64, 0x64, 0x5f, 0x66,
	0x75, 0x6e, 0x64, 0x73, 0x5f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescOnce sync.Once
	file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescData = file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDesc
)

func file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescGZIP() []byte {
	file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescOnce.Do(func() {
		file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescData)
	})
	return file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDescData
}

var file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_goTypes = []interface{}{
	(*BenefitsComponent)(nil),       // 0: frontend.pay.add_funds_v2.onboarding.BenefitsComponent
	(*InfoPopUpDetails)(nil),        // 1: frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails
	(*RangeBasedBenefitsInfo)(nil),  // 2: frontend.pay.add_funds_v2.onboarding.RangeBasedBenefitsInfo
	(*BenefitInfo)(nil),             // 3: frontend.pay.add_funds_v2.onboarding.BenefitInfo
	(*ui.IconTextComponent)(nil),    // 4: api.typesv2.ui.IconTextComponent
	(*widget.BackgroundColour)(nil), // 5: api.typesv2.common.ui.widget.BackgroundColour
	(*common.Image)(nil),            // 6: api.typesv2.common.Image
	(*common.Text)(nil),             // 7: api.typesv2.common.Text
	(*typesv2.Money)(nil),           // 8: api.typesv2.Money
}
var file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_depIdxs = []int32{
	4,  // 0: frontend.pay.add_funds_v2.onboarding.BenefitsComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	1,  // 1: frontend.pay.add_funds_v2.onboarding.BenefitsComponent.info_popup:type_name -> frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails
	2,  // 2: frontend.pay.add_funds_v2.onboarding.BenefitsComponent.range_based_benefits:type_name -> frontend.pay.add_funds_v2.onboarding.RangeBasedBenefitsInfo
	5,  // 3: frontend.pay.add_funds_v2.onboarding.BenefitsComponent.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	6,  // 4: frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails.title_image:type_name -> api.typesv2.common.Image
	7,  // 5: frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails.title:type_name -> api.typesv2.common.Text
	7,  // 6: frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails.subtitle:type_name -> api.typesv2.common.Text
	7,  // 7: frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails.description:type_name -> api.typesv2.common.Text
	5,  // 8: frontend.pay.add_funds_v2.onboarding.InfoPopUpDetails.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	8,  // 9: frontend.pay.add_funds_v2.onboarding.RangeBasedBenefitsInfo.min_amount:type_name -> api.typesv2.Money
	8,  // 10: frontend.pay.add_funds_v2.onboarding.RangeBasedBenefitsInfo.max_amount:type_name -> api.typesv2.Money
	3,  // 11: frontend.pay.add_funds_v2.onboarding.RangeBasedBenefitsInfo.benefit_infos:type_name -> frontend.pay.add_funds_v2.onboarding.BenefitInfo
	8,  // 12: frontend.pay.add_funds_v2.onboarding.BenefitInfo.min_amount:type_name -> api.typesv2.Money
	8,  // 13: frontend.pay.add_funds_v2.onboarding.BenefitInfo.max_amount:type_name -> api.typesv2.Money
	6,  // 14: frontend.pay.add_funds_v2.onboarding.BenefitInfo.left_icon:type_name -> api.typesv2.common.Image
	6,  // 15: frontend.pay.add_funds_v2.onboarding.BenefitInfo.left_icon_disabled:type_name -> api.typesv2.common.Image
	7,  // 16: frontend.pay.add_funds_v2.onboarding.BenefitInfo.title:type_name -> api.typesv2.common.Text
	7,  // 17: frontend.pay.add_funds_v2.onboarding.BenefitInfo.title_disabled:type_name -> api.typesv2.common.Text
	4,  // 18: frontend.pay.add_funds_v2.onboarding.BenefitInfo.right_icon_text:type_name -> api.typesv2.ui.IconTextComponent
	4,  // 19: frontend.pay.add_funds_v2.onboarding.BenefitInfo.right_icon_text_disabled:type_name -> api.typesv2.ui.IconTextComponent
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_init() }
func file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_init() {
	if File_api_frontend_pay_add_funds_v2_onboarding_benefit_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitsComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoPopUpDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RangeBasedBenefitsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_goTypes,
		DependencyIndexes: file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_depIdxs,
		MessageInfos:      file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_msgTypes,
	}.Build()
	File_api_frontend_pay_add_funds_v2_onboarding_benefit_proto = out.File
	file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_rawDesc = nil
	file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_goTypes = nil
	file_api_frontend_pay_add_funds_v2_onboarding_benefit_proto_depIdxs = nil
}
