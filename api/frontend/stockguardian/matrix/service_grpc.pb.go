// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/stockguardian/matrix/service.proto

package matrix

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Matrix_SkipCustomerApplicationStage_FullMethodName  = "/frontend.stockguardian.matrix.Matrix/SkipCustomerApplicationStage"
	Matrix_ResetCustomerApplicationStage_FullMethodName = "/frontend.stockguardian.matrix.Matrix/ResetCustomerApplicationStage"
)

// MatrixClient is the client API for Matrix service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MatrixClient interface {
	SkipCustomerApplicationStage(ctx context.Context, in *SkipCustomerApplicationStageRequest, opts ...grpc.CallOption) (*SkipCustomerApplicationStageResponse, error)
	ResetCustomerApplicationStage(ctx context.Context, in *ResetCustomerApplicationStageRequest, opts ...grpc.CallOption) (*ResetCustomerApplicationStageResponse, error)
}

type matrixClient struct {
	cc grpc.ClientConnInterface
}

func NewMatrixClient(cc grpc.ClientConnInterface) MatrixClient {
	return &matrixClient{cc}
}

func (c *matrixClient) SkipCustomerApplicationStage(ctx context.Context, in *SkipCustomerApplicationStageRequest, opts ...grpc.CallOption) (*SkipCustomerApplicationStageResponse, error) {
	out := new(SkipCustomerApplicationStageResponse)
	err := c.cc.Invoke(ctx, Matrix_SkipCustomerApplicationStage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *matrixClient) ResetCustomerApplicationStage(ctx context.Context, in *ResetCustomerApplicationStageRequest, opts ...grpc.CallOption) (*ResetCustomerApplicationStageResponse, error) {
	out := new(ResetCustomerApplicationStageResponse)
	err := c.cc.Invoke(ctx, Matrix_ResetCustomerApplicationStage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MatrixServer is the server API for Matrix service.
// All implementations should embed UnimplementedMatrixServer
// for forward compatibility
type MatrixServer interface {
	SkipCustomerApplicationStage(context.Context, *SkipCustomerApplicationStageRequest) (*SkipCustomerApplicationStageResponse, error)
	ResetCustomerApplicationStage(context.Context, *ResetCustomerApplicationStageRequest) (*ResetCustomerApplicationStageResponse, error)
}

// UnimplementedMatrixServer should be embedded to have forward compatible implementations.
type UnimplementedMatrixServer struct {
}

func (UnimplementedMatrixServer) SkipCustomerApplicationStage(context.Context, *SkipCustomerApplicationStageRequest) (*SkipCustomerApplicationStageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkipCustomerApplicationStage not implemented")
}
func (UnimplementedMatrixServer) ResetCustomerApplicationStage(context.Context, *ResetCustomerApplicationStageRequest) (*ResetCustomerApplicationStageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetCustomerApplicationStage not implemented")
}

// UnsafeMatrixServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MatrixServer will
// result in compilation errors.
type UnsafeMatrixServer interface {
	mustEmbedUnimplementedMatrixServer()
}

func RegisterMatrixServer(s grpc.ServiceRegistrar, srv MatrixServer) {
	s.RegisterService(&Matrix_ServiceDesc, srv)
}

func _Matrix_SkipCustomerApplicationStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SkipCustomerApplicationStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatrixServer).SkipCustomerApplicationStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Matrix_SkipCustomerApplicationStage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatrixServer).SkipCustomerApplicationStage(ctx, req.(*SkipCustomerApplicationStageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Matrix_ResetCustomerApplicationStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetCustomerApplicationStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatrixServer).ResetCustomerApplicationStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Matrix_ResetCustomerApplicationStage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatrixServer).ResetCustomerApplicationStage(ctx, req.(*ResetCustomerApplicationStageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Matrix_ServiceDesc is the grpc.ServiceDesc for Matrix service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Matrix_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.stockguardian.matrix.Matrix",
	HandlerType: (*MatrixServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SkipCustomerApplicationStage",
			Handler:    _Matrix_SkipCustomerApplicationStage_Handler,
		},
		{
			MethodName: "ResetCustomerApplicationStage",
			Handler:    _Matrix_ResetCustomerApplicationStage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/stockguardian/matrix/service.proto",
}
