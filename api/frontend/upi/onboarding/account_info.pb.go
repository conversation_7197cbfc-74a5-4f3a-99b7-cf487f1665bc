// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/upi/onboarding/account_info.proto

package onboarding

import (
	accounts "github.com/epifi/gamma/api/accounts"
	enums "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	account "github.com/epifi/gamma/api/typesv2/account"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AccountInfo stores various info regarding the account such as id,masked account number etc
type AccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id denotes the upi account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// masked_account_number denotes the masked account number of the account
	MaskedAccountNumber string `protobuf:"bytes,2,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	// ifsc code for the account
	IfscCode string `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// BankInfo stores various info regarding the bank like the name, ifsc, logo etc
	BankInfo *BankInfo `protobuf:"bytes,4,opt,name=bank_info,json=bankInfo,proto3" json:"bank_info,omitempty"`
	// UpiAccountPreference specifies the preference of the account
	UpiAccountPreference enums.UpiAccountPreference `protobuf:"varint,5,opt,name=upi_account_preference,json=upiAccountPreference,proto3,enum=frontend.upi.onboarding.enums.UpiAccountPreference" json:"upi_account_preference,omitempty"`
	// Type of the account
	AccountType accounts.Type `protobuf:"varint,6,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
	// like tpap_account_id, connected_account id etc.
	DerivedAccountId string `protobuf:"bytes,7,opt,name=derived_account_id,json=derivedAccountId,proto3" json:"derived_account_id,omitempty"`
	// Account Product Offering associated with the AccountType.
	// This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
	//
	// For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
	Apo account.AccountProductOffering `protobuf:"varint,8,opt,name=apo,proto3,enum=api.typesv2.account.AccountProductOffering" json:"apo,omitempty"`
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_upi_onboarding_account_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_upi_onboarding_account_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_upi_onboarding_account_info_proto_rawDescGZIP(), []int{0}
}

func (x *AccountInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccountInfo) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *AccountInfo) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *AccountInfo) GetBankInfo() *BankInfo {
	if x != nil {
		return x.BankInfo
	}
	return nil
}

func (x *AccountInfo) GetUpiAccountPreference() enums.UpiAccountPreference {
	if x != nil {
		return x.UpiAccountPreference
	}
	return enums.UpiAccountPreference(0)
}

func (x *AccountInfo) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *AccountInfo) GetDerivedAccountId() string {
	if x != nil {
		return x.DerivedAccountId
	}
	return ""
}

func (x *AccountInfo) GetApo() account.AccountProductOffering {
	if x != nil {
		return x.Apo
	}
	return account.AccountProductOffering(0)
}

var File_api_frontend_upi_onboarding_account_info_proto protoreflect.FileDescriptor

var file_api_frontend_upi_onboarding_account_info_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x17, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb9, 0x03, 0x0a, 0x0b, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x69, 0x0a, 0x16, 0x75, 0x70, 0x69,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14,
	0x75, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x72, 0x69, 0x76,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x03, 0x61, 0x70, 0x6f, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x03, 0x61, 0x70, 0x6f, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_upi_onboarding_account_info_proto_rawDescOnce sync.Once
	file_api_frontend_upi_onboarding_account_info_proto_rawDescData = file_api_frontend_upi_onboarding_account_info_proto_rawDesc
)

func file_api_frontend_upi_onboarding_account_info_proto_rawDescGZIP() []byte {
	file_api_frontend_upi_onboarding_account_info_proto_rawDescOnce.Do(func() {
		file_api_frontend_upi_onboarding_account_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_upi_onboarding_account_info_proto_rawDescData)
	})
	return file_api_frontend_upi_onboarding_account_info_proto_rawDescData
}

var file_api_frontend_upi_onboarding_account_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_frontend_upi_onboarding_account_info_proto_goTypes = []interface{}{
	(*AccountInfo)(nil),                 // 0: frontend.upi.onboarding.AccountInfo
	(*BankInfo)(nil),                    // 1: frontend.upi.onboarding.BankInfo
	(enums.UpiAccountPreference)(0),     // 2: frontend.upi.onboarding.enums.UpiAccountPreference
	(accounts.Type)(0),                  // 3: accounts.Type
	(account.AccountProductOffering)(0), // 4: api.typesv2.account.AccountProductOffering
}
var file_api_frontend_upi_onboarding_account_info_proto_depIdxs = []int32{
	1, // 0: frontend.upi.onboarding.AccountInfo.bank_info:type_name -> frontend.upi.onboarding.BankInfo
	2, // 1: frontend.upi.onboarding.AccountInfo.upi_account_preference:type_name -> frontend.upi.onboarding.enums.UpiAccountPreference
	3, // 2: frontend.upi.onboarding.AccountInfo.account_type:type_name -> accounts.Type
	4, // 3: frontend.upi.onboarding.AccountInfo.apo:type_name -> api.typesv2.account.AccountProductOffering
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_frontend_upi_onboarding_account_info_proto_init() }
func file_api_frontend_upi_onboarding_account_info_proto_init() {
	if File_api_frontend_upi_onboarding_account_info_proto != nil {
		return
	}
	file_api_frontend_upi_onboarding_bank_info_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_upi_onboarding_account_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_upi_onboarding_account_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_upi_onboarding_account_info_proto_goTypes,
		DependencyIndexes: file_api_frontend_upi_onboarding_account_info_proto_depIdxs,
		MessageInfos:      file_api_frontend_upi_onboarding_account_info_proto_msgTypes,
	}.Build()
	File_api_frontend_upi_onboarding_account_info_proto = out.File
	file_api_frontend_upi_onboarding_account_info_proto_rawDesc = nil
	file_api_frontend_upi_onboarding_account_info_proto_goTypes = nil
	file_api_frontend_upi_onboarding_account_info_proto_depIdxs = nil
}
