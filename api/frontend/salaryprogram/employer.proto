syntax = "proto3";

package frontend.salaryprogram;

import "api/frontend/salaryprogram/enums.proto";

option go_package = "github.com/epifi/gamma/api/frontend/salaryprogram";
option java_package = "com.github.epifi.gamma.api.frontend.salaryprogram";

message Employer {
  // unique internal id of the Employer. can be empty if not a verified employer
  string id = 1;
  // legal name, or name by source
  string legal_name = 2;
  // trade name, can be empty
  string trade_name = 3;
  // whether employer is verified internally or not
  bool is_verified_employer = 4;
  // Enum denoting the channel by which the employer is onboarded for the salary program such as B2B , B2C
  EmployerSalaryProgramChannel salary_program_channel = 8;
}
