// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upcomingtransactions/model/upcoming_transaction.proto

package model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	payment "github.com/epifi/gamma/api/order/payment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = payment.AccountingEntryType(0)
)

// Validate checks the field values on UpcomingTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpcomingTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpcomingTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpcomingTransactionMultiError, or nil if none found.
func (m *UpcomingTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *UpcomingTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ComputedHash

	// no validation rules for ActorId

	// no validation rules for WithEntityId

	// no validation rules for WithEntityType

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MinDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MaxDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubscriptionId

	// no validation rules for CreditDebit

	// no validation rules for LastTransactionId

	// no validation rules for ExecutionStatus

	// no validation rules for Source

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpcomingTransactionMultiError(errors)
	}

	return nil
}

// UpcomingTransactionMultiError is an error wrapping multiple validation
// errors returned by UpcomingTransaction.ValidateAll() if the designated
// constraints aren't met.
type UpcomingTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpcomingTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpcomingTransactionMultiError) AllErrors() []error { return m }

// UpcomingTransactionValidationError is the validation error returned by
// UpcomingTransaction.Validate if the designated constraints aren't met.
type UpcomingTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpcomingTransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpcomingTransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpcomingTransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpcomingTransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpcomingTransactionValidationError) ErrorName() string {
	return "UpcomingTransactionValidationError"
}

// Error satisfies the builtin error interface
func (e UpcomingTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpcomingTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpcomingTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpcomingTransactionValidationError{}
