// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /Users/<USER>/go/src/github.com/epifi/gamma/api/p2pinvestment/eligibility/eligibility.pb.go

package eligibility

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing TierEligibilityParam while reading from DB
func (a *TierEligibilityParam) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the TierEligibilityParam in string format in DB
func (a *TierEligibilityParam) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TierEligibilityParam
func (a *TierEligibilityParam) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TierEligibilityParam
func (a *TierEligibilityParam) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing BalanceEligibilityParam while reading from DB
func (a *BalanceEligibilityParam) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, marshalledData, a)
}

// Valuer interface implementation for storing the BalanceEligibilityParam in string format in DB
func (a *BalanceEligibilityParam) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for BalanceEligibilityParam
func (a *BalanceEligibilityParam) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for BalanceEligibilityParam
func (a *BalanceEligibilityParam) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
