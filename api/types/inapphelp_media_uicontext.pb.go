// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/inapphelp_media_uicontext.proto

package types

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Different Txn states for which story is shown
type InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState int32

const (
	InapphelpMediaUIContextMeta_TxnReceiptMeta_STORY_STATE_UNSPECIFIED InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState = 0
	InapphelpMediaUIContextMeta_TxnReceiptMeta_UPI_DEEMED_SUCCESS      InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState = 1
	InapphelpMediaUIContextMeta_TxnReceiptMeta_UPI_P2P_FAILED_TXN      InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState = 2
	InapphelpMediaUIContextMeta_TxnReceiptMeta_UPI_P2M_FAILED_TXN      InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState = 3
	// context for the story to be shown in the transaction receipt for the charges deducted for ecs/enach transactions
	InapphelpMediaUIContextMeta_TxnReceiptMeta_ECS_ENACH_CHARGES InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState = 4
)

// Enum value maps for InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState.
var (
	InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState_name = map[int32]string{
		0: "STORY_STATE_UNSPECIFIED",
		1: "UPI_DEEMED_SUCCESS",
		2: "UPI_P2P_FAILED_TXN",
		3: "UPI_P2M_FAILED_TXN",
		4: "ECS_ENACH_CHARGES",
	}
	InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState_value = map[string]int32{
		"STORY_STATE_UNSPECIFIED": 0,
		"UPI_DEEMED_SUCCESS":      1,
		"UPI_P2P_FAILED_TXN":      2,
		"UPI_P2M_FAILED_TXN":      3,
		"ECS_ENACH_CHARGES":       4,
	}
)

func (x InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState) Enum() *InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState {
	p := new(InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState)
	*p = x
	return p
}

func (x InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_types_inapphelp_media_uicontext_proto_enumTypes[0].Descriptor()
}

func (InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState) Type() protoreflect.EnumType {
	return &file_api_types_inapphelp_media_uicontext_proto_enumTypes[0]
}

func (x InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState.Descriptor instead.
func (InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState) EnumDescriptor() ([]byte, []int) {
	return file_api_types_inapphelp_media_uicontext_proto_rawDescGZIP(), []int{0, 1, 0}
}

// additional info associated with UIContext (api/inapphelp/media/enum.proto)
// eg: data to identify individual FAQ category screen
type InapphelpMediaUIContextMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Meta:
	//
	//	*InapphelpMediaUIContextMeta_FaqCategory
	//	*InapphelpMediaUIContextMeta_TxnReceipt
	Meta isInapphelpMediaUIContextMeta_Meta `protobuf_oneof:"meta"`
}

func (x *InapphelpMediaUIContextMeta) Reset() {
	*x = InapphelpMediaUIContextMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_inapphelp_media_uicontext_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InapphelpMediaUIContextMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InapphelpMediaUIContextMeta) ProtoMessage() {}

func (x *InapphelpMediaUIContextMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_inapphelp_media_uicontext_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InapphelpMediaUIContextMeta.ProtoReflect.Descriptor instead.
func (*InapphelpMediaUIContextMeta) Descriptor() ([]byte, []int) {
	return file_api_types_inapphelp_media_uicontext_proto_rawDescGZIP(), []int{0}
}

func (m *InapphelpMediaUIContextMeta) GetMeta() isInapphelpMediaUIContextMeta_Meta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (x *InapphelpMediaUIContextMeta) GetFaqCategory() *InapphelpMediaUIContextMeta_FAQCategoryMeta {
	if x, ok := x.GetMeta().(*InapphelpMediaUIContextMeta_FaqCategory); ok {
		return x.FaqCategory
	}
	return nil
}

func (x *InapphelpMediaUIContextMeta) GetTxnReceipt() *InapphelpMediaUIContextMeta_TxnReceiptMeta {
	if x, ok := x.GetMeta().(*InapphelpMediaUIContextMeta_TxnReceipt); ok {
		return x.TxnReceipt
	}
	return nil
}

type isInapphelpMediaUIContextMeta_Meta interface {
	isInapphelpMediaUIContextMeta_Meta()
}

type InapphelpMediaUIContextMeta_FaqCategory struct {
	// for media on faq category screens
	FaqCategory *InapphelpMediaUIContextMeta_FAQCategoryMeta `protobuf:"bytes,1,opt,name=faq_category,json=faqCategory,proto3,oneof"`
}

type InapphelpMediaUIContextMeta_TxnReceipt struct {
	// for media for specific pay transaction receipt flows
	TxnReceipt *InapphelpMediaUIContextMeta_TxnReceiptMeta `protobuf:"bytes,2,opt,name=txn_receipt,json=txnReceipt,proto3,oneof"`
}

func (*InapphelpMediaUIContextMeta_FaqCategory) isInapphelpMediaUIContextMeta_Meta() {}

func (*InapphelpMediaUIContextMeta_TxnReceipt) isInapphelpMediaUIContextMeta_Meta() {}

// meta data required to identify individual FAQ category screens
type InapphelpMediaUIContextMeta_FAQCategoryMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryId   int64  `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
}

func (x *InapphelpMediaUIContextMeta_FAQCategoryMeta) Reset() {
	*x = InapphelpMediaUIContextMeta_FAQCategoryMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_inapphelp_media_uicontext_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InapphelpMediaUIContextMeta_FAQCategoryMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InapphelpMediaUIContextMeta_FAQCategoryMeta) ProtoMessage() {}

func (x *InapphelpMediaUIContextMeta_FAQCategoryMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_inapphelp_media_uicontext_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InapphelpMediaUIContextMeta_FAQCategoryMeta.ProtoReflect.Descriptor instead.
func (*InapphelpMediaUIContextMeta_FAQCategoryMeta) Descriptor() ([]byte, []int) {
	return file_api_types_inapphelp_media_uicontext_proto_rawDescGZIP(), []int{0, 0}
}

func (x *InapphelpMediaUIContextMeta_FAQCategoryMeta) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *InapphelpMediaUIContextMeta_FAQCategoryMeta) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

// meta data required to identify specific pay txn receipt flows
type InapphelpMediaUIContextMeta_TxnReceiptMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoryState InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState `protobuf:"varint,1,opt,name=story_state,json=storyState,proto3,enum=types.InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState" json:"story_state,omitempty"`
}

func (x *InapphelpMediaUIContextMeta_TxnReceiptMeta) Reset() {
	*x = InapphelpMediaUIContextMeta_TxnReceiptMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_inapphelp_media_uicontext_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InapphelpMediaUIContextMeta_TxnReceiptMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InapphelpMediaUIContextMeta_TxnReceiptMeta) ProtoMessage() {}

func (x *InapphelpMediaUIContextMeta_TxnReceiptMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_inapphelp_media_uicontext_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InapphelpMediaUIContextMeta_TxnReceiptMeta.ProtoReflect.Descriptor instead.
func (*InapphelpMediaUIContextMeta_TxnReceiptMeta) Descriptor() ([]byte, []int) {
	return file_api_types_inapphelp_media_uicontext_proto_rawDescGZIP(), []int{0, 1}
}

func (x *InapphelpMediaUIContextMeta_TxnReceiptMeta) GetStoryState() InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState {
	if x != nil {
		return x.StoryState
	}
	return InapphelpMediaUIContextMeta_TxnReceiptMeta_STORY_STATE_UNSPECIFIED
}

var File_api_types_inapphelp_media_uicontext_proto protoreflect.FileDescriptor

var file_api_types_inapphelp_media_uicontext_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x69, 0x6e, 0x61, 0x70,
	0x70, 0x68, 0x65, 0x6c, 0x70, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x75, 0x69, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x22, 0xaa, 0x04, 0x0a, 0x1b, 0x49, 0x6e, 0x61, 0x70, 0x70, 0x68, 0x65, 0x6c, 0x70,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x55, 0x49, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x57, 0x0a, 0x0c, 0x66, 0x61, 0x71, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x49, 0x6e, 0x61, 0x70, 0x70, 0x68, 0x65, 0x6c, 0x70, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x55,
	0x49, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x46, 0x41, 0x51,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b,
	0x66, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x54, 0x0a, 0x0b, 0x74,
	0x78, 0x6e, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x61, 0x70, 0x70, 0x68, 0x65,
	0x6c, 0x70, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x55, 0x49, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70,
	0x74, 0x1a, 0x57, 0x0a, 0x0f, 0x46, 0x41, 0x51, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xfa, 0x01, 0x0a, 0x0e, 0x54,
	0x78, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x5d, 0x0a,
	0x0b, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x61, 0x70, 0x70,
	0x68, 0x65, 0x6c, 0x70, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x55, 0x49, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x88, 0x01, 0x0a,
	0x0a, 0x53, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53,
	0x54, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x50, 0x49, 0x5f,
	0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x50, 0x49, 0x5f,
	0x50, 0x32, 0x4d, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x10, 0x03,
	0x12, 0x15, 0x0a, 0x11, 0x45, 0x43, 0x53, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x04, 0x42, 0x06, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x42,
	0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_inapphelp_media_uicontext_proto_rawDescOnce sync.Once
	file_api_types_inapphelp_media_uicontext_proto_rawDescData = file_api_types_inapphelp_media_uicontext_proto_rawDesc
)

func file_api_types_inapphelp_media_uicontext_proto_rawDescGZIP() []byte {
	file_api_types_inapphelp_media_uicontext_proto_rawDescOnce.Do(func() {
		file_api_types_inapphelp_media_uicontext_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_inapphelp_media_uicontext_proto_rawDescData)
	})
	return file_api_types_inapphelp_media_uicontext_proto_rawDescData
}

var file_api_types_inapphelp_media_uicontext_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_types_inapphelp_media_uicontext_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_types_inapphelp_media_uicontext_proto_goTypes = []interface{}{
	(InapphelpMediaUIContextMeta_TxnReceiptMeta_StoryState)(0), // 0: types.InapphelpMediaUIContextMeta.TxnReceiptMeta.StoryState
	(*InapphelpMediaUIContextMeta)(nil),                        // 1: types.InapphelpMediaUIContextMeta
	(*InapphelpMediaUIContextMeta_FAQCategoryMeta)(nil),        // 2: types.InapphelpMediaUIContextMeta.FAQCategoryMeta
	(*InapphelpMediaUIContextMeta_TxnReceiptMeta)(nil),         // 3: types.InapphelpMediaUIContextMeta.TxnReceiptMeta
}
var file_api_types_inapphelp_media_uicontext_proto_depIdxs = []int32{
	2, // 0: types.InapphelpMediaUIContextMeta.faq_category:type_name -> types.InapphelpMediaUIContextMeta.FAQCategoryMeta
	3, // 1: types.InapphelpMediaUIContextMeta.txn_receipt:type_name -> types.InapphelpMediaUIContextMeta.TxnReceiptMeta
	0, // 2: types.InapphelpMediaUIContextMeta.TxnReceiptMeta.story_state:type_name -> types.InapphelpMediaUIContextMeta.TxnReceiptMeta.StoryState
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_types_inapphelp_media_uicontext_proto_init() }
func file_api_types_inapphelp_media_uicontext_proto_init() {
	if File_api_types_inapphelp_media_uicontext_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_inapphelp_media_uicontext_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InapphelpMediaUIContextMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_inapphelp_media_uicontext_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InapphelpMediaUIContextMeta_FAQCategoryMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_inapphelp_media_uicontext_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InapphelpMediaUIContextMeta_TxnReceiptMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_types_inapphelp_media_uicontext_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*InapphelpMediaUIContextMeta_FaqCategory)(nil),
		(*InapphelpMediaUIContextMeta_TxnReceipt)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_inapphelp_media_uicontext_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_inapphelp_media_uicontext_proto_goTypes,
		DependencyIndexes: file_api_types_inapphelp_media_uicontext_proto_depIdxs,
		EnumInfos:         file_api_types_inapphelp_media_uicontext_proto_enumTypes,
		MessageInfos:      file_api_types_inapphelp_media_uicontext_proto_msgTypes,
	}.Build()
	File_api_types_inapphelp_media_uicontext_proto = out.File
	file_api_types_inapphelp_media_uicontext_proto_rawDesc = nil
	file_api_types_inapphelp_media_uicontext_proto_goTypes = nil
	file_api_types_inapphelp_media_uicontext_proto_depIdxs = nil
}
