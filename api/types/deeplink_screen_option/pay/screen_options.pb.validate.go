// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/types/deeplink_screen_option/pay/screen_options.proto

package pay

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pay "github.com/epifi/gamma/api/frontend/pay"

	timeline "github.com/epifi/gamma/api/frontend/deeplink/timeline"

	transaction "github.com/epifi/gamma/api/frontend/pay/transaction"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pay.OrderStatus(0)

	_ = timeline.TransactionUIEntryPoint(0)

	_ = transaction.PaymentOptionType(0)
)

// Validate checks the field values on PostPaymentScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostPaymentScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostPaymentScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostPaymentScreenOptionsMultiError, or nil if none found.
func (m *PostPaymentScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *PostPaymentScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "HeaderComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "HeaderComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptionsValidationError{
				field:  "HeaderComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentStatusDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "PaymentStatusDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "PaymentStatusDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentStatusDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptionsValidationError{
				field:  "PaymentStatusDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "TxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "TxnDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptionsValidationError{
				field:  "TxnDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorInfoItem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "ErrorInfoItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "ErrorInfoItem",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorInfoItem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptionsValidationError{
				field:  "ErrorInfoItem",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PostPaymentScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PostPaymentScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PostPaymentScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EventProperties

	// no validation rules for Banner

	// no validation rules for OrderId

	// no validation rules for RedirectionType

	// no validation rules for OrderStatus

	if all {
		switch v := interface{}(m.GetPartnerTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptionsValidationError{
					field:  "PartnerTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptionsValidationError{
				field:  "PartnerTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostPaymentScreenOptionsMultiError(errors)
	}

	return nil
}

// PostPaymentScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by PostPaymentScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type PostPaymentScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostPaymentScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostPaymentScreenOptionsMultiError) AllErrors() []error { return m }

// PostPaymentScreenOptionsValidationError is the validation error returned by
// PostPaymentScreenOptions.Validate if the designated constraints aren't met.
type PostPaymentScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostPaymentScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostPaymentScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostPaymentScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostPaymentScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostPaymentScreenOptionsValidationError) ErrorName() string {
	return "PostPaymentScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e PostPaymentScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostPaymentScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostPaymentScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostPaymentScreenOptionsValidationError{}

// Validate checks the field values on
// InitiateAuthForBeneficiaryActivationScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InitiateAuthForBeneficiaryActivationScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateAuthForBeneficiaryActivationScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// InitiateAuthForBeneficiaryActivationScreenOptionsMultiError, or nil if none found.
func (m *InitiateAuthForBeneficiaryActivationScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAuthForBeneficiaryActivationScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAuthForBeneficiaryActivationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAuthForBeneficiaryActivationScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorFrom

	// no validation rules for PiTo

	// no validation rules for AuthMode

	if len(errors) > 0 {
		return InitiateAuthForBeneficiaryActivationScreenOptionsMultiError(errors)
	}

	return nil
}

// InitiateAuthForBeneficiaryActivationScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// InitiateAuthForBeneficiaryActivationScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type InitiateAuthForBeneficiaryActivationScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAuthForBeneficiaryActivationScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAuthForBeneficiaryActivationScreenOptionsMultiError) AllErrors() []error { return m }

// InitiateAuthForBeneficiaryActivationScreenOptionsValidationError is the
// validation error returned by
// InitiateAuthForBeneficiaryActivationScreenOptions.Validate if the
// designated constraints aren't met.
type InitiateAuthForBeneficiaryActivationScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAuthForBeneficiaryActivationScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InitiateAuthForBeneficiaryActivationScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitiateAuthForBeneficiaryActivationScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e InitiateAuthForBeneficiaryActivationScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAuthForBeneficiaryActivationScreenOptionsValidationError) ErrorName() string {
	return "InitiateAuthForBeneficiaryActivationScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAuthForBeneficiaryActivationScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAuthForBeneficiaryActivationScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAuthForBeneficiaryActivationScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAuthForBeneficiaryActivationScreenOptionsValidationError{}

// Validate checks the field values on PaymentOptionsScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PaymentOptionsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaymentOptionsScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PaymentOptionsScreenOptionsMultiError, or nil if none found.
func (m *PaymentOptionsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentOptionsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentOptionsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentOptionsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentOptionsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentOptionsScreenOptionsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentOptionsScreenOptionsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentOptionsScreenOptionsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionUiEntryPoint

	// no validation rules for OrchestrationMetadata

	// no validation rules for ShouldHideCloseIcon

	if utf8.RuneCountInString(m.GetActorTo()) < 1 {
		err := PaymentOptionsScreenOptionsValidationError{
			field:  "ActorTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PiTo

	if all {
		switch v := interface{}(m.GetPostPaymentDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentOptionsScreenOptionsValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentOptionsScreenOptionsValidationError{
					field:  "PostPaymentDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostPaymentDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentOptionsScreenOptionsValidationError{
				field:  "PostPaymentDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	if len(errors) > 0 {
		return PaymentOptionsScreenOptionsMultiError(errors)
	}

	return nil
}

// PaymentOptionsScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by PaymentOptionsScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type PaymentOptionsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentOptionsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentOptionsScreenOptionsMultiError) AllErrors() []error { return m }

// PaymentOptionsScreenOptionsValidationError is the validation error returned
// by PaymentOptionsScreenOptions.Validate if the designated constraints
// aren't met.
type PaymentOptionsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentOptionsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentOptionsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentOptionsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentOptionsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentOptionsScreenOptionsValidationError) ErrorName() string {
	return "PaymentOptionsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e PaymentOptionsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentOptionsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentOptionsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentOptionsScreenOptionsValidationError{}

// Validate checks the field values on
// RecurringPaymentCancellationDisclaimerScreenOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RecurringPaymentCancellationDisclaimerScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RecurringPaymentCancellationDisclaimerScreenOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// RecurringPaymentCancellationDisclaimerScreenOptionsMultiError, or nil if
// none found.
func (m *RecurringPaymentCancellationDisclaimerScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentCancellationDisclaimerScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "TopImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "TopImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
				field:  "TopImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCloseIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloseIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
				field:  "CloseIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetKeyPoint() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
						field:  fmt.Sprintf("KeyPoint[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
						field:  fmt.Sprintf("KeyPoint[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  fmt.Sprintf("KeyPoint[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSwipeButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "SwipeButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
					field:  "SwipeButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSwipeButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{
				field:  "SwipeButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColour

	if len(errors) > 0 {
		return RecurringPaymentCancellationDisclaimerScreenOptionsMultiError(errors)
	}

	return nil
}

// RecurringPaymentCancellationDisclaimerScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// RecurringPaymentCancellationDisclaimerScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type RecurringPaymentCancellationDisclaimerScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentCancellationDisclaimerScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentCancellationDisclaimerScreenOptionsMultiError) AllErrors() []error { return m }

// RecurringPaymentCancellationDisclaimerScreenOptionsValidationError is the
// validation error returned by
// RecurringPaymentCancellationDisclaimerScreenOptions.Validate if the
// designated constraints aren't met.
type RecurringPaymentCancellationDisclaimerScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentCancellationDisclaimerScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RecurringPaymentCancellationDisclaimerScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RecurringPaymentCancellationDisclaimerScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e RecurringPaymentCancellationDisclaimerScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringPaymentCancellationDisclaimerScreenOptionsValidationError) ErrorName() string {
	return "RecurringPaymentCancellationDisclaimerScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentCancellationDisclaimerScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentCancellationDisclaimerScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentCancellationDisclaimerScreenOptionsValidationError{}

// Validate checks the field values on
// UpdateStandingInstructionPinScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateStandingInstructionPinScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateStandingInstructionPinScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateStandingInstructionPinScreenOptionsMultiError, or nil if none found.
func (m *UpdateStandingInstructionPinScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateStandingInstructionPinScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStandingInstructionPinScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetUpdatedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "UpdatedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "UpdatedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStandingInstructionPinScreenOptionsValidationError{
				field:  "UpdatedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStandingInstructionPinScreenOptionsValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]int32, len(m.GetStatusDisplay()))
		i := 0
		for key := range m.GetStatusDisplay() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetStatusDisplay()[key]
			_ = val

			// no validation rules for StatusDisplay[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
							field:  fmt.Sprintf("StatusDisplay[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
							field:  fmt.Sprintf("StatusDisplay[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UpdateStandingInstructionPinScreenOptionsValidationError{
						field:  fmt.Sprintf("StatusDisplay[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for OverallPollingDurationInSeconds

	// no validation rules for AutoDismissAfterInSeconds

	if all {
		switch v := interface{}(m.GetErrorDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "ErrorDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptionsValidationError{
					field:  "ErrorDisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStandingInstructionPinScreenOptionsValidationError{
				field:  "ErrorDisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateStandingInstructionPinScreenOptionsMultiError(errors)
	}

	return nil
}

// UpdateStandingInstructionPinScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// UpdateStandingInstructionPinScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type UpdateStandingInstructionPinScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateStandingInstructionPinScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateStandingInstructionPinScreenOptionsMultiError) AllErrors() []error { return m }

// UpdateStandingInstructionPinScreenOptionsValidationError is the validation
// error returned by UpdateStandingInstructionPinScreenOptions.Validate if the
// designated constraints aren't met.
type UpdateStandingInstructionPinScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateStandingInstructionPinScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateStandingInstructionPinScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateStandingInstructionPinScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateStandingInstructionPinScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateStandingInstructionPinScreenOptionsValidationError) ErrorName() string {
	return "UpdateStandingInstructionPinScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateStandingInstructionPinScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateStandingInstructionPinScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateStandingInstructionPinScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateStandingInstructionPinScreenOptionsValidationError{}

// Validate checks the field values on
// AutoPayFrequencySelectionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AutoPayFrequencySelectionBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AutoPayFrequencySelectionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// AutoPayFrequencySelectionBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *AutoPayFrequencySelectionBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoPayFrequencySelectionBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AutoPayFrequencySelectionBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// AutoPayFrequencySelectionBottomSheetScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// AutoPayFrequencySelectionBottomSheetScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type AutoPayFrequencySelectionBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoPayFrequencySelectionBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoPayFrequencySelectionBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError is the
// validation error returned by
// AutoPayFrequencySelectionBottomSheetScreenOptions.Validate if the
// designated constraints aren't met.
type AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoPayFrequencySelectionBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoPayFrequencySelectionBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on SharePostPaymentScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SharePostPaymentScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SharePostPaymentScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SharePostPaymentScreenOptionsMultiError, or nil if none found.
func (m *SharePostPaymentScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SharePostPaymentScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "HeaderIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "HeaderIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptionsValidationError{
				field:  "HeaderIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentStatusDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "PaymentStatusDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "PaymentStatusDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentStatusDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptionsValidationError{
				field:  "PaymentStatusDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetErrorInfoSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "ErrorInfoSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "ErrorInfoSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorInfoSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptionsValidationError{
				field:  "ErrorInfoSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionDetailsSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "TransactionDetailsSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "TransactionDetailsSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDetailsSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptionsValidationError{
				field:  "TransactionDetailsSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFooterIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "FooterIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptionsValidationError{
					field:  "FooterIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptionsValidationError{
				field:  "FooterIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenBgColour

	if len(errors) > 0 {
		return SharePostPaymentScreenOptionsMultiError(errors)
	}

	return nil
}

// SharePostPaymentScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by SharePostPaymentScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type SharePostPaymentScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharePostPaymentScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharePostPaymentScreenOptionsMultiError) AllErrors() []error { return m }

// SharePostPaymentScreenOptionsValidationError is the validation error
// returned by SharePostPaymentScreenOptions.Validate if the designated
// constraints aren't met.
type SharePostPaymentScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharePostPaymentScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SharePostPaymentScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SharePostPaymentScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SharePostPaymentScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SharePostPaymentScreenOptionsValidationError) ErrorName() string {
	return "SharePostPaymentScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SharePostPaymentScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharePostPaymentScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharePostPaymentScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharePostPaymentScreenOptionsValidationError{}

// Validate checks the field values on PostPaymentScreenOptions_HeaderComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PostPaymentScreenOptions_HeaderComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PostPaymentScreenOptions_HeaderComponent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PostPaymentScreenOptions_HeaderComponentMultiError, or nil if none found.
func (m *PostPaymentScreenOptions_HeaderComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PostPaymentScreenOptions_HeaderComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_HeaderComponentValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_HeaderComponentValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_HeaderComponentValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderRightCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_HeaderComponentValidationError{
					field:  "HeaderRightCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_HeaderComponentValidationError{
					field:  "HeaderRightCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderRightCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_HeaderComponentValidationError{
				field:  "HeaderRightCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostPaymentScreenOptions_HeaderComponentMultiError(errors)
	}

	return nil
}

// PostPaymentScreenOptions_HeaderComponentMultiError is an error wrapping
// multiple validation errors returned by
// PostPaymentScreenOptions_HeaderComponent.ValidateAll() if the designated
// constraints aren't met.
type PostPaymentScreenOptions_HeaderComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostPaymentScreenOptions_HeaderComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostPaymentScreenOptions_HeaderComponentMultiError) AllErrors() []error { return m }

// PostPaymentScreenOptions_HeaderComponentValidationError is the validation
// error returned by PostPaymentScreenOptions_HeaderComponent.Validate if the
// designated constraints aren't met.
type PostPaymentScreenOptions_HeaderComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostPaymentScreenOptions_HeaderComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostPaymentScreenOptions_HeaderComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostPaymentScreenOptions_HeaderComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostPaymentScreenOptions_HeaderComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostPaymentScreenOptions_HeaderComponentValidationError) ErrorName() string {
	return "PostPaymentScreenOptions_HeaderComponentValidationError"
}

// Error satisfies the builtin error interface
func (e PostPaymentScreenOptions_HeaderComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostPaymentScreenOptions_HeaderComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostPaymentScreenOptions_HeaderComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostPaymentScreenOptions_HeaderComponentValidationError{}

// Validate checks the field values on
// PostPaymentScreenOptions_PaymentStatusDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PostPaymentScreenOptions_PaymentStatusDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PostPaymentScreenOptions_PaymentStatusDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// PostPaymentScreenOptions_PaymentStatusDetailsMultiError, or nil if none found.
func (m *PostPaymentScreenOptions_PaymentStatusDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PostPaymentScreenOptions_PaymentStatusDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "StatusTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAmount() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
						field:  fmt.Sprintf("Amount[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
						field:  fmt.Sprintf("Amount[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  fmt.Sprintf("Amount[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetConversionAmount() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
						field:  fmt.Sprintf("ConversionAmount[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
						field:  fmt.Sprintf("ConversionAmount[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  fmt.Sprintf("ConversionAmount[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTxnCategory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
						field:  fmt.Sprintf("TxnCategory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
						field:  fmt.Sprintf("TxnCategory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  fmt.Sprintf("TxnCategory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetStatusIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "StatusIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostPaymentScreenOptions_PaymentStatusDetailsMultiError(errors)
	}

	return nil
}

// PostPaymentScreenOptions_PaymentStatusDetailsMultiError is an error wrapping
// multiple validation errors returned by
// PostPaymentScreenOptions_PaymentStatusDetails.ValidateAll() if the
// designated constraints aren't met.
type PostPaymentScreenOptions_PaymentStatusDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostPaymentScreenOptions_PaymentStatusDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostPaymentScreenOptions_PaymentStatusDetailsMultiError) AllErrors() []error { return m }

// PostPaymentScreenOptions_PaymentStatusDetailsValidationError is the
// validation error returned by
// PostPaymentScreenOptions_PaymentStatusDetails.Validate if the designated
// constraints aren't met.
type PostPaymentScreenOptions_PaymentStatusDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostPaymentScreenOptions_PaymentStatusDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostPaymentScreenOptions_PaymentStatusDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e PostPaymentScreenOptions_PaymentStatusDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostPaymentScreenOptions_PaymentStatusDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostPaymentScreenOptions_PaymentStatusDetailsValidationError) ErrorName() string {
	return "PostPaymentScreenOptions_PaymentStatusDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PostPaymentScreenOptions_PaymentStatusDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostPaymentScreenOptions_PaymentStatusDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostPaymentScreenOptions_PaymentStatusDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostPaymentScreenOptions_PaymentStatusDetailsValidationError{}

// Validate checks the field values on PostPaymentScreenOptions_TxnDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PostPaymentScreenOptions_TxnDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostPaymentScreenOptions_TxnDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PostPaymentScreenOptions_TxnDetailsMultiError, or nil if none found.
func (m *PostPaymentScreenOptions_TxnDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PostPaymentScreenOptions_TxnDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPaymentInstrument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_TxnDetailsValidationError{
					field:  "PaymentInstrument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_TxnDetailsValidationError{
					field:  "PaymentInstrument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentInstrument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_TxnDetailsValidationError{
				field:  "PaymentInstrument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_TxnDetailsValidationError{
					field:  "TxnId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_TxnDetailsValidationError{
					field:  "TxnId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_TxnDetailsValidationError{
				field:  "TxnId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_TxnDetailsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_TxnDetailsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_TxnDetailsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostPaymentScreenOptions_TxnDetailsMultiError(errors)
	}

	return nil
}

// PostPaymentScreenOptions_TxnDetailsMultiError is an error wrapping multiple
// validation errors returned by
// PostPaymentScreenOptions_TxnDetails.ValidateAll() if the designated
// constraints aren't met.
type PostPaymentScreenOptions_TxnDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostPaymentScreenOptions_TxnDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostPaymentScreenOptions_TxnDetailsMultiError) AllErrors() []error { return m }

// PostPaymentScreenOptions_TxnDetailsValidationError is the validation error
// returned by PostPaymentScreenOptions_TxnDetails.Validate if the designated
// constraints aren't met.
type PostPaymentScreenOptions_TxnDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostPaymentScreenOptions_TxnDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostPaymentScreenOptions_TxnDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostPaymentScreenOptions_TxnDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostPaymentScreenOptions_TxnDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostPaymentScreenOptions_TxnDetailsValidationError) ErrorName() string {
	return "PostPaymentScreenOptions_TxnDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PostPaymentScreenOptions_TxnDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostPaymentScreenOptions_TxnDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostPaymentScreenOptions_TxnDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostPaymentScreenOptions_TxnDetailsValidationError{}

// Validate checks the field values on PostPaymentScreenOptions_ErrorInfoItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PostPaymentScreenOptions_ErrorInfoItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PostPaymentScreenOptions_ErrorInfoItem with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// PostPaymentScreenOptions_ErrorInfoItemMultiError, or nil if none found.
func (m *PostPaymentScreenOptions_ErrorInfoItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PostPaymentScreenOptions_ErrorInfoItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_ErrorInfoItemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostPaymentScreenOptions_ErrorInfoItemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostPaymentScreenOptions_ErrorInfoItemValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_ErrorInfoItemValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PostPaymentScreenOptions_ErrorInfoItemValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PostPaymentScreenOptions_ErrorInfoItemValidationError{
					field:  fmt.Sprintf("Actions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PostPaymentScreenOptions_ErrorInfoItemMultiError(errors)
	}

	return nil
}

// PostPaymentScreenOptions_ErrorInfoItemMultiError is an error wrapping
// multiple validation errors returned by
// PostPaymentScreenOptions_ErrorInfoItem.ValidateAll() if the designated
// constraints aren't met.
type PostPaymentScreenOptions_ErrorInfoItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostPaymentScreenOptions_ErrorInfoItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostPaymentScreenOptions_ErrorInfoItemMultiError) AllErrors() []error { return m }

// PostPaymentScreenOptions_ErrorInfoItemValidationError is the validation
// error returned by PostPaymentScreenOptions_ErrorInfoItem.Validate if the
// designated constraints aren't met.
type PostPaymentScreenOptions_ErrorInfoItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostPaymentScreenOptions_ErrorInfoItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostPaymentScreenOptions_ErrorInfoItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostPaymentScreenOptions_ErrorInfoItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostPaymentScreenOptions_ErrorInfoItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostPaymentScreenOptions_ErrorInfoItemValidationError) ErrorName() string {
	return "PostPaymentScreenOptions_ErrorInfoItemValidationError"
}

// Error satisfies the builtin error interface
func (e PostPaymentScreenOptions_ErrorInfoItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostPaymentScreenOptions_ErrorInfoItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostPaymentScreenOptions_ErrorInfoItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostPaymentScreenOptions_ErrorInfoItemValidationError{}

// Validate checks the field values on
// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoMultiError, or
// nil if none found.
func (m *UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatusIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{
					field:  "StatusIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{
					field:  "StatusIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{
				field:  "StatusIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{
					field:  "StatusText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{
					field:  "StatusText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{
				field:  "StatusText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoMultiError(errors)
	}

	return nil
}

// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoMultiError is an
// error wrapping multiple validation errors returned by
// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo.ValidateAll()
// if the designated constraints aren't met.
type UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoMultiError) AllErrors() []error {
	return m
}

// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError
// is the validation error returned by
// UpdateStandingInstructionPinScreenOptions_StatusDisplayInfo.Validate if the
// designated constraints aren't met.
type UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError) ErrorName() string {
	return "UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateStandingInstructionPinScreenOptions_StatusDisplayInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateStandingInstructionPinScreenOptions_StatusDisplayInfoValidationError{}

// Validate checks the field values on
// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionMultiError,
// or nil if none found.
func (m *AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTrailingIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{
					field:  "TrailingIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{
					field:  "TrailingIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrailingIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{
				field:  "TrailingIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionMultiError(errors)
	}

	return nil
}

// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionMultiError
// is an error wrapping multiple validation errors returned by
// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption.ValidateAll()
// if the designated constraints aren't met.
type AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionMultiError) AllErrors() []error {
	return m
}

// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError
// is the validation error returned by
// AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption.Validate
// if the designated constraints aren't met.
type AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError) ErrorName() string {
	return "AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError"
}

// Error satisfies the builtin error interface
func (e AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoPayFrequencySelectionBottomSheetScreenOptions_PaymentFrequencyOptionValidationError{}

// Validate checks the field values on
// SharePostPaymentScreenOptions_PaymentStatusDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SharePostPaymentScreenOptions_PaymentStatusDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SharePostPaymentScreenOptions_PaymentStatusDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// SharePostPaymentScreenOptions_PaymentStatusDetailsMultiError, or nil if
// none found.
func (m *SharePostPaymentScreenOptions_PaymentStatusDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SharePostPaymentScreenOptions_PaymentStatusDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatusIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "StatusIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "StatusTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "StatusTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeeName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "PayeeName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "PayeeName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeeName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "PayeeName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentInstrument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "PaymentInstrument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "PaymentInstrument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentInstrument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "PaymentInstrument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColour

	if all {
		switch v := interface{}(m.GetTimestampSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "TimestampSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
					field:  "TimestampSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestampSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{
				field:  "TimestampSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SharePostPaymentScreenOptions_PaymentStatusDetailsMultiError(errors)
	}

	return nil
}

// SharePostPaymentScreenOptions_PaymentStatusDetailsMultiError is an error
// wrapping multiple validation errors returned by
// SharePostPaymentScreenOptions_PaymentStatusDetails.ValidateAll() if the
// designated constraints aren't met.
type SharePostPaymentScreenOptions_PaymentStatusDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharePostPaymentScreenOptions_PaymentStatusDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharePostPaymentScreenOptions_PaymentStatusDetailsMultiError) AllErrors() []error { return m }

// SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError is the
// validation error returned by
// SharePostPaymentScreenOptions_PaymentStatusDetails.Validate if the
// designated constraints aren't met.
type SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError) ErrorName() string {
	return "SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharePostPaymentScreenOptions_PaymentStatusDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharePostPaymentScreenOptions_PaymentStatusDetailsValidationError{}

// Validate checks the field values on
// SharePostPaymentScreenOptions_ErrorInfoSection with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SharePostPaymentScreenOptions_ErrorInfoSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SharePostPaymentScreenOptions_ErrorInfoSection with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// SharePostPaymentScreenOptions_ErrorInfoSectionMultiError, or nil if none found.
func (m *SharePostPaymentScreenOptions_ErrorInfoSection) ValidateAll() error {
	return m.validate(true)
}

func (m *SharePostPaymentScreenOptions_ErrorInfoSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SharePostPaymentScreenOptions_ErrorInfoSectionMultiError(errors)
	}

	return nil
}

// SharePostPaymentScreenOptions_ErrorInfoSectionMultiError is an error
// wrapping multiple validation errors returned by
// SharePostPaymentScreenOptions_ErrorInfoSection.ValidateAll() if the
// designated constraints aren't met.
type SharePostPaymentScreenOptions_ErrorInfoSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharePostPaymentScreenOptions_ErrorInfoSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharePostPaymentScreenOptions_ErrorInfoSectionMultiError) AllErrors() []error { return m }

// SharePostPaymentScreenOptions_ErrorInfoSectionValidationError is the
// validation error returned by
// SharePostPaymentScreenOptions_ErrorInfoSection.Validate if the designated
// constraints aren't met.
type SharePostPaymentScreenOptions_ErrorInfoSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharePostPaymentScreenOptions_ErrorInfoSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SharePostPaymentScreenOptions_ErrorInfoSectionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SharePostPaymentScreenOptions_ErrorInfoSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SharePostPaymentScreenOptions_ErrorInfoSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SharePostPaymentScreenOptions_ErrorInfoSectionValidationError) ErrorName() string {
	return "SharePostPaymentScreenOptions_ErrorInfoSectionValidationError"
}

// Error satisfies the builtin error interface
func (e SharePostPaymentScreenOptions_ErrorInfoSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharePostPaymentScreenOptions_ErrorInfoSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharePostPaymentScreenOptions_ErrorInfoSectionValidationError{}

// Validate checks the field values on
// SharePostPaymentScreenOptions_TransactionDetailsSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SharePostPaymentScreenOptions_TransactionDetailsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SharePostPaymentScreenOptions_TransactionDetailsSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SharePostPaymentScreenOptions_TransactionDetailsSectionMultiError, or nil
// if none found.
func (m *SharePostPaymentScreenOptions_TransactionDetailsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *SharePostPaymentScreenOptions_TransactionDetailsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactionDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{
						field:  fmt.Sprintf("TransactionDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{
						field:  fmt.Sprintf("TransactionDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{
					field:  fmt.Sprintf("TransactionDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BgColour

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SharePostPaymentScreenOptions_TransactionDetailsSectionMultiError(errors)
	}

	return nil
}

// SharePostPaymentScreenOptions_TransactionDetailsSectionMultiError is an
// error wrapping multiple validation errors returned by
// SharePostPaymentScreenOptions_TransactionDetailsSection.ValidateAll() if
// the designated constraints aren't met.
type SharePostPaymentScreenOptions_TransactionDetailsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharePostPaymentScreenOptions_TransactionDetailsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharePostPaymentScreenOptions_TransactionDetailsSectionMultiError) AllErrors() []error {
	return m
}

// SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError is
// the validation error returned by
// SharePostPaymentScreenOptions_TransactionDetailsSection.Validate if the
// designated constraints aren't met.
type SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError) ErrorName() string {
	return "SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError"
}

// Error satisfies the builtin error interface
func (e SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharePostPaymentScreenOptions_TransactionDetailsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharePostPaymentScreenOptions_TransactionDetailsSectionValidationError{}

// Validate checks the field values on
// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionMultiError,
// or nil if none found.
func (m *SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection) ValidateAll() error {
	return m.validate(true)
}

func (m *SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExecutionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError{
					field:  "ExecutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError{
					field:  "ExecutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError{
				field:  "ExecutionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColour

	if len(errors) > 0 {
		return SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionMultiError(errors)
	}

	return nil
}

// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionMultiError
// is an error wrapping multiple validation errors returned by
// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection.ValidateAll()
// if the designated constraints aren't met.
type SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionMultiError) AllErrors() []error {
	return m
}

// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError
// is the validation error returned by
// SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection.Validate
// if the designated constraints aren't met.
type SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError) ErrorName() string {
	return "SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError"
}

// Error satisfies the builtin error interface
func (e SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharePostPaymentScreenOptions_PaymentStatusDetails_TimeStampSectionValidationError{}

// Validate checks the field values on
// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailMultiError,
// or nil if none found.
func (m *SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRelatedItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
					field:  "RelatedItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
					field:  "RelatedItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRelatedItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{
				field:  "RelatedItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailMultiError(errors)
	}

	return nil
}

// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailMultiError
// is an error wrapping multiple validation errors returned by
// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail.ValidateAll()
// if the designated constraints aren't met.
type SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailMultiError) AllErrors() []error {
	return m
}

// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError
// is the validation error returned by
// SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail.Validate
// if the designated constraints aren't met.
type SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError) ErrorName() string {
	return "SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError"
}

// Error satisfies the builtin error interface
func (e SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharePostPaymentScreenOptions_TransactionDetailsSection_TransactionDetailValidationError{}
