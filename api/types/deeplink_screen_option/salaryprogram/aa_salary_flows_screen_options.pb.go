// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto

package salaryprogram

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	polling "github.com/epifi/gamma/api/frontend/document_upload/polling"
	types "github.com/epifi/gamma/api/types"
	connected_account "github.com/epifi/gamma/api/types/connected_account"
	deeplink_screen_option "github.com/epifi/gamma/api/types/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/types/ui"
	widget "github.com/epifi/gamma/api/types/ui/widget"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AaSalaryLandingScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=20580-51478&t=D2Vo6oWO3FqPjHyB-4
	LoaderText string `protobuf:"bytes,2,opt,name=loader_text,json=loaderText,proto3" json:"loader_text,omitempty"`
}

func (x *AaSalaryLandingScreenOptions) Reset() {
	*x = AaSalaryLandingScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryLandingScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryLandingScreenOptions) ProtoMessage() {}

func (x *AaSalaryLandingScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryLandingScreenOptions.ProtoReflect.Descriptor instead.
func (*AaSalaryLandingScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *AaSalaryLandingScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AaSalaryLandingScreenOptions) GetLoaderText() string {
	if x != nil {
		return x.LoaderText
	}
	return ""
}

type AaSalaryProgramFlowsTerminalScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// title & desc of terminal screen for any flow in aa salary program flows
	Title *types.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc  *types.Text `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// icon to be displayed in the center of the bottom sheet, this icon is based on the flow type and status(success/failures)
	StatusBasedIcon *types.VisualElement `protobuf:"bytes,4,opt,name=status_based_icon,json=statusBasedIcon,proto3" json:"status_based_icon,omitempty"`
	// proceed_cta indicates the subsequent screen that the flow should navigate to.
	ProceedCta *deeplink.Cta `protobuf:"bytes,6,opt,name=proceed_cta,json=proceedCta,proto3" json:"proceed_cta,omitempty"`
	// aa_salary_program_terminal_screen states terminal screen name
	// figma for a sample state: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51258&mode=design&t=LCaSFfUaiubQKZSm-0
	// https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5199-47531&t=6mKN7q19PSHVnJUk-4
	AaSalaryProgramTerminalScreen string `protobuf:"bytes,7,opt,name=aa_salary_program_terminal_screen,json=aaSalaryProgramTerminalScreen,proto3" json:"aa_salary_program_terminal_screen,omitempty"`
	// background color of the bottom sheet
	BgColor *widget.BackgroundColour `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// show_confetti is used whether to show confetti or not
	ShowConfetti bool `protobuf:"varint,9,opt,name=show_confetti,json=showConfetti,proto3" json:"show_confetti,omitempty"`
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) Reset() {
	*x = AaSalaryProgramFlowsTerminalScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsTerminalScreenOptions) ProtoMessage() {}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsTerminalScreenOptions.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsTerminalScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetDesc() *types.Text {
	if x != nil {
		return x.Desc
	}
	return nil
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetStatusBasedIcon() *types.VisualElement {
	if x != nil {
		return x.StatusBasedIcon
	}
	return nil
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetProceedCta() *deeplink.Cta {
	if x != nil {
		return x.ProceedCta
	}
	return nil
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetAaSalaryProgramTerminalScreen() string {
	if x != nil {
		return x.AaSalaryProgramTerminalScreen
	}
	return ""
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *AaSalaryProgramFlowsTerminalScreenOptions) GetShowConfetti() bool {
	if x != nil {
		return x.ShowConfetti
	}
	return false
}

type AaSalaryProgramFlowsAmountTransferSetupScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// top_cashback_icon indicates the rupee cashback icon
	TopCashbackIcon *types.VisualElement `protobuf:"bytes,2,opt,name=top_cashback_icon,json=topCashbackIcon,proto3" json:"top_cashback_icon,omitempty"`
	// title states the text with eligible cashback percentage
	Title *types.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// transfer_amount_cta takes to payment screen
	TransferAmountCta *deeplink.Cta `protobuf:"bytes,4,opt,name=transfer_amount_cta,json=transferAmountCta,proto3" json:"transfer_amount_cta,omitempty"`
	// amount_selection_card is used to select the amount to be transferred, this also tells the cashback percentage
	AmountSelectionCard *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard `protobuf:"bytes,5,opt,name=amount_selection_card,json=amountSelectionCard,proto3" json:"amount_selection_card,omitempty"`
	// icon actions that navigates to back screen
	BackNavigationElement *types.VisualElement `protobuf:"bytes,6,opt,name=back_navigation_element,json=backNavigationElement,proto3" json:"back_navigation_element,omitempty"`
	// shows the info icon
	InfoNavigationElement *ui.IconTextComponent `protobuf:"bytes,7,opt,name=info_navigation_element,json=infoNavigationElement,proto3" json:"info_navigation_element,omitempty"`
	// animation for cashback
	Animation string `protobuf:"bytes,8,opt,name=animation,proto3" json:"animation,omitempty"`
	// background color of the screen
	BgColor *widget.BackgroundColour `protobuf:"bytes,9,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// Powered by federal bank
	PoweredBy *ui.IconTextComponent `protobuf:"bytes,10,opt,name=powered_by,json=poweredBy,proto3" json:"powered_by,omitempty"`
	// ui version of the screen
	Version types.Version `protobuf:"varint,11,opt,name=version,proto3,enum=types.Version" json:"version,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions) ProtoMessage() {}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetTopCashbackIcon() *types.VisualElement {
	if x != nil {
		return x.TopCashbackIcon
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetTransferAmountCta() *deeplink.Cta {
	if x != nil {
		return x.TransferAmountCta
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetAmountSelectionCard() *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard {
	if x != nil {
		return x.AmountSelectionCard
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetBackNavigationElement() *types.VisualElement {
	if x != nil {
		return x.BackNavigationElement
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetInfoNavigationElement() *ui.IconTextComponent {
	if x != nil {
		return x.InfoNavigationElement
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetAnimation() string {
	if x != nil {
		return x.Animation
	}
	return ""
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetPoweredBy() *ui.IconTextComponent {
	if x != nil {
		return x.PoweredBy
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions) GetVersion() types.Version {
	if x != nil {
		return x.Version
	}
	return types.Version(0)
}

type AddFundsViaOffAppTransferScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header             *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TopIcon            *types.VisualElement                       `protobuf:"bytes,2,opt,name=top_icon,json=topIcon,proto3" json:"top_icon,omitempty"`
	Title              *types.Text                                `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	InfoComponents     []*OffAppAddFundsInfoComponent             `protobuf:"bytes,4,rep,name=info_components,json=infoComponents,proto3" json:"info_components,omitempty"`
	BackNavigationIcon *types.VisualElement                       `protobuf:"bytes,5,opt,name=back_navigation_icon,json=backNavigationIcon,proto3" json:"back_navigation_icon,omitempty"`
	InfoIcon           *ui.IconTextComponent                      `protobuf:"bytes,6,opt,name=info_icon,json=infoIcon,proto3" json:"info_icon,omitempty"`
	Cta                *deeplink.Cta                              `protobuf:"bytes,7,opt,name=cta,proto3" json:"cta,omitempty"`
	BgColor            *widget.BackgroundColour                   `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *AddFundsViaOffAppTransferScreenOptions) Reset() {
	*x = AddFundsViaOffAppTransferScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddFundsViaOffAppTransferScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFundsViaOffAppTransferScreenOptions) ProtoMessage() {}

func (x *AddFundsViaOffAppTransferScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFundsViaOffAppTransferScreenOptions.ProtoReflect.Descriptor instead.
func (*AddFundsViaOffAppTransferScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetTopIcon() *types.VisualElement {
	if x != nil {
		return x.TopIcon
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetInfoComponents() []*OffAppAddFundsInfoComponent {
	if x != nil {
		return x.InfoComponents
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetBackNavigationIcon() *types.VisualElement {
	if x != nil {
		return x.BackNavigationIcon
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetInfoIcon() *ui.IconTextComponent {
	if x != nil {
		return x.InfoIcon
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *AddFundsViaOffAppTransferScreenOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

type OffAppAddFundsInfoComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNumber *types.VisualElement `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Title        *types.Text          `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Types that are assignable to Content:
	//
	//	*OffAppAddFundsInfoComponent_IconsList_
	//	*OffAppAddFundsInfoComponent_CopyText_
	Content  isOffAppAddFundsInfoComponent_Content `protobuf_oneof:"content"`
	BgColour *widget.BackgroundColour              `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *OffAppAddFundsInfoComponent) Reset() {
	*x = OffAppAddFundsInfoComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OffAppAddFundsInfoComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OffAppAddFundsInfoComponent) ProtoMessage() {}

func (x *OffAppAddFundsInfoComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OffAppAddFundsInfoComponent.ProtoReflect.Descriptor instead.
func (*OffAppAddFundsInfoComponent) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *OffAppAddFundsInfoComponent) GetSerialNumber() *types.VisualElement {
	if x != nil {
		return x.SerialNumber
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (m *OffAppAddFundsInfoComponent) GetContent() isOffAppAddFundsInfoComponent_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent) GetIconsList() *OffAppAddFundsInfoComponent_IconsList {
	if x, ok := x.GetContent().(*OffAppAddFundsInfoComponent_IconsList_); ok {
		return x.IconsList
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent) GetCopyText() *OffAppAddFundsInfoComponent_CopyText {
	if x, ok := x.GetContent().(*OffAppAddFundsInfoComponent_CopyText_); ok {
		return x.CopyText
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

type isOffAppAddFundsInfoComponent_Content interface {
	isOffAppAddFundsInfoComponent_Content()
}

type OffAppAddFundsInfoComponent_IconsList_ struct {
	IconsList *OffAppAddFundsInfoComponent_IconsList `protobuf:"bytes,3,opt,name=icons_list,json=iconsList,proto3,oneof"`
}

type OffAppAddFundsInfoComponent_CopyText_ struct {
	CopyText *OffAppAddFundsInfoComponent_CopyText `protobuf:"bytes,4,opt,name=copy_text,json=copyText,proto3,oneof"`
}

func (*OffAppAddFundsInfoComponent_IconsList_) isOffAppAddFundsInfoComponent_Content() {}

func (*OffAppAddFundsInfoComponent_CopyText_) isOffAppAddFundsInfoComponent_Content() {}

// screen options for AA_SALARY_SOURCE_OF_FUND_SCREEN
type AaSalarySourceOfFundScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header  *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TopIcon *types.VisualElement                       `protobuf:"bytes,2,opt,name=top_icon,json=topIcon,proto3" json:"top_icon,omitempty"`
	// title of the screen eg - Verify your salary status
	Title *types.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// subtitle of the screen eg - To calculate cashback - verify your salary status
	Subtitle *types.Text `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// data security label eg - Your data is safe & confidential
	SecurityLabel              *ui.IconTextComponent                                         `protobuf:"bytes,5,opt,name=security_label,json=securityLabel,proto3" json:"security_label,omitempty"`
	ConnectedAccountsComponent *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent `protobuf:"bytes,6,opt,name=connected_accounts_component,json=connectedAccountsComponent,proto3" json:"connected_accounts_component,omitempty"`
	TermsInfo                  []*widget.CheckboxItem                                        `protobuf:"bytes,7,rep,name=terms_info,json=termsInfo,proto3" json:"terms_info,omitempty"`
	// component to show ITR coming soon
	BottomView *connected_account.ComingSoonComponent `protobuf:"bytes,8,opt,name=bottom_view,json=bottomView,proto3" json:"bottom_view,omitempty"`
	// consume deeplink from ConnectedAccountRowComponent
	ProceedCta *deeplink.Cta `protobuf:"bytes,9,opt,name=proceed_cta,json=proceedCta,proto3" json:"proceed_cta,omitempty"`
}

func (x *AaSalarySourceOfFundScreenOptions) Reset() {
	*x = AaSalarySourceOfFundScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalarySourceOfFundScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalarySourceOfFundScreenOptions) ProtoMessage() {}

func (x *AaSalarySourceOfFundScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalarySourceOfFundScreenOptions.ProtoReflect.Descriptor instead.
func (*AaSalarySourceOfFundScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *AaSalarySourceOfFundScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetTopIcon() *types.VisualElement {
	if x != nil {
		return x.TopIcon
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetSubtitle() *types.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetSecurityLabel() *ui.IconTextComponent {
	if x != nil {
		return x.SecurityLabel
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetConnectedAccountsComponent() *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent {
	if x != nil {
		return x.ConnectedAccountsComponent
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetTermsInfo() []*widget.CheckboxItem {
	if x != nil {
		return x.TermsInfo
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetBottomView() *connected_account.ComingSoonComponent {
	if x != nil {
		return x.BottomView
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions) GetProceedCta() *deeplink.Cta {
	if x != nil {
		return x.ProceedCta
	}
	return nil
}

type AASalaryDataPullScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header        *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ScreenBgColor *widget.BackgroundColour                   `protobuf:"bytes,2,opt,name=screen_bg_color,json=screenBgColor,proto3" json:"screen_bg_color,omitempty"`
	// data fetch or download/upload is in progress
	ProgressState *AASalaryDataPullScreenOptions_UiData `protobuf:"bytes,3,opt,name=progressState,proto3" json:"progressState,omitempty"`
	// data download/upload has failed
	ErrorState *AASalaryDataPullScreenOptions_UiData `protobuf:"bytes,4,opt,name=errorState,proto3" json:"errorState,omitempty"`
	// if polling option is nil client do not need to poll
	PollingOptions *polling.DocumentPollingOption `protobuf:"bytes,5,opt,name=polling_options,json=pollingOptions,proto3" json:"polling_options,omitempty"`
	// do not pull bank data if false
	PullBankData bool `protobuf:"varint,6,opt,name=pull_bank_data,json=pullBankData,proto3" json:"pull_bank_data,omitempty"`
	// on upload succeed redirect to this deeplink
	SuccessDeeplink *deeplink.Deeplink                                   `protobuf:"bytes,7,opt,name=success_deeplink,json=successDeeplink,proto3" json:"success_deeplink,omitempty"`
	Params          *AASalaryDataPullScreenOptions_AccountDataPullParams `protobuf:"bytes,8,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *AASalaryDataPullScreenOptions) Reset() {
	*x = AASalaryDataPullScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AASalaryDataPullScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AASalaryDataPullScreenOptions) ProtoMessage() {}

func (x *AASalaryDataPullScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AASalaryDataPullScreenOptions.ProtoReflect.Descriptor instead.
func (*AASalaryDataPullScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *AASalaryDataPullScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions) GetScreenBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.ScreenBgColor
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions) GetProgressState() *AASalaryDataPullScreenOptions_UiData {
	if x != nil {
		return x.ProgressState
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions) GetErrorState() *AASalaryDataPullScreenOptions_UiData {
	if x != nil {
		return x.ErrorState
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions) GetPollingOptions() *polling.DocumentPollingOption {
	if x != nil {
		return x.PollingOptions
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions) GetPullBankData() bool {
	if x != nil {
		return x.PullBankData
	}
	return false
}

func (x *AASalaryDataPullScreenOptions) GetSuccessDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.SuccessDeeplink
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions) GetParams() *AASalaryDataPullScreenOptions_AccountDataPullParams {
	if x != nil {
		return x.Params
	}
	return nil
}

type AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title            *types.Text                                                                               `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	RupeesSymbolText *types.Text                                                                               `protobuf:"bytes,2,opt,name=rupees_symbol_text,json=rupeesSymbolText,proto3" json:"rupees_symbol_text,omitempty"`
	EditIcon         *types.VisualElement                                                                      `protobuf:"bytes,3,opt,name=edit_icon,json=editIcon,proto3" json:"edit_icon,omitempty"`
	BgColor          *widget.BackgroundColour                                                                  `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	InitialAmount    *types.Money                                                                              `protobuf:"bytes,5,opt,name=initial_amount,json=initialAmount,proto3" json:"initial_amount,omitempty"`
	SliderOption     *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption    `protobuf:"bytes,6,opt,name=slider_option,json=sliderOption,proto3" json:"slider_option,omitempty"`
	BenefitsSubCard  *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard `protobuf:"bytes,7,opt,name=benefits_sub_card,json=benefitsSubCard,proto3" json:"benefits_sub_card,omitempty"`
	AmountInfo       *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo      `protobuf:"bytes,8,opt,name=amount_info,json=amountInfo,proto3" json:"amount_info,omitempty"`
	// Amount ranges based on which we decide users eligibility
	// Whether eligible for 1% cashback or 2% cashback
	AmountRanges []*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange `protobuf:"bytes,9,rep,name=amount_ranges,json=amountRanges,proto3" json:"amount_ranges,omitempty"`
	// Suggested amount to be displayed to the user to select from amount user wants to transfer
	AmountSuggestions []*types.Text `protobuf:"bytes,10,rep,name=amount_suggestions,json=amountSuggestions,proto3" json:"amount_suggestions,omitempty"`
	// Index of the selected amount suggestion.
	SelectedSuggestionIndex int32 `protobuf:"varint,11,opt,name=selected_suggestion_index,json=selectedSuggestionIndex,proto3" json:"selected_suggestion_index,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) ProtoMessage() {}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetRupeesSymbolText() *types.Text {
	if x != nil {
		return x.RupeesSymbolText
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetEditIcon() *types.VisualElement {
	if x != nil {
		return x.EditIcon
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetInitialAmount() *types.Money {
	if x != nil {
		return x.InitialAmount
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetSliderOption() *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption {
	if x != nil {
		return x.SliderOption
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetBenefitsSubCard() *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard {
	if x != nil {
		return x.BenefitsSubCard
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetAmountInfo() *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo {
	if x != nil {
		return x.AmountInfo
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetAmountRanges() []*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange {
	if x != nil {
		return x.AmountRanges
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetAmountSuggestions() []*types.Text {
	if x != nil {
		return x.AmountSuggestions
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard) GetSelectedSuggestionIndex() int32 {
	if x != nil {
		return x.SelectedSuggestionIndex
	}
	return 0
}

// AmountRange represents a range of monetary values.
// It includes a minimum value (`min_value`), a maximum value (`max_value`), and a tag (`tag`) to describe the cashback user eligible for.
type AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinValue *types.Money `protobuf:"bytes,1,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"` // The minimum monetary value in the range.
	MaxValue *types.Money `protobuf:"bytes,2,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"` // The maximum monetary value in the range.
	Tag      *types.Text  `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`                           // A tag to describe the cashback user eligible for. Used in v1.
	// A tag to describe the cashback user eligible for. This is used in v2 version of the screen.
	TagItc *ui.IconTextComponent `protobuf:"bytes,4,opt,name=tag_itc,json=tagItc,proto3" json:"tag_itc,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) ProtoMessage() {}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2, 1}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) GetMinValue() *types.Money {
	if x != nil {
		return x.MinValue
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) GetMaxValue() *types.Money {
	if x != nil {
		return x.MaxValue
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) GetTag() *types.Text {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange) GetTagItc() *ui.IconTextComponent {
	if x != nil {
		return x.TagItc
	}
	return nil
}

type AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto.
	SliderRanges  []*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges `protobuf:"bytes,1,rep,name=slider_ranges,json=sliderRanges,proto3" json:"slider_ranges,omitempty"`
	SplitPointers []uint32                                                                                              `protobuf:"varint,2,rep,packed,name=split_pointers,json=splitPointers,proto3" json:"split_pointers,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) ProtoMessage() {
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2, 0, 0}
}

// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto.
func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) GetSliderRanges() []*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges {
	if x != nil {
		return x.SliderRanges
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption) GetSplitPointers() []uint32 {
	if x != nil {
		return x.SplitPointers
	}
	return nil
}

type AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinAmountDisplayVal *types.Text  `protobuf:"bytes,3,opt,name=min_amount_display_val,json=minAmountDisplayVal,proto3" json:"min_amount_display_val,omitempty"`
	MaxAmountDisplayVal *types.Text  `protobuf:"bytes,4,opt,name=max_amount_display_val,json=maxAmountDisplayVal,proto3" json:"max_amount_display_val,omitempty"`
	MinAmount           *types.Money `protobuf:"bytes,1,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	MaxAmount           *types.Money `protobuf:"bytes,2,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	// error text when user enters amount lesser than the range
	MinAmountError *types.Text `protobuf:"bytes,5,opt,name=min_amount_error,json=minAmountError,proto3" json:"min_amount_error,omitempty"`
	MaxAmountError *types.Text `protobuf:"bytes,6,opt,name=max_amount_error,json=maxAmountError,proto3" json:"max_amount_error,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) ProtoMessage() {
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2, 0, 1}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) GetMinAmountDisplayVal() *types.Text {
	if x != nil {
		return x.MinAmountDisplayVal
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) GetMaxAmountDisplayVal() *types.Text {
	if x != nil {
		return x.MaxAmountDisplayVal
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) GetMinAmount() *types.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) GetMaxAmount() *types.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) GetMinAmountError() *types.Text {
	if x != nil {
		return x.MinAmountError
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo) GetMaxAmountError() *types.Text {
	if x != nil {
		return x.MaxAmountError
	}
	return nil
}

type AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BgColour *widget.BackgroundColour `protobuf:"bytes,1,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Benefit1 *ui.IconTextComponent    `protobuf:"bytes,2,opt,name=benefit1,proto3" json:"benefit1,omitempty"`
	Benefit2 *ui.IconTextComponent    `protobuf:"bytes,3,opt,name=benefit2,proto3" json:"benefit2,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) ProtoMessage() {
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2, 0, 2}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) GetBenefit1() *ui.IconTextComponent {
	if x != nil {
		return x.Benefit1
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard) GetBenefit2() *ui.IconTextComponent {
	if x != nil {
		return x.Benefit2
	}
	return nil
}

// Deprecated in support of (`AmountRange`) message
//
// Deprecated: Marked as deprecated in api/types/deeplink_screen_option/salaryprogram/aa_salary_flows_screen_options.proto.
type AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinValue *types.Money `protobuf:"bytes,1,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`
	MaxValue *types.Money `protobuf:"bytes,2,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`
	Tag      *types.Text  `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) Reset() {
	*x = AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) ProtoMessage() {
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{2, 0, 0, 0}
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) GetMinValue() *types.Money {
	if x != nil {
		return x.MinValue
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) GetMaxValue() *types.Money {
	if x != nil {
		return x.MaxValue
	}
	return nil
}

func (x *AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges) GetTag() *types.Text {
	if x != nil {
		return x.Tag
	}
	return nil
}

type OffAppAddFundsInfoComponent_IconsList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *types.Text            `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	AppIcons []*types.VisualElement `protobuf:"bytes,2,rep,name=app_icons,json=appIcons,proto3" json:"app_icons,omitempty"`
}

func (x *OffAppAddFundsInfoComponent_IconsList) Reset() {
	*x = OffAppAddFundsInfoComponent_IconsList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OffAppAddFundsInfoComponent_IconsList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OffAppAddFundsInfoComponent_IconsList) ProtoMessage() {}

func (x *OffAppAddFundsInfoComponent_IconsList) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OffAppAddFundsInfoComponent_IconsList.ProtoReflect.Descriptor instead.
func (*OffAppAddFundsInfoComponent_IconsList) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{4, 0}
}

func (x *OffAppAddFundsInfoComponent_IconsList) GetHeader() *types.Text {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent_IconsList) GetAppIcons() []*types.VisualElement {
	if x != nil {
		return x.AppIcons
	}
	return nil
}

type OffAppAddFundsInfoComponent_CopyText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text     *types.Text              `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	BgColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	CopyIcon *types.VisualElement     `protobuf:"bytes,3,opt,name=copy_icon,json=copyIcon,proto3" json:"copy_icon,omitempty"`
	// e.g UPI_ID, ACCOUNT_NUMBER, IFSC etc.
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *OffAppAddFundsInfoComponent_CopyText) Reset() {
	*x = OffAppAddFundsInfoComponent_CopyText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OffAppAddFundsInfoComponent_CopyText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OffAppAddFundsInfoComponent_CopyText) ProtoMessage() {}

func (x *OffAppAddFundsInfoComponent_CopyText) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OffAppAddFundsInfoComponent_CopyText.ProtoReflect.Descriptor instead.
func (*OffAppAddFundsInfoComponent_CopyText) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{4, 1}
}

func (x *OffAppAddFundsInfoComponent_CopyText) GetText() *types.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent_CopyText) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent_CopyText) GetCopyIcon() *types.VisualElement {
	if x != nil {
		return x.CopyIcon
	}
	return nil
}

func (x *OffAppAddFundsInfoComponent_CopyText) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankIcon         *types.VisualElement `protobuf:"bytes,1,opt,name=bank_icon,json=bankIcon,proto3" json:"bank_icon,omitempty"`
	BankAccountLabel *types.Text          `protobuf:"bytes,2,opt,name=bank_account_label,json=bankAccountLabel,proto3" json:"bank_account_label,omitempty"`
	IsSelected       bool                 `protobuf:"varint,3,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
	Deeplink         *deeplink.Deeplink   `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) Reset() {
	*x = AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) ProtoMessage() {}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent.ProtoReflect.Descriptor instead.
func (*AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{5, 0}
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) GetBankIcon() *types.VisualElement {
	if x != nil {
		return x.BankIcon
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) GetBankAccountLabel() *types.Text {
	if x != nil {
		return x.BankAccountLabel
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title eg - Choose salary account to auto-fetch bank statement
	Title *types.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// list of connected accounts
	ConnectedAccounts []*AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent `protobuf:"bytes,2,rep,name=connected_accounts,json=connectedAccounts,proto3" json:"connected_accounts,omitempty"`
	// cta to connect new account
	ConnectNewAccount *ui.IconTextComponent `protobuf:"bytes,3,opt,name=connect_new_account,json=connectNewAccount,proto3" json:"connect_new_account,omitempty"`
	PartnerLogo       *ui.IconTextComponent `protobuf:"bytes,4,opt,name=partner_logo,json=partnerLogo,proto3" json:"partner_logo,omitempty"`
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) Reset() {
	*x = AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) ProtoMessage() {}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent.ProtoReflect.Descriptor instead.
func (*AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{5, 1}
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) GetConnectedAccounts() []*AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent {
	if x != nil {
		return x.ConnectedAccounts
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) GetConnectNewAccount() *ui.IconTextComponent {
	if x != nil {
		return x.ConnectNewAccount
	}
	return nil
}

func (x *AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent) GetPartnerLogo() *ui.IconTextComponent {
	if x != nil {
		return x.PartnerLogo
	}
	return nil
}

type AASalaryDataPullScreenOptions_UiData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon     *types.VisualElement  `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Title    *types.Text           `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle *types.Text           `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	RetryCta *ui.IconTextComponent `protobuf:"bytes,4,opt,name=retry_cta,json=retryCta,proto3" json:"retry_cta,omitempty"`
	// deeplink should be set to nil, click gesture handled by client
	CloseCta *ui.IconTextComponent `protobuf:"bytes,5,opt,name=close_cta,json=closeCta,proto3" json:"close_cta,omitempty"`
}

func (x *AASalaryDataPullScreenOptions_UiData) Reset() {
	*x = AASalaryDataPullScreenOptions_UiData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AASalaryDataPullScreenOptions_UiData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AASalaryDataPullScreenOptions_UiData) ProtoMessage() {}

func (x *AASalaryDataPullScreenOptions_UiData) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AASalaryDataPullScreenOptions_UiData.ProtoReflect.Descriptor instead.
func (*AASalaryDataPullScreenOptions_UiData) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{6, 0}
}

func (x *AASalaryDataPullScreenOptions_UiData) GetIcon() *types.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions_UiData) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions_UiData) GetSubtitle() *types.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions_UiData) GetRetryCta() *ui.IconTextComponent {
	if x != nil {
		return x.RetryCta
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions_UiData) GetCloseCta() *ui.IconTextComponent {
	if x != nil {
		return x.CloseCta
	}
	return nil
}

type AASalaryDataPullScreenOptions_AccountDataPullParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// redirects to terminal screen in case of network errors and no api calls can be made
	NetworkErrorDeeplink *deeplink.Deeplink `protobuf:"bytes,1,opt,name=network_error_deeplink,json=networkErrorDeeplink,proto3" json:"network_error_deeplink,omitempty"`
	// number of retried allowed in case of network error
	MaxRetryCountAllowed int32    `protobuf:"varint,2,opt,name=max_retry_count_allowed,json=maxRetryCountAllowed,proto3" json:"max_retry_count_allowed,omitempty"`
	ConsentHandles       []string `protobuf:"bytes,3,rep,name=consent_handles,json=consentHandles,proto3" json:"consent_handles,omitempty"`
}

func (x *AASalaryDataPullScreenOptions_AccountDataPullParams) Reset() {
	*x = AASalaryDataPullScreenOptions_AccountDataPullParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AASalaryDataPullScreenOptions_AccountDataPullParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AASalaryDataPullScreenOptions_AccountDataPullParams) ProtoMessage() {}

func (x *AASalaryDataPullScreenOptions_AccountDataPullParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AASalaryDataPullScreenOptions_AccountDataPullParams.ProtoReflect.Descriptor instead.
func (*AASalaryDataPullScreenOptions_AccountDataPullParams) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP(), []int{6, 1}
}

func (x *AASalaryDataPullScreenOptions_AccountDataPullParams) GetNetworkErrorDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.NetworkErrorDeeplink
	}
	return nil
}

func (x *AASalaryDataPullScreenOptions_AccountDataPullParams) GetMaxRetryCountAllowed() int32 {
	if x != nil {
		return x.MaxRetryCountAllowed
	}
	return 0
}

func (x *AASalaryDataPullScreenOptions_AccountDataPullParams) GetConsentHandles() []string {
	if x != nil {
		return x.ConsentHandles
	}
	return nil
}

var File_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto protoreflect.FileDescriptor

var file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDesc = []byte{
	0x0a, 0x53, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x2f, 0x61, 0x61, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x73,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2a, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2f, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x6f,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f,
	0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6f, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x01, 0x0a, 0x1c, 0x41, 0x61,
	0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x72, 0x54, 0x65, 0x78, 0x74, 0x22, 0xe1, 0x03, 0x0a, 0x29, 0x41, 0x61, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1f, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x12, 0x40, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x61, 0x73, 0x65, 0x64, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x63,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x43, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x21,
	0x61, 0x61, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x61, 0x61, 0x53, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x65, 0x74, 0x74, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x68, 0x6f,
	0x77, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x74, 0x74, 0x69, 0x22, 0xbf, 0x17, 0x0a, 0x34, 0x41, 0x61,
	0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f,
	0x77, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x53, 0x65, 0x74, 0x75, 0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x11,
	0x74, 0x6f, 0x70, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x74,
	0x6f, 0x70, 0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x21,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x46, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x74, 0x61, 0x12, 0xa8, 0x01, 0x0a, 0x15, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x65, 0x74, 0x75, 0x70, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x13, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x4c, 0x0a, 0x17, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x76,
	0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x15, 0x62, 0x61, 0x63,
	0x6b, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x53, 0x0a, 0x17, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x15, 0x69, 0x6e, 0x66, 0x6f, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6e, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x28, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xc4, 0x0f, 0x0a, 0x13, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x12, 0x72, 0x75, 0x70, 0x65, 0x65, 0x73, 0x5f, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x10, 0x72,
	0x75, 0x70, 0x65, 0x65, 0x73, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x31, 0x0a, 0x09, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x64, 0x69, 0x74, 0x49, 0x63,
	0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x33, 0x0a, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xa7, 0x01, 0x0a, 0x0d, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x81, 0x01,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x65,
	0x74, 0x75, 0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x72, 0x64, 0x2e, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0c, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0xb1, 0x01, 0x0a, 0x11, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x75, 0x62,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x84, 0x01, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x65, 0x74, 0x75,
	0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x72, 0x64, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x75, 0x62, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x75, 0x62, 0x43,
	0x61, 0x72, 0x64, 0x12, 0xa0, 0x01, 0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x7f, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x65, 0x74, 0x75, 0x70, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x2e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x91, 0x01, 0x0a, 0x0d, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x6c,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x65,
	0x74, 0x75, 0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x12, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x11, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3a, 0x0a, 0x19, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x1a, 0xfa, 0x02, 0x0a, 0x0c, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0xb8, 0x01, 0x0a, 0x0d, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x8e, 0x01, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x65, 0x74, 0x75,
	0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x72, 0x64, 0x2e, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0c, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x87, 0x01, 0x0a, 0x0c, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x29, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x03,
	0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x03, 0x74, 0x61, 0x67, 0x3a, 0x02, 0x18, 0x01, 0x1a,
	0xd8, 0x02, 0x0a, 0x0a, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40,
	0x0a, 0x16, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x13, 0x6d, 0x69, 0x6e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x56, 0x61, 0x6c,
	0x12, 0x40, 0x0a, 0x16, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x13, 0x6d,
	0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x56,
	0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2b, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x10,
	0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x0e, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0xc3, 0x01, 0x0a, 0x0f, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x75, 0x62, 0x43, 0x61, 0x72, 0x64, 0x12, 0x3e,
	0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x37,
	0x0a, 0x08, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x31, 0x12, 0x37, 0x0a, 0x08, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x32,
	0x1a, 0xb8, 0x01, 0x0a, 0x0b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x29, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x29, 0x0a, 0x09, 0x6d,
	0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x6d, 0x61,
	0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x34, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x74, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x74, 0x63, 0x22, 0xa2, 0x04, 0x0a, 0x26,
	0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x56, 0x69, 0x61, 0x4f, 0x66, 0x66, 0x41, 0x70,
	0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x2f, 0x0a, 0x08, 0x74, 0x6f, 0x70, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x74, 0x6f, 0x70, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x70, 0x0a, 0x0f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x4f, 0x66, 0x66, 0x41, 0x70,
	0x70, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x69, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x46, 0x0a, 0x14, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6e,
	0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x62, 0x61, 0x63, 0x6b,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x38,
	0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08,
	0x69, 0x6e, 0x66, 0x6f, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x03, 0x63,
	0x74, 0x61, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x22, 0xc5, 0x05, 0x0a, 0x1b, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x41, 0x64, 0x64, 0x46, 0x75,
	0x6e, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x39, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x72,
	0x0a, 0x0a, 0x69, 0x63, 0x6f, 0x6e, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x51, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e,
	0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x6f, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x2e, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x43,
	0x6f, 0x70, 0x79, 0x54, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x1a, 0x63, 0x0a, 0x09, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x23, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08,
	0x61, 0x70, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x1a, 0xb2, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x70,
	0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x63, 0x6f, 0x70, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xf5, 0x09, 0x0a, 0x21, 0x41, 0x61, 0x53,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x66, 0x46, 0x75, 0x6e,
	0x64, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x08, 0x74, 0x6f, 0x70, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x07, 0x74, 0x6f, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x08,
	0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0xaa, 0x01, 0x0a, 0x1c, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x68, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x61,
	0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x66, 0x46, 0x75,
	0x6e, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x1a, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x62, 0x6f, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x6f, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x37, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x63,
	0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x43, 0x74, 0x61, 0x1a, 0xe6, 0x01, 0x0a,
	0x1c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x0a,
	0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x39, 0x0a, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x1a, 0xe8, 0x02, 0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x6a, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x2e, 0x41, 0x61, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4f, 0x66, 0x46, 0x75, 0x6e, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x11, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x12, 0x4b, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x6e,
	0x65, 0x77, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e, 0x65, 0x77, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x6f,
	0x22, 0xa9, 0x09, 0x0a, 0x1d, 0x41, 0x41, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74,
	0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x0f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x76, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41, 0x41, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x55, 0x69, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0d, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x70, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x2e, 0x41, 0x41, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c,
	0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x55,
	0x69, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x60, 0x0a, 0x0f, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x70, 0x75, 0x6c,
	0x6c, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x10, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x0f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x12, 0x77, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x5f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x41,
	0x41, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0xf2, 0x01, 0x0a, 0x06, 0x55,
	0x69, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x65, 0x74,
	0x72, 0x79, 0x43, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x63,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x74, 0x61, 0x1a,
	0xca, 0x01, 0x0a, 0x15, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50,
	0x75, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x51, 0x0a, 0x16, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x14, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x35, 0x0a, 0x17,
	0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x6d,
	0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x42, 0x90, 0x01, 0x0a,
	0x45, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x01, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescOnce sync.Once
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescData = file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDesc
)

func file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescGZIP() []byte {
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescOnce.Do(func() {
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescData)
	})
	return file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDescData
}

var file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_goTypes = []interface{}{
	(*AaSalaryLandingScreenOptions)(nil),                                                                       // 0: types.deeplink_screen_option.salaryprogram.AaSalaryLandingScreenOptions
	(*AaSalaryProgramFlowsTerminalScreenOptions)(nil),                                                          // 1: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions)(nil),                                               // 2: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions
	(*AddFundsViaOffAppTransferScreenOptions)(nil),                                                             // 3: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions
	(*OffAppAddFundsInfoComponent)(nil),                                                                        // 4: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent
	(*AaSalarySourceOfFundScreenOptions)(nil),                                                                  // 5: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions
	(*AASalaryDataPullScreenOptions)(nil),                                                                      // 6: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard)(nil),                           // 7: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange)(nil),                                   // 8: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountRange
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption)(nil),              // 9: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo)(nil),                // 10: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard)(nil),           // 11: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.BenefitsSubCard
	(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges)(nil), // 12: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption.SliderRanges
	(*OffAppAddFundsInfoComponent_IconsList)(nil),                                                              // 13: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.IconsList
	(*OffAppAddFundsInfoComponent_CopyText)(nil),                                                               // 14: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.CopyText
	(*AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent)(nil),                                     // 15: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountRowComponent
	(*AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent)(nil),                                       // 16: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountsComponent
	(*AASalaryDataPullScreenOptions_UiData)(nil),                                                               // 17: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData
	(*AASalaryDataPullScreenOptions_AccountDataPullParams)(nil),                                                // 18: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.AccountDataPullParams
	(*deeplink_screen_option.ScreenOptionHeader)(nil),                                                          // 19: types.deeplink_screen_option.ScreenOptionHeader
	(*types.Text)(nil),                            // 20: types.Text
	(*types.VisualElement)(nil),                   // 21: types.VisualElement
	(*deeplink.Cta)(nil),                          // 22: frontend.deeplink.Cta
	(*widget.BackgroundColour)(nil),               // 23: types.ui.widget.BackgroundColour
	(*ui.IconTextComponent)(nil),                  // 24: types.ui.IconTextComponent
	(types.Version)(0),                            // 25: types.Version
	(*widget.CheckboxItem)(nil),                   // 26: types.ui.widget.CheckboxItem
	(*connected_account.ComingSoonComponent)(nil), // 27: types.connected_account.ComingSoonComponent
	(*polling.DocumentPollingOption)(nil),         // 28: frontend.document_upload.polling.DocumentPollingOption
	(*deeplink.Deeplink)(nil),                     // 29: frontend.deeplink.Deeplink
	(*types.Money)(nil),                           // 30: types.Money
}
var file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_depIdxs = []int32{
	19, // 0: types.deeplink_screen_option.salaryprogram.AaSalaryLandingScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	19, // 1: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	20, // 2: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions.title:type_name -> types.Text
	20, // 3: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions.desc:type_name -> types.Text
	21, // 4: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions.status_based_icon:type_name -> types.VisualElement
	22, // 5: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions.proceed_cta:type_name -> frontend.deeplink.Cta
	23, // 6: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsTerminalScreenOptions.bg_color:type_name -> types.ui.widget.BackgroundColour
	19, // 7: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	21, // 8: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.top_cashback_icon:type_name -> types.VisualElement
	20, // 9: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.title:type_name -> types.Text
	22, // 10: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.transfer_amount_cta:type_name -> frontend.deeplink.Cta
	7,  // 11: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.amount_selection_card:type_name -> types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard
	21, // 12: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.back_navigation_element:type_name -> types.VisualElement
	24, // 13: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.info_navigation_element:type_name -> types.ui.IconTextComponent
	23, // 14: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.bg_color:type_name -> types.ui.widget.BackgroundColour
	24, // 15: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.powered_by:type_name -> types.ui.IconTextComponent
	25, // 16: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.version:type_name -> types.Version
	19, // 17: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	21, // 18: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.top_icon:type_name -> types.VisualElement
	20, // 19: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.title:type_name -> types.Text
	4,  // 20: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.info_components:type_name -> types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent
	21, // 21: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.back_navigation_icon:type_name -> types.VisualElement
	24, // 22: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.info_icon:type_name -> types.ui.IconTextComponent
	22, // 23: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.cta:type_name -> frontend.deeplink.Cta
	23, // 24: types.deeplink_screen_option.salaryprogram.AddFundsViaOffAppTransferScreenOptions.bg_color:type_name -> types.ui.widget.BackgroundColour
	21, // 25: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.serial_number:type_name -> types.VisualElement
	20, // 26: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.title:type_name -> types.Text
	13, // 27: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.icons_list:type_name -> types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.IconsList
	14, // 28: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.copy_text:type_name -> types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.CopyText
	23, // 29: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.bg_colour:type_name -> types.ui.widget.BackgroundColour
	19, // 30: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	21, // 31: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.top_icon:type_name -> types.VisualElement
	20, // 32: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.title:type_name -> types.Text
	20, // 33: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.subtitle:type_name -> types.Text
	24, // 34: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.security_label:type_name -> types.ui.IconTextComponent
	16, // 35: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.connected_accounts_component:type_name -> types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountsComponent
	26, // 36: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.terms_info:type_name -> types.ui.widget.CheckboxItem
	27, // 37: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.bottom_view:type_name -> types.connected_account.ComingSoonComponent
	22, // 38: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.proceed_cta:type_name -> frontend.deeplink.Cta
	19, // 39: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	23, // 40: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.screen_bg_color:type_name -> types.ui.widget.BackgroundColour
	17, // 41: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.progressState:type_name -> types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData
	17, // 42: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.errorState:type_name -> types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData
	28, // 43: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.polling_options:type_name -> frontend.document_upload.polling.DocumentPollingOption
	29, // 44: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.success_deeplink:type_name -> frontend.deeplink.Deeplink
	18, // 45: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.params:type_name -> types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.AccountDataPullParams
	20, // 46: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.title:type_name -> types.Text
	20, // 47: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.rupees_symbol_text:type_name -> types.Text
	21, // 48: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.edit_icon:type_name -> types.VisualElement
	23, // 49: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.bg_color:type_name -> types.ui.widget.BackgroundColour
	30, // 50: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.initial_amount:type_name -> types.Money
	9,  // 51: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.slider_option:type_name -> types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption
	11, // 52: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.benefits_sub_card:type_name -> types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.BenefitsSubCard
	10, // 53: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.amount_info:type_name -> types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo
	8,  // 54: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.amount_ranges:type_name -> types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountRange
	20, // 55: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.amount_suggestions:type_name -> types.Text
	30, // 56: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountRange.min_value:type_name -> types.Money
	30, // 57: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountRange.max_value:type_name -> types.Money
	20, // 58: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountRange.tag:type_name -> types.Text
	24, // 59: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountRange.tag_itc:type_name -> types.ui.IconTextComponent
	12, // 60: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption.slider_ranges:type_name -> types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption.SliderRanges
	20, // 61: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo.min_amount_display_val:type_name -> types.Text
	20, // 62: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo.max_amount_display_val:type_name -> types.Text
	30, // 63: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo.min_amount:type_name -> types.Money
	30, // 64: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo.max_amount:type_name -> types.Money
	20, // 65: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo.min_amount_error:type_name -> types.Text
	20, // 66: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.AmountInfo.max_amount_error:type_name -> types.Text
	23, // 67: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.BenefitsSubCard.bg_colour:type_name -> types.ui.widget.BackgroundColour
	24, // 68: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.BenefitsSubCard.benefit1:type_name -> types.ui.IconTextComponent
	24, // 69: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.BenefitsSubCard.benefit2:type_name -> types.ui.IconTextComponent
	30, // 70: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption.SliderRanges.min_value:type_name -> types.Money
	30, // 71: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption.SliderRanges.max_value:type_name -> types.Money
	20, // 72: types.deeplink_screen_option.salaryprogram.AaSalaryProgramFlowsAmountTransferSetupScreenOptions.AmountSelectionCard.SliderOption.SliderRanges.tag:type_name -> types.Text
	20, // 73: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.IconsList.header:type_name -> types.Text
	21, // 74: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.IconsList.app_icons:type_name -> types.VisualElement
	20, // 75: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.CopyText.text:type_name -> types.Text
	23, // 76: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.CopyText.bg_colour:type_name -> types.ui.widget.BackgroundColour
	21, // 77: types.deeplink_screen_option.salaryprogram.OffAppAddFundsInfoComponent.CopyText.copy_icon:type_name -> types.VisualElement
	21, // 78: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountRowComponent.bank_icon:type_name -> types.VisualElement
	20, // 79: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountRowComponent.bank_account_label:type_name -> types.Text
	29, // 80: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountRowComponent.deeplink:type_name -> frontend.deeplink.Deeplink
	20, // 81: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountsComponent.title:type_name -> types.Text
	15, // 82: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountsComponent.connected_accounts:type_name -> types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountRowComponent
	24, // 83: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountsComponent.connect_new_account:type_name -> types.ui.IconTextComponent
	24, // 84: types.deeplink_screen_option.salaryprogram.AaSalarySourceOfFundScreenOptions.ConnectedAccountsComponent.partner_logo:type_name -> types.ui.IconTextComponent
	21, // 85: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData.icon:type_name -> types.VisualElement
	20, // 86: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData.title:type_name -> types.Text
	20, // 87: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData.subtitle:type_name -> types.Text
	24, // 88: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData.retry_cta:type_name -> types.ui.IconTextComponent
	24, // 89: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.UiData.close_cta:type_name -> types.ui.IconTextComponent
	29, // 90: types.deeplink_screen_option.salaryprogram.AASalaryDataPullScreenOptions.AccountDataPullParams.network_error_deeplink:type_name -> frontend.deeplink.Deeplink
	91, // [91:91] is the sub-list for method output_type
	91, // [91:91] is the sub-list for method input_type
	91, // [91:91] is the sub-list for extension type_name
	91, // [91:91] is the sub-list for extension extendee
	0,  // [0:91] is the sub-list for field type_name
}

func init() {
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_init()
}
func file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_init() {
	if File_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryLandingScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsTerminalScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddFundsViaOffAppTransferScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OffAppAddFundsInfoComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalarySourceOfFundScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AASalaryDataPullScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_AmountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_BenefitsSubCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramFlowsAmountTransferSetupScreenOptions_AmountSelectionCard_SliderOption_SliderRanges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OffAppAddFundsInfoComponent_IconsList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OffAppAddFundsInfoComponent_CopyText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalarySourceOfFundScreenOptions_ConnectedAccountRowComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalarySourceOfFundScreenOptions_ConnectedAccountsComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AASalaryDataPullScreenOptions_UiData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AASalaryDataPullScreenOptions_AccountDataPullParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*OffAppAddFundsInfoComponent_IconsList_)(nil),
		(*OffAppAddFundsInfoComponent_CopyText_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_goTypes,
		DependencyIndexes: file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_depIdxs,
		MessageInfos:      file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_msgTypes,
	}.Build()
	File_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto = out.File
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_rawDesc = nil
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_goTypes = nil
	file_api_types_deeplink_screen_option_salaryprogram_aa_salary_flows_screen_options_proto_depIdxs = nil
}
