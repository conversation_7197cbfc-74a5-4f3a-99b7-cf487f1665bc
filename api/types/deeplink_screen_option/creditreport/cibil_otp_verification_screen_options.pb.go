// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/deeplink_screen_option/creditreport/cibil_otp_verification_screen_options.proto

package creditreport

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/types"
	deeplink_screen_option "github.com/epifi/gamma/api/types/deeplink_screen_option"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// screen options for CIBIL_OTP_VERIFICATION
// figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10481-46100&mode=design&t=lQDePsjB2iwNFvf1-0
type CibilOtpVerificationScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header         *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	BackAction     *deeplink.BackAction                       `protobuf:"bytes,2,opt,name=back_action,json=backAction,proto3" json:"back_action,omitempty"`
	Title          *types.Text                                `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle       *types.Text                                `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	OtpFailureHint *types.Text                                `protobuf:"bytes,11,opt,name=otp_failure_hint,json=otpFailureHint,proto3" json:"otp_failure_hint,omitempty"`
	// input other than otp that are possible in the scree
	//
	// Types that are assignable to InputAlternate:
	//
	//	*CibilOtpVerificationScreenOptions_ResendOtpInputOption
	//	*CibilOtpVerificationScreenOptions_ChangeNumberInputOption
	InputAlternate isCibilOtpVerificationScreenOptions_InputAlternate `protobuf_oneof:"inputAlternate"`
	BottomIcon     *types.VisualElement                               `protobuf:"bytes,7,opt,name=bottom_icon,json=bottomIcon,proto3" json:"bottom_icon,omitempty"`
	// client request id identifies the workflow for which the authentication is taking place
	ClientRequestId string `protobuf:"bytes,8,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// challenge id uniquely identifies the question for which the answer is submitted
	ChallengeId string `protobuf:"bytes,9,opt,name=challenge_id,json=challengeId,proto3" json:"challenge_id,omitempty"`
	// string form of vendorgateway.Vendor. Doing this since client does not have access to vendorgateway package proto.
	Vendor string `protobuf:"bytes,10,opt,name=vendor,proto3" json:"vendor,omitempty"`
}

func (x *CibilOtpVerificationScreenOptions) Reset() {
	*x = CibilOtpVerificationScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CibilOtpVerificationScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CibilOtpVerificationScreenOptions) ProtoMessage() {}

func (x *CibilOtpVerificationScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CibilOtpVerificationScreenOptions.ProtoReflect.Descriptor instead.
func (*CibilOtpVerificationScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *CibilOtpVerificationScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetBackAction() *deeplink.BackAction {
	if x != nil {
		return x.BackAction
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetSubtitle() *types.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetOtpFailureHint() *types.Text {
	if x != nil {
		return x.OtpFailureHint
	}
	return nil
}

func (m *CibilOtpVerificationScreenOptions) GetInputAlternate() isCibilOtpVerificationScreenOptions_InputAlternate {
	if m != nil {
		return m.InputAlternate
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetResendOtpInputOption() *ResendOtpInputOption {
	if x, ok := x.GetInputAlternate().(*CibilOtpVerificationScreenOptions_ResendOtpInputOption); ok {
		return x.ResendOtpInputOption
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetChangeNumberInputOption() *ChangeNumberInputOption {
	if x, ok := x.GetInputAlternate().(*CibilOtpVerificationScreenOptions_ChangeNumberInputOption); ok {
		return x.ChangeNumberInputOption
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetBottomIcon() *types.VisualElement {
	if x != nil {
		return x.BottomIcon
	}
	return nil
}

func (x *CibilOtpVerificationScreenOptions) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *CibilOtpVerificationScreenOptions) GetChallengeId() string {
	if x != nil {
		return x.ChallengeId
	}
	return ""
}

func (x *CibilOtpVerificationScreenOptions) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

type isCibilOtpVerificationScreenOptions_InputAlternate interface {
	isCibilOtpVerificationScreenOptions_InputAlternate()
}

type CibilOtpVerificationScreenOptions_ResendOtpInputOption struct {
	// option to resend otp
	ResendOtpInputOption *ResendOtpInputOption `protobuf:"bytes,5,opt,name=resend_otp_input_option,json=resendOtpInputOption,proto3,oneof"`
}

type CibilOtpVerificationScreenOptions_ChangeNumberInputOption struct {
	// option to change mobile number
	ChangeNumberInputOption *ChangeNumberInputOption `protobuf:"bytes,6,opt,name=change_number_input_option,json=changeNumberInputOption,proto3,oneof"`
}

func (*CibilOtpVerificationScreenOptions_ResendOtpInputOption) isCibilOtpVerificationScreenOptions_InputAlternate() {
}

func (*CibilOtpVerificationScreenOptions_ChangeNumberInputOption) isCibilOtpVerificationScreenOptions_InputAlternate() {
}

type ResendOtpInputOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first text in the list will contain "Didn't receive OTP?"
	// second text will contain "Resend in", the time will be decided by client based on countdown_timer.
	// Once timer becomes 0, "Resend in" turns into clickable "resend".
	ResendOtpText []*types.Text `protobuf:"bytes,1,rep,name=resend_otp_text,json=resendOtpText,proto3" json:"resend_otp_text,omitempty"`
	// time after which resend will be enabled
	CountdownFromInSeconds int32 `protobuf:"varint,2,opt,name=countdown_from_in_seconds,json=countdownFromInSeconds,proto3" json:"countdown_from_in_seconds,omitempty"`
}

func (x *ResendOtpInputOption) Reset() {
	*x = ResendOtpInputOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResendOtpInputOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResendOtpInputOption) ProtoMessage() {}

func (x *ResendOtpInputOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResendOtpInputOption.ProtoReflect.Descriptor instead.
func (*ResendOtpInputOption) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *ResendOtpInputOption) GetResendOtpText() []*types.Text {
	if x != nil {
		return x.ResendOtpText
	}
	return nil
}

func (x *ResendOtpInputOption) GetCountdownFromInSeconds() int32 {
	if x != nil {
		return x.CountdownFromInSeconds
	}
	return 0
}

type ChangeNumberInputOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first text in the list will contain "Not receiving OTP?"
	// second text will contain "Change mobile number" which is clickable
	ChangeNumberText []*types.Text `protobuf:"bytes,1,rep,name=change_number_text,json=changeNumberText,proto3" json:"change_number_text,omitempty"`
}

func (x *ChangeNumberInputOption) Reset() {
	*x = ChangeNumberInputOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeNumberInputOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeNumberInputOption) ProtoMessage() {}

func (x *ChangeNumberInputOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeNumberInputOption.ProtoReflect.Descriptor instead.
func (*ChangeNumberInputOption) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *ChangeNumberInputOption) GetChangeNumberText() []*types.Text {
	if x != nil {
		return x.ChangeNumberText
	}
	return nil
}

var File_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto protoreflect.FileDescriptor

var file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDesc = []byte{
	0x0a, 0x59, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2f,
	0x63, 0x69, 0x62, 0x69, 0x6c, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xde, 0x05, 0x0a, 0x21, 0x43, 0x69, 0x62, 0x69, 0x6c, 0x4f, 0x74, 0x70, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a,
	0x10, 0x6f, 0x74, 0x70, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x68, 0x69, 0x6e,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x6f, 0x74, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x48, 0x69, 0x6e, 0x74, 0x12, 0x78, 0x0a, 0x17, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f,
	0x74, 0x70, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x74, 0x70, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x14, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64,
	0x4f, 0x74, 0x70, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x81,
	0x01, 0x0a, 0x1a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x17, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x62,
	0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x42, 0x10, 0x0a, 0x0e, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x74, 0x70,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x74, 0x70, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x39, 0x0a, 0x19, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x16, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x46, 0x72,
	0x6f, 0x6d, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x54, 0x0a, 0x17, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x10, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x65, 0x78,
	0x74, 0x42, 0x8e, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescOnce sync.Once
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescData = file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDesc
)

func file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescGZIP() []byte {
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescOnce.Do(func() {
		file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescData)
	})
	return file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDescData
}

var file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_goTypes = []interface{}{
	(*CibilOtpVerificationScreenOptions)(nil),         // 0: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions
	(*ResendOtpInputOption)(nil),                      // 1: types.deeplink_screen_option.creditreport.ResendOtpInputOption
	(*ChangeNumberInputOption)(nil),                   // 2: types.deeplink_screen_option.creditreport.ChangeNumberInputOption
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 3: types.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.BackAction)(nil),                       // 4: frontend.deeplink.BackAction
	(*types.Text)(nil),                                // 5: types.Text
	(*types.VisualElement)(nil),                       // 6: types.VisualElement
}
var file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_depIdxs = []int32{
	3,  // 0: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	4,  // 1: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.back_action:type_name -> frontend.deeplink.BackAction
	5,  // 2: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.title:type_name -> types.Text
	5,  // 3: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.subtitle:type_name -> types.Text
	5,  // 4: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.otp_failure_hint:type_name -> types.Text
	1,  // 5: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.resend_otp_input_option:type_name -> types.deeplink_screen_option.creditreport.ResendOtpInputOption
	2,  // 6: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.change_number_input_option:type_name -> types.deeplink_screen_option.creditreport.ChangeNumberInputOption
	6,  // 7: types.deeplink_screen_option.creditreport.CibilOtpVerificationScreenOptions.bottom_icon:type_name -> types.VisualElement
	5,  // 8: types.deeplink_screen_option.creditreport.ResendOtpInputOption.resend_otp_text:type_name -> types.Text
	5,  // 9: types.deeplink_screen_option.creditreport.ChangeNumberInputOption.change_number_text:type_name -> types.Text
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() {
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_init()
}
func file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_init() {
	if File_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CibilOtpVerificationScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResendOtpInputOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeNumberInputOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CibilOtpVerificationScreenOptions_ResendOtpInputOption)(nil),
		(*CibilOtpVerificationScreenOptions_ChangeNumberInputOption)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_goTypes,
		DependencyIndexes: file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_depIdxs,
		MessageInfos:      file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_msgTypes,
	}.Build()
	File_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto = out.File
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_rawDesc = nil
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_goTypes = nil
	file_api_types_deeplink_screen_option_creditreport_cibil_otp_verification_screen_options_proto_depIdxs = nil
}
