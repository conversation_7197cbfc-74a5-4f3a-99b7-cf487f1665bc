//nolint:gocritic, dupl
package comms

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
)

var SupportPhone string

// Interface which implements all sms types we will have
// Adding a new version for a template or adding a new template is as simple as adding new methods here
// service level code does not need to change for any new template additions
type ISmsOption interface {
	GetType() SmsType
	GetActualMessage(template string) (string, error)
	GetTemplateVersion() TemplateVersion
}

func (o *SmsOption_OnboardingOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.OnboardingOtpSmsOption.GetSmsType()
}

func (o *SmsOption_OnboardingOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.OnboardingOtpSmsOption.GetOption().(type) {
	case *OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1:
		return o.OnboardingOtpSmsOption.GetOnboardingOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint: dupl
func (o *SmsOption_OnboardingOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("no onboarding sms option is nil")
	}
	switch o.OnboardingOtpSmsOption.GetOption().(type) {
	case *OnboardingOtpSmsOption_OnboardingOtpSmsOptionV1:
		otp := o.OnboardingOtpSmsOption.GetOnboardingOtpSmsOptionV1().GetOtp()
		androidClientSignature := o.OnboardingOtpSmsOption.GetOnboardingOtpSmsOptionV1().GetAndroidClientSignature()
		if otp == "" || androidClientSignature == "" {
			return "", errors.New("missing template parameters")
		}
		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		msg = ReplaceWithTrim(msg, "{#android_client_signature#}", androidClientSignature, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for onboarding template")
}

func (o *SmsOption_WaitlistOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.WaitlistOtpSmsOption.GetSmsType()
}

func (o *SmsOption_WaitlistOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist sms option is nil")
	}
	switch o.WaitlistOtpSmsOption.GetOption().(type) {
	case *WaitlistOtpSmsOption_WaitlistOtpSmsOptionV1:
		otp := o.WaitlistOtpSmsOption.GetWaitlistOtpSmsOptionV1().GetOtp()
		if otp == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist otp template")
}

func (o *SmsOption_WaitlistOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistOtpSmsOption.GetOption().(type) {
	case *WaitlistOtpSmsOption_WaitlistOtpSmsOptionV1:
		return o.WaitlistOtpSmsOption.GetWaitlistOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardBlockSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardBlockSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardBlockSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card block sms option is nil")
	}
	switch o.DebitCardBlockSmsOption.GetOption().(type) {
	case *DebitCardBlockSmsOption_DebitCardBlockSmsOptionV1:
		cardLastFour := o.DebitCardBlockSmsOption.GetDebitCardBlockSmsOptionV1().GetCardLastFourDigits()
		if cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card block template")
}

func (o *SmsOption_DebitCardBlockSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardBlockSmsOption.GetOption().(type) {
	case *DebitCardBlockSmsOption_DebitCardBlockSmsOptionV1:
		return o.DebitCardBlockSmsOption.GetDebitCardBlockSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardNewIssuanceSoftPinSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardNewIssuanceSoftPinSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardNewIssuanceSoftPinSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card new issuance sms option is nil")
	}
	switch o.DebitCardNewIssuanceSoftPinSmsOption.GetOption().(type) {
	case *DebitCardNewCardIssuanceSoftPin_DebitCardNewCardIssuanceSoftPinSmsOptionV1:
		firstName := o.DebitCardNewIssuanceSoftPinSmsOption.GetDebitCardNewCardIssuanceSoftPinSmsOptionV1().GetName().GetFirstName()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card new issuance template")
}

func (o *SmsOption_DebitCardNewIssuanceSoftPinSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardNewIssuanceSoftPinSmsOption.GetOption().(type) {
	case *DebitCardNewCardIssuanceSoftPin_DebitCardNewCardIssuanceSoftPinSmsOptionV1:
		return o.DebitCardNewIssuanceSoftPinSmsOption.GetDebitCardNewCardIssuanceSoftPinSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardNewIssuanceSoftPinReSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardNewIssuanceSoftPinReSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardNewIssuanceSoftPinReSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card new issuance re sms option is nil")
	}
	switch o.DebitCardNewIssuanceSoftPinReSmsOption.GetOption().(type) {
	case *DebitCardNewCardIssuanceSoftPinRe_DebitCardNewCardIssuanceSoftPinReSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for debit card new issuance re template")
}

func (o *SmsOption_DebitCardNewIssuanceSoftPinReSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardNewIssuanceSoftPinReSmsOption.GetOption().(type) {
	case *DebitCardNewCardIssuanceSoftPinRe_DebitCardNewCardIssuanceSoftPinReSmsOptionV1:
		return o.DebitCardNewIssuanceSoftPinReSmsOption.GetDebitCardNewCardIssuanceSoftPinReSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardOnSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardOnSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardOnSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card on sms option is nil")
	}
	switch o.DebitCardOnSmsOption.GetOption().(type) {
	case *DebitCardOnSmsOption_DebitCardOnSmsOptionV1:
		firstName := o.DebitCardOnSmsOption.GetDebitCardOnSmsOptionV1().GetName().GetFirstName()
		cardLastFour := o.DebitCardOnSmsOption.GetDebitCardOnSmsOptionV1().GetCardLastFourDigits()
		if firstName == "" || cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card on template")
}

func (o *SmsOption_DebitCardOnSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardOnSmsOption.GetOption().(type) {
	case *DebitCardOnSmsOption_DebitCardOnSmsOptionV1:
		return o.DebitCardOnSmsOption.GetDebitCardOnSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardOffSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card off sms option is nil")
	}
	switch o.DebitCardOffSmsOption.GetOption().(type) {
	case *DebitCardOffSmsOption_DebitCardOffSmsOptionV1:
		cardLastFour := o.DebitCardOffSmsOption.GetDebitCardOffSmsOptionV1().GetCardLastFourDigits()
		if cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card off template")
}

func (o *SmsOption_DebitCardOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardOffSmsOption.GetOption().(type) {
	case *DebitCardOffSmsOption_DebitCardOffSmsOptionV1:
		return o.DebitCardOffSmsOption.GetDebitCardOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardOnOffFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardOnOffFailureSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardOnOffFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card on off failure sms option is nil")
	}
	switch o.DebitCardOnOffFailureSmsOption.GetOption().(type) {
	case *DebitCardOnOffFailureSmsOption_DebitCardOnOffFailureSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for debit card on off failure template")
}

func (o *SmsOption_DebitCardOnOffFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardOnOffFailureSmsOption.GetOption().(type) {
	case *DebitCardOnOffFailureSmsOption_DebitCardOnOffFailureSmsOptionV1:
		return o.DebitCardOnOffFailureSmsOption.GetDebitCardOnOffFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardInternationalOnSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardInternationalOnSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardInternationalOnSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card int on sms option is nil")
	}
	switch o.DebitCardInternationalOnSmsOption.GetOption().(type) {
	case *DebitCardInternationalOnSmsOption_DebitCardInternationalOnSmsOptionV1:
		firstName := o.DebitCardInternationalOnSmsOption.GetDebitCardInternationalOnSmsOptionV1().GetName().GetFirstName()
		cardLastFour := o.DebitCardInternationalOnSmsOption.GetDebitCardInternationalOnSmsOptionV1().GetCardLastFourDigits()
		if firstName == "" || cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card int on failure template")
}

func (o *SmsOption_DebitCardInternationalOnSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardInternationalOnSmsOption.GetOption().(type) {
	case *DebitCardInternationalOnSmsOption_DebitCardInternationalOnSmsOptionV1:
		return o.DebitCardInternationalOnSmsOption.GetDebitCardInternationalOnSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardInternationalOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardInternationalOffSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardInternationalOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card int off sms option is nil")
	}
	switch o.DebitCardInternationalOffSmsOption.GetOption().(type) {
	case *DebitCardInternationalOffSmsOption_DebitCardInternationalOffSmsOptionV1:
		cardLastFour := o.DebitCardInternationalOffSmsOption.GetDebitCardInternationalOffSmsOptionV1().GetCardLastFourDigits()
		if cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for int debit card off template")
}

func (o *SmsOption_DebitCardInternationalOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardInternationalOffSmsOption.GetOption().(type) {
	case *DebitCardInternationalOffSmsOption_DebitCardInternationalOffSmsOptionV1:
		return o.DebitCardInternationalOffSmsOption.GetDebitCardInternationalOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardInternationalOnOffFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardInternationalOnOffFailureSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardInternationalOnOffFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card int on off failure sms option is nil")
	}
	switch o.DebitCardInternationalOnOffFailureSmsOption.GetOption().(type) {
	case *DebitCardInternationalOnOffFailureSmsOption_DebitCardInternationalOnOffFailureSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for int debit card on off failure template")
}

func (o *SmsOption_DebitCardInternationalOnOffFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardInternationalOnOffFailureSmsOption.GetOption().(type) {
	case *DebitCardInternationalOnOffFailureSmsOption_DebitCardInternationalOnOffFailureSmsOptionV1:
		return o.DebitCardInternationalOnOffFailureSmsOption.GetDebitCardInternationalOnOffFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardEcomOnSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardEcomOnSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardEcomOnSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card ecom on sms option is nil")
	}
	switch o.DebitCardEcomOnSmsOption.GetOption().(type) {
	case *DebitCardEcomOnSmsOption_DebitCardEcomOnSmsOptionV1:
		firstName := o.DebitCardEcomOnSmsOption.GetDebitCardEcomOnSmsOptionV1().GetName().GetFirstName()
		cardLastFour := o.DebitCardEcomOnSmsOption.GetDebitCardEcomOnSmsOptionV1().GetCardLastFourDigits()
		if firstName == "" || cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card ecom on failure template")
}

func (o *SmsOption_DebitCardEcomOnSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardEcomOnSmsOption.GetOption().(type) {
	case *DebitCardEcomOnSmsOption_DebitCardEcomOnSmsOptionV1:
		return o.DebitCardEcomOnSmsOption.GetDebitCardEcomOnSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardEcomOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardEcomOffSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardEcomOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card ecom off sms option is nil")
	}
	switch o.DebitCardEcomOffSmsOption.GetOption().(type) {
	case *DebitCardEcomOffSmsOption_DebitCardEcomOffSmsOptionV1:
		firstName := o.DebitCardEcomOffSmsOption.GetDebitCardEcomOffSmsOptionV1().GetName().GetFirstName()
		cardLastFour := o.DebitCardEcomOffSmsOption.GetDebitCardEcomOffSmsOptionV1().GetCardLastFourDigits()
		if firstName == "" || cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card ecom off failure template")
}

func (o *SmsOption_DebitCardEcomOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardEcomOffSmsOption.GetOption().(type) {
	case *DebitCardEcomOffSmsOption_DebitCardEcomOffSmsOptionV1:
		return o.DebitCardEcomOffSmsOption.GetDebitCardEcomOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardActivateSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardActivateSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardActivateSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card activate sms option is nil")
	}
	switch o.DebitCardActivateSmsOption.GetOption().(type) {
	case *DebitCardActivateSmsOption_DebitCardActivateSmsOptionV1:
		firstName := o.DebitCardActivateSmsOption.GetDebitCardActivateSmsOptionV1().GetName().GetFirstName()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card activate template")
}

func (o *SmsOption_DebitCardActivateSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardActivateSmsOption.GetOption().(type) {
	case *DebitCardActivateSmsOption_DebitCardActivateSmsOptionV1:
		return o.DebitCardActivateSmsOption.GetDebitCardActivateSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardBlockFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardBlockFailureSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardBlockFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card block failure sms option is nil")
	}
	switch o.DebitCardBlockFailureSmsOption.GetOption().(type) {
	case *DebitCardBlockFailureSmsOption_DebitCardBlockFailureSmsOptionV1:
		firstName := o.DebitCardBlockFailureSmsOption.GetDebitCardBlockFailureSmsOptionV1().GetName().GetFirstName()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card block failure template")
}

func (o *SmsOption_DebitCardBlockFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardBlockFailureSmsOption.GetOption().(type) {
	case *DebitCardBlockFailureSmsOption_DebitCardBlockFailureSmsOptionV1:
		return o.DebitCardBlockFailureSmsOption.GetDebitCardBlockFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardDispatchSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardDispatchSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardDispatchSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card dispatch sms option is nil")
	}
	switch o.DebitCardDispatchSmsOption.GetOption().(type) {
	case *DebitCardDispatchSmsOption_DebitCardDispatchSmsOptionV1:
		firstName := o.DebitCardDispatchSmsOption.GetDebitCardDispatchSmsOptionV1().GetName().GetFirstName()
		cardLastFour := o.DebitCardDispatchSmsOption.GetDebitCardDispatchSmsOptionV1().GetCardLastFourDigits()
		trackingNumber := o.DebitCardDispatchSmsOption.GetDebitCardDispatchSmsOptionV1().GetTrackingNumber()
		dispatchTime := o.DebitCardDispatchSmsOption.GetDebitCardDispatchSmsOptionV1().GetDispatchTime()
		logisticsPartner := o.DebitCardDispatchSmsOption.GetDebitCardDispatchSmsOptionV1().GetLogisticsPartner()
		if firstName == "" || cardLastFour == "" || len(cardLastFour) < 4 || trackingNumber == "" || logisticsPartner == "" || dispatchTime == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#logistics_partner#}", logisticsPartner, 1)
		msg = ReplaceWithTrim(msg, "{#tracking_number#}", trackingNumber, 1)
		msg = ReplaceWithTrim(msg, "{date_time}", dispatchTime.AsTime().Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card dispatch template")
}

func (o *SmsOption_DebitCardDispatchSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardDispatchSmsOption.GetOption().(type) {
	case *DebitCardDispatchSmsOption_DebitCardDispatchSmsOptionV1:
		return o.DebitCardDispatchSmsOption.GetDebitCardDispatchSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardIncorrectPinRetriesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardIncorrectPinRetriesSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardIncorrectPinRetriesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card incorrect pin retry sms option is nil")
	}
	switch o.DebitCardIncorrectPinRetriesSmsOption.GetOption().(type) {
	case *DebitCardInCorrectPinRetriesSmsOption_DebitCardIncorrectPinRetriesSmsOptionV1:
		cardLastFour := o.DebitCardIncorrectPinRetriesSmsOption.GetDebitCardIncorrectPinRetriesSmsOptionV1().GetCardLastFourDigits()
		if cardLastFour == "" || len(cardLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card incorrect pin retry template")
}

func (o *SmsOption_DebitCardIncorrectPinRetriesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardIncorrectPinRetriesSmsOption.GetOption().(type) {
	case *DebitCardInCorrectPinRetriesSmsOption_DebitCardIncorrectPinRetriesSmsOptionV1:
		return o.DebitCardIncorrectPinRetriesSmsOption.GetDebitCardIncorrectPinRetriesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardFreezeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardFreezeSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardFreezeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card freeze sms option is nil")
	}
	switch o.DebitCardFreezeSmsOption.GetOption().(type) {
	case *DebitCardFreezeSmsOption_DebitCardFreezeSmsOptionV1:
		cardLastFour := o.DebitCardFreezeSmsOption.GetDebitCardFreezeSmsOptionV1().GetCardLastFourDigits()
		firstName := o.DebitCardFreezeSmsOption.GetDebitCardFreezeSmsOptionV1().GetName().GetFirstName()
		if cardLastFour == "" || len(cardLastFour) < 4 || firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#first_name#}", firstName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card freeze template")
}

func (o *SmsOption_DebitCardFreezeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardFreezeSmsOption.GetOption().(type) {
	case *DebitCardFreezeSmsOption_DebitCardFreezeSmsOptionV1:
		return o.DebitCardFreezeSmsOption.GetDebitCardFreezeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardUnfreezeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardUnfreezeSmsOption.GetSmsType()
}

// nolint: dupl
func (o *SmsOption_DebitCardUnfreezeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card unfreeze sms option is nil")
	}
	switch o.DebitCardUnfreezeSmsOption.GetOption().(type) {
	case *DebitCardUnFreezeSmsOption_DebitCardUnfreezeSmsOptionV1:
		cardLastFour := o.DebitCardUnfreezeSmsOption.GetDebitCardUnfreezeSmsOptionV1().GetCardLastFourDigits()
		firstName := o.DebitCardUnfreezeSmsOption.GetDebitCardUnfreezeSmsOptionV1().GetName().GetFirstName()
		if cardLastFour == "" || len(cardLastFour) < 4 || firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#debit_card_last_four#}", getMaskedLastFour(cardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#first_name#}", firstName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card unfreeze template")
}

func (o *SmsOption_DebitCardUnfreezeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardUnfreezeSmsOption.GetOption().(type) {
	case *DebitCardUnFreezeSmsOption_DebitCardUnfreezeSmsOptionV1:
		return o.DebitCardUnfreezeSmsOption.GetDebitCardUnfreezeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardChangeAtmPinSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardChangeAtmPinSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardChangeAtmPinSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("debit card change atm pin sms option is nil")
	}
	switch o.DebitCardChangeAtmPinSmsOption.GetOption().(type) {
	case *DebitCardChangeAtmPinSmsOption_DebitCardChangeAtmPinSmsOptionV1:
		firstName := o.DebitCardChangeAtmPinSmsOption.GetDebitCardChangeAtmPinSmsOptionV1().GetName().GetFirstName()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for debit card change atm pin template")
}

func (o *SmsOption_DebitCardChangeAtmPinSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardChangeAtmPinSmsOption.GetOption().(type) {
	case *DebitCardChangeAtmPinSmsOption_DebitCardChangeAtmPinSmsOptionV1:
		return o.DebitCardChangeAtmPinSmsOption.GetDebitCardChangeAtmPinSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_UpiRegistrationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.UpiRegistrationSmsOption.GetSmsType()
}

func (o *SmsOption_UpiRegistrationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("upi registration sms option is nil")
	}
	switch o.UpiRegistrationSmsOption.GetOption().(type) {
	case *UpiRegistrationSmsOption_UpiRegistrationSmsOptionV1:
		firstName := o.UpiRegistrationSmsOption.GetUpiRegistrationSmsOptionV1().GetName().GetFirstName()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for upi registration template")
}

func (o *SmsOption_UpiRegistrationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UpiRegistrationSmsOption.GetOption().(type) {
	case *UpiRegistrationSmsOption_UpiRegistrationSmsOptionV1:
		return o.UpiRegistrationSmsOption.GetUpiRegistrationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CashWithdrawalAtmSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CashWithdrawalAtmSmsOption.GetSmsType()
}

func (o *SmsOption_CashWithdrawalAtmSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cash withdrawal atm sms option is nil")
	}
	switch o.CashWithdrawalAtmSmsOption.GetOption().(type) {
	case *CashWithdrawalAtmSmsOption_CashWithdrawalAtmSmsOptionV1:
		withdrawnAmt := o.CashWithdrawalAtmSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetWithdrawnAmount()
		balanceAmt := o.CashWithdrawalAtmSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetBalanceAmount()
		txnTimestamp := o.CashWithdrawalAtmSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetTransactionTimestamp()
		if withdrawnAmt == nil || balanceAmt == nil || txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))
		msg := ReplaceWithTrim(template, "{#withdraw_amount#}", money.ToDisplayStringWithINRSymbol(withdrawnAmt), 1)
		msg = ReplaceWithTrim(msg, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#time#}", txnTimestamp.AsTime().In(datetime.IST).Format("15:04"), 1)
		msg = ReplaceWithTrim(msg, "{#date#}", txnTimestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cash withdrawal atm template")
}

func (o *SmsOption_CashWithdrawalAtmSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CashWithdrawalAtmSmsOption.GetOption().(type) {
	case *CashWithdrawalAtmSmsOption_CashWithdrawalAtmSmsOptionV1:
		return o.CashWithdrawalAtmSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NeftDebitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NeftDebitSmsOption.GetSmsType()
}

func (o *SmsOption_NeftDebitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("neft debit sms option is nil")
	}
	switch o.NeftDebitSmsOption.GetOption().(type) {
	case *NeftDebitSmsOption_NeftDebitSmsOptionV1:
		firstName := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetName().GetFirstName()
		paymentMode := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetPaymentMode()
		receiverName := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetReceiverName().ToString()
		balanceAmt := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetBalanceAmount()
		txnTimestamp := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetTransactionTimestamp()
		refNumber := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetReferenceNumber()
		txnAmount := o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetTransactionAmount()
		if firstName == "" || paymentMode == "" || receiverName == "" || balanceAmt == nil ||
			txnTimestamp == nil || refNumber == "" || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for neft debit atm template")
}

func (o *SmsOption_NeftDebitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NeftDebitSmsOption.GetOption().(type) {
	case *NeftDebitSmsOption_NeftDebitSmsOptionV1:
		return o.NeftDebitSmsOption.GetNeftDebitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NeftCreditSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NeftCreditSmsOption.GetSmsType()
}

func (o *SmsOption_NeftCreditSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("neft credit sms option is nil")
	}
	switch o.NeftCreditSmsOption.GetOption().(type) {
	case *NeftCreditSmsOption_NeftCreditSmsOptionV1:
		firstName := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetName().GetFirstName()
		senderName := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetSenderName().GetFirstName()
		paymentMode := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetPaymentMode()
		txnTimestamp := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetTransactionAmount()
		refNumber := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetReferenceNumber()
		balanceAmt := o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetBalanceAmount()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request -- firstName")
		}
		if paymentMode == "" {
			return "", errors.New("mandatory params missing in request -- paymentMode")
		}
		if senderName == "" {
			return "", errors.New("mandatory params missing in request -- senderName")
		}
		if balanceAmt == nil {
			return "", errors.New("mandatory params missing in request -- balanceAmt")
		}
		if txnAmount == nil {
			return "", errors.New("mandatory params missing in request -- txnAmount")
		}
		if txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request -- txnTimestamp")
		}
		if refNumber == "" {
			return "", errors.New("mandatory params missing in request -- refNumber")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#balance_amount#}", money.ToDisplayStringWithoutSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		msg = ReplaceWithTrim(msg, "{#sender_name#}", senderName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for neft credit atm template")
}

func (o *SmsOption_NeftCreditSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NeftCreditSmsOption.GetOption().(type) {
	case *NeftCreditSmsOption_NeftCreditSmsOptionV1:
		return o.NeftCreditSmsOption.GetNeftCreditSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NeftCreditOtherBankSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NeftCreditOtherBankSmsOption.GetSmsType()
}

func (o *SmsOption_NeftCreditOtherBankSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("neft credit other bank sms option is nil")
	}
	switch o.NeftCreditOtherBankSmsOption.GetOption().(type) {
	case *NeftCreditOtherBankSmsOption_NeftCreditOtherBankSmsOptionV1:
		paymentMode := o.NeftCreditOtherBankSmsOption.GetNeftCreditOtherBankSmsOptionV1().GetPaymentMode()
		txnTimestamp := o.NeftCreditOtherBankSmsOption.GetNeftCreditOtherBankSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.NeftCreditOtherBankSmsOption.GetNeftCreditOtherBankSmsOptionV1().GetTransactionAmount()
		balanceAmt := o.NeftCreditOtherBankSmsOption.GetNeftCreditOtherBankSmsOptionV1().GetBalanceAmount()
		accountNumberLastFour := o.NeftCreditOtherBankSmsOption.GetNeftCreditOtherBankSmsOptionV1().GetAccountNumberLastFourDigits()
		if accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || paymentMode == "" || balanceAmt == nil || txnTimestamp == nil || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for neft credit other bank template")
}

func (o *SmsOption_NeftCreditOtherBankSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NeftCreditOtherBankSmsOption.GetOption().(type) {
	case *NeftCreditOtherBankSmsOption_NeftCreditOtherBankSmsOptionV1:
		return o.NeftCreditOtherBankSmsOption.GetNeftCreditOtherBankSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NeftCreditConfirmationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NeftCreditConfirmationSmsOption.GetSmsType()
}

func (o *SmsOption_NeftCreditConfirmationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("neft credit confirmation sms option is nil")
	}
	switch o.NeftCreditConfirmationSmsOption.GetOption().(type) {
	case *NeftCreditConfirmationSmsOption_NeftCreditConfirmationSmsOptionV1:
		firstName := o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetName().GetFirstName()
		receiverName := o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetReceiverName().ToString()
		paymentMode := o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetPaymentMode()
		txnTimestamp := o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetTransactionAmount()
		refNumber := o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetReferenceNumber()
		if firstName == "" || paymentMode == "" || receiverName == "" ||
			txnTimestamp == nil || refNumber == "" || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		return msg, nil
	}

	return "", fmt.Errorf("no valid version found for neft credit confirmation atm template")
}

func (o *SmsOption_NeftCreditConfirmationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NeftCreditConfirmationSmsOption.GetOption().(type) {
	case *NeftCreditConfirmationSmsOption_NeftCreditConfirmationSmsOptionV1:
		return o.NeftCreditConfirmationSmsOption.GetNeftCreditConfirmationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_PosDebitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.PosDebitSmsOption.GetSmsType()
}

func (o *SmsOption_PosDebitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("pos debit sms option is nil")
	}
	switch o.PosDebitSmsOption.GetOption().(type) {
	case *PosDebitSmsOption_PosDebitSmsOptionV1:
		receiverName := o.PosDebitSmsOption.GetPosDebitSmsOptionV1().GetReceiverName().ToString()
		txnTimestamp := o.PosDebitSmsOption.GetPosDebitSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.PosDebitSmsOption.GetPosDebitSmsOptionV1().GetTransactionAmount()
		balanceAmt := o.PosDebitSmsOption.GetPosDebitSmsOptionV1().GetBalanceAmount()
		if balanceAmt == nil || receiverName == "" || txnTimestamp == nil || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for pos debit template")
}

func (o *SmsOption_PosDebitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PosDebitSmsOption.GetOption().(type) {
	case *PosDebitSmsOption_PosDebitSmsOptionV1:
		return o.PosDebitSmsOption.GetPosDebitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_PosReversalCreditSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.PosReversalCreditSmsOption.GetSmsType()
}

func (o *SmsOption_PosReversalCreditSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("pos reversal credit sms option is nil")
	}
	switch o.PosReversalCreditSmsOption.GetOption().(type) {
	case *PosReversalCreditSmsOption_PosReversalCreditSmsOptionV1:
		accountNumberLastFour := o.PosReversalCreditSmsOption.GetPosReversalCreditSmsOptionV1().GetAccountNumberLastFourDigits()
		txnTimestamp := o.PosReversalCreditSmsOption.GetPosReversalCreditSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.PosReversalCreditSmsOption.GetPosReversalCreditSmsOptionV1().GetTransactionAmount()
		balanceAmt := o.PosReversalCreditSmsOption.GetPosReversalCreditSmsOptionV1().GetBalanceAmount()
		if balanceAmt == nil || accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for pos reversal credit template")
}

func (o *SmsOption_PosReversalCreditSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PosReversalCreditSmsOption.GetOption().(type) {
	case *PosReversalCreditSmsOption_PosReversalCreditSmsOptionV1:
		return o.PosReversalCreditSmsOption.GetPosReversalCreditSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_UnsuccessfulAtmReversalCreditSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.UnsuccessfulAtmReversalCreditSmsOption.GetSmsType()
}

func (o *SmsOption_UnsuccessfulAtmReversalCreditSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("unsuccessful atm reversal credit sms option is nil")
	}
	switch o.UnsuccessfulAtmReversalCreditSmsOption.GetOption().(type) {
	case *UnsuccessfulAtmReversalCreditSmsOption_UnsuccessfulAtmReversalCreditSmsOptionV1:
		txnTimestamp := o.UnsuccessfulAtmReversalCreditSmsOption.GetUnsuccessfulAtmReversalCreditSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.UnsuccessfulAtmReversalCreditSmsOption.GetUnsuccessfulAtmReversalCreditSmsOptionV1().GetTransactionAmount()
		balanceAmt := o.UnsuccessfulAtmReversalCreditSmsOption.GetUnsuccessfulAtmReversalCreditSmsOptionV1().GetBalanceAmount()
		if balanceAmt == nil || txnTimestamp == nil || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for unsuccessful atm reversal credit template")
}

func (o *SmsOption_UnsuccessfulAtmReversalCreditSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UnsuccessfulAtmReversalCreditSmsOption.GetOption().(type) {
	case *UnsuccessfulAtmReversalCreditSmsOption_UnsuccessfulAtmReversalCreditSmsOptionV1:
		return o.UnsuccessfulAtmReversalCreditSmsOption.GetUnsuccessfulAtmReversalCreditSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RtgsCreditConfirmationSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RtgsCreditConfirmationSmsOption.GetSmsType()
}

func (o *SmsOption_RtgsCreditConfirmationSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("unsuccessful atm reversal credit sms option is nil")
	}
	switch o.RtgsCreditConfirmationSmsOption.GetOption().(type) {
	case *RtgsCreditConfirmationSmsOption_RtgsCreditConfirmationSmsOptionV1:
		txnTimestamp := o.RtgsCreditConfirmationSmsOption.GetRtgsCreditConfirmationSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.RtgsCreditConfirmationSmsOption.GetRtgsCreditConfirmationSmsOptionV1().GetTransactionAmount()
		firstName := o.RtgsCreditConfirmationSmsOption.GetRtgsCreditConfirmationSmsOptionV1().GetName().GetFirstName()
		receiverName := o.RtgsCreditConfirmationSmsOption.GetRtgsCreditConfirmationSmsOptionV1().GetReceiverName().ToString()
		refNumber := o.RtgsCreditConfirmationSmsOption.GetRtgsCreditConfirmationSmsOptionV1().GetReferenceNumber()
		if refNumber == "" || txnTimestamp == nil || txnAmount == nil || firstName == "" || receiverName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for unsuccessful atm reversal credit template")
}

func (o *SmsOption_RtgsCreditConfirmationSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RtgsCreditConfirmationSmsOption.GetOption().(type) {
	case *RtgsCreditConfirmationSmsOption_RtgsCreditConfirmationSmsOptionV1:
		return o.RtgsCreditConfirmationSmsOption.GetRtgsCreditConfirmationSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RtgsDebitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RtgsDebitSmsOption.GetSmsType()
}

func (o *SmsOption_RtgsDebitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("unsuccessful atm reversal credit sms option is nil")
	}
	switch o.RtgsDebitSmsOption.GetOption().(type) {
	case *RtgsDebitSmsOption_RtgsDebitSmsOptionV1:
		txnTimestamp := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetTransactionAmount()
		firstName := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetName().GetFirstName()
		receiverName := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetReceiverName().ToString()
		refNumber := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetReferenceNumber()
		balanceAmt := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetBalanceAmount()
		paymentMode := o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetPaymentMode()
		if refNumber == "" || txnTimestamp == nil || txnAmount == nil || firstName == "" ||
			receiverName == "" || balanceAmt == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		msg = ReplaceWithTrim(msg, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for unsuccessful atm reversal credit template")
}

func (o *SmsOption_RtgsDebitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RtgsDebitSmsOption.GetOption().(type) {
	case *RtgsDebitSmsOption_RtgsDebitSmsOptionV1:
		return o.RtgsDebitSmsOption.GetRtgsDebitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCashDepositMachineSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCashDepositMachineSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCashDepositMachineSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit cash deposit machine sms option is nil")
	}
	switch o.CreditCashDepositMachineSmsOption.GetOption().(type) {
	case *CreditCashDepositMachineSmsOption_CreditCashDepositMachineSmsOptionV1:
		txnAmount := o.CreditCashDepositMachineSmsOption.GetCreditCashDepositMachineSmsOptionV1().GetTransactionAmount()
		firstName := o.CreditCashDepositMachineSmsOption.GetCreditCashDepositMachineSmsOptionV1().GetName().GetFirstName()
		balanceAmt := o.CreditCashDepositMachineSmsOption.GetCreditCashDepositMachineSmsOptionV1().GetBalanceAmount()
		location := o.CreditCashDepositMachineSmsOption.GetCreditCashDepositMachineSmsOptionV1().GetLocation()
		if location == "" || txnAmount == nil || firstName == "" ||
			balanceAmt == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#balance_amount#}", money.ToDisplayStringWithINRSymbol(balanceAmt), 1)
		msg = ReplaceWithTrim(msg, "{#location#}", location, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for unsuccessful atm reversal credit template")
}

func (o *SmsOption_CreditCashDepositMachineSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCashDepositMachineSmsOption.GetOption().(type) {
	case *CreditCashDepositMachineSmsOption_CreditCashDepositMachineSmsOptionV1:
		return o.CreditCashDepositMachineSmsOption.GetCreditCashDepositMachineSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_UpiCreditSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.UpiCreditSmsOption.GetSmsType()
}

func (o *SmsOption_UpiCreditSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("upi credit sms option is nil")
	}
	switch o.UpiCreditSmsOption.GetOption().(type) {
	case *UpiCreditSmsOption_UpiCreditSmsOptionV1:
		txnAmount := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetTransactionAmount()
		firstName := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetName().GetFirstName()
		senderUpiId := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetSenderUpiId()
		accountNumberLastFour := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetAccountNumberLastFourDigits()
		txnTimestamp := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetTransactionTimestamp()
		senderName := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetSenderName().GetFirstName()

		if txnAmount == nil || firstName == "" ||
			senderUpiId == "" || accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil || senderName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#sender_upi_id#}", senderUpiId, 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#sender_name#}", senderName, 1)
		return msg, nil
	case *UpiCreditSmsOption_UpiCreditSmsOptionV2:
		txnAmount := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV2().GetTransactionAmount()
		senderUpiId := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV2().GetSenderUpiId()
		accountNumberLastFour := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV2().GetAccountNumberLastFourDigits()
		txnTimestamp := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV2().GetTransactionTimestamp()
		senderName := o.UpiCreditSmsOption.GetUpiCreditSmsOptionV2().GetSenderName().GetFirstName()

		if txnAmount == nil ||
			senderUpiId == "" || accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil || senderName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#sender_upi_id#}", senderUpiId, 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#sender_name#}", senderName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for upi credit template")
}

func (o *SmsOption_UpiCreditSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UpiCreditSmsOption.GetOption().(type) {
	case *UpiCreditSmsOption_UpiCreditSmsOptionV1:
		return o.UpiCreditSmsOption.GetUpiCreditSmsOptionV1().GetTemplateVersion()
	case *UpiCreditSmsOption_UpiCreditSmsOptionV2:
		return o.UpiCreditSmsOption.GetUpiCreditSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_UpiDebitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.UpiDebitSmsOption.GetSmsType()
}

// nolint: funlen
func (o *SmsOption_UpiDebitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("upi debit sms option is nil")
	}
	switch o.UpiDebitSmsOption.GetOption().(type) {
	case *UpiDebitSmsOption_UpiDebitSmsOptionV1:
		txnAmount := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV1().GetTransactionAmount()
		firstName := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV1().GetName().GetFirstName()
		senderPi := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV1().GetSenderPi()
		accountNumberLastFour := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV1().GetAccountNumberLastFourDigits()
		txnTimestamp := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV1().GetTransactionTimestamp()
		if txnAmount == nil {
			return "", errors.New("mandatory params missing in request -- txnAmount")
		}
		if firstName == "" {
			return "", errors.New("mandatory params missing in request -- firstName")
		}
		if senderPi == "" {
			return "", errors.New("mandatory params missing in request -- senderPi")
		}
		if accountNumberLastFour == "" {
			return "", errors.New("mandatory params missing in request -- accountNumberLastFour")
		}
		if txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request -- txnTimestamp")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#sender_pi#}", senderPi, 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)
		return msg, nil
	case *UpiDebitSmsOption_UpiDebitSmsOptionV2:
		txnAmount := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV2().GetTransactionAmount()
		senderPi := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV2().GetSenderPi()
		accountNumberLastFour := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV2().GetAccountNumberLastFourDigits()
		txnTimestamp := o.UpiDebitSmsOption.GetUpiDebitSmsOptionV2().GetTransactionTimestamp()
		if txnAmount == nil {
			return "", errors.New("mandatory params missing in request -- txnAmount")
		}
		if senderPi == "" {
			return "", errors.New("mandatory params missing in request -- senderPi")
		}
		if accountNumberLastFour == "" {
			return "", errors.New("mandatory params missing in request -- accountNumberLastFour")
		}
		if txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request -- txnTimestamp")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#sender_pi#}", senderPi, 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for upi debit template")
}

func (o *SmsOption_UpiDebitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UpiDebitSmsOption.GetOption().(type) {
	case *UpiDebitSmsOption_UpiDebitSmsOptionV1:
		return o.UpiDebitSmsOption.GetUpiDebitSmsOptionV1().GetTemplateVersion()
	case *UpiDebitSmsOption_UpiDebitSmsOptionV2:
		return o.UpiDebitSmsOption.GetUpiDebitSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CollectRequestSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CollectRequestSmsOption.GetSmsType()
}

func (o *SmsOption_CollectRequestSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("collect request sms option is nil")
	}
	switch o.CollectRequestSmsOption.GetOption().(type) {
	case *CollectRequestSmsOption_CollectRequestSmsOptionV1:
		txnAmount := o.CollectRequestSmsOption.GetCollectRequestSmsOptionV1().GetTransactionAmount()
		firstName := o.CollectRequestSmsOption.GetCollectRequestSmsOptionV1().GetName().GetFirstName()
		senderName := o.CollectRequestSmsOption.GetCollectRequestSmsOptionV1().GetSenderName().GetFirstName()

		if txnAmount == nil || firstName == "" ||
			senderName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#sender_name#}", senderName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for collect request template")
}

func (o *SmsOption_CollectRequestSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CollectRequestSmsOption.GetOption().(type) {
	case *CollectRequestSmsOption_CollectRequestSmsOptionV1:
		return o.CollectRequestSmsOption.GetCollectRequestSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_FailedTransactonSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FailedTransactonSmsOption.GetSmsType()
}

func (o *SmsOption_FailedTransactonSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("failed trasaction sms option is nil")
	}
	switch o.FailedTransactonSmsOption.GetOption().(type) {
	case *FailedTransactionSmsOption_FailedTransactionSmsOptionV1:
		txnAmount := o.FailedTransactonSmsOption.GetFailedTransactionSmsOptionV1().GetTransactionAmount()

		if txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for failed trasaction template")
}

func (o *SmsOption_FailedTransactonSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FailedTransactonSmsOption.GetOption().(type) {
	case *FailedTransactionSmsOption_FailedTransactionSmsOptionV1:
		return o.FailedTransactonSmsOption.GetFailedTransactionSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_GenericPiCreditSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.GenericPiCreditSmsOption.GetSmsType()
}

func (o *SmsOption_GenericPiCreditSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi credit sms option is nil")
	}
	switch o.GenericPiCreditSmsOption.GetOption().(type) {
	case *GenericPiCreditSmsOption_GenericPiCreditSmsOptionV1:
		txnAmount := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV1().GetTransactionAmount()
		firstName := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV1().GetName().GetFirstName()
		accountNumberLastFour := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV1().GetAccountNumberLastFourDigits()
		txnTimestamp := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV1().GetTransactionTimestamp()
		if txnAmount == nil || firstName == "" ||
			accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		return msg, nil
	case *GenericPiCreditSmsOption_GenericPiCreditSmsOptionV2:
		txnAmount := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV2().GetTransactionAmount()
		firstName := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV2().GetName().GetFirstName()
		accountNumberLastFour := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV2().GetAccountNumberLastFourDigits()
		txnTimestamp := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV2().GetTransactionTimestamp()
		senderPi := o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV2().GetSenderPi()

		if txnAmount == nil || firstName == "" ||
			accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil || senderPi == "" {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#sender_pi#}", senderPi, 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for generic pi credit template")
}

func (o *SmsOption_GenericPiCreditSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.GenericPiCreditSmsOption.GetOption().(type) {
	case *GenericPiCreditSmsOption_GenericPiCreditSmsOptionV1:
		return o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV1().GetTemplateVersion()
	case *GenericPiCreditSmsOption_GenericPiCreditSmsOptionV2:
		return o.GenericPiCreditSmsOption.GetGenericPiCreditSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_GenericPiDebitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.GenericPiDebitSmsOption.GetSmsType()
}

func (o *SmsOption_GenericPiDebitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.GenericPiDebitSmsOption.GetOption().(type) {
	case *GenericPiDebitSmsOption_GenericPiDebitSmsOptionV1:
		txnAmount := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetTransactionAmount()
		firstName := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetName().GetFirstName()
		senderPi := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetSenderPi()
		accountNumberLastFour := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetAccountNumberLastFourDigits()
		txnTimestamp := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetTransactionTimestamp()
		senderName := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetSenderName().GetFirstName()

		if txnAmount == nil || firstName == "" ||
			senderPi == "" || accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil || senderName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#sender_pi#}", senderPi, 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#sender_name#}", senderName, 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)

		return msg, nil
	case *GenericPiDebitSmsOption_GenericPiDebitSmsOptionV2:
		txnAmount := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV2().GetTransactionAmount()
		senderPi := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV2().GetSenderPi()
		accountNumberLastFour := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV2().GetAccountNumberLastFourDigits()
		txnTimestamp := o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV2().GetTransactionTimestamp()

		if txnAmount == nil ||
			senderPi == "" || accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#sender_pi#}", senderPi, 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for generic pi debit template")
}

func (o *SmsOption_GenericPiDebitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.GenericPiDebitSmsOption.GetOption().(type) {
	case *GenericPiDebitSmsOption_GenericPiDebitSmsOptionV1:
		return o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV1().GetTemplateVersion()
	case *GenericPiDebitSmsOption_GenericPiDebitSmsOptionV2:
		return o.GenericPiDebitSmsOption.GetGenericPiDebitSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_FdOpenSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FdOpenSmsOption.GetSmsType()
}

func (o *SmsOption_FdOpenSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.FdOpenSmsOption.GetOption().(type) {
	case *FdOpenSmsOption_FdOpenSmsOptionV1:
		fdName := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetFdName()
		fdAmount := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetFdAmount()
		interestRate := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetInterestRate()
		duration := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetDuration()
		maturityDate := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetMaturityDate()
		maturityAmount := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetMaturityAmount()
		renewalInstructions := o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetRenewalInstruction()
		if fdName == "" || fdAmount == nil ||
			interestRate == "" || duration == "" || maturityDate == nil || maturityAmount == nil || renewalInstructions == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#fd_name#}", fdName, 1)
		msg = ReplaceWithTrim(msg, "{#fd_amt#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(fdAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#fd_int#}", interestRate, 1)
		msg = ReplaceWithTrim(msg, "{#fd_duration#}", duration, 1)
		msg = ReplaceWithTrim(msg, "{#fd_maturity_date#}", maturityDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#fd_maturity_amt#}", money.ToDisplayStringWithINRSymbol(maturityAmount), 1)
		msg = ReplaceWithTrim(msg, "{#fd_renewal_instruction#}", renewalInstructions, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for fd open template")
}

func (o *SmsOption_FdOpenSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FdOpenSmsOption.GetOption().(type) {
	case *FdOpenSmsOption_FdOpenSmsOptionV1:
		return o.FdOpenSmsOption.GetFdOpenSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SdOpenSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SdOpenSmsOption.GetSmsType()
}

func (o *SmsOption_SdOpenSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.SdOpenSmsOption.GetOption().(type) {
	case *SdOpenSmsOption_SdOpenSmsOptionV1:
		sdName := o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetSdName()
		sdAmount := o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetSdAmount()
		interestRate := o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetInterestRate()
		duration := o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetDuration()
		maturityDate := o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetMaturityDate()
		maturityAmount := o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetMaturityAmount()
		if sdName == "" || sdAmount == nil ||
			interestRate == "" || duration == "" || maturityDate == nil || maturityAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#sd_name#}", sdName, 1)
		msg = ReplaceWithTrim(msg, "{#sd_amt#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(sdAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#sd_int#}", interestRate, 1)
		msg = ReplaceWithTrim(msg, "{#sd_duration#}", duration, 1)
		msg = ReplaceWithTrim(msg, "{#sd_maturity_date#}", maturityDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#sd_maturity_amt#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(maturityAmount, 2), 1)
		return msg, nil
	case *SdOpenSmsOption_SdOpenSmsOptionV2:
		sdName := o.SdOpenSmsOption.GetSdOpenSmsOptionV2().GetSdName()
		sdAmount := o.SdOpenSmsOption.GetSdOpenSmsOptionV2().GetSdAmount()
		interestRate := o.SdOpenSmsOption.GetSdOpenSmsOptionV2().GetInterestRate()
		duration := o.SdOpenSmsOption.GetSdOpenSmsOptionV2().GetDuration()
		maturityDate := o.SdOpenSmsOption.GetSdOpenSmsOptionV2().GetMaturityDate()
		if sdName == "" || sdAmount == nil ||
			interestRate == "" || duration == "" || maturityDate == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#sd_name#}", sdName, 1)
		msg = ReplaceWithTrim(msg, "{#sd_amt#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(sdAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#sd_int#}", interestRate, 1)
		msg = ReplaceWithTrim(msg, "{#sd_duration#}", duration, 1)
		msg = ReplaceWithTrim(msg, "{#sd_maturity_date#}", maturityDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for sd open template")
}

func (o *SmsOption_SdOpenSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SdOpenSmsOption.GetOption().(type) {
	case *SdOpenSmsOption_SdOpenSmsOptionV1:
		return o.SdOpenSmsOption.GetSdOpenSmsOptionV1().GetTemplateVersion()
	case *SdOpenSmsOption_SdOpenSmsOptionV2:
		return o.SdOpenSmsOption.GetSdOpenSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_FdSdXDaysBeforeMatuaritySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FdSdXDaysBeforeMatuaritySmsOption.GetSmsType()
}

func (o *SmsOption_FdSdXDaysBeforeMatuaritySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.FdSdXDaysBeforeMatuaritySmsOption.GetOption().(type) {
	case *FdSdXDaysBeforeMatuaritySmsOption_FdSdXDaysBeforeMatuaritySmsOptionV1:
		depositType := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetDepositType()
		depositNumber := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetDepositNumberLastFourDigits()
		openingDate := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetOpeningDate()
		duration := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetDuration()
		maturityDate := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetMaturityDate()
		renewalInstructions := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetRenewalInstruction()
		renewalPeriod := o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetRenewalPeriod()

		if depositType == "" || depositNumber == "" || len(depositNumber) < 4 ||
			openingDate == nil || duration == "" || maturityDate == nil || renewalPeriod == "" || renewalInstructions == "" {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#deposit_inst#}", depositType, 1)
		msg = ReplaceWithTrim(msg, "{#deposit_no_last_four#}", getMaskedLastFour(depositNumber), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_maturity_date#}", maturityDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_duration#}", duration, 1)
		msg = ReplaceWithTrim(msg, "{#deposit_open_date#}", openingDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_renewal_period#}", renewalPeriod, 1)
		msg = ReplaceWithTrim(msg, "{#deposit_renewal_instruction#}", renewalInstructions, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for fd sd x days before maturity template")
}

func (o *SmsOption_FdSdXDaysBeforeMatuaritySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FdSdXDaysBeforeMatuaritySmsOption.GetOption().(type) {
	case *FdSdXDaysBeforeMatuaritySmsOption_FdSdXDaysBeforeMatuaritySmsOptionV1:
		return o.FdSdXDaysBeforeMatuaritySmsOption.GetFdSdXDaysBeforeMatuaritySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SdXDaysBeforeMaturitySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SdXDaysBeforeMaturitySmsOption.GetSmsType()
}

func (o *SmsOption_SdXDaysBeforeMaturitySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option is nil")
	}
	switch o.SdXDaysBeforeMaturitySmsOption.GetOption().(type) {
	case *SdXDaysBeforeMaturitySmsOption_SdXDaysBeforeMaturitySmsOptionV1:
		params := o.SdXDaysBeforeMaturitySmsOption.GetSdXDaysBeforeMaturitySmsOptionV1()
		depositName := params.GetDepositName()
		depositAmount := params.GetAmount()
		maturityDate := params.GetMaturityDate()

		if depositName == "" {
			return "", errors.New("deposit name param is missing")
		}
		if depositAmount == nil {
			return "", errors.New("amount param missing in request")
		}
		if maturityDate == nil {
			return "", errors.New("maturity date param missing in request")
		}

		msg := ReplaceWithTrim(template, "{#deposit_amount#}", money.ToDisplayStringWithINRSymbol(
			depositAmount), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_name#}", depositName, 1)
		msg = ReplaceWithTrim(msg, "{#deposit_maturity_date#}", maturityDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for sd x days before maturity template")
}

func (o *SmsOption_SdXDaysBeforeMaturitySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SdXDaysBeforeMaturitySmsOption.GetOption().(type) {
	case *SdXDaysBeforeMaturitySmsOption_SdXDaysBeforeMaturitySmsOptionV1:
		return o.SdXDaysBeforeMaturitySmsOption.GetSdXDaysBeforeMaturitySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_AddFundsSdSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.AddFundsSdSmsOption.GetSmsType()
}

func (o *SmsOption_AddFundsSdSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.AddFundsSdSmsOption.GetOption().(type) {
	case *AddFundsSdSmsOption_AddFundsSdSmsOptionV1:
		sdName := o.AddFundsSdSmsOption.GetAddFundsSdSmsOptionV1().GetSdName()
		addAmount := o.AddFundsSdSmsOption.GetAddFundsSdSmsOptionV1().GetSdAddAmount()
		accountNumberLastFour := o.AddFundsSdSmsOption.GetAddFundsSdSmsOptionV1().GetSdAccountNumberLastFourDigits()

		if sdName == "" || addAmount == nil ||
			accountNumberLastFour == "" || len(accountNumberLastFour) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#sd_name#}", sdName, 1)
		msg = ReplaceWithTrim(msg, "{#sd_add_amt#}", money.ToDisplayStringWithINRSymbol(addAmount), 1)
		msg = ReplaceWithTrim(msg, "{#sd_account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for add funds sd template")
}

func (o *SmsOption_AddFundsSdSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AddFundsSdSmsOption.GetOption().(type) {
	case *AddFundsSdSmsOption_AddFundsSdSmsOptionV1:
		return o.AddFundsSdSmsOption.GetAddFundsSdSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_FdSdClosureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FdSdClosureSmsOption.GetSmsType()
}

func (o *SmsOption_FdSdClosureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.FdSdClosureSmsOption.GetOption().(type) {
	case *FdSdClosureSmsOption_FdSdClosureSmsOptionV1:
		depositType := o.FdSdClosureSmsOption.GetFdSdClosureSmsOptionV1().GetDepositType()
		depositNumber := o.FdSdClosureSmsOption.GetFdSdClosureSmsOptionV1().GetDepositNumberLastFourDigits()
		maturityAmount := o.FdSdClosureSmsOption.GetFdSdClosureSmsOptionV1().GetMaturityAmount()
		savingsAccountNumber := o.FdSdClosureSmsOption.GetFdSdClosureSmsOptionV1().GetSavingAccountNumberLastFourDigits()
		maturityDate := o.FdSdClosureSmsOption.GetFdSdClosureSmsOptionV1().GetMaturityDate()

		if depositType == "" || depositNumber == "" || len(depositNumber) < 4 ||
			maturityAmount == nil || maturityDate == nil || savingsAccountNumber == "" || len(savingsAccountNumber) < 4 {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#deposit_inst#}", depositType, 1)
		msg = ReplaceWithTrim(msg, "{#deposit_no_last_four#}", getMaskedLastFour(depositNumber), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_maturity_date#}", maturityDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_maturity_amt#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(maturityAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(savingsAccountNumber), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for fd sd closure template")
}

func (o *SmsOption_FdSdClosureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FdSdClosureSmsOption.GetOption().(type) {
	case *FdSdClosureSmsOption_FdSdClosureSmsOptionV1:
		return o.FdSdClosureSmsOption.GetFdSdClosureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_InterestPaidInSbSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.InterestPaidInSbSmsOption.GetSmsType()
}

func (o *SmsOption_InterestPaidInSbSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.InterestPaidInSbSmsOption.GetOption().(type) {
	case *InterestPaidInSBSmsOption_InterestPaidInSbSmsOptionV1:
		interestAmount := o.InterestPaidInSbSmsOption.GetInterestPaidInSbSmsOptionV1().GetInterestAmount()
		accountNumber := o.InterestPaidInSbSmsOption.GetInterestPaidInSbSmsOptionV1().GetAccountNumberLastFourDigits()
		timestamp := o.InterestPaidInSbSmsOption.GetInterestPaidInSbSmsOptionV1().GetTimestamp()
		balance := o.InterestPaidInSbSmsOption.GetInterestPaidInSbSmsOptionV1().GetBalance()

		if interestAmount == nil || accountNumber == "" || len(accountNumber) < 4 ||
			timestamp == nil || balance == nil {
			return "", errors.New("mandatory params missing in request")
		}
		timestamp = timestamppb.New(timestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#interest_amt#}", money.ToDisplayStringWithINRSymbol(interestAmount), 1)
		msg = ReplaceWithTrim(msg, "{#last_four_acct_no#}", getMaskedLastFour(accountNumber), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", timestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#balance#}", money.ToDisplayStringWithINRSymbol(balance), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_InterestPaidInSbSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.InterestPaidInSbSmsOption.GetOption().(type) {
	case *InterestPaidInSBSmsOption_InterestPaidInSbSmsOptionV1:
		return o.InterestPaidInSbSmsOption.GetInterestPaidInSbSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MobileNumberAddSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MobileNumberAddSmsOption.GetSmsType()
}

func (o *SmsOption_MobileNumberAddSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MobileNumberAddSmsOption.GetOption().(type) {
	case *MobileNumberAddSmsOption_MobileNumberAddSmsOptionV1:
		newMobileNumber := o.MobileNumberAddSmsOption.GetMobileNumberAddSmsOptionV1().GetNewMobileNumber()
		reqAcceptedTimestamp := o.MobileNumberAddSmsOption.GetMobileNumberAddSmsOptionV1().GetRequestAcceptedTimestamp()
		customerId := o.MobileNumberAddSmsOption.GetMobileNumberAddSmsOptionV1().GetCustomerIdLastFourDigits()

		if newMobileNumber == nil || customerId == "" || len(customerId) < 4 ||
			reqAcceptedTimestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		reqAcceptedTimestamp = timestamppb.New(reqAcceptedTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#new_mobile_no#}", newMobileNumber.ToStringNationalNumber(), 1)
		msg = ReplaceWithTrim(msg, "{#date#}", reqAcceptedTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#time#}", reqAcceptedTimestamp.AsTime().Format("15:04"), 1)
		msg = ReplaceWithTrim(msg, "{#customer_id#}", getMaskedLastFour(customerId), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mobile number add template")
}

func (o *SmsOption_MobileNumberAddSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MobileNumberAddSmsOption.GetOption().(type) {
	case *MobileNumberAddSmsOption_MobileNumberAddSmsOptionV1:
		return o.MobileNumberAddSmsOption.GetMobileNumberAddSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MobileNumberModifySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MobileNumberModifySmsOption.GetSmsType()
}

func (o *SmsOption_MobileNumberModifySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MobileNumberModifySmsOption.GetOption().(type) {
	case *MobileNumberModifySmsOption_MobileNumberModifySmsOptionV1:
		oldMobilNumber := o.MobileNumberModifySmsOption.GetMobileNumberModifySmsOptionV1().GetOldMobileNumber()
		newMobileNumber := o.MobileNumberModifySmsOption.GetMobileNumberModifySmsOptionV1().GetNewMobileNumber()
		reqAcceptedTimestamp := o.MobileNumberModifySmsOption.GetMobileNumberModifySmsOptionV1().GetRequestAcceptedTimestamp()
		customerId := o.MobileNumberModifySmsOption.GetMobileNumberModifySmsOptionV1().GetCustomerIdLastFourDigits()

		if newMobileNumber == nil || oldMobilNumber == nil || customerId == "" || len(customerId) < 4 ||
			reqAcceptedTimestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		reqAcceptedTimestamp = timestamppb.New(reqAcceptedTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#new_mobile_no#}", newMobileNumber.ToStringNationalNumber(), 1)
		msg = ReplaceWithTrim(msg, "{#old_mobile_no#}", oldMobilNumber.ToStringNationalNumber(), 1)
		msg = ReplaceWithTrim(msg, "{#date#}", reqAcceptedTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#time#}", reqAcceptedTimestamp.AsTime().Format("15:04"), 1)
		msg = ReplaceWithTrim(msg, "{#customer_id#}", getMaskedLastFour(customerId), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mobile number modify template")
}

func (o *SmsOption_MobileNumberModifySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MobileNumberModifySmsOption.GetOption().(type) {
	case *MobileNumberModifySmsOption_MobileNumberModifySmsOptionV1:
		return o.MobileNumberModifySmsOption.GetMobileNumberModifySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint: dupl
func (o *SmsOption_CardControlOnSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CardControlOnSmsOption.GetSmsType()
}

// nolint:dupl
func (o *SmsOption_CardControlOnSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.CardControlOnSmsOption.GetOption().(type) {
	case *CardControlOnSmsOption_CardControlOnSmsOptionV1:
		name := o.CardControlOnSmsOption.GetCardControlOnSmsOptionV1().GetName()
		debitCardLastFour := o.CardControlOnSmsOption.GetCardControlOnSmsOptionV1().GetDebitCardLastFourDigits()
		cardMode := o.CardControlOnSmsOption.GetCardControlOnSmsOptionV1().GetCardMode()

		if debitCardLastFour == "" || len(debitCardLastFour) < 4 || cardMode == "" || name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(debitCardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#card_mode#}", cardMode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for card control on template")
}

func (o *SmsOption_CardControlOnSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CardControlOnSmsOption.GetOption().(type) {
	case *CardControlOnSmsOption_CardControlOnSmsOptionV1:
		return o.CardControlOnSmsOption.GetCardControlOnSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint: dupl
func (o *SmsOption_CardControlOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CardControlOffSmsOption.GetSmsType()
}

// nolint:dupl
func (o *SmsOption_CardControlOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.CardControlOffSmsOption.GetOption().(type) {
	case *CardControlOffSmsOption_CardControlOffSmsOptionV1:
		name := o.CardControlOffSmsOption.GetCardControlOffSmsOptionV1().GetName()
		debitCardLastFour := o.CardControlOffSmsOption.GetCardControlOffSmsOptionV1().GetDebitCardLastFourDigits()
		cardMode := o.CardControlOffSmsOption.GetCardControlOffSmsOptionV1().GetCardMode()

		if debitCardLastFour == "" || len(debitCardLastFour) < 4 || cardMode == "" || name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#debit_card_last_four#}", getMaskedLastFour(debitCardLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#card_mode#}", cardMode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for card control off template")
}

func (o *SmsOption_CardControlOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CardControlOffSmsOption.GetOption().(type) {
	case *CardControlOffSmsOption_CardControlOffSmsOptionV1:
		return o.CardControlOffSmsOption.GetCardControlOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_VkycApprovedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycApprovedSmsOption.GetSmsType()
}

func (o *SmsOption_VkycApprovedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.VkycApprovedSmsOption.GetOption().(type) {
	case *VkycApprovedSmsOption_VkycApprovedSmsOptionV1:
		name := o.VkycApprovedSmsOption.GetVkycApprovedSmsOptionV1().GetName()

		if name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc approved template")
}

func (o *SmsOption_VkycApprovedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycApprovedSmsOption.GetOption().(type) {
	case *VkycApprovedSmsOption_VkycApprovedSmsOptionV1:
		return o.VkycApprovedSmsOption.GetVkycApprovedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_WaitlistAccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.WaitlistAccessSmsOption.GetSmsType()
}

func (o *SmsOption_WaitlistAccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("waitlist access sms option is nil")
	}
	switch o.WaitlistAccessSmsOption.GetOption().(type) {
	case *WaitlistAccessSmsOption_WaitlistAccessSmsOptionV1:
		name := o.WaitlistAccessSmsOption.GetWaitlistAccessSmsOptionV1().GetName()

		if name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		return msg, nil
	case *WaitlistAccessSmsOption_WaitlistAccessSmsOptionV2:
		name := o.WaitlistAccessSmsOption.GetWaitlistAccessSmsOptionV2().GetName()

		if name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for waitlist access template")
}

func (o *SmsOption_WaitlistAccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WaitlistAccessSmsOption.GetOption().(type) {
	case *WaitlistAccessSmsOption_WaitlistAccessSmsOptionV1:
		return o.WaitlistAccessSmsOption.GetWaitlistAccessSmsOptionV1().GetTemplateVersion()
	case *WaitlistAccessSmsOption_WaitlistAccessSmsOptionV2:
		return o.WaitlistAccessSmsOption.GetWaitlistAccessSmsOptionV2().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_VkycWithLimitSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycWithLimitSmsOption.GetSmsType()
}

func (o *SmsOption_VkycWithLimitSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc with limit sms option is nil")
	}
	switch o.VkycWithLimitSmsOption.GetOption().(type) {
	case *VkycWithLimitSmsOption_VkycWithLimitSmsOptionV1:
		name := o.VkycWithLimitSmsOption.GetVkycWithLimitSmsOptionV1().GetName()

		if name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc with limit template")
}

func (o *SmsOption_VkycWithLimitSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycWithLimitSmsOption.GetOption().(type) {
	case *VkycWithLimitSmsOption_VkycWithLimitSmsOptionV1:
		return o.VkycWithLimitSmsOption.GetVkycWithLimitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_FiniteCodeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FiniteCodeSmsOption.GetSmsType()
}

func (o *SmsOption_FiniteCodeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("finite code sms option is nil")
	}
	switch o.FiniteCodeSmsOption.GetOption().(type) {
	case *FiniteCodeSmsOption_FiniteCodeSmsOptionV1:
		name := o.FiniteCodeSmsOption.GetFiniteCodeSmsOptionV1().GetName()
		finiteCode := o.FiniteCodeSmsOption.GetFiniteCodeSmsOptionV1().GetFiniteCode()

		if (name == nil) || (finiteCode == "") {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for finite code template")
}

func (o *SmsOption_FiniteCodeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FiniteCodeSmsOption.GetOption().(type) {
	case *FiniteCodeSmsOption_FiniteCodeSmsOptionV1:
		return o.FiniteCodeSmsOption.GetFiniteCodeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CboFiniteCodeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CboFiniteCodeSmsOption.GetSmsType()
}

func (o *SmsOption_CboFiniteCodeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cbo finite code sms option is nil")
	}
	switch o.CboFiniteCodeSmsOption.GetOption().(type) {
	case *CboFiniteCodeSmsOption_CboFiniteCodeSmsOptionV1:
		finiteCode := o.CboFiniteCodeSmsOption.GetCboFiniteCodeSmsOptionV1().GetFiniteCode()

		if finiteCode == "" {
			return "", errors.New("mandatory param finite code missing in request")
		}
		msg := ReplaceWithTrim(template, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cbo finite code template")
}

func (o *SmsOption_CboFiniteCodeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CboFiniteCodeSmsOption.GetOption().(type) {
	case *CboFiniteCodeSmsOption_CboFiniteCodeSmsOptionV1:
		return o.CboFiniteCodeSmsOption.GetCboFiniteCodeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_VkycSixWeeksBeforeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycSixWeeksBeforeSmsOption.GetSmsType()
}

func (o *SmsOption_VkycSixWeeksBeforeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc six weeks before sms option is nil")
	}
	switch o.VkycSixWeeksBeforeSmsOption.GetOption().(type) {
	case *VkycSixWeeksBeforeSmsOption_VkycSixWeeksBeforeSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc six weeks before template")
}

func (o *SmsOption_VkycSixWeeksBeforeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycSixWeeksBeforeSmsOption.GetOption().(type) {
	case *VkycSixWeeksBeforeSmsOption_VkycSixWeeksBeforeSmsOptionV1:
		return o.VkycSixWeeksBeforeSmsOption.GetVkycSixWeeksBeforeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_VkycFourWeeksBeforeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycFourWeeksBeforeSmsOption.GetSmsType()
}

func (o *SmsOption_VkycFourWeeksBeforeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc four weeks before sms option is nil")
	}
	switch o.VkycFourWeeksBeforeSmsOption.GetOption().(type) {
	case *VkycFourWeeksBeforeSmsOption_VkycFourWeeksBeforeSmsOptionV1:
		name := o.VkycFourWeeksBeforeSmsOption.GetVkycFourWeeksBeforeSmsOptionV1().GetName()
		accountFreezeDate := o.VkycFourWeeksBeforeSmsOption.GetVkycFourWeeksBeforeSmsOptionV1().GetAccountFreezeDate()
		if (name == nil) || (accountFreezeDate == nil) {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#account_freeze_date#}", accountFreezeDate.AsTime().Format("02-01-2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc four weeks before template")
}

func (o *SmsOption_VkycFourWeeksBeforeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycFourWeeksBeforeSmsOption.GetOption().(type) {
	case *VkycFourWeeksBeforeSmsOption_VkycFourWeeksBeforeSmsOptionV1:
		return o.VkycFourWeeksBeforeSmsOption.GetVkycFourWeeksBeforeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_VkycTenDaysBeforeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycTenDaysBeforeSmsOption.GetSmsType()
}

func (o *SmsOption_VkycTenDaysBeforeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc ten days before sms option is nil")
	}
	switch o.VkycTenDaysBeforeSmsOption.GetOption().(type) {
	case *VkycTenDaysBeforeSmsOption_VkycTenDaysBeforeSmsOptionV1:
		accountFreezeDate := o.VkycTenDaysBeforeSmsOption.GetVkycTenDaysBeforeSmsOptionV1().GetAccountFreezeDate()
		if accountFreezeDate == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#account_freeze_date#}", accountFreezeDate.AsTime().Format("02-01-2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc ten days before template")
}

func (o *SmsOption_VkycTenDaysBeforeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycTenDaysBeforeSmsOption.GetOption().(type) {
	case *VkycTenDaysBeforeSmsOption_VkycTenDaysBeforeSmsOptionV1:
		return o.VkycTenDaysBeforeSmsOption.GetVkycTenDaysBeforeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CboFiniteCodeReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CboFiniteCodeReminderSmsOption.GetSmsType()
}

func (o *SmsOption_CboFiniteCodeReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cbo finite code sms option is nil")
	}
	switch o.CboFiniteCodeReminderSmsOption.GetOption().(type) {
	case *CboFiniteCodeReminderSmsOption_CboFiniteCodeReminderSmsOptionV1:
		finiteCode := o.CboFiniteCodeReminderSmsOption.GetCboFiniteCodeReminderSmsOptionV1().GetFiniteCode()

		if finiteCode == "" {
			return "", errors.New("mandatory param finite code missing in request")
		}
		msg := ReplaceWithTrim(template, "{#finite_code#}", finiteCode, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cbo finite code template")
}

func (o *SmsOption_CboFiniteCodeReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CboFiniteCodeReminderSmsOption.GetOption().(type) {
	case *CboFiniteCodeReminderSmsOption_CboFiniteCodeReminderSmsOptionV1:
		return o.CboFiniteCodeReminderSmsOption.GetCboFiniteCodeReminderSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NonCboReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NonCboReminderSmsOption.GetSmsType()
}

// nolint:dupl
func (o *SmsOption_NonCboReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("non-cbo reminder sms option is nil")
	}
	switch o.NonCboReminderSmsOption.GetOption().(type) {
	case *NonCBOReminderSmsOption_NonCboReminderSmsOptionV1:
		finiteCode := o.NonCboReminderSmsOption.GetNonCboReminderSmsOptionV1().GetFiniteCode()
		name := o.NonCboReminderSmsOption.GetNonCboReminderSmsOptionV1().GetName()
		if finiteCode == "" || name == "" {
			return "", errors.New("mandatory param finite code or name missing in request")
		}
		msg := ReplaceWithTrim(template, "{#finite_code#}", finiteCode, 1)
		msg = ReplaceWithTrim(msg, "{#name#}", name, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for non-cbo reminder template")
}

func (o *SmsOption_NonCboReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NonCboReminderSmsOption.GetOption().(type) {
	case *NonCBOReminderSmsOption_NonCboReminderSmsOptionV1:
		return o.NonCboReminderSmsOption.GetNonCboReminderSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func getMaskedLastFour(msg string) string {
	if len([]rune(msg)) < 4 {
		return "XXXXXXXX" + msg
	}
	asRunes := []rune(msg)
	return "XXXXXXXX" + string(asRunes[len(asRunes)-4:])
}

// Since DLT allows max 30 characters in a variable, this method will trim any additional chars
// we have uploaded all templates with two consecutive variables so we take max length as 60
func ReplaceWithTrim(msg, variable, val string, limit int) string {
	if len([]rune(val)) <= 60 {
		return strings.Replace(msg, variable, val, limit)
	}
	runeString := []rune(val)
	trimmedVal := runeString[:60]
	return strings.Replace(msg, variable, string(trimmedVal), limit)
}

func (o *SmsOption_PanReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.PanReminderSmsOption.GetSmsType()
}

func (o *SmsOption_PanReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("pan reminder sms option is nil")
	}
	switch o.PanReminderSmsOption.GetOption().(type) {
	case *PanReminderSmsOption_PanReminderSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for pan reminder template")
}

func (o *SmsOption_PanReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PanReminderSmsOption.GetOption().(type) {
	case *PanReminderSmsOption_PanReminderSmsOptionV1:
		return o.PanReminderSmsOption.GetPanReminderSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_EkycReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.EkycReminderSmsOption.GetSmsType()
}

func (o *SmsOption_EkycReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("ekyc reminder sms option is nil")
	}
	switch o.EkycReminderSmsOption.GetOption().(type) {
	case *EKYCReminderSmsOption_EkycReminderSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for ekyc reminder template")
}

func (o *SmsOption_EkycReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.EkycReminderSmsOption.GetOption().(type) {
	case *EKYCReminderSmsOption_EkycReminderSmsOptionV1:
		return o.EkycReminderSmsOption.GetEkycReminderSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NameMismatchUpdateSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NameMismatchUpdateSmsOption.GetSmsType()
}

func (o *SmsOption_NameMismatchUpdateSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("name mismatch update sms option is nil")
	}
	switch o.NameMismatchUpdateSmsOption.GetOption().(type) {
	case *NameMismatchUpdateSmsOption_NameMismatchUpdateSmsOptionV1:
		name := o.NameMismatchUpdateSmsOption.GetNameMismatchUpdateSmsOptionV1().GetFirstName()
		if name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for name mismatch update template")
}

func (o *SmsOption_NameMismatchUpdateSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NameMismatchUpdateSmsOption.GetOption().(type) {
	case *NameMismatchUpdateSmsOption_NameMismatchUpdateSmsOptionV1:
		return o.NameMismatchUpdateSmsOption.GetNameMismatchUpdateSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LivenessReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LivenessReminderSmsOption.GetSmsType()
}

func (o *SmsOption_LivenessReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("liveness reminder sms option is nil")
	}
	switch o.LivenessReminderSmsOption.GetOption().(type) {
	case *LivenessReminderSmsOption_LivenessReminderSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for liveness reminder template")
}

func (o *SmsOption_LivenessReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LivenessReminderSmsOption.GetOption().(type) {
	case *LivenessReminderSmsOption_LivenessReminderSmsOptionV1:
		return o.LivenessReminderSmsOption.GetLivenessReminderSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_KycValidationFailureSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.KycValidationFailureSmsOption.GetSmsType()
}

func (o *SmsOption_KycValidationFailureSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("liveness reminder sms option is nil")
	}
	switch o.KycValidationFailureSmsOption.GetOption().(type) {
	case *KYCValidationFailureSmsOption_KycValidationFailureSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for kyc validaion failure template")
}

func (o *SmsOption_KycValidationFailureSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.KycValidationFailureSmsOption.GetOption().(type) {
	case *KYCValidationFailureSmsOption_KycValidationFailureSmsOptionV1:
		return o.KycValidationFailureSmsOption.GetKycValidationFailureSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DebitCardDeliverySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardDeliverySmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardDeliverySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("liveness reminder sms option is nil")
	}
	switch o.DebitCardDeliverySmsOption.GetOption().(type) {
	case *DebitCardDeliverySmsOption_DebitCardDeliverySmsOptionV1:
		name := o.DebitCardDeliverySmsOption.GetDebitCardDeliverySmsOptionV1().GetName()
		if name == nil {
			return "", errors.New("mandatory params missing in request")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name.GetFirstName(), 1)
		msg = ReplaceWithTrim(msg, "{#carrier_partner#}", o.DebitCardDeliverySmsOption.GetDebitCardDeliverySmsOptionV1().GetCarrierPartner(), 1)
		msg = ReplaceWithTrim(msg, "{#awb#}", o.DebitCardDeliverySmsOption.GetDebitCardDeliverySmsOptionV1().GetAwb(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for kyc validaion failure template")
}

func (o *SmsOption_DebitCardDeliverySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardDeliverySmsOption.GetOption().(type) {
	case *DebitCardDeliverySmsOption_DebitCardDeliverySmsOptionV1:
		return o.DebitCardDeliverySmsOption.GetDebitCardDeliverySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetSmsType()
}

func (o *SmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit unclear beneficiary details sms option is nil")
	}
	switch o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetOption().(type) {
	case *GenericPiDebitUnclearBeneficiaryDetailsSmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOptionV1:
		txnAmount := o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetGenericPiDebitUnclearBeneficaryDetailsSmsOptionV1().GetTransactionAmount()
		accountNumberLastFour := o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetGenericPiDebitUnclearBeneficaryDetailsSmsOptionV1().GetAccountNumberLastFourDigits()
		txnTimestamp := o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetGenericPiDebitUnclearBeneficaryDetailsSmsOptionV1().GetTransactionTimestamp()

		if accountNumberLastFour == "" || len(accountNumberLastFour) < 4 || txnTimestamp == nil || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#txn_amount#}", money.ToDisplayStringInIndianFormatWithCurrencyCode(txnAmount, 2), 1)
		msg = ReplaceWithTrim(msg, "{#account_no_last_four#}", getMaskedLastFour(accountNumberLastFour), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#phone_number#}", SupportPhone, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for generic pi debit unclear beneficiary details template")
}

func (o *SmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetOption().(type) {
	case *GenericPiDebitUnclearBeneficiaryDetailsSmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOptionV1:
		return o.GenericPiDebitUnclearBeneficaryDetailsSmsOption.GetGenericPiDebitUnclearBeneficaryDetailsSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CommunityLoginOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CommunityLoginOtpSmsOption.GetSmsType()
}

func (o *SmsOption_CommunityLoginOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CommunityLoginOtpSmsOption.GetOption().(type) {
	case *CommunityLoginOtpSmsOption_CommunityLoginOtpSmsOptionV1:
		return o.CommunityLoginOtpSmsOption.GetCommunityLoginOtpSmsOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *SmsOption_CommunityLoginOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("no onboarding sms option is nil")
	}
	switch o.CommunityLoginOtpSmsOption.GetOption().(type) {
	case *CommunityLoginOtpSmsOption_CommunityLoginOtpSmsOptionV1:
		otp := o.CommunityLoginOtpSmsOption.GetCommunityLoginOtpSmsOptionV1().GetOtp()
		if otp == "" {
			return "", errors.New("missing template parameters")
		}
		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for community otp template")
	}
}

func (o *SmsOption_CardOutForDeliverySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CardOutForDeliverySmsOption.GetSmsType()
}

func (o *SmsOption_CardOutForDeliverySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("card out for delivery sms option is nil")
	}
	switch o.CardOutForDeliverySmsOption.GetOption().(type) {
	case *CardOutForDeliverySmsOption_CardOutForDeliverySmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}",
			o.CardOutForDeliverySmsOption.GetCardOutForDeliverySmsOptionV1().GetName().GetFirstName(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for card out for delivery template")
}

func (o *SmsOption_CardOutForDeliverySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CardOutForDeliverySmsOption.GetOption().(type) {
	case *CardOutForDeliverySmsOption_CardOutForDeliverySmsOptionV1:
		return o.CardOutForDeliverySmsOption.GetCardOutForDeliverySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CardDeliveryDelaySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CardDeliveryDelaySmsOption.GetSmsType()
}

func (o *SmsOption_CardDeliveryDelaySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("card delivery delay sms option is nil")
	}
	switch o.CardDeliveryDelaySmsOption.GetOption().(type) {
	case *CardDeliveryDelaySmsOption_CardDeliveryDelaySmsOptionV1:
		msg := ReplaceWithTrim(template, "{#first_name#}",
			o.CardDeliveryDelaySmsOption.GetCardDeliveryDelaySmsOptionV1().GetName().GetFirstName(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for card delivery delay template")
}

func (o *SmsOption_CardDeliveryDelaySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CardDeliveryDelaySmsOption.GetOption().(type) {
	case *CardDeliveryDelaySmsOption_CardDeliveryDelaySmsOptionV1:
		return o.CardDeliveryDelaySmsOption.GetCardDeliveryDelaySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CardDispatchTimelineInfoSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CardDispatchTimelineInfoSmsOption.GetSmsType()
}

func (o *SmsOption_CardDispatchTimelineInfoSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("card dispatch info sms option is nil")
	}
	switch o.CardDispatchTimelineInfoSmsOption.GetOption().(type) {
	case *CardDispatchTimelineInfoSmsOption_CardDispatchTimelineInfoSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for card dispatch info template")
}

func (o *SmsOption_CardDispatchTimelineInfoSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CardDispatchTimelineInfoSmsOption.GetOption().(type) {
	case *CardDispatchTimelineInfoSmsOption_CardDispatchTimelineInfoSmsOptionV1:
		return o.CardDispatchTimelineInfoSmsOption.GetCardDispatchTimelineInfoSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_OnboardingKycCompleteSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.OnboardingKycCompleteSmsOption.GetSmsType()
}

func (o *SmsOption_OnboardingKycCompleteSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("onboarding kyc sms option is nil")
	}
	switch o.OnboardingKycCompleteSmsOption.GetOption().(type) {
	case *OnboardingKYCCompleteSmsOption_OnboardingKycCompleteSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for onboarding kyc template")
}

func (o *SmsOption_OnboardingKycCompleteSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.OnboardingKycCompleteSmsOption.GetOption().(type) {
	case *OnboardingKYCCompleteSmsOption_OnboardingKycCompleteSmsOptionV1:
		return o.OnboardingKycCompleteSmsOption.GetOnboardingKycCompleteSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_FitSmartDepositAddFundsSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FitSmartDepositAddFundsSmsOption.GetSmsType()
}

// nolint:dupl
func (o *SmsOption_FitSmartDepositAddFundsSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("FIT Add funds sms option is nil")
	}
	switch o.FitSmartDepositAddFundsSmsOption.GetOption().(type) {
	case *FITSmartDepositAddFundsSMSOption_OptionV1:
		opt := o.FitSmartDepositAddFundsSmsOption.GetOptionV1()
		smartDepositAccNo := getMaskedLastFour(opt.GetSmartDepositAccNo())
		savingsAccNo := getMaskedLastFour(opt.GetSavingsAccNo())
		ruleName := opt.GetRuleName()
		execCount := opt.GetExecCount()
		execDate := opt.GetExecDate()
		deposit_amt := opt.GetDepositAmount()
		// pluralSuffix variable is not whitelisted. Commenting untill the variable is added in the whitelisted template.
		// var pluralSuffix string
		// if execCount > 1 {
		// 	pluralSuffix = "s"
		// }

		msg := ReplaceWithTrim(template, "{#sd_acc_no_last_four#}", smartDepositAccNo, 1)
		msg = ReplaceWithTrim(msg, "{#savings_acc_no_last_four#}", savingsAccNo, 1)
		msg = ReplaceWithTrim(msg, "{#rule_name#}", ruleName, 1)
		msg = ReplaceWithTrim(msg, "{#count#}", fmt.Sprint(execCount), 1)
		// msg = ReplaceWithTrim(msg, "{#plural_suffix#}", pluralSuffix, 1)
		msg = ReplaceWithTrim(msg, "{#execution_date#}", execDate.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#deposit_amt#}", money.ToDisplayStringWithINRSymbol(deposit_amt), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for FIT Add funds SMS template")
}

func (o *SmsOption_FitSmartDepositAddFundsSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FitSmartDepositAddFundsSmsOption.GetOption().(type) {
	case *FITSmartDepositAddFundsSMSOption_OptionV1:
		return o.FitSmartDepositAddFundsSmsOption.GetOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CashWithdrawalAtmFallbackSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CashWithdrawalAtmFallbackSmsOption.GetSmsType()
}

func (o *SmsOption_CashWithdrawalAtmFallbackSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cash withdrawal atm sms option is nil")
	}
	switch o.CashWithdrawalAtmFallbackSmsOption.GetOption().(type) {
	case *CashWithdrawalAtmFallbackSmsOption_CashWithdrawalAtmSmsOptionV1:
		withdrawnAmt := o.CashWithdrawalAtmFallbackSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetWithdrawnAmount()
		txnTimestamp := o.CashWithdrawalAtmFallbackSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetTransactionTimestamp()
		if withdrawnAmt == nil || txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))
		msg := ReplaceWithTrim(template, "{#withdraw_amount#}", money.ToDisplayStringWithINRSymbol(withdrawnAmt), 1)
		msg = ReplaceWithTrim(msg, "{#time#}", txnTimestamp.AsTime().In(datetime.IST).Format("15:04"), 1)
		msg = ReplaceWithTrim(msg, "{#date#}", txnTimestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for cash withdrawal atm template")
}

func (o *SmsOption_CashWithdrawalAtmFallbackSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CashWithdrawalAtmFallbackSmsOption.GetOption().(type) {
	case *CashWithdrawalAtmFallbackSmsOption_CashWithdrawalAtmSmsOptionV1:
		return o.CashWithdrawalAtmFallbackSmsOption.GetCashWithdrawalAtmSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_PosDebitFallbackSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.PosDebitFallbackSmsOption.GetSmsType()
}

func (o *SmsOption_PosDebitFallbackSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("pos debit sms option is nil")
	}
	switch o.PosDebitFallbackSmsOption.GetOption().(type) {
	case *PosDebitFallbackSmsOption_PosDebitSmsOptionV1:
		txnTimestamp := o.PosDebitFallbackSmsOption.GetPosDebitSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.PosDebitFallbackSmsOption.GetPosDebitSmsOptionV1().GetTransactionAmount()
		if txnTimestamp == nil || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#time#}", txnTimestamp.AsTime().In(datetime.IST).Format("15:04"), 1)
		msg = ReplaceWithTrim(msg, "{#date#}", txnTimestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#var1#}", "", 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for pos debit template")
}

func (o *SmsOption_PosDebitFallbackSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PosDebitFallbackSmsOption.GetOption().(type) {
	case *PosDebitFallbackSmsOption_PosDebitSmsOptionV1:
		return o.PosDebitFallbackSmsOption.GetPosDebitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NeftDebitFallbackSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NeftDebitFallbackSmsOption.GetSmsType()
}

func (o *SmsOption_NeftDebitFallbackSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("neft debit sms option is nil")
	}
	switch o.NeftDebitFallbackSmsOption.GetOption().(type) {
	case *NeftDebitFallbackSmsOption_NeftDebitSmsOptionV1:
		firstName := o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetName().GetFirstName()
		paymentMode := o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetPaymentMode()
		receiverName := o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetReceiverName().ToString()
		txnTimestamp := o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetTransactionTimestamp()
		refNumber := o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetReferenceNumber()
		txnAmount := o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetTransactionAmount()
		if firstName == "" || paymentMode == "" || receiverName == "" || txnTimestamp == nil ||
			refNumber == "" || txnAmount == nil {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		msg = ReplaceWithTrim(msg, "{#var1#}", "", 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for neft debit atm template")
}

func (o *SmsOption_NeftDebitFallbackSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NeftDebitFallbackSmsOption.GetOption().(type) {
	case *NeftDebitFallbackSmsOption_NeftDebitSmsOptionV1:
		return o.NeftDebitFallbackSmsOption.GetNeftDebitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NeftCreditFallbackSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NeftCreditFallbackSmsOption.GetSmsType()
}

func (o *SmsOption_NeftCreditFallbackSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("neft credit sms option is nil")
	}
	switch o.NeftCreditFallbackSmsOption.GetOption().(type) {
	case *NeftCreditFallbackSmsOption_NeftCreditSmsOptionV1:
		firstName := o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetName().GetFirstName()
		senderName := o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetSenderName().GetFirstName()
		paymentMode := o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetPaymentMode()
		txnTimestamp := o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetTransactionAmount()
		refNumber := o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetReferenceNumber()
		if firstName == "" {
			return "", errors.New("mandatory params missing in request -- firstName")
		}
		if paymentMode == "" {
			return "", errors.New("mandatory params missing in request -- paymentMode")
		}
		if senderName == "" {
			return "", errors.New("mandatory params missing in request -- senderName")
		}
		if txnAmount == nil {
			return "", errors.New("mandatory params missing in request -- txnAmount")
		}
		if txnTimestamp == nil {
			return "", errors.New("mandatory params missing in request -- txnTimestamp")
		}
		if refNumber == "" {
			return "", errors.New("mandatory params missing in request -- refNumber")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		msg = ReplaceWithTrim(msg, "{#sender_name#}", senderName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		msg = ReplaceWithTrim(msg, "{#var1#}", "", 1)
		msg = ReplaceWithTrim(msg, "{#var2#}", "", 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for neft credit atm template")
}

func (o *SmsOption_NeftCreditFallbackSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NeftCreditFallbackSmsOption.GetOption().(type) {
	case *NeftCreditFallbackSmsOption_NeftCreditSmsOptionV1:
		return o.NeftCreditFallbackSmsOption.GetNeftCreditSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RtgsDebitFallbackSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RtgsDebitFallbackSmsOption.GetSmsType()
}

func (o *SmsOption_RtgsDebitFallbackSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("unsuccessful atm reversal credit sms option is nil")
	}
	switch o.RtgsDebitFallbackSmsOption.GetOption().(type) {
	case *RtgsDebitFallbackSmsOption_RtgsDebitSmsOptionV1:
		txnTimestamp := o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetTransactionTimestamp()
		txnAmount := o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetTransactionAmount()
		firstName := o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetName().GetFirstName()
		receiverName := o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetReceiverName().ToString()
		refNumber := o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetReferenceNumber()
		paymentMode := o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetPaymentMode()
		if refNumber == "" || txnTimestamp == nil || txnAmount == nil || firstName == "" ||
			receiverName == "" {
			return "", errors.New("mandatory params missing in request")
		}
		txnTimestamp = timestamppb.New(txnTimestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		msg = ReplaceWithTrim(msg, "{#txn_amount#}", money.ToDisplayStringWithINRSymbol(txnAmount), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", txnTimestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		msg = ReplaceWithTrim(msg, "{#reciever_name#}", receiverName, 1)
		msg = ReplaceWithTrim(msg, "{#reference_no#}", refNumber, 1)
		msg = ReplaceWithTrim(msg, "{#payment_mode#}", paymentMode, 1)
		msg = ReplaceWithTrim(msg, "{#var1#}", "", 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for unsuccessful atm reversal credit template")
}

func (o *SmsOption_RtgsDebitFallbackSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RtgsDebitFallbackSmsOption.GetOption().(type) {
	case *RtgsDebitFallbackSmsOption_RtgsDebitSmsOptionV1:
		return o.RtgsDebitFallbackSmsOption.GetRtgsDebitSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_InterestPaidInSbFallbackSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.InterestPaidInSbFallbackSmsOption.GetSmsType()
}

func (o *SmsOption_InterestPaidInSbFallbackSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.InterestPaidInSbFallbackSmsOption.GetOption().(type) {
	case *InterestPaidInSBFallbackSmsOption_InterestPaidInSbSmsOptionV1:
		interestAmount := o.InterestPaidInSbFallbackSmsOption.GetInterestPaidInSbSmsOptionV1().GetInterestAmount()
		accountNumber := o.InterestPaidInSbFallbackSmsOption.GetInterestPaidInSbSmsOptionV1().GetAccountNumberLastFourDigits()
		timestamp := o.InterestPaidInSbFallbackSmsOption.GetInterestPaidInSbSmsOptionV1().GetTimestamp()

		if interestAmount == nil || accountNumber == "" || len(accountNumber) < 4 ||
			timestamp == nil {
			return "", errors.New("mandatory params missing in request")
		}
		timestamp = timestamppb.New(timestamp.AsTime().In(datetime.IST))

		msg := ReplaceWithTrim(template, "{#interest_amt#}", money.ToDisplayStringWithINRSymbol(interestAmount), 1)
		msg = ReplaceWithTrim(msg, "{#last_four_acct_no#}", getMaskedLastFour(accountNumber), 1)
		msg = ReplaceWithTrim(msg, "{#date_time#}", timestamp.AsTime().In(datetime.IST).Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_InterestPaidInSbFallbackSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.InterestPaidInSbFallbackSmsOption.GetOption().(type) {
	case *InterestPaidInSBFallbackSmsOption_InterestPaidInSbSmsOptionV1:
		return o.InterestPaidInSbFallbackSmsOption.GetInterestPaidInSbSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateReceivedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateReceivedSmsOption.GetSmsType()
}

func (o *SmsOption_MandateReceivedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateReceivedSmsOption.GetOption().(type) {
	case *MandateReceivedSmsOption_MandateReceivedV1:

		mandateAmount := o.MandateReceivedSmsOption.GetMandateReceivedV1().GetMandateAmount()
		PayeeName := o.MandateReceivedSmsOption.GetMandateReceivedV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", PayeeName.ToFirstNameLastNameString(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateReceivedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateReceivedSmsOption.GetOption().(type) {
	case *MandateReceivedSmsOption_MandateReceivedV1:
		return o.MandateReceivedSmsOption.GetMandateReceivedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateApprovedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateApprovedSmsOption.GetSmsType()
}

//nolint:dupl
func (o *SmsOption_MandateApprovedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateApprovedSmsOption.GetOption().(type) {
	case *MandateApprovedSmsOption_MandateApprovedV1:

		mandateAmount := o.MandateApprovedSmsOption.GetMandateApprovedV1().GetMandateAmount()
		mandateStartDate := o.MandateApprovedSmsOption.GetMandateApprovedV1().GetMandateStartDate()
		payeeId := o.MandateApprovedSmsOption.GetMandateApprovedV1().GetPayeeId()
		mandateFrequency := o.MandateApprovedSmsOption.GetMandateApprovedV1().GetMandateFrequency()
		payeeName := o.MandateApprovedSmsOption.GetMandateApprovedV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToString(), 1)
		msg = ReplaceWithTrim(msg, "{#MandateStartDate#}", mandateStartDate, 1)
		msg = ReplaceWithTrim(msg, "{#PayeeId#}", payeeId, 1)
		msg = ReplaceWithTrim(msg, "{#MandateFrequency#}", mandateFrequency, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateApprovedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateApprovedSmsOption.GetOption().(type) {
	case *MandateApprovedSmsOption_MandateApprovedV1:
		return o.MandateApprovedSmsOption.GetMandateApprovedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateDeclinedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateDeclinedSmsOption.GetSmsType()
}

//nolint:dupl
func (o *SmsOption_MandateDeclinedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateDeclinedSmsOption.GetOption().(type) {
	case *MandateDeclinedSmsOption_MandateDeclinedV1:

		mandateAmount := o.MandateDeclinedSmsOption.GetMandateDeclinedV1().GetMandateAmount()
		mandateStartDate := o.MandateDeclinedSmsOption.GetMandateDeclinedV1().GetMandateExecutionDate()
		payeeId := o.MandateDeclinedSmsOption.GetMandateDeclinedV1().GetPayeeId()
		mandateFrequency := o.MandateDeclinedSmsOption.GetMandateDeclinedV1().GetMandateFrequency()
		payeeName := o.MandateDeclinedSmsOption.GetMandateDeclinedV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToString(), 1)
		msg = ReplaceWithTrim(msg, "{#MandateExecutionDate#}", mandateStartDate, 1)
		msg = ReplaceWithTrim(msg, "{#PayeeId#}", payeeId, 1)
		msg = ReplaceWithTrim(msg, "{#MandateFrequency#}", mandateFrequency, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateDeclinedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateDeclinedSmsOption.GetOption().(type) {
	case *MandateDeclinedSmsOption_MandateDeclinedV1:
		return o.MandateDeclinedSmsOption.GetMandateDeclinedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateCreatedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateCreatedSmsOption.GetSmsType()
}

func (o *SmsOption_MandateCreatedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateCreatedSmsOption.GetOption().(type) {
	case *MandateCreatedSmsOption_MandateCreatedV1:

		mandateAmount := o.MandateCreatedSmsOption.GetMandateCreatedV1().GetMandateAmount()
		payeeName := o.MandateCreatedSmsOption.GetMandateCreatedV1().GetPayeeName()
		payerName := o.MandateCreatedSmsOption.GetMandateCreatedV1().GetPayerName()
		mandateFrequency := o.MandateCreatedSmsOption.GetMandateCreatedV1().GetMandateFrequency()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToFirstNameLastNameString(), 1)
		msg = ReplaceWithTrim(msg, "{#PayerName#}", payerName.ToFirstNameLastNameString(), 1)
		msg = ReplaceWithTrim(msg, "{#MandateFrequency#}", mandateFrequency, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateCreatedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateCreatedSmsOption.GetOption().(type) {
	case *MandateCreatedSmsOption_MandateCreatedV1:
		return o.MandateCreatedSmsOption.GetMandateCreatedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateExecutionSuccessfulSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateExecutionSuccessfulSmsOption.GetSmsType()
}

//nolint:dupl
func (o *SmsOption_MandateExecutionSuccessfulSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateExecutionSuccessfulSmsOption.GetOption().(type) {
	case *MandateExecutionSuccessfulSmsOption_MandateExecutionSuccessfulV1:

		mandateAmount := o.MandateExecutionSuccessfulSmsOption.GetMandateExecutionSuccessfulV1().GetMandateAmount()
		mandateExecutionDate := o.MandateExecutionSuccessfulSmsOption.GetMandateExecutionSuccessfulV1().GetMandateExecutionDate()
		payeeId := o.MandateExecutionSuccessfulSmsOption.GetMandateExecutionSuccessfulV1().GetPayeeId()
		payeeName := o.MandateExecutionSuccessfulSmsOption.GetMandateExecutionSuccessfulV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToString(), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeId#}", payeeId, 1)
		msg = ReplaceWithTrim(msg, "{#MandateExecutionDate#}", mandateExecutionDate, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateExecutionSuccessfulSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateExecutionSuccessfulSmsOption.GetOption().(type) {
	case *MandateExecutionSuccessfulSmsOption_MandateExecutionSuccessfulV1:
		return o.MandateExecutionSuccessfulSmsOption.GetMandateExecutionSuccessfulV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateExecutionFailedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateExecutionFailedSmsOption.GetSmsType()
}

//nolint:dupl
func (o *SmsOption_MandateExecutionFailedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateExecutionFailedSmsOption.GetOption().(type) {
	case *MandateExecutionFailedSmsOption_MandateExecutionFailedV1:

		mandateAmount := o.MandateExecutionFailedSmsOption.GetMandateExecutionFailedV1().GetMandateAmount()
		mandateExecutionDate := o.MandateExecutionFailedSmsOption.GetMandateExecutionFailedV1().GetMandateExecutionDate()
		payeeId := o.MandateExecutionFailedSmsOption.GetMandateExecutionFailedV1().GetPayeeId()
		payeeName := o.MandateExecutionFailedSmsOption.GetMandateExecutionFailedV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeId#}", payeeId, 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToString(), 1)
		msg = ReplaceWithTrim(msg, "{#MandateExecutionDate#}", mandateExecutionDate, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateExecutionFailedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateExecutionFailedSmsOption.GetOption().(type) {
	case *MandateExecutionFailedSmsOption_MandateExecutionFailedV1:
		return o.MandateExecutionFailedSmsOption.GetMandateExecutionFailedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateRevokedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateRevokedSmsOption.GetSmsType()
}

func (o *SmsOption_MandateRevokedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateRevokedSmsOption.GetOption().(type) {
	case *MandateRevokedSmsOption_MandateRevokedV1:

		mandateAmount := o.MandateRevokedSmsOption.GetMandateRevokedV1().GetMandateAmount()
		payeeName := o.MandateRevokedSmsOption.GetMandateRevokedV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToFirstNameLastNameString(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateRevokedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateRevokedSmsOption.GetOption().(type) {
	case *MandateRevokedSmsOption_MandateRevokedV1:
		return o.MandateRevokedSmsOption.GetMandateRevokedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateModifiedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateModifiedSmsOption.GetSmsType()
}

func (o *SmsOption_MandateModifiedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("generic pi debit sms option is nil")
	}
	switch o.MandateModifiedSmsOption.GetOption().(type) {
	case *MandateModifiedSmsOption_MandateModifiedV1:

		mandateAmount := o.MandateModifiedSmsOption.GetMandateModifiedV1().GetMandateAmount()
		payeeName := o.MandateModifiedSmsOption.GetMandateModifiedV1().GetPayeeName()

		msg := ReplaceWithTrim(template, "{#MandateAmount#}", money.ToDisplayStringWithINRSymbol(mandateAmount), 1)
		msg = ReplaceWithTrim(msg, "{#PayeeName#}", payeeName.ToFirstNameLastNameString(), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for interest paind in sb template")
}

func (o *SmsOption_MandateModifiedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateModifiedSmsOption.GetOption().(type) {
	case *MandateModifiedSmsOption_MandateModifiedV1:
		return o.MandateModifiedSmsOption.GetMandateModifiedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ManualLivenessPassedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ManualLivenessPassedSmsOption.GetSmsType()
}

func (o *SmsOption_ManualLivenessPassedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ManualLivenessPassedSmsOption.GetOption().(type) {
	case *ManualLivenessPassedSmsOption_ManualLivenessPassedV1:
		return o.ManualLivenessPassedSmsOption.GetManualLivenessPassedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ManualLivenessPassedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("manual livenesspassed sms in nil")
	}
	switch o.ManualLivenessPassedSmsOption.GetOption().(type) {
	case *ManualLivenessPassedSmsOption_ManualLivenessPassedV1:
		name := o.ManualLivenessPassedSmsOption.GetManualLivenessPassedV1().GetName().ToString()
		if name == "" {
			return "", errors.New("missing temmplate parameters")
		}
		msg := ReplaceWithTrim(template, "{#first_name#}", name, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for manual liveness template")
}

func (o *SmsOption_TransactionReversedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.TransactionReversedSmsOption.GetSmsType()
}

func (o *SmsOption_TransactionReversedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.TransactionReversedSmsOption.GetOption().(type) {
	case *TransactionReversedSmsOption_TransactionReversedV1:
		return o.TransactionReversedSmsOption.GetTransactionReversedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_TransactionReversedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("transaction reversed sms option is nil")
	}
	switch o.TransactionReversedSmsOption.GetOption().(type) {
	case *TransactionReversedSmsOption_TransactionReversedV1:
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(o.TransactionReversedSmsOption.GetTransactionReversedV1().GetAmount()), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Transaction reversed template")
}

func (o *SmsOption_SiCreatedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SiCreatedSmsOption.GetSmsType()
}

func (o *SmsOption_SiCreatedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SiCreatedSmsOption.GetOption().(type) {
	case *SICreatedSmsOption_SiCreatedV1:
		return o.SiCreatedSmsOption.GetSiCreatedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SiCreatedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("si creation sms option is nil")
	}
	switch o.SiCreatedSmsOption.GetOption().(type) {
	case *SICreatedSmsOption_SiCreatedV1:
		amount, err := money.ToString(o.SiCreatedSmsOption.GetSiCreatedV1().GetSiAmount(), 2)
		if err != nil {
			return "", fmt.Errorf("couldn't convert money to string. error: %w", err)
		}
		siFrequency := o.SiCreatedSmsOption.GetSiCreatedV1().GetSiFrequency()
		payeeName := o.SiCreatedSmsOption.GetSiCreatedV1().GetPayeeName().ToFirstNameLastNameString()

		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#frequency#}", siFrequency, 1)
		msg = ReplaceWithTrim(msg, "{#payee_name#}", payeeName, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for si created template")
}

func (o *SmsOption_SiDeclinedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SiDeclinedSmsOption.GetSmsType()
}

func (o *SmsOption_SiDeclinedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SiDeclinedSmsOption.GetOption().(type) {
	case *SIDeclinedSmsOption_SiDeclinedV1:
		return o.SiDeclinedSmsOption.GetSiDeclinedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SiDeclinedSmsOption) GetActualMessage(template string) (string, error) { // nolint:dupl
	if o == nil {
		return "", fmt.Errorf("si declined sms option is nil")
	}
	switch o.SiDeclinedSmsOption.GetOption().(type) {
	case *SIDeclinedSmsOption_SiDeclinedV1:
		amount, err := money.ToString(o.SiDeclinedSmsOption.GetSiDeclinedV1().GetSiAmount(), 2)
		if err != nil {
			return "", fmt.Errorf("couldn't convert money to string. error: %w", err)
		}
		payeeName := o.SiDeclinedSmsOption.GetSiDeclinedV1().GetPayeeName().ToFirstNameLastNameString()

		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#payee_name#}", payeeName, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for si declined template")
}

func (o *SmsOption_SiExecutionSuccessfulSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SiExecutionSuccessfulSmsOption.GetSmsType()
}

func (o *SmsOption_SiExecutionSuccessfulSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SiExecutionSuccessfulSmsOption.GetOption().(type) {
	case *SIExecutionSuccessfulSmsOption_SiExecutionSuccessfulV1:
		return o.SiExecutionSuccessfulSmsOption.GetSiExecutionSuccessfulV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SiExecutionSuccessfulSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("si execution sms option is nil")
	}
	switch o.SiExecutionSuccessfulSmsOption.GetOption().(type) {
	case *SIExecutionSuccessfulSmsOption_SiExecutionSuccessfulV1:
		amount, err := money.ToString(o.SiExecutionSuccessfulSmsOption.GetSiExecutionSuccessfulV1().GetSiAmount(), 2)
		if err != nil {
			return "", fmt.Errorf("couldn't convert money to string. error: %w", err)
		}
		payeeName := o.SiExecutionSuccessfulSmsOption.GetSiExecutionSuccessfulV1().GetPayeeName().ToFirstNameLastNameString()
		executionDate := o.SiExecutionSuccessfulSmsOption.GetSiExecutionSuccessfulV1().GetSiExecutionDate()

		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#payee_name#}", payeeName, 1)
		msg = ReplaceWithTrim(msg, "{#execution_date#}", executionDate, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for si execution successful template")
}

func (o *SmsOption_SiExecutionFailedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.SiExecutionFailedSmsOption.GetSmsType()
}

func (o *SmsOption_SiExecutionFailedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.SiExecutionFailedSmsOption.GetOption().(type) {
	case *SIExecutionFailedSmsOption_SiExecutionFailedV1:
		return o.SiExecutionFailedSmsOption.GetSiExecutionFailedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_SiExecutionFailedSmsOption) GetActualMessage(template string) (string, error) { // nolint:dupl
	if o == nil {
		return "", fmt.Errorf("si execution failed sms option is nil")
	}
	switch o.SiExecutionFailedSmsOption.GetOption().(type) {
	case *SIExecutionFailedSmsOption_SiExecutionFailedV1:
		amount, err := money.ToString(o.SiExecutionFailedSmsOption.GetSiExecutionFailedV1().GetSiAmount(), 2)
		if err != nil {
			return "", fmt.Errorf("couldn't convert money to string. error: %w", err)
		}
		payeeName := o.SiExecutionFailedSmsOption.GetSiExecutionFailedV1().GetPayeeName().ToFirstNameLastNameString()

		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#payee_name#}", payeeName, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for si execution failed template")
}

func (o *SmsOption_MandateAuthorisedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateAuthorisedSmsOption.GetSmsType()
}

func (o *SmsOption_MandateAuthorisedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateAuthorisedSmsOption.GetOption().(type) {
	case *MandateAuthorisedSmsOption_MandateAuthorisedV1:
		return o.MandateAuthorisedSmsOption.GetMandateAuthorisedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateAuthorisedSmsOption) GetActualMessage(template string) (string, error) { //nolint:dupl
	if o == nil {
		return "", fmt.Errorf("mandate authorised sms option is nil")
	}
	switch o.MandateAuthorisedSmsOption.GetOption().(type) {
	case *MandateAuthorisedSmsOption_MandateAuthorisedV1:
		amount, err := money.ToString(o.MandateAuthorisedSmsOption.GetMandateAuthorisedV1().GetAmount(), 2)
		if err != nil {
			return "", fmt.Errorf("couldn't convert money to string. error: %w", err)
		}
		payeeName := o.MandateAuthorisedSmsOption.GetMandateAuthorisedV1().GetPayeeName().ToFirstNameLastNameString()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#payeename#}", payeeName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mandate authorised template")
}

func (o *SmsOption_MandateAcceptanceSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateAcceptanceSmsOption.GetSmsType()
}

func (o *SmsOption_MandateAcceptanceSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateAcceptanceSmsOption.GetOption().(type) {
	case *MandateAcceptanceSmsOption_MandateAcceptanceV1:
		return o.MandateAcceptanceSmsOption.GetMandateAcceptanceV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateAcceptanceSmsOption) GetActualMessage(template string) (string, error) { //nolint:dupl
	if o == nil {
		return "", fmt.Errorf("mandate acceptance sms option is nil")
	}
	switch o.MandateAcceptanceSmsOption.GetOption().(type) {
	case *MandateAcceptanceSmsOption_MandateAcceptanceV1:
		amount, err := money.ToString(o.MandateAcceptanceSmsOption.GetMandateAcceptanceV1().GetAmount(), 2)
		if err != nil {
			return "", fmt.Errorf("couldn't convert money to string. error: %w", err)
		}
		payerName := o.MandateAcceptanceSmsOption.GetMandateAcceptanceV1().GetPayerName().ToFirstNameLastNameString()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#payername#}", payerName, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mandate acceptance template")
}

func (o *SmsOption_MandatePausedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandatePausedSmsOption.GetSmsType()
}

func (o *SmsOption_MandatePausedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandatePausedSmsOption.GetOption().(type) {
	case *MandatePausedSmsOption_MandatePausedV1:
		return o.MandatePausedSmsOption.GetMandatePausedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandatePausedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mandate paused sms option is nil")
	}
	switch o.MandatePausedSmsOption.GetOption().(type) {
	case *MandatePausedSmsOption_MandatePausedV1:

		payeeName := o.MandatePausedSmsOption.GetMandatePausedV1().GetPayeeName().ToFirstNameLastNameString()
		payerName := o.MandatePausedSmsOption.GetMandatePausedV1().GetPayerName().ToFirstNameLastNameString()

		msg := ReplaceWithTrim(template, "{#payeename#}", payeeName, 1)
		msg = ReplaceWithTrim(msg, "{#payername#}", payerName, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mandate paused template")
}

func (o *SmsOption_MandateUnpausedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MandateUnpausedSmsOption.GetSmsType()
}

func (o *SmsOption_MandateUnpausedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MandateUnpausedSmsOption.GetOption().(type) {
	case *MandateUnpausedSmsOption_MandateUnpausedV1:
		return o.MandateUnpausedSmsOption.GetMandateUnpausedV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MandateUnpausedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("mandate unpaused sms option is nil")
	}
	switch o.MandateUnpausedSmsOption.GetOption().(type) {
	case *MandateUnpausedSmsOption_MandateUnpausedV1:

		payeeName := o.MandateUnpausedSmsOption.GetMandateUnpausedV1().GetPayeeName().ToFirstNameLastNameString()
		msg := ReplaceWithTrim(template, "{#payeename#}", payeeName, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for mandate unpaused template")
}

func (o *SmsOption_MutualFundWithdrawalOtpOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MutualFundWithdrawalOtpOption.GetSmsType()
}

// nolint:dupl
// {#otp#} is the OTP to confirm your withdrawal from {#mutual_fund#}. Amount: {#amount#}. 82275JXpmM1 -Fi
func (o *SmsOption_MutualFundWithdrawalOtpOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Mutual fund withdrawal sms option is nil")
	}
	switch o.MutualFundWithdrawalOtpOption.GetOption().(type) {
	case *MutualFundWithdrawalOtpOption_MutualFundWithdrawalOtpV1:
		opt := o.MutualFundWithdrawalOtpOption.GetMutualFundWithdrawalOtpV1()

		otp := opt.GetOtp()
		mutualFundName := opt.GetMutualFundName()
		amount := opt.GetWithdrawnAmount()

		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		msg = ReplaceWithTrim(msg, "{#mutual_fund#}", mutualFundName, 1)
		msg = ReplaceWithTrim(msg, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mutual fund withdrawal SMS template")
}

func (o *SmsOption_MutualFundWithdrawalOtpOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MutualFundWithdrawalOtpOption.GetOption().(type) {
	case *MutualFundWithdrawalOtpOption_MutualFundWithdrawalOtpV1:
		return o.MutualFundWithdrawalOtpOption.GetMutualFundWithdrawalOtpV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MfOneTimeBuyOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MfOneTimeBuyOption.GetSmsType()
}

// nolint:dupl
// {#otp#} is the OTP to confirm your lump sum investment in {#mutual_fund#}. Amount: INR {#amount#}. 82275JXpmM1 - F
func (o *SmsOption_MfOneTimeBuyOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Mutual fund one time buy sms option is nil")
	}
	switch o.MfOneTimeBuyOption.GetOption().(type) {
	case *MutualFundOneTimeBuyOtpOption_MutualFundOneTimeBuyOtpV1:
		opt := o.MfOneTimeBuyOption.GetMutualFundOneTimeBuyOtpV1()

		otp := opt.GetOtp()
		mutualFundName := opt.GetMutualFundName()
		amount := opt.GetAmount()

		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		msg = ReplaceWithTrim(msg, "{#mutual_fund#}", mutualFundName, 1)
		msg = ReplaceWithTrim(msg, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mutual fund one time buy SMS template")
}

func (o *SmsOption_MfOneTimeBuyOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfOneTimeBuyOption.GetOption().(type) {
	case *MutualFundOneTimeBuyOtpOption_MutualFundOneTimeBuyOtpV1:
		return o.MfOneTimeBuyOption.GetMutualFundOneTimeBuyOtpV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_MfRegisterSipOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.MfRegisterSipOption.GetSmsType()
}

// nolint:dupl
// {#otp#} is the OTP to confirm your {#frequency#} investment in {#mutual_fund#}. Amount: INR {#amount#}. 82275JXpmM1 - Fi
func (o *SmsOption_MfRegisterSipOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Mutual fund register sip option is nil")
	}
	switch o.MfRegisterSipOption.GetOption().(type) {
	case *MutualFundRegisterSIPOtpOption_MutualFundRegisterSipOtpV1:
		opt := o.MfRegisterSipOption.GetMutualFundRegisterSipOtpV1()

		otp := opt.GetOtp()
		mutualFundName := opt.GetMutualFundName()
		amount := opt.GetAmount()
		frequency := opt.GetFrequency()

		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		msg = ReplaceWithTrim(msg, "{#mutual_fund#}", mutualFundName, 1)
		msg = ReplaceWithTrim(msg, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		msg = ReplaceWithTrim(msg, "{#frequency#}", frequency, 1)

		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mutual fund register sip option")
}

func (o *SmsOption_MfRegisterSipOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MfRegisterSipOption.GetOption().(type) {
	case *MutualFundRegisterSIPOtpOption_MutualFundRegisterSipOtpV1:
		return o.MfRegisterSipOption.GetMutualFundRegisterSipOtpV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ObVkycReminderOneSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ObVkycReminderOneSmsOption.GetSmsType()
}

func (o *SmsOption_ObVkycReminderOneSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Mutual fund withdrawal sms option is nil")
	}
	switch o.ObVkycReminderOneSmsOption.GetOption().(type) {
	case *OBVkycReminderOneSmsOption_ObVkycReminderOneSmsOptionV1:
		rewardAmount := strconv.Itoa(int(o.ObVkycReminderOneSmsOption.GetObVkycReminderOneSmsOptionV1().GetRewardAmount().GetUnits()))
		msg := ReplaceWithTrim(template, "{#rewardAmount#}", rewardAmount, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Mutual fund withdrawal SMS template")
}

func (o *SmsOption_ObVkycReminderOneSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ObVkycReminderOneSmsOption.GetOption().(type) {
	case *OBVkycReminderOneSmsOption_ObVkycReminderOneSmsOptionV1:
		return o.ObVkycReminderOneSmsOption.GetObVkycReminderOneSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ObVkycReminderTwoSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ObVkycReminderTwoSmsOption.GetSmsType()
}

func (o *SmsOption_ObVkycReminderTwoSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Mutual fund withdrawal sms option is nil")
	}
	switch o.ObVkycReminderTwoSmsOption.GetOption().(type) {
	case *OBVkycReminderTwoSmsOption_ObVkycReminderTwoSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for Mutual fund withdrawal SMS template")
}

func (o *SmsOption_ObVkycReminderTwoSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ObVkycReminderTwoSmsOption.GetOption().(type) {
	case *OBVkycReminderTwoSmsOption_ObVkycReminderTwoSmsOptionV1:
		return o.ObVkycReminderTwoSmsOption.GetObVkycReminderTwoSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_TodChargesDebitOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.TodChargesDebitOption.GetSmsType()
}

func (o *SmsOption_TodChargesDebitOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.TodChargesDebitOption.GetOption().(type) {
	case *TodChargesDebitOption_TodChargesDebitOptionV1:
		return o.TodChargesDebitOption.GetTodChargesDebitOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_TodChargesDebitOption) GetActualMessage(template string) (string, error) { //nolint:dupl
	if o == nil {
		return "", fmt.Errorf("Tod charges debit sms option is nil")
	}
	switch o.TodChargesDebitOption.GetOption().(type) {
	case *TodChargesDebitOption_TodChargesDebitOptionV1:
		amount := o.TodChargesDebitOption.GetTodChargesDebitOptionV1().GetTransactionAmount()
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithoutSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Tod charges debit template")
}

func (o *SmsOption_ObScreenerVerificationReminderOneOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ObScreenerVerificationReminderOneOption.GetSmsType()
}

func (o *SmsOption_ObScreenerVerificationReminderOneOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Employment Verification sms option is nil")
	}
	switch o.ObScreenerVerificationReminderOneOption.GetOption().(type) {
	case *OBScreenerVerficationReminderOneOption_ObScreenerVerificationReminderOneOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for Employment Verification SMS template")
}

func (o *SmsOption_ObScreenerVerificationReminderOneOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ObScreenerVerificationReminderOneOption.GetOption().(type) {
	case *OBScreenerVerficationReminderOneOption_ObScreenerVerificationReminderOneOptionV1:
		return o.ObScreenerVerificationReminderOneOption.GetObScreenerVerificationReminderOneOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ObScreenerVerificationReminderTwoOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ObScreenerVerificationReminderTwoOption.GetSmsType()
}

func (o *SmsOption_ObScreenerVerificationReminderTwoOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Employment Verification sms option is nil")
	}
	switch o.ObScreenerVerificationReminderTwoOption.GetOption().(type) {
	case *OBScreenerVerficationReminderTwoOption_ObScreenerVerificationReminderTwoOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for Employment Verification SMS template")
}

func (o *SmsOption_ObScreenerVerificationReminderTwoOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ObScreenerVerificationReminderTwoOption.GetOption().(type) {
	case *OBScreenerVerficationReminderTwoOption_ObScreenerVerificationReminderTwoOptionV1:
		return o.ObScreenerVerificationReminderTwoOption.GetObScreenerVerificationReminderTwoOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_EcsReturnChargesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.EcsReturnChargesSmsOption.GetSmsType()
}

func (o *SmsOption_EcsReturnChargesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.EcsReturnChargesSmsOption.GetOption().(type) {
	case *EcsReturnChargesSmsOption_EcsReturnChargesSmsOptionV1:
		return o.EcsReturnChargesSmsOption.GetEcsReturnChargesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_EcsReturnChargesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("ecs return charges sms option is nil")
	}
	switch o.EcsReturnChargesSmsOption.GetOption().(type) {
	case *EcsReturnChargesSmsOption_EcsReturnChargesSmsOptionV1:
		amount := o.EcsReturnChargesSmsOption.GetEcsReturnChargesSmsOptionV1().GetAmount()
		if amount == nil {
			return "", errors.New("mandatory params missing in request -- amount")
		}
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Ecs return charges template")
}

func (o *SmsOption_AtmDeclineFeesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.AtmDeclineFeesSmsOption.GetSmsType()
}

func (o *SmsOption_AtmDeclineFeesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AtmDeclineFeesSmsOption.GetOption().(type) {
	case *AtmDeclineFeesSmsOption_AtmDeclineFeesSmsOptionV1:
		return o.AtmDeclineFeesSmsOption.GetAtmDeclineFeesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_AtmDeclineFeesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Atm Decline Fees sms option is nil")
	}
	switch o.AtmDeclineFeesSmsOption.GetOption().(type) {
	case *AtmDeclineFeesSmsOption_AtmDeclineFeesSmsOptionV1:
		amount := o.AtmDeclineFeesSmsOption.GetAtmDeclineFeesSmsOptionV1().GetAmount()
		if amount == nil {
			return "", errors.New("mandatory params missing in request -- amount")
		}
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Atm Decline Fees template")
}

func (o *SmsOption_DuplicateCardFeeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DuplicateCardFeeSmsOption.GetSmsType()
}

func (o *SmsOption_DuplicateCardFeeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DuplicateCardFeeSmsOption.GetOption().(type) {
	case *DuplicateCardFeeSmsOption_DuplicateCardFeeSmsOptionV1:
		return o.DuplicateCardFeeSmsOption.GetDuplicateCardFeeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_DuplicateCardFeeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("DuplicateCard Fee sms option is nil")
	}
	switch o.DuplicateCardFeeSmsOption.GetOption().(type) {
	case *DuplicateCardFeeSmsOption_DuplicateCardFeeSmsOptionV1:
		amount := o.DuplicateCardFeeSmsOption.GetDuplicateCardFeeSmsOptionV1().GetAmount()
		if amount == nil {
			return "", errors.New("mandatory params missing in request -- amount")
		}
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for DuplicateCard Fee template")
}

func (o *SmsOption_AtmWithdrawalComplaintPenaltySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.AtmWithdrawalComplaintPenaltySmsOption.GetSmsType()
}

func (o *SmsOption_AtmWithdrawalComplaintPenaltySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AtmWithdrawalComplaintPenaltySmsOption.GetOption().(type) {
	case *AtmWithdrawalComplaintPenaltySmsOption_AtmWithdrawalComplaintPenaltySmsOptionV1:
		return o.AtmWithdrawalComplaintPenaltySmsOption.GetAtmWithdrawalComplaintPenaltySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_AtmWithdrawalComplaintPenaltySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Atm Withdrawal Complaint Penalty sms option is nil")
	}
	switch o.AtmWithdrawalComplaintPenaltySmsOption.GetOption().(type) {
	case *AtmWithdrawalComplaintPenaltySmsOption_AtmWithdrawalComplaintPenaltySmsOptionV1:
		amount := o.AtmWithdrawalComplaintPenaltySmsOption.GetAtmWithdrawalComplaintPenaltySmsOptionV1().GetAmount()
		if amount == nil {
			return "", errors.New("mandatory params missing in request -- amount")
		}
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Atm Withdrawal Complaint Penalty template")
}

func (o *SmsOption_InternationAtmChargesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.InternationAtmChargesSmsOption.GetSmsType()
}

func (o *SmsOption_InternationAtmChargesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.InternationAtmChargesSmsOption.GetOption().(type) {
	case *InternationAtmChargesSmsOption_InternationAtmChargesSmsOptionV1:
		return o.InternationAtmChargesSmsOption.GetInternationAtmChargesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_InternationAtmChargesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Internation Atm Charges sms option is nil")
	}
	switch o.InternationAtmChargesSmsOption.GetOption().(type) {
	case *InternationAtmChargesSmsOption_InternationAtmChargesSmsOptionV1:
		amount := o.InternationAtmChargesSmsOption.GetInternationAtmChargesSmsOptionV1().GetAmount()
		if amount == nil {
			return "", errors.New("mandatory params missing in request -- amount")
		}
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Internation Atm Charges template")
}

func (o *SmsOption_OtherBankAtmUsageChargesSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.OtherBankAtmUsageChargesSmsOption.GetSmsType()
}

func (o *SmsOption_OtherBankAtmUsageChargesSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.OtherBankAtmUsageChargesSmsOption.GetOption().(type) {
	case *OtherBankAtmUsageChargesSmsOption_OtherBankAtmUsageChargesSmsOptionV1:
		return o.OtherBankAtmUsageChargesSmsOption.GetOtherBankAtmUsageChargesSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_OtherBankAtmUsageChargesSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("Other Bank Atm Usage Charges sms option is nil")
	}
	switch o.OtherBankAtmUsageChargesSmsOption.GetOption().(type) {
	case *OtherBankAtmUsageChargesSmsOption_OtherBankAtmUsageChargesSmsOptionV1:
		amount := o.OtherBankAtmUsageChargesSmsOption.GetOtherBankAtmUsageChargesSmsOptionV1().GetAmount()
		if amount == nil {
			return "", errors.New("mandatory params missing in request -- amount")
		}
		msg := ReplaceWithTrim(template, "{#amount#}", money.ToDisplayStringWithINRSymbol(amount), 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for Other Bank Atm Usage Charges template")
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetSmsType()
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1:
		return o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc account closer more then zero balance for forty five sms is nil")
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1:
		amount := o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1().GetAmount()
		remainingDay := o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1().GetRemainingDays()
		link := o.VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1().GetLink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#remaining_day}", remainingDay, 1)
		msg = ReplaceWithTrim(msg, "{#link}", link, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc account closer more then zero balance for forty five sms template")
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetSmsType()
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1:
		return o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc account closer more then zero balance for thirty sms is nil")
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1:
		amount := o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1().GetAmount()
		remainingDay := o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1().GetRemainingDays()
		link := o.VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1().GetLink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#remaining_day}", remainingDay, 1)
		msg = ReplaceWithTrim(msg, "{#link}", link, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc account closer more then zero balance for thirty sms template")
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetSmsType()
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1:
		return o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc account closer more then zero balance for eleven sms is nil")
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1:
		amount := o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1().GetAmount()
		remainingDay := o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1().GetRemainingDays()
		link := o.VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1().GetLink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#remaining_day}", remainingDay, 1)
		msg = ReplaceWithTrim(msg, "{#link}", link, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc account closer more then zero balance for eleven sms template")
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetSmsType()
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1:
		return o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc account closer more then zero balance for five sms is nil")
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1:
		amount := o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1().GetAmount()
		remainingDay := o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1().GetRemainingDays()
		link := o.VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1().GetLink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#remaining_day}", remainingDay, 1)
		msg = ReplaceWithTrim(msg, "{#link}", link, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc account closer more then zero balance for five sms template")
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetSmsType()
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1:
		return o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc account closer more then zero balance for three sms is nil")
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption_VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1:
		amount := o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1().GetAmount()
		remainingDay := o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1().GetRemainingDays()
		link := o.VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption.GetVkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1().GetLink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#remaining_day}", remainingDay, 1)
		msg = ReplaceWithTrim(msg, "{#link}", link, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc account closer more then zero balance for three sms template")
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption.GetSmsType()
}

func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption_VkycAccountClosureMoreThenZeroBalanceOneDaySmsOptionV1:
		return o.VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption.GetVkycAccountClosureMoreThenZeroBalanceOneDaySmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("vkyc account closer more then zero balance for one sms is nil")
	}
	switch o.VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption.GetOption().(type) {
	case *VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption_VkycAccountClosureMoreThenZeroBalanceOneDaySmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for vkyc account closer more then zero balance for one sms template")
}

func (o *SmsOption_OnboardingAccountBlockDayZeroSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.OnboardingAccountBlockDayZeroSmsOption.GetSmsType()
}

func (o *SmsOption_OnboardingAccountBlockDayZeroSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.OnboardingAccountBlockDayZeroSmsOption.GetOption().(type) {
	case *OnboardingAccountBlockDayZeroSmsOption_OnboardingAccountBlockDayZeroSmsOptionV1:
		return o.OnboardingAccountBlockDayZeroSmsOption.GetOnboardingAccountBlockDayZeroSmsOptionV1().GetTemplateVersion()
	}

	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_OnboardingAccountBlockDayZeroSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("onboarding account block day zero sms option is nil")
	}
	switch o.OnboardingAccountBlockDayZeroSmsOption.GetOption().(type) {
	case *OnboardingAccountBlockDayZeroSmsOption_OnboardingAccountBlockDayZeroSmsOptionV1:
		username := o.OnboardingAccountBlockDayZeroSmsOption.GetOnboardingAccountBlockDayZeroSmsOptionV1().GetUserName()
		email := o.OnboardingAccountBlockDayZeroSmsOption.GetOnboardingAccountBlockDayZeroSmsOptionV1().GetEmail()
		msg := ReplaceWithTrim(template, "{#username#}", username, 1)
		msg = ReplaceWithTrim(msg, "{#email#}", email, 1)
		return msg, nil
	}

	return "", fmt.Errorf("no valid version found for account block day zero sms template")
}

func (o *SmsOption_OnboardingAccountUnblockSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.OnboardingAccountUnblockSmsOption.GetSmsType()
}

func (o *SmsOption_OnboardingAccountUnblockSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.OnboardingAccountUnblockSmsOption.GetOption().(type) {
	case *OnboardingAccountUnblockSmsOption_OnboardingAccountUnblockSmsOptionV1:
		return o.OnboardingAccountUnblockSmsOption.GetOnboardingAccountUnblockSmsOptionV1().GetTemplateVersion()
	}

	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_OnboardingAccountUnblockSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("onboarding account unblock sms option is nil")
	}
	switch o.OnboardingAccountUnblockSmsOption.GetOption().(type) {
	case *OnboardingAccountUnblockSmsOption_OnboardingAccountUnblockSmsOptionV1:
		username := o.OnboardingAccountUnblockSmsOption.GetOnboardingAccountUnblockSmsOptionV1().GetUserName()
		msg := ReplaceWithTrim(template, "{#username#}", username, 1)
		return msg, nil
	}

	return "", fmt.Errorf("no valid version found for account unblock sms template")
}

func (o *SmsOption_WealthAccountNomineeDeclarationOtpOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.WealthAccountNomineeDeclarationOtpOption.GetSmsType()
}

func (o *SmsOption_WealthAccountNomineeDeclarationOtpOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("wealth account nominee declaration otp sms option is nil")
	}
	switch o.WealthAccountNomineeDeclarationOtpOption.GetOption().(type) {
	case *WealthAccountNomineeDeclarationOTPOption_WealthAccountNomineeDeclarationOtpV1:
		opt := o.WealthAccountNomineeDeclarationOtpOption.GetWealthAccountNomineeDeclarationOtpV1()

		otp := opt.GetOtp()
		choice := opt.GetChoice()

		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		msg = ReplaceWithTrim(msg, "{#choice#}", choice, 1)
		return msg, nil
	default:
		return "", errors.New("invalid wealth account nominee declaration otp sms template")
	}
}

func (o *SmsOption_WealthAccountNomineeDeclarationOtpOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.WealthAccountNomineeDeclarationOtpOption.GetOption().(type) {
	case *WealthAccountNomineeDeclarationOTPOption_WealthAccountNomineeDeclarationOtpV1:
		return o.WealthAccountNomineeDeclarationOtpOption.GetWealthAccountNomineeDeclarationOtpV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_OnboardingDobAndPanDropOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.OnboardingDobAndPanDropOffSmsOption.GetSmsType()
}

func (o *SmsOption_OnboardingDobAndPanDropOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.OnboardingDobAndPanDropOffSmsOption.GetOption().(type) {
	case *OnboardingDobAndPanDropOffSmsOption_OnboardingDobAndPanDropOffSmsOptionV1:
		return o.OnboardingDobAndPanDropOffSmsOption.GetOnboardingDobAndPanDropOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_OnboardingDobAndPanDropOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("onboarding dob and pan drop off sms option is nil")
	}
	switch o.OnboardingDobAndPanDropOffSmsOption.GetOption().(type) {
	case *OnboardingDobAndPanDropOffSmsOption_OnboardingDobAndPanDropOffSmsOptionV1:
		username := o.OnboardingDobAndPanDropOffSmsOption.GetOnboardingDobAndPanDropOffSmsOptionV1().GetUserName()
		deepLink := o.OnboardingDobAndPanDropOffSmsOption.GetOnboardingDobAndPanDropOffSmsOptionV1().GetDeepLink()
		msg := ReplaceWithTrim(template, "{#var#}", username, 1)
		msg = ReplaceWithTrim(msg, "{#link#}", deepLink, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for dob and pan drop off sms template")
}

func (o *SmsOption_CreditReportDownloadOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditReportDownloadOtpSmsOption.GetSmsType()
}

func (o *SmsOption_CreditReportDownloadOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditReportDownloadOtpSmsOption.GetOption().(type) {
	case *CreditReportDownloadOtpSmsOption_CreditReportDownloadOtpSmsOptionV1:
		return o.CreditReportDownloadOtpSmsOption.GetCreditReportDownloadOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

// nolint:dupl
func (o *SmsOption_CreditReportDownloadOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit report download otp sms option is nil")
	}
	switch o.CreditReportDownloadOtpSmsOption.GetOption().(type) {
	case *CreditReportDownloadOtpSmsOption_CreditReportDownloadOtpSmsOptionV1:
		otp := o.CreditReportDownloadOtpSmsOption.GetCreditReportDownloadOtpSmsOptionV1().GetOtp()
		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		hash := o.CreditReportDownloadOtpSmsOption.GetCreditReportDownloadOtpSmsOptionV1().GetHash()
		msg = ReplaceWithTrim(msg, "{#hash#}", hash, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit report download otp sms template")
}

func (o *SmsOption_CategorySpendsExceededReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CategorySpendsExceededReminderSmsOption.GetSmsType()
}

func (o *SmsOption_CategorySpendsExceededReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CategorySpendsExceededReminderSmsOption.GetOption().(type) {
	case *CategorySpendsExceededReminderSmsOption_ReminderSmsOptionV1:
		return o.CategorySpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetTemplateVersion()
	case *CategorySpendsExceededReminderSmsOption_ReminderCategorySpendsSmsOption:
		return o.CategorySpendsExceededReminderSmsOption.GetReminderCategorySpendsSmsOption().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CategorySpendsExceededReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("category spends exceeded reminder sms option is nil")
	}
	switch o.CategorySpendsExceededReminderSmsOption.GetOption().(type) {
	case *CategorySpendsExceededReminderSmsOption_ReminderSmsOptionV1:
		category := o.CategorySpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetCategory()
		msg := ReplaceWithTrim(template, "{#category#}", category, 1)
		amount := o.CategorySpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetAmount()
		msg = ReplaceWithTrim(msg, "{#amount#}", amount, 1)
		deeplink := o.CategorySpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetDeeplink()
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		date := o.CategorySpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetDate()
		msg = ReplaceWithTrim(msg, "{#date#}", date.AsTime().Format(datetime.DATE_LAYOUT_YYYYMMDD), 1)
		return msg, nil
	case *CategorySpendsExceededReminderSmsOption_ReminderCategorySpendsSmsOption:
		category := o.CategorySpendsExceededReminderSmsOption.GetReminderCategorySpendsSmsOption().GetCategory()
		msg := ReplaceWithTrim(template, "{#category#}", category, 1)
		amount := o.CategorySpendsExceededReminderSmsOption.GetReminderCategorySpendsSmsOption().GetAmount()
		msg = ReplaceWithTrim(msg, "{#amount#}", amount, 1)
		deeplink := o.CategorySpendsExceededReminderSmsOption.GetReminderCategorySpendsSmsOption().GetDeeplink()
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		configuredAmount := o.CategorySpendsExceededReminderSmsOption.GetReminderCategorySpendsSmsOption().GetConfiguredAmount()
		msg = ReplaceWithTrim(msg, "{#configured_amount#}", configuredAmount, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for category spends exceeded reminder sms")
}

func (o *SmsOption_AmountSpendsExceededReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.AmountSpendsExceededReminderSmsOption.GetSmsType()
}

func (o *SmsOption_AmountSpendsExceededReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AmountSpendsExceededReminderSmsOption.GetOption().(type) {
	case *AmountSpendsExceededReminderSmsOption_ReminderSmsOptionV1:
		return o.AmountSpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetTemplateVersion()
	case *AmountSpendsExceededReminderSmsOption_ReminderAmountSpendsSmsOption:
		return o.AmountSpendsExceededReminderSmsOption.GetReminderAmountSpendsSmsOption().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_AmountSpendsExceededReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New(" spends exceeded reminder sms option is nil")
	}
	switch o.AmountSpendsExceededReminderSmsOption.GetOption().(type) {
	case *AmountSpendsExceededReminderSmsOption_ReminderSmsOptionV1:
		amount := o.AmountSpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetAmount()
		deeplink := o.AmountSpendsExceededReminderSmsOption.GetReminderSmsOptionV1().GetDeeplink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		return msg, nil
	case *AmountSpendsExceededReminderSmsOption_ReminderAmountSpendsSmsOption:
		amount := o.AmountSpendsExceededReminderSmsOption.GetReminderAmountSpendsSmsOption().GetAmount()
		deeplink := o.AmountSpendsExceededReminderSmsOption.GetReminderAmountSpendsSmsOption().GetDeeplink()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		return msg, nil
	}

	return "", errors.New("no valid version found for amount spends exceeded reminder sms")
}

func (o *SmsOption_CreditCardBillPaymentDueDateReminderSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardBillPaymentDueDateReminderSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardBillPaymentDueDateReminderSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardBillPaymentDueDateReminderSmsOption.GetOption().(type) {
	case *CreditCardBillPaymentDueDateReminderSmsOption_ReminderSmsOptionV1:
		return o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderSmsOptionV1().GetTemplateVersion()
	case *CreditCardBillPaymentDueDateReminderSmsOption_ReminderCcDueDateSmsOption:
		return o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderCcDueDateSmsOption().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardBillPaymentDueDateReminderSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("credit card due date reminder sms option is nil")
	}
	switch o.CreditCardBillPaymentDueDateReminderSmsOption.GetOption().(type) {
	case *CreditCardBillPaymentDueDateReminderSmsOption_ReminderSmsOptionV1:
		amount := o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderSmsOptionV1().GetAmount()
		date := o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderSmsOptionV1().GetDate()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#date#}", date.AsTime().Format("January 2, 2006"), 1)
		return msg, nil
	case *CreditCardBillPaymentDueDateReminderSmsOption_ReminderCcDueDateSmsOption:
		amount := o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderCcDueDateSmsOption().GetAmount()
		deeplink := o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderCcDueDateSmsOption().GetDeeplink()
		date := o.CreditCardBillPaymentDueDateReminderSmsOption.GetReminderCcDueDateSmsOption().GetDate()
		msg := ReplaceWithTrim(template, "{#amount#}", amount, 1)
		msg = ReplaceWithTrim(msg, "{#deeplink#}", deeplink, 1)
		msg = ReplaceWithTrim(msg, "{#date#}", date.AsTime().Format("January 2, 2006"), 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for credit card due date reminder sms")
}

func (o *SmsOption_CreditCardEmiCreatedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiCreatedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardEmiCreatedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiCreatedSmsOption.GetOption().(type) {
	case *CreditCardEmiCreatedSmsOption_CreditCardEmiCreatedSmsOptionV1:
		return o.CreditCardEmiCreatedSmsOption.GetCreditCardEmiCreatedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardEmiCreatedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("cc emi created sms option is nil")
	}
	switch o.CreditCardEmiCreatedSmsOption.GetOption().(type) {
	case *CreditCardEmiCreatedSmsOption_CreditCardEmiCreatedSmsOptionV1:
		firstName := o.CreditCardEmiCreatedSmsOption.GetCreditCardEmiCreatedSmsOptionV1().GetCustomerName().GetFirstName()
		msg := ReplaceWithTrim(template, "{#first_name#}", firstName, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for pl loan agreement otp sms")
}

func (o *SmsOption_CreditCardEmiClosedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiClosedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardEmiClosedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiClosedSmsOption.GetOption().(type) {
	case *CreditCardEmiClosedSmsOption_CreditCardEmiClosedSmsOptionV1:
		return o.CreditCardEmiClosedSmsOption.GetCreditCardEmiClosedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardEmiClosedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("cc emi closed sms option is nil")
	}
	switch o.CreditCardEmiClosedSmsOption.GetOption().(type) {
	case *CreditCardEmiClosedSmsOption_CreditCardEmiClosedSmsOptionV1:
		merchantName := o.CreditCardEmiClosedSmsOption.GetCreditCardEmiClosedSmsOptionV1().GetMerchantName()
		msg := ReplaceWithTrim(template, "{#merchant_name#}", merchantName, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for pl loan agreement otp sms")
}

func (o *SmsOption_CreditCardEmiPreClosedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiPreClosedSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardEmiPreClosedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiPreClosedSmsOption.GetOption().(type) {
	case *CreditCardEmiPreClosedSmsOption_CreditCardEmiPreClosedSmsOptionV1:
		return o.CreditCardEmiPreClosedSmsOption.GetCreditCardEmiPreClosedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardEmiPreClosedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("cc emi closed sms option is nil")
	}
	switch o.CreditCardEmiPreClosedSmsOption.GetOption().(type) {
	case *CreditCardEmiPreClosedSmsOption_CreditCardEmiPreClosedSmsOptionV1:
		merchantName := o.CreditCardEmiPreClosedSmsOption.GetCreditCardEmiPreClosedSmsOptionV1().GetMerchantName()
		currentTimestamp := o.CreditCardEmiPreClosedSmsOption.GetCreditCardEmiPreClosedSmsOptionV1().GetCurrentTimestamp().String()
		dueAmount := o.CreditCardEmiPreClosedSmsOption.GetCreditCardEmiPreClosedSmsOptionV1().GetDueAmount().String()
		preClosureFee := o.CreditCardEmiPreClosedSmsOption.GetCreditCardEmiPreClosedSmsOptionV1().GetPreClosureFee().String()
		msg := ReplaceWithTrim(template, "{#merchant_name#}", merchantName, 1)
		msg = ReplaceWithTrim(msg, "{#current_timestamp#}", currentTimestamp, 1)
		msg = ReplaceWithTrim(msg, "{#due_amount#}", dueAmount, 1)
		msg = ReplaceWithTrim(msg, "{#pre_closure_fee#}", preClosureFee, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for pl loan agreement otp sms")
}

func (o *SmsOption_CreditCardEmiCancelledSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiCancelledSmsOption.GetSmsType()
}

func (o *SmsOption_CreditCardEmiCancelledSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiCancelledSmsOption.GetOption().(type) {
	case *CreditCardEmiCancelledSmsOption_CreditCardEmiCancelledSmsOptionV1:
		return o.CreditCardEmiCancelledSmsOption.GetCreditCardEmiCancelledSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CreditCardEmiCancelledSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("cc emi closed sms option is nil")
	}
	switch o.CreditCardEmiCancelledSmsOption.GetOption().(type) {
	case *CreditCardEmiCancelledSmsOption_CreditCardEmiCancelledSmsOptionV1:
		merchantName := o.CreditCardEmiCancelledSmsOption.GetCreditCardEmiCancelledSmsOptionV1().GetMerchantName()
		msg := ReplaceWithTrim(template, "{#merchant_name#}", merchantName, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for pl loan agreement otp sms")
}

func (o *SmsOption_PlLoanAgreementOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.PlLoanAgreementOtpSmsOption.GetSmsType()
}

func (o *SmsOption_PlLoanAgreementOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.PlLoanAgreementOtpSmsOption.GetOption().(type) {
	case *PlLoanAgreementOtpSmsOption_PlLoanAgreementOtpSmsOptionV1:
		return o.PlLoanAgreementOtpSmsOption.GetPlLoanAgreementOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_PlLoanAgreementOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("pl loan agreement otp sms option is nil")
	}
	switch o.PlLoanAgreementOtpSmsOption.GetOption().(type) {
	case *PlLoanAgreementOtpSmsOption_PlLoanAgreementOtpSmsOptionV1:
		otp := o.PlLoanAgreementOtpSmsOption.GetPlLoanAgreementOtpSmsOptionV1().GetOtp()
		msg := ReplaceWithTrim(template, "{#var#}", otp, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for pl loan agreement otp sms")
}

func (o *SmsOption_AlternateContactFlowOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.AlternateContactFlowOtpSmsOption.GetSmsType()
}

func (o *SmsOption_AlternateContactFlowOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.AlternateContactFlowOtpSmsOption.GetOption().(type) {
	case *AlternateContactFlowOtpSmsOption_AlternateContactOtpSmsOptionV1:
		return o.AlternateContactFlowOtpSmsOption.GetAlternateContactOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_AlternateContactFlowOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("alternate contact otp sms option is nil")
	}
	switch o.AlternateContactFlowOtpSmsOption.GetOption().(type) {
	case *AlternateContactFlowOtpSmsOption_AlternateContactOtpSmsOptionV1:
		otp := o.AlternateContactFlowOtpSmsOption.GetAlternateContactOtpSmsOptionV1().GetOtp()
		msg := ReplaceWithTrim(template, "{#var#}", otp, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for alternate contact flow otp sms")
}

func (o *SmsOption_UpiPinSetResetSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.UpiPinSetResetSmsOption.GetSmsType()
}

func (o *SmsOption_UpiPinSetResetSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("upi pin set sms option is nil")
	}
	switch o.UpiPinSetResetSmsOption.GetOption().(type) {
	case *UpiPinSetResetSmsOption_UpiPinSetResetSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for upi pin set template")
}

func (o *SmsOption_UpiPinSetResetSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.UpiPinSetResetSmsOption.GetOption().(type) {
	case *UpiPinSetResetSmsOption_UpiPinSetResetSmsOptionV1:
		return o.UpiPinSetResetSmsOption.GetUpiPinSetResetSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (s *SmsOption_RiskAccountFreezeSmsOption) GetTemplateVersion() TemplateVersion {
	if s == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch s.RiskAccountFreezeSmsOption.GetOption().(type) {
	case *RiskAccountFreezeSmsOption_RiskAccountFreezeSmsOptionV1:
		return s.RiskAccountFreezeSmsOption.GetRiskAccountFreezeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (s *SmsOption_RiskAccountFreezeSmsOption) GetActualMessage(template string) (string, error) {
	if s == nil {
		return "", fmt.Errorf("risk freeze sms nil")
	}
	switch s.RiskAccountFreezeSmsOption.GetOption().(type) {
	case *RiskAccountFreezeSmsOption_RiskAccountFreezeSmsOptionV1:
		contactEmail := s.RiskAccountFreezeSmsOption.GetRiskAccountFreezeSmsOptionV1().GetFiContactEmail()
		msg := ReplaceWithTrim(template, "{#fi_contact_email#}", contactEmail, 1)
		return msg, nil
	}
	return "", fmt.Errorf("no valid version found for risk freeze sms template")
}

func (s *SmsOption_RiskAccountFreezeSmsOption) GetType() SmsType {
	if s == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return s.RiskAccountFreezeSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardForexRefundReceivedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.DebitCardForexRefundReceivedSmsOption.GetSmsType()
}

func (o *SmsOption_DebitCardForexRefundReceivedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("dc forex refund received set sms option is nil")
	}
	switch o.DebitCardForexRefundReceivedSmsOption.GetOption().(type) {
	case *DebitCardForexRefundReceivedSmsOption_DebitCardForexRefundReceivedSmsOptionV1:
		refundAmt := money.ToDisplayStringInIndianFormat(o.DebitCardForexRefundReceivedSmsOption.GetDebitCardForexRefundReceivedSmsOptionV1().GetRefundAmount(), 0, false)
		template = strings.Replace(template, "{#refund_amt#}", refundAmt, 1)
		template = strings.Replace(template, "{#acc_number#}", o.DebitCardForexRefundReceivedSmsOption.GetDebitCardForexRefundReceivedSmsOptionV1().GetAccountNumber(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for upi pin set template")
}

func (o *SmsOption_DebitCardForexRefundReceivedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.DebitCardForexRefundReceivedSmsOption.GetOption().(type) {
	case *DebitCardForexRefundReceivedSmsOption_DebitCardForexRefundReceivedSmsOptionV1:
		return o.DebitCardForexRefundReceivedSmsOption.GetDebitCardForexRefundReceivedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CallRecordingPostRiskUseCaseSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CallRecordingPostRiskUseCaseSmsOption.GetSmsType()
}

func (o *SmsOption_CallRecordingPostRiskUseCaseSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("call recording post risk use case sms option is nil")
	}
	switch o.CallRecordingPostRiskUseCaseSmsOption.GetOption().(type) {
	case *CallRecordingPostRiskUseCaseSmsOption_CallRecordingPostRiskUseCaseSmsOptionV1:
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for upi pin set template")
}

func (o *SmsOption_CallRecordingPostRiskUseCaseSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CallRecordingPostRiskUseCaseSmsOption.GetOption().(type) {
	case *CallRecordingPostRiskUseCaseSmsOption_CallRecordingPostRiskUseCaseSmsOptionV1:
		return o.CallRecordingPostRiskUseCaseSmsOption.GetCallRecordingPostRiskUseCaseSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfLienMarkSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfLienMarkSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_LamfLienMarkSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfLienMarkSuccessSmsOption.GetOption().(type) {
	case *LamfLienMarkSuccessSmsOption_LamfLienMarkSuccessSmsOptionV1:
		return o.LamfLienMarkSuccessSmsOption.GetLamfLienMarkSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfLienMarkSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf lien mark success sms option is nil")
	}
	switch o.LamfLienMarkSuccessSmsOption.GetOption().(type) {
	case *LamfLienMarkSuccessSmsOption_LamfLienMarkSuccessSmsOptionV1:
		options := o.LamfLienMarkSuccessSmsOption.GetLamfLienMarkSuccessSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#continue_link#}", options.GetContinueLink(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf lien mark success sms option template")
}

func (o *SmsOption_LamfLoanDisbursedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfLoanDisbursedSmsOption.GetSmsType()
}

func (o *SmsOption_LamfLoanDisbursedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfLoanDisbursedSmsOption.GetOption().(type) {
	case *LamfLoanDisbursedSmsOption_LamfLoanDisbursedSmsOptionV1:
		return o.LamfLoanDisbursedSmsOption.GetLamfLoanDisbursedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfLoanDisbursedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf loan disbursed sms is nil")
	}
	switch o.LamfLoanDisbursedSmsOption.GetOption().(type) {
	case *LamfLoanDisbursedSmsOption_LamfLoanDisbursedSmsOptionV1:
		options := o.LamfLoanDisbursedSmsOption.GetLamfLoanDisbursedSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#status_link#}", options.GetStatusLink(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf loan disbursed sms template")
}

func (o *SmsOption_LamfAllEmiPaidLoanClosureInitiatedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfAllEmiPaidLoanClosureInitiatedSmsOption.GetSmsType()
}

func (o *SmsOption_LamfAllEmiPaidLoanClosureInitiatedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfAllEmiPaidLoanClosureInitiatedSmsOption.GetOption().(type) {
	case *LamfAllEmiPaidLoanClosureInitiatedSmsOption_LamfAllEmiPaidLoanClosureInitiatedSmsOptionV1:
		return o.LamfAllEmiPaidLoanClosureInitiatedSmsOption.GetLamfAllEmiPaidLoanClosureInitiatedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfAllEmiPaidLoanClosureInitiatedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf all emi paid loan closure initiated sms is nil")
	}
	switch o.LamfAllEmiPaidLoanClosureInitiatedSmsOption.GetOption().(type) {
	case *LamfAllEmiPaidLoanClosureInitiatedSmsOption_LamfAllEmiPaidLoanClosureInitiatedSmsOptionV1:
		options := o.LamfAllEmiPaidLoanClosureInitiatedSmsOption.GetLamfAllEmiPaidLoanClosureInitiatedSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf all emi paid loan closure initiated sms template")
}

func (o *SmsOption_LamfRepaymentAutoRecoveredByBajajSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfRepaymentAutoRecoveredByBajajSmsOption.GetSmsType()
}

func (o *SmsOption_LamfRepaymentAutoRecoveredByBajajSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfRepaymentAutoRecoveredByBajajSmsOption.GetOption().(type) {
	case *LamfRepaymentAutoRecoveredByBajajSmsOption_LamfRepaymentAutoRecoveredByBajajSmsOptionV1:
		return o.LamfRepaymentAutoRecoveredByBajajSmsOption.GetLamfRepaymentAutoRecoveredByBajajSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfRepaymentAutoRecoveredByBajajSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment auto recovered by bajaj sms is nil")
	}
	switch o.LamfRepaymentAutoRecoveredByBajajSmsOption.GetOption().(type) {
	case *LamfRepaymentAutoRecoveredByBajajSmsOption_LamfRepaymentAutoRecoveredByBajajSmsOptionV1:
		options := o.LamfRepaymentAutoRecoveredByBajajSmsOption.GetLamfRepaymentAutoRecoveredByBajajSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#emi_amount#}", options.GetEmiAmount(), 1)
		body = strings.Replace(body, "{#account_last_four_digits#}", options.GetAccountLastFourDigits(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf repayment auto recovered by bajaj sms template")
}

func (o *SmsOption_LamfRepaymentAutoRecoveredByFiSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfRepaymentAutoRecoveredByFiSmsOption.GetSmsType()
}

func (o *SmsOption_LamfRepaymentAutoRecoveredByFiSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfRepaymentAutoRecoveredByFiSmsOption.GetOption().(type) {
	case *LamfRepaymentAutoRecoveredByFiSmsOption_LamfRepaymentAutoRecoveredByFiSmsOptionV1:
		return o.LamfRepaymentAutoRecoveredByFiSmsOption.GetLamfRepaymentAutoRecoveredByFiSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfRepaymentAutoRecoveredByFiSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment auto recovered by fi sms is nil")
	}
	switch o.LamfRepaymentAutoRecoveredByFiSmsOption.GetOption().(type) {
	case *LamfRepaymentAutoRecoveredByFiSmsOption_LamfRepaymentAutoRecoveredByFiSmsOptionV1:
		options := o.LamfRepaymentAutoRecoveredByFiSmsOption.GetLamfRepaymentAutoRecoveredByFiSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#emi_amount#}", options.GetEmiAmount(), 1)
		body = strings.Replace(body, "{#account_last_four_digits#}", options.GetAccountLastFourDigits(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf repayment auto recovered by fi sms template")
}

func (o *SmsOption_LamfRepaymentUpcomingEmiSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfRepaymentUpcomingEmiSmsOption.GetSmsType()
}

func (o *SmsOption_LamfRepaymentUpcomingEmiSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfRepaymentUpcomingEmiSmsOption.GetOption().(type) {
	case *LamfRepaymentUpcomingEmiSmsOption_LamfRepaymentUpcomingEmiSmsOptionV1:
		return o.LamfRepaymentUpcomingEmiSmsOption.GetLamfRepaymentUpcomingEmiSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfRepaymentUpcomingEmiSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment upcoming emi sms is nil")
	}
	switch o.LamfRepaymentUpcomingEmiSmsOption.GetOption().(type) {
	case *LamfRepaymentUpcomingEmiSmsOption_LamfRepaymentUpcomingEmiSmsOptionV1:
		options := o.LamfRepaymentUpcomingEmiSmsOption.GetLamfRepaymentUpcomingEmiSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#emi_amount#}", options.GetEmiAmount(), 1)
		body = strings.Replace(body, "{#due_date#}", options.GetDueDate(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf repayment upcoming emi sms template")
}

func (o *SmsOption_LamfRepaymentUpcomingEmiLowBalanceSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfRepaymentUpcomingEmiLowBalanceSmsOption.GetSmsType()
}

func (o *SmsOption_LamfRepaymentUpcomingEmiLowBalanceSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfRepaymentUpcomingEmiLowBalanceSmsOption.GetOption().(type) {
	case *LamfRepaymentUpcomingEmiLowBalanceSmsOption_LamfRepaymentUpcomingEmiLowBalanceSmsOptionV1:
		return o.LamfRepaymentUpcomingEmiLowBalanceSmsOption.GetLamfRepaymentUpcomingEmiLowBalanceSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfRepaymentUpcomingEmiLowBalanceSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment upcoming emi low balance sms is nil")
	}
	switch o.LamfRepaymentUpcomingEmiLowBalanceSmsOption.GetOption().(type) {
	case *LamfRepaymentUpcomingEmiLowBalanceSmsOption_LamfRepaymentUpcomingEmiLowBalanceSmsOptionV1:
		options := o.LamfRepaymentUpcomingEmiLowBalanceSmsOption.GetLamfRepaymentUpcomingEmiLowBalanceSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#emi_amount#}", options.GetEmiAmount(), 1)
		body = strings.Replace(body, "{#due_date#}", options.GetDueDate(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf repayment upcoming emi low balance sms template")
}

func (o *SmsOption_LamfRepaymentEmandateBounceSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfRepaymentEmandateBounceSmsOption.GetSmsType()
}

func (o *SmsOption_LamfRepaymentEmandateBounceSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfRepaymentEmandateBounceSmsOption.GetOption().(type) {
	case *LamfRepaymentEmandateBounceSmsOption_LamfRepaymentEmandateBounceSmsOptionV1:
		return o.LamfRepaymentEmandateBounceSmsOption.GetLamfRepaymentEmandateBounceSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfRepaymentEmandateBounceSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment emandate bounce sms is nil")
	}
	switch o.LamfRepaymentEmandateBounceSmsOption.GetOption().(type) {
	case *LamfRepaymentEmandateBounceSmsOption_LamfRepaymentEmandateBounceSmsOptionV1:
		options := o.LamfRepaymentEmandateBounceSmsOption.GetLamfRepaymentEmandateBounceSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		body = strings.Replace(body, "{#account_last_four_digits#}", options.GetAccountLastFourDigits(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf repayment emandate bounce sms template")
}

func (o *SmsOption_LamfRepaymentPrepaymentSuccessSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.LamfRepaymentPrepaymentSuccessSmsOption.GetSmsType()
}

func (o *SmsOption_LamfRepaymentPrepaymentSuccessSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.LamfRepaymentPrepaymentSuccessSmsOption.GetOption().(type) {
	case *LamfRepaymentPrepaymentSuccessSmsOption_LamfRepaymentPrepaymentSuccessSmsOptionV1:
		return o.LamfRepaymentPrepaymentSuccessSmsOption.GetLamfRepaymentPrepaymentSuccessSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_LamfRepaymentPrepaymentSuccessSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment prepayment success sms is nil")
	}
	switch o.LamfRepaymentPrepaymentSuccessSmsOption.GetOption().(type) {
	case *LamfRepaymentPrepaymentSuccessSmsOption_LamfRepaymentPrepaymentSuccessSmsOptionV1:
		options := o.LamfRepaymentPrepaymentSuccessSmsOption.GetLamfRepaymentPrepaymentSuccessSmsOptionV1()
		body := strings.Replace(template, "{#investor_name#}", options.GetInvestorName(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for lamf repayment prepayment success sms template")
}

func (o *SmsOption_CcFilitePanDob2HrDropOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CcFilitePanDob2HrDropOffSmsOption.GetSmsType()
}

func (o *SmsOption_CcFilitePanDob2HrDropOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CcFilitePanDob2HrDropOffSmsOption.GetOption().(type) {
	case *CcFilitePanDob2HrDropOffSmsOption_CcFilitePanDob2HrDropOffSmsOptionV1:
		return o.CcFilitePanDob2HrDropOffSmsOption.GetCcFilitePanDob2HrDropOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CcFilitePanDob2HrDropOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("lamf repayment prepayment success sms is nil")
	}
	switch o.CcFilitePanDob2HrDropOffSmsOption.GetOption().(type) {
	case *CcFilitePanDob2HrDropOffSmsOption_CcFilitePanDob2HrDropOffSmsOptionV1:
		options := o.CcFilitePanDob2HrDropOffSmsOption.GetCcFilitePanDob2HrDropOffSmsOptionV1()
		body := strings.Replace(template, "{#Link#}", options.GetDeepLink(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for cc fi lite pan dob 2hr sms template")
}

func (o *SmsOption_CcFilitePanDob120HrDropOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CcFilitePanDob120HrDropOffSmsOption.GetSmsType()
}

func (o *SmsOption_CcFilitePanDob120HrDropOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CcFilitePanDob120HrDropOffSmsOption.GetOption().(type) {
	case *CcFilitePanDob120HrDropOffSmsOption_CcFilitePanDob120HrDropOffSmsOptionV1:
		return o.CcFilitePanDob120HrDropOffSmsOption.GetCcFilitePanDob120HrDropOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CcFilitePanDob120HrDropOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cc fi lite pan dob drop off sms is nil")
	}
	switch o.CcFilitePanDob120HrDropOffSmsOption.GetOption().(type) {
	case *CcFilitePanDob120HrDropOffSmsOption_CcFilitePanDob120HrDropOffSmsOptionV1:
		options := o.CcFilitePanDob120HrDropOffSmsOption.GetCcFilitePanDob120HrDropOffSmsOptionV1()
		body := strings.Replace(template, "{#Link#}", options.GetDeepLink(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for cc fi lite pan dob 120Hr sms template")
}

func (o *SmsOption_CcFiLiteEkycDropOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CcFiLiteEkycDropOffSmsOption.GetSmsType()
}

func (o *SmsOption_CcFiLiteEkycDropOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CcFiLiteEkycDropOffSmsOption.GetOption().(type) {
	case *CcFiLiteEKYCDropOffSmsOption_CcFiLiteEkycDropOffSmsOptionV1:
		return o.CcFiLiteEkycDropOffSmsOption.GetCcFiLiteEkycDropOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CcFiLiteEkycDropOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cc fi lite ekyc drop off sms is nil")
	}
	switch o.CcFiLiteEkycDropOffSmsOption.GetOption().(type) {
	case *CcFiLiteEKYCDropOffSmsOption_CcFiLiteEkycDropOffSmsOptionV1:
		options := o.CcFiLiteEkycDropOffSmsOption.GetCcFiLiteEkycDropOffSmsOptionV1()
		body := strings.Replace(template, "{#Link#}", options.GetDeepLink(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for cc fi lite ekyc sms template")
}

func (o *SmsOption_CcCcFiLiteVkycDropOffSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CcCcFiLiteVkycDropOffSmsOption.GetSmsType()
}

func (o *SmsOption_CcCcFiLiteVkycDropOffSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CcCcFiLiteVkycDropOffSmsOption.GetOption().(type) {
	case *CcFiLiteVKYCDropOffSmsOption_CcFiLiteVkycDropOffSmsOptionV1:
		return o.CcCcFiLiteVkycDropOffSmsOption.GetCcFiLiteVkycDropOffSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CcCcFiLiteVkycDropOffSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cc fi lite vkyc drop off sms is nil")
	}
	switch o.CcCcFiLiteVkycDropOffSmsOption.GetOption().(type) {
	case *CcFiLiteVKYCDropOffSmsOption_CcFiLiteVkycDropOffSmsOptionV1:
		options := o.CcCcFiLiteVkycDropOffSmsOption.GetCcFiLiteVkycDropOffSmsOptionV1()
		body := strings.Replace(template, "{#Link#}", options.GetLink(), 1)
		body = strings.Replace(body, "{#Name#}", options.GetName(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for cc fi lite vkyc sms template")
}

func (o *SmsOption_FiStoreOrderDeliveryStatusUpdateSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.FiStoreOrderDeliveryStatusUpdateSmsOption.GetSmsType()
}

func (o *SmsOption_FiStoreOrderDeliveryStatusUpdateSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("sms option null for fi store order delivery status update sms")
	}
	switch o.FiStoreOrderDeliveryStatusUpdateSmsOption.GetOption().(type) {
	case *FiStoreOrderDeliveryStatusUpdateSmsOption_FiStoreOrderDeliveryStatusUpdateSmsOptionV1:
		template = strings.Replace(template, "{#order_tracking_link#}", o.FiStoreOrderDeliveryStatusUpdateSmsOption.GetFiStoreOrderDeliveryStatusUpdateSmsOptionV1().GetOrderTrackingLink(), 1)
		template = strings.Replace(template, "{#order_id#}", o.FiStoreOrderDeliveryStatusUpdateSmsOption.GetFiStoreOrderDeliveryStatusUpdateSmsOptionV1().GetOrderId(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for fi store order delivery status update sms")
}

func (o *SmsOption_FiStoreOrderDeliveryStatusUpdateSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.FiStoreOrderDeliveryStatusUpdateSmsOption.GetOption().(type) {
	case *FiStoreOrderDeliveryStatusUpdateSmsOption_FiStoreOrderDeliveryStatusUpdateSmsOptionV1:
		return o.FiStoreOrderDeliveryStatusUpdateSmsOption.GetFiStoreOrderDeliveryStatusUpdateSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskOutcallFormLoginOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskOutcallFormLoginOtpSmsOption.GetSmsType()
}

func (o *SmsOption_RiskOutcallFormLoginOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskOutcallFormLoginOtpSmsOption.GetOption().(type) {
	case *RiskOutcallFormLoginOtpSmsOption_RiskOutcallFormLoginOtpSmsOptionV1:
		return o.RiskOutcallFormLoginOtpSmsOption.GetRiskOutcallFormLoginOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskOutcallFormLoginOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk outcall form login otp sms is nil")
	}
	switch o.RiskOutcallFormLoginOtpSmsOption.GetOption().(type) {
	case *RiskOutcallFormLoginOtpSmsOption_RiskOutcallFormLoginOtpSmsOptionV1:
		options := o.RiskOutcallFormLoginOtpSmsOption.GetRiskOutcallFormLoginOtpSmsOptionV1()
		body := strings.Replace(template, "{#otp#}", options.GetOtp(), 1)
		return body, nil
	}
	return "", fmt.Errorf("no valid version found for risk outcall form login otp sms template")
}

func (o *SmsOption_CibilReportSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CibilReportSmsOption.GetSmsType()
}

func (o *SmsOption_CibilReportSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CibilReportSmsOption.GetOption().(type) {
	case *CibilReportSmsOption_CibilReportOptionV1:
		return o.CibilReportSmsOption.GetCibilReportOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_CibilReportSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("pl loan agreement otp sms option is nil")
	}
	return template, nil
}

func (o *SmsOption_NonResidentOnboardingOtpSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.NonResidentOnboardingOtpSmsOption.GetSmsType()
}

func (o *SmsOption_NonResidentOnboardingOtpSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.NonResidentOnboardingOtpSmsOption.GetOption().(type) {
	case *NonResidentOnboardingOtpSmsOption_NonResidentOnboardingOtpSmsOptionV1:
		return o.NonResidentOnboardingOtpSmsOption.GetNonResidentOnboardingOtpSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_NonResidentOnboardingOtpSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("nr onboarding onboarding otp sms option is nil")
	}
	switch o.NonResidentOnboardingOtpSmsOption.GetOption().(type) {
	case *NonResidentOnboardingOtpSmsOption_NonResidentOnboardingOtpSmsOptionV1:
		otp := o.NonResidentOnboardingOtpSmsOption.GetNonResidentOnboardingOtpSmsOptionV1().GetOtp()
		msg := ReplaceWithTrim(template, "{#var#}", otp, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for nr onboarding otp")
}

func (o *SmsOption_StockguardianLoanApplicationEsignSms) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.StockguardianLoanApplicationEsignSms.GetSmsType()
}

func (o *SmsOption_StockguardianLoanApplicationEsignSms) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.StockguardianLoanApplicationEsignSms.GetOption().(type) {
	case *StockguardianLoanApplicationEsignSms_StockguardianLoanApplicationEsignSmsV1:
		return o.StockguardianLoanApplicationEsignSms.GetStockguardianLoanApplicationEsignSmsV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_StockguardianLoanApplicationEsignSms) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", errors.New("esign otp sms option is nil")
	}
	switch o.StockguardianLoanApplicationEsignSms.GetOption().(type) {
	case *StockguardianLoanApplicationEsignSms_StockguardianLoanApplicationEsignSmsV1:
		otp := o.StockguardianLoanApplicationEsignSms.GetStockguardianLoanApplicationEsignSmsV1().GetOtp()
		msg := ReplaceWithTrim(template, "{#otp#}", otp, 1)
		return msg, nil
	}
	return "", errors.New("no valid version found for for esign otp")
}

func (o *SmsOption_CxUserCallDroppedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.CxUserCallDroppedSmsOption.GetSmsType()
}

func (o *SmsOption_CxUserCallDroppedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cx call dropped sms option is nil")
	}
	switch o.CxUserCallDroppedSmsOption.GetOption().(type) {
	case *CxUserCallDroppedSmsOption_CxUserCallDroppedSmsOptionV1:
		getHelpFiLink := o.CxUserCallDroppedSmsOption.GetCxUserCallDroppedSmsOptionV1().GetContactUsFlowLink()
		downloadFiLink := o.CxUserCallDroppedSmsOption.GetCxUserCallDroppedSmsOptionV1().GetAppDownloadLink()
		// adding mo-engage link for fall-back
		if getHelpFiLink == "" {
			getHelpFiLink = "u3.mnge.co/g5YORz2"
		}
		if downloadFiLink == "" {
			downloadFiLink = "u3.mnge.co/mGym0Jr"
		}

		// Replace the first occurrence of {#var#} with link1
		template = strings.Replace(template, "{#var#}", getHelpFiLink, 1)

		// Replace the second occurrence of {#var#} with link2
		template = strings.Replace(template, "{#var#}", downloadFiLink, 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cx call dropped template")
}

func (o *SmsOption_CxUserCallDroppedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CxUserCallDroppedSmsOption.GetOption().(type) {
	case *CxUserCallDroppedSmsOption_CxUserCallDroppedSmsOptionV1:
		return o.CxUserCallDroppedSmsOption.GetCxUserCallDroppedSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskOpsCfSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskOpsCfSmsOption.GetSmsType()
}

func (o *SmsOption_RiskOpsCfSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk ops cf sms option is nil")
	}
	switch o.RiskOpsCfSmsOption.GetOption().(type) {
	case *RiskOpsCFSmsOption_RiskAccountFreezeSmsOptionV1:
		template = strings.Replace(template, "{#freeze_reason#}", o.RiskOpsCfSmsOption.GetRiskAccountFreezeSmsOptionV1().GetFreezeReason(), 1)
		template = strings.Replace(template, "{#freeze_type#}", o.RiskOpsCfSmsOption.GetRiskAccountFreezeSmsOptionV1().GetFreezeType(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for risk ops cf sms template")
}

func (o *SmsOption_RiskOpsCfSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskOpsCfSmsOption.GetOption().(type) {
	case *RiskOpsCFSmsOption_RiskAccountFreezeSmsOptionV1:
		return o.RiskOpsCfSmsOption.GetRiskAccountFreezeSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskUnifiedLeaDebitFreezeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskUnifiedLeaDebitFreezeSmsOption.GetSmsType()
}

func (o *SmsOption_RiskUnifiedLeaDebitFreezeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk unified LEA debit freeze sms option is nil")
	}
	switch o.RiskUnifiedLeaDebitFreezeSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaDebitFreezeSmsOption_RiskUnifiedLeaDebitFreezeOptionV1:
		template = strings.Replace(template, "{#reminderText#}", o.RiskUnifiedLeaDebitFreezeSmsOption.GetRiskUnifiedLeaDebitFreezeOptionV1().GetReminderText(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for risk unified LEA debit freeze")
}

func (o *SmsOption_RiskUnifiedLeaDebitFreezeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskUnifiedLeaDebitFreezeSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaDebitFreezeSmsOption_RiskUnifiedLeaDebitFreezeOptionV1:
		return o.RiskUnifiedLeaDebitFreezeSmsOption.GetRiskUnifiedLeaDebitFreezeOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskUnifiedLeaCreditFreezeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskUnifiedLeaCreditFreezeSmsOption.GetSmsType()
}

func (o *SmsOption_RiskUnifiedLeaCreditFreezeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk unified Credit freeze use case sms option is nil")
	}
	switch o.RiskUnifiedLeaCreditFreezeSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaCreditFreezeSmsOption_RiskUnifiedLeaCreditFreezeOptionV1:
		template = strings.Replace(template, "{#reminderText#}", o.RiskUnifiedLeaCreditFreezeSmsOption.GetRiskUnifiedLeaCreditFreezeOptionV1().GetReminderText(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for risk unified LEA Credit freeze")
}

func (o *SmsOption_RiskUnifiedLeaCreditFreezeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskUnifiedLeaCreditFreezeSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaCreditFreezeSmsOption_RiskUnifiedLeaCreditFreezeOptionV1:
		return o.RiskUnifiedLeaCreditFreezeSmsOption.GetRiskUnifiedLeaCreditFreezeOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskUnifiedLeaTotalFreezeSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskUnifiedLeaTotalFreezeSmsOption.GetSmsType()
}

func (o *SmsOption_RiskUnifiedLeaTotalFreezeSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk unified LEA Total freeze use case sms option is nil")
	}
	switch o.RiskUnifiedLeaTotalFreezeSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaTotalFreezeSmsOption_RiskUnifiedLeaTotalFreezeOptionV1:
		template = strings.Replace(template, "{#reminderText#}", o.RiskUnifiedLeaTotalFreezeSmsOption.GetRiskUnifiedLeaTotalFreezeOptionV1().GetReminderText(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for risk unified LEA Total freeze")
}

func (o *SmsOption_RiskUnifiedLeaTotalFreezeSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskUnifiedLeaTotalFreezeSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaTotalFreezeSmsOption_RiskUnifiedLeaTotalFreezeOptionV1:
		return o.RiskUnifiedLeaTotalFreezeSmsOption.GetRiskUnifiedLeaTotalFreezeOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskUnifiedLeaLienSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskUnifiedLeaLienSmsOption.GetSmsType()
}

func (o *SmsOption_RiskUnifiedLeaLienSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk unified LEA LIEN use case sms option is nil")
	}
	switch o.RiskUnifiedLeaLienSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaLienSmsOption_RiskUnifiedLeaLienOptionV1:
		template = strings.Replace(template, "{#reminderText#}", o.RiskUnifiedLeaLienSmsOption.GetRiskUnifiedLeaLienOptionV1().GetReminderText(), 1)
		template = strings.Replace(template, "{#amount#}", o.RiskUnifiedLeaLienSmsOption.GetRiskUnifiedLeaLienOptionV1().GetAmount(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for risk unified LEA LIEN")
}

func (o *SmsOption_RiskUnifiedLeaLienSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskUnifiedLeaLienSmsOption.GetOption().(type) {
	case *RiskUnifiedLeaLienSmsOption_RiskUnifiedLeaLienOptionV1:
		return o.RiskUnifiedLeaLienSmsOption.GetRiskUnifiedLeaLienOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_RiskCreditFreezeAppliedSmsOption) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.RiskCreditFreezeAppliedSmsOption.GetSmsType()
}

func (o *SmsOption_RiskCreditFreezeAppliedSmsOption) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("risk credit freeze use case sms option is nil")
	}
	switch o.RiskCreditFreezeAppliedSmsOption.GetOption().(type) {
	case *RiskCreditFreezeAppliedSmsOption_RiskOpsCreditFreezeOptionV1:
		template = strings.Replace(template, "{#reminder#}", o.RiskCreditFreezeAppliedSmsOption.GetRiskOpsCreditFreezeOptionV1().GetReminderHeading(), 1)
		template = strings.Replace(template, "{#freeze_type#}", o.RiskCreditFreezeAppliedSmsOption.GetRiskOpsCreditFreezeOptionV1().GetFreezeType(), 1)
		template = strings.Replace(template, "{#freeze_reason#}", o.RiskCreditFreezeAppliedSmsOption.GetRiskOpsCreditFreezeOptionV1().GetFreezeReason(), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for risk credit freeze")
}

func (o *SmsOption_RiskCreditFreezeAppliedSmsOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.RiskCreditFreezeAppliedSmsOption.GetOption().(type) {
	case *RiskCreditFreezeAppliedSmsOption_RiskOpsCreditFreezeOptionV1:
		return o.RiskCreditFreezeAppliedSmsOption.GetRiskOpsCreditFreezeOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ChequeCreditProcessingFailedFint) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ChequeCreditProcessingFailedFint.GetSmsType()
}

func (o *SmsOption_ChequeCreditProcessingFailedFint) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cheque credit processing failed sms option is nil")
	}
	switch o.ChequeCreditProcessingFailedFint.GetOption().(type) {
	case *ChequeCreditProcessingFailedFint_ChequeCreditProcessingFailedFintSmsOptionV1:
		template = strings.Replace(template, "{#var#}", money.ToDisplayStringWithINRSymbol(o.ChequeCreditProcessingFailedFint.GetChequeCreditProcessingFailedFintSmsOptionV1().GetTransactionAmount()), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cheque credit processing failed sms option")
}

func (o *SmsOption_ChequeCreditProcessingFailedFint) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ChequeCreditProcessingFailedFint.GetOption().(type) {
	case *ChequeCreditProcessingFailedFint_ChequeCreditProcessingFailedFintSmsOptionV1:
		return o.ChequeCreditProcessingFailedFint.GetChequeCreditProcessingFailedFintSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}

func (o *SmsOption_ChequeCreditProcessingFint) GetType() SmsType {
	if o == nil {
		return SmsType_SMS_TYPE_UNSPECIFIED
	}
	return o.ChequeCreditProcessingFint.GetSmsType()
}

func (o *SmsOption_ChequeCreditProcessingFint) GetActualMessage(template string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("cheque credit processing started sms option is nil")
	}
	switch o.ChequeCreditProcessingFint.GetOption().(type) {
	case *ChequeCreditProcessingFint_ChequeCreditProcessingFintSmsOptionV1:
		template = strings.Replace(template, "{#var#}", money.ToDisplayStringWithINRSymbol(o.ChequeCreditProcessingFint.GetChequeCreditProcessingFintSmsOptionV1().GetTransactionAmount()), 1)
		return template, nil
	}
	return "", fmt.Errorf("no valid version found for cheque credit processing started sms option")
}

func (o *SmsOption_ChequeCreditProcessingFint) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.ChequeCreditProcessingFint.GetOption().(type) {
	case *ChequeCreditProcessingFint_ChequeCreditProcessingFintSmsOptionV1:
		return o.ChequeCreditProcessingFint.GetChequeCreditProcessingFintSmsOptionV1().GetTemplateVersion()
	}
	return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
}
