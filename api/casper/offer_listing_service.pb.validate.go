// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/casper/offer_listing_service.proto

package casper

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateOfferListingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOfferListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOfferListingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOfferListingRequestMultiError, or nil if none found.
func (m *CreateOfferListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOfferListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	if len(errors) > 0 {
		return CreateOfferListingRequestMultiError(errors)
	}

	return nil
}

// CreateOfferListingRequestMultiError is an error wrapping multiple validation
// errors returned by CreateOfferListingRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateOfferListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOfferListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOfferListingRequestMultiError) AllErrors() []error { return m }

// CreateOfferListingRequestValidationError is the validation error returned by
// CreateOfferListingRequest.Validate if the designated constraints aren't met.
type CreateOfferListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOfferListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOfferListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOfferListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOfferListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOfferListingRequestValidationError) ErrorName() string {
	return "CreateOfferListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOfferListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOfferListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOfferListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOfferListingRequestValidationError{}

// Validate checks the field values on CreateOfferListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOfferListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOfferListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOfferListingResponseMultiError, or nil if none found.
func (m *CreateOfferListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOfferListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOfferListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferListingId

	if len(errors) > 0 {
		return CreateOfferListingResponseMultiError(errors)
	}

	return nil
}

// CreateOfferListingResponseMultiError is an error wrapping multiple
// validation errors returned by CreateOfferListingResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateOfferListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOfferListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOfferListingResponseMultiError) AllErrors() []error { return m }

// CreateOfferListingResponseValidationError is the validation error returned
// by CreateOfferListingResponse.Validate if the designated constraints aren't met.
type CreateOfferListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOfferListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOfferListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOfferListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOfferListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOfferListingResponseValidationError) ErrorName() string {
	return "CreateOfferListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOfferListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOfferListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOfferListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOfferListingResponseValidationError{}

// Validate checks the field values on UpdateOfferListingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOfferListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOfferListingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOfferListingRequestMultiError, or nil if none found.
func (m *UpdateOfferListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOfferListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferListingId

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	if len(errors) > 0 {
		return UpdateOfferListingRequestMultiError(errors)
	}

	return nil
}

// UpdateOfferListingRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateOfferListingRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateOfferListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOfferListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOfferListingRequestMultiError) AllErrors() []error { return m }

// UpdateOfferListingRequestValidationError is the validation error returned by
// UpdateOfferListingRequest.Validate if the designated constraints aren't met.
type UpdateOfferListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOfferListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOfferListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOfferListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOfferListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOfferListingRequestValidationError) ErrorName() string {
	return "UpdateOfferListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOfferListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOfferListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOfferListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOfferListingRequestValidationError{}

// Validate checks the field values on UpdateOfferListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOfferListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOfferListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOfferListingResponseMultiError, or nil if none found.
func (m *UpdateOfferListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOfferListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOfferListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateOfferListingResponseMultiError(errors)
	}

	return nil
}

// UpdateOfferListingResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateOfferListingResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateOfferListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOfferListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOfferListingResponseMultiError) AllErrors() []error { return m }

// UpdateOfferListingResponseValidationError is the validation error returned
// by UpdateOfferListingResponse.Validate if the designated constraints aren't met.
type UpdateOfferListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOfferListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOfferListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOfferListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOfferListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOfferListingResponseValidationError) ErrorName() string {
	return "UpdateOfferListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOfferListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOfferListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOfferListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOfferListingResponseValidationError{}

// Validate checks the field values on DeleteOfferListingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOfferListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOfferListingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOfferListingRequestMultiError, or nil if none found.
func (m *DeleteOfferListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOfferListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferListingId

	if len(errors) > 0 {
		return DeleteOfferListingRequestMultiError(errors)
	}

	return nil
}

// DeleteOfferListingRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteOfferListingRequest.ValidateAll() if the
// designated constraints aren't met.
type DeleteOfferListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOfferListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOfferListingRequestMultiError) AllErrors() []error { return m }

// DeleteOfferListingRequestValidationError is the validation error returned by
// DeleteOfferListingRequest.Validate if the designated constraints aren't met.
type DeleteOfferListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOfferListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOfferListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOfferListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOfferListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOfferListingRequestValidationError) ErrorName() string {
	return "DeleteOfferListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOfferListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOfferListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOfferListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOfferListingRequestValidationError{}

// Validate checks the field values on DeleteOfferListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOfferListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOfferListingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOfferListingResponseMultiError, or nil if none found.
func (m *DeleteOfferListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOfferListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteOfferListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteOfferListingResponseMultiError(errors)
	}

	return nil
}

// DeleteOfferListingResponseMultiError is an error wrapping multiple
// validation errors returned by DeleteOfferListingResponse.ValidateAll() if
// the designated constraints aren't met.
type DeleteOfferListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOfferListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOfferListingResponseMultiError) AllErrors() []error { return m }

// DeleteOfferListingResponseValidationError is the validation error returned
// by DeleteOfferListingResponse.Validate if the designated constraints aren't met.
type DeleteOfferListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOfferListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOfferListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOfferListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOfferListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOfferListingResponseValidationError) ErrorName() string {
	return "DeleteOfferListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOfferListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOfferListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOfferListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOfferListingResponseValidationError{}

// Validate checks the field values on GetOfferListingByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOfferListingByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOfferListingByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOfferListingByIdRequestMultiError, or nil if none found.
func (m *GetOfferListingByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOfferListingByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferListingId

	if len(errors) > 0 {
		return GetOfferListingByIdRequestMultiError(errors)
	}

	return nil
}

// GetOfferListingByIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetOfferListingByIdRequest.ValidateAll() if
// the designated constraints aren't met.
type GetOfferListingByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOfferListingByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOfferListingByIdRequestMultiError) AllErrors() []error { return m }

// GetOfferListingByIdRequestValidationError is the validation error returned
// by GetOfferListingByIdRequest.Validate if the designated constraints aren't met.
type GetOfferListingByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOfferListingByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOfferListingByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOfferListingByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOfferListingByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOfferListingByIdRequestValidationError) ErrorName() string {
	return "GetOfferListingByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOfferListingByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOfferListingByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOfferListingByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOfferListingByIdRequestValidationError{}

// Validate checks the field values on GetOfferListingByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOfferListingByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOfferListingByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOfferListingByIdResponseMultiError, or nil if none found.
func (m *GetOfferListingByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOfferListingByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOfferListingByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOfferListingByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOfferListingByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferListing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOfferListingByIdResponseValidationError{
					field:  "OfferListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOfferListingByIdResponseValidationError{
					field:  "OfferListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferListing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOfferListingByIdResponseValidationError{
				field:  "OfferListing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOfferListingByIdResponseMultiError(errors)
	}

	return nil
}

// GetOfferListingByIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetOfferListingByIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetOfferListingByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOfferListingByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOfferListingByIdResponseMultiError) AllErrors() []error { return m }

// GetOfferListingByIdResponseValidationError is the validation error returned
// by GetOfferListingByIdResponse.Validate if the designated constraints
// aren't met.
type GetOfferListingByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOfferListingByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOfferListingByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOfferListingByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOfferListingByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOfferListingByIdResponseValidationError) ErrorName() string {
	return "GetOfferListingByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOfferListingByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOfferListingByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOfferListingByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOfferListingByIdResponseValidationError{}

// Validate checks the field values on GetDisplayActiveListingsForOfferRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetDisplayActiveListingsForOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDisplayActiveListingsForOfferRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetDisplayActiveListingsForOfferRequestMultiError, or nil if none found.
func (m *GetDisplayActiveListingsForOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisplayActiveListingsForOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if len(errors) > 0 {
		return GetDisplayActiveListingsForOfferRequestMultiError(errors)
	}

	return nil
}

// GetDisplayActiveListingsForOfferRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetDisplayActiveListingsForOfferRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDisplayActiveListingsForOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisplayActiveListingsForOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisplayActiveListingsForOfferRequestMultiError) AllErrors() []error { return m }

// GetDisplayActiveListingsForOfferRequestValidationError is the validation
// error returned by GetDisplayActiveListingsForOfferRequest.Validate if the
// designated constraints aren't met.
type GetDisplayActiveListingsForOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisplayActiveListingsForOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisplayActiveListingsForOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisplayActiveListingsForOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisplayActiveListingsForOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisplayActiveListingsForOfferRequestValidationError) ErrorName() string {
	return "GetDisplayActiveListingsForOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisplayActiveListingsForOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisplayActiveListingsForOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisplayActiveListingsForOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisplayActiveListingsForOfferRequestValidationError{}

// Validate checks the field values on GetDisplayActiveListingsForOfferResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetDisplayActiveListingsForOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDisplayActiveListingsForOfferResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDisplayActiveListingsForOfferResponseMultiError, or nil if none found.
func (m *GetDisplayActiveListingsForOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisplayActiveListingsForOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisplayActiveListingsForOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisplayActiveListingsForOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisplayActiveListingsForOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOfferListings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDisplayActiveListingsForOfferResponseValidationError{
						field:  fmt.Sprintf("OfferListings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDisplayActiveListingsForOfferResponseValidationError{
						field:  fmt.Sprintf("OfferListings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDisplayActiveListingsForOfferResponseValidationError{
					field:  fmt.Sprintf("OfferListings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDisplayActiveListingsForOfferResponseMultiError(errors)
	}

	return nil
}

// GetDisplayActiveListingsForOfferResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetDisplayActiveListingsForOfferResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDisplayActiveListingsForOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisplayActiveListingsForOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisplayActiveListingsForOfferResponseMultiError) AllErrors() []error { return m }

// GetDisplayActiveListingsForOfferResponseValidationError is the validation
// error returned by GetDisplayActiveListingsForOfferResponse.Validate if the
// designated constraints aren't met.
type GetDisplayActiveListingsForOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisplayActiveListingsForOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisplayActiveListingsForOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisplayActiveListingsForOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisplayActiveListingsForOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisplayActiveListingsForOfferResponseValidationError) ErrorName() string {
	return "GetDisplayActiveListingsForOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisplayActiveListingsForOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisplayActiveListingsForOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisplayActiveListingsForOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisplayActiveListingsForOfferResponseValidationError{}

// Validate checks the field values on GetOffersRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersRequestMultiError, or nil if none found.
func (m *GetOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RedemptionMode

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOffersRequestMultiError(errors)
	}

	return nil
}

// GetOffersRequestMultiError is an error wrapping multiple validation errors
// returned by GetOffersRequest.ValidateAll() if the designated constraints
// aren't met.
type GetOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersRequestMultiError) AllErrors() []error { return m }

// GetOffersRequestValidationError is the validation error returned by
// GetOffersRequest.Validate if the designated constraints aren't met.
type GetOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersRequestValidationError) ErrorName() string { return "GetOffersRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersRequestValidationError{}

// Validate checks the field values on GetOffersResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersResponseMultiError, or nil if none found.
func (m *GetOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOffersResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetOfferIdToListingMap()))
		i := 0
		for key := range m.GetOfferIdToListingMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOfferIdToListingMap()[key]
			_ = val

			// no validation rules for OfferIdToListingMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetOffersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetOffersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetOffersResponseValidationError{
						field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for OfferIdToAvailableUserLevelInventoryMap

	// no validation rules for OfferIdToUserLevelAttemptsRemainingMap

	// no validation rules for OfferIdToRemainingMonthlyRedemptionsMap

	if len(errors) > 0 {
		return GetOffersResponseMultiError(errors)
	}

	return nil
}

// GetOffersResponseMultiError is an error wrapping multiple validation errors
// returned by GetOffersResponse.ValidateAll() if the designated constraints
// aren't met.
type GetOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersResponseMultiError) AllErrors() []error { return m }

// GetOffersResponseValidationError is the validation error returned by
// GetOffersResponse.Validate if the designated constraints aren't met.
type GetOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersResponseValidationError) ErrorName() string {
	return "GetOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersResponseValidationError{}

// Validate checks the field values on GetOffersByFiltersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOffersByFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersByFiltersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersByFiltersRequestMultiError, or nil if none found.
func (m *GetOffersByFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersByFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetOfferFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersByFiltersRequestValidationError{
					field:  "OfferFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersByFiltersRequestValidationError{
					field:  "OfferFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersByFiltersRequestValidationError{
				field:  "OfferFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersByFiltersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersByFiltersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersByFiltersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOffersByFiltersRequestMultiError(errors)
	}

	return nil
}

// GetOffersByFiltersRequestMultiError is an error wrapping multiple validation
// errors returned by GetOffersByFiltersRequest.ValidateAll() if the
// designated constraints aren't met.
type GetOffersByFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersByFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersByFiltersRequestMultiError) AllErrors() []error { return m }

// GetOffersByFiltersRequestValidationError is the validation error returned by
// GetOffersByFiltersRequest.Validate if the designated constraints aren't met.
type GetOffersByFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersByFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersByFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersByFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersByFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersByFiltersRequestValidationError) ErrorName() string {
	return "GetOffersByFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOffersByFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersByFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersByFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersByFiltersRequestValidationError{}

// Validate checks the field values on GetOffersByFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOffersByFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersByFiltersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersByFiltersResponseMultiError, or nil if none found.
func (m *GetOffersByFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersByFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersByFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersByFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersByFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersByFiltersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersByFiltersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersByFiltersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOffersByFiltersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOffersByFiltersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOffersByFiltersResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetOfferIdToListingMap()))
		i := 0
		for key := range m.GetOfferIdToListingMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOfferIdToListingMap()[key]
			_ = val

			// no validation rules for OfferIdToListingMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetOffersByFiltersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetOffersByFiltersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetOffersByFiltersResponseValidationError{
						field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for OfferIdToAvailableUserLevelInventoryMap

	// no validation rules for OfferIdToUserLevelAttemptsRemainingMap

	// no validation rules for OfferIdToRemainingMonthlyRedemptionsMap

	if len(errors) > 0 {
		return GetOffersByFiltersResponseMultiError(errors)
	}

	return nil
}

// GetOffersByFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by GetOffersByFiltersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetOffersByFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersByFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersByFiltersResponseMultiError) AllErrors() []error { return m }

// GetOffersByFiltersResponseValidationError is the validation error returned
// by GetOffersByFiltersResponse.Validate if the designated constraints aren't met.
type GetOffersByFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersByFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersByFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersByFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersByFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersByFiltersResponseValidationError) ErrorName() string {
	return "GetOffersByFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOffersByFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersByFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersByFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersByFiltersResponseValidationError{}

// Validate checks the field values on OfferFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferFilters with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferFiltersMultiError, or
// nil if none found.
func (m *OfferFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferFiltersValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferFiltersValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferFiltersValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTillTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferFiltersValidationError{
					field:  "TillTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferFiltersValidationError{
					field:  "TillTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTillTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferFiltersValidationError{
				field:  "TillTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardOfferType

	// no validation rules for RedemptionMode

	if len(errors) > 0 {
		return OfferFiltersMultiError(errors)
	}

	return nil
}

// OfferFiltersMultiError is an error wrapping multiple validation errors
// returned by OfferFilters.ValidateAll() if the designated constraints aren't met.
type OfferFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferFiltersMultiError) AllErrors() []error { return m }

// OfferFiltersValidationError is the validation error returned by
// OfferFilters.Validate if the designated constraints aren't met.
type OfferFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferFiltersValidationError) ErrorName() string { return "OfferFiltersValidationError" }

// Error satisfies the builtin error interface
func (e OfferFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferFiltersValidationError{}

// Validate checks the field values on GetCardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardOffersRequestMultiError, or nil if none found.
func (m *GetCardOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RedemptionMode

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardOffersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardOffersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardOffersRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiltersV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardOffersRequestValidationError{
					field:  "FiltersV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardOffersRequestValidationError{
					field:  "FiltersV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiltersV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardOffersRequestValidationError{
				field:  "FiltersV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCardOffersRequestMultiError(errors)
	}

	return nil
}

// GetCardOffersRequestMultiError is an error wrapping multiple validation
// errors returned by GetCardOffersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCardOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardOffersRequestMultiError) AllErrors() []error { return m }

// GetCardOffersRequestValidationError is the validation error returned by
// GetCardOffersRequest.Validate if the designated constraints aren't met.
type GetCardOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardOffersRequestValidationError) ErrorName() string {
	return "GetCardOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardOffersRequestValidationError{}

// Validate checks the field values on GetCardOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCardOffersResponseMultiError, or nil if none found.
func (m *GetCardOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCardOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCardOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCardOffersResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetOfferIdToListingMap()))
		i := 0
		for key := range m.GetOfferIdToListingMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOfferIdToListingMap()[key]
			_ = val

			// no validation rules for OfferIdToListingMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetCardOffersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetCardOffersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetCardOffersResponseValidationError{
						field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetCardOffersResponseMultiError(errors)
	}

	return nil
}

// GetCardOffersResponseMultiError is an error wrapping multiple validation
// errors returned by GetCardOffersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardOffersResponseMultiError) AllErrors() []error { return m }

// GetCardOffersResponseValidationError is the validation error returned by
// GetCardOffersResponse.Validate if the designated constraints aren't met.
type GetCardOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardOffersResponseValidationError) ErrorName() string {
	return "GetCardOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardOffersResponseValidationError{}
