// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/analyser/investment/model
package model

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &AnalysisTasksEvent{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *AnalysisTasksEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}
