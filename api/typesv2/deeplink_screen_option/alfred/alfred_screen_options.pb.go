// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/alfred/alfred_screen_options.proto

package alfred

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Screen options for RECORD_CONSENT_PROFILE_UPDATE_API
type RecordConsentProfileUpdateScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header           *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId          string                                     `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientRequestId  string                                     `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	ConsentRequestId string                                     `protobuf:"bytes,4,opt,name=consent_request_id,json=consentRequestId,proto3" json:"consent_request_id,omitempty"`
	ConsentType      string                                     `protobuf:"bytes,5,opt,name=consent_type,json=consentType,proto3" json:"consent_type,omitempty"`
}

func (x *RecordConsentProfileUpdateScreenOptions) Reset() {
	*x = RecordConsentProfileUpdateScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordConsentProfileUpdateScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordConsentProfileUpdateScreenOptions) ProtoMessage() {}

func (x *RecordConsentProfileUpdateScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordConsentProfileUpdateScreenOptions.ProtoReflect.Descriptor instead.
func (*RecordConsentProfileUpdateScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *RecordConsentProfileUpdateScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RecordConsentProfileUpdateScreenOptions) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RecordConsentProfileUpdateScreenOptions) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *RecordConsentProfileUpdateScreenOptions) GetConsentRequestId() string {
	if x != nil {
		return x.ConsentRequestId
	}
	return ""
}

func (x *RecordConsentProfileUpdateScreenOptions) GetConsentType() string {
	if x != nil {
		return x.ConsentType
	}
	return ""
}

// Screen options for SAVE_DOB_FOR_PROFILE_UPDATE
type SaveDobForProfileUpdateScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header          *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClientRequestId string                                     `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	Dob             *date.Date                                 `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
}

func (x *SaveDobForProfileUpdateScreenOptions) Reset() {
	*x = SaveDobForProfileUpdateScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveDobForProfileUpdateScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveDobForProfileUpdateScreenOptions) ProtoMessage() {}

func (x *SaveDobForProfileUpdateScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveDobForProfileUpdateScreenOptions.ProtoReflect.Descriptor instead.
func (*SaveDobForProfileUpdateScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *SaveDobForProfileUpdateScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaveDobForProfileUpdateScreenOptions) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *SaveDobForProfileUpdateScreenOptions) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

type RequestChoiceBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header         *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Title          *common.Text                               `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	RequestChoices []*RequestChoice                           `protobuf:"bytes,3,rep,name=request_choices,json=requestChoices,proto3" json:"request_choices,omitempty"`
}

func (x *RequestChoiceBottomSheetScreenOptions) Reset() {
	*x = RequestChoiceBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestChoiceBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestChoiceBottomSheetScreenOptions) ProtoMessage() {}

func (x *RequestChoiceBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestChoiceBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*RequestChoiceBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *RequestChoiceBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RequestChoiceBottomSheetScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *RequestChoiceBottomSheetScreenOptions) GetRequestChoices() []*RequestChoice {
	if x != nil {
		return x.RequestChoices
	}
	return nil
}

type RequestChoice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title    *common.Text    `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle *common.Text    `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	Price    *common.Text    `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	Ctas     []*deeplink.Cta `protobuf:"bytes,4,rep,name=ctas,proto3" json:"ctas,omitempty"`
}

func (x *RequestChoice) Reset() {
	*x = RequestChoice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestChoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestChoice) ProtoMessage() {}

func (x *RequestChoice) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestChoice.ProtoReflect.Descriptor instead.
func (*RequestChoice) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *RequestChoice) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *RequestChoice) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *RequestChoice) GetPrice() *common.Text {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *RequestChoice) GetCtas() []*deeplink.Cta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

type ProvisionNewRequestScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header      *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestType string                                     `protobuf:"bytes,2,opt,name=request_type,json=requestType,proto3" json:"request_type,omitempty"`
	// OPTIONAL blob data which can be used by different request handlers to determine some parameters e.g. year in case of tax document generation
	Blob []byte `protobuf:"bytes,3,opt,name=blob,proto3" json:"blob,omitempty"`
}

func (x *ProvisionNewRequestScreenOptions) Reset() {
	*x = ProvisionNewRequestScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProvisionNewRequestScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProvisionNewRequestScreenOptions) ProtoMessage() {}

func (x *ProvisionNewRequestScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProvisionNewRequestScreenOptions.ProtoReflect.Descriptor instead.
func (*ProvisionNewRequestScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *ProvisionNewRequestScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProvisionNewRequestScreenOptions) GetRequestType() string {
	if x != nil {
		return x.RequestType
	}
	return ""
}

func (x *ProvisionNewRequestScreenOptions) GetBlob() []byte {
	if x != nil {
		return x.Blob
	}
	return nil
}

// screen options for REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET
// figma: https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10655&t=EdZ92LttpzP4EDW3-4
type RequestChoiceScrollableBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Title  *ui.IconTextComponent                      `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// descriptions for options
	// e.g. CHOOSE A FINANCIAL YEAR
	ChoicesDescription *ui.IconTextComponent `protobuf:"bytes,3,opt,name=choices_description,json=choicesDescription,proto3" json:"choices_description,omitempty"`
	Choices            []*RequestChoiceV2    `protobuf:"bytes,4,rep,name=choices,proto3" json:"choices,omitempty"`
	FooterInfo         *ui.IconTextComponent `protobuf:"bytes,5,opt,name=footer_info,json=footerInfo,proto3" json:"footer_info,omitempty"`
	// the cta only defines buttons state (text and color), the action on click is determined via the cta od selected request_choice
	Cta *deeplink.Cta `protobuf:"bytes,6,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) Reset() {
	*x = RequestChoiceScrollableBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestChoiceScrollableBottomSheetScreenOptions) ProtoMessage() {}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestChoiceScrollableBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*RequestChoiceScrollableBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) GetChoicesDescription() *ui.IconTextComponent {
	if x != nil {
		return x.ChoicesDescription
	}
	return nil
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) GetChoices() []*RequestChoiceV2 {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) GetFooterInfo() *ui.IconTextComponent {
	if x != nil {
		return x.FooterInfo
	}
	return nil
}

func (x *RequestChoiceScrollableBottomSheetScreenOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

type RequestChoiceV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChoiceText *ui.IconTextComponent `protobuf:"bytes,1,opt,name=choice_text,json=choiceText,proto3" json:"choice_text,omitempty"`
	// action to be applied when the submit button on bottom sheet is clicked
	Cta *deeplink.Cta `protobuf:"bytes,2,opt,name=cta,proto3" json:"cta,omitempty"`
	// default select state, note that only one option is selected at max
	IsSelected bool `protobuf:"varint,3,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
}

func (x *RequestChoiceV2) Reset() {
	*x = RequestChoiceV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestChoiceV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestChoiceV2) ProtoMessage() {}

func (x *RequestChoiceV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestChoiceV2.ProtoReflect.Descriptor instead.
func (*RequestChoiceV2) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *RequestChoiceV2) GetChoiceText() *ui.IconTextComponent {
	if x != nil {
		return x.ChoiceText
	}
	return nil
}

func (x *RequestChoiceV2) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *RequestChoiceV2) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

// screen options for INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET
// figma: https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10700&t=EdZ92LttpzP4EDW3-4
type InfoAcknowledgementBottomSheetScreenOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Title  *ui.IconTextComponent                      `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon   *common.VisualElement                      `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	// description text below icon
	Description *ui.IconTextComponent `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Ctas        []*deeplink.Cta       `protobuf:"bytes,7,rep,name=ctas,proto3" json:"ctas,omitempty"`
}

func (x *InfoAcknowledgementBottomSheetScreenOption) Reset() {
	*x = InfoAcknowledgementBottomSheetScreenOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoAcknowledgementBottomSheetScreenOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoAcknowledgementBottomSheetScreenOption) ProtoMessage() {}

func (x *InfoAcknowledgementBottomSheetScreenOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoAcknowledgementBottomSheetScreenOption.ProtoReflect.Descriptor instead.
func (*InfoAcknowledgementBottomSheetScreenOption) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *InfoAcknowledgementBottomSheetScreenOption) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *InfoAcknowledgementBottomSheetScreenOption) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *InfoAcknowledgementBottomSheetScreenOption) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *InfoAcknowledgementBottomSheetScreenOption) GetDescription() *ui.IconTextComponent {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *InfoAcknowledgementBottomSheetScreenOption) GetCtas() []*deeplink.Cta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

// screen options for ALFRED_REQUEST_CHOICE
// when client receives this deeplink then client will make call to GetRequestChoices rpc
type AlfredRequestChoicesScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// stringified RequestChoiceFilter. Refer: api/alfred/internal/request_choice_filter.proto
	ChoicesFilter []byte `protobuf:"bytes,2,opt,name=choices_filter,json=choicesFilter,proto3" json:"choices_filter,omitempty"`
}

func (x *AlfredRequestChoicesScreenOptions) Reset() {
	*x = AlfredRequestChoicesScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlfredRequestChoicesScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlfredRequestChoicesScreenOptions) ProtoMessage() {}

func (x *AlfredRequestChoicesScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlfredRequestChoicesScreenOptions.ProtoReflect.Descriptor instead.
func (*AlfredRequestChoicesScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP(), []int{8}
}

func (x *AlfredRequestChoicesScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AlfredRequestChoicesScreenOptions) GetChoicesFilter() []byte {
	if x != nil {
		return x.ChoicesFilter
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDesc = []byte{
	0x0a, 0x45, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2f, 0x61, 0x6c, 0x66, 0x72,
	0x65, 0x64, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x6c, 0x66, 0x72,
	0x65, 0x64, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x91, 0x02, 0x0a, 0x27, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x24, 0x53, 0x61, 0x76, 0x65,
	0x44, 0x6f, 0x62, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x03,
	0x64, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f,
	0x62, 0x22, 0x8a, 0x02, 0x0a, 0x25, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f,
	0x69, 0x63, 0x65, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x61, 0x0a, 0x0f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22, 0xd1,
	0x01, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75,
	0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x63, 0x74, 0x61, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x04, 0x63, 0x74,
	0x61, 0x73, 0x22, 0xa9, 0x01, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x4e, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6c,
	0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x62, 0x6c, 0x6f, 0x62, 0x22, 0xd2,
	0x03, 0x0a, 0x2f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65,
	0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x13, 0x63,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x63, 0x68, 0x6f,
	0x69, 0x63, 0x65, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x54, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x56, 0x32, 0x52, 0x07, 0x63, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x66,
	0x6f, 0x6f, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x03,
	0x63, 0x74, 0x61, 0x22, 0xa0, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x56, 0x32, 0x12, 0x42, 0x0a, 0x0b, 0x63, 0x68, 0x6f, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x0a, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x03, 0x63,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61,
	0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xdd, 0x02, 0x0a, 0x2a, 0x49, 0x6e, 0x66, 0x6f, 0x41,
	0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35,
	0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x04, 0x63, 0x74,
	0x61, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61,
	0x52, 0x04, 0x63, 0x74, 0x61, 0x73, 0x22, 0x9a, 0x01, 0x0a, 0x21, 0x41, 0x6c, 0x66, 0x72, 0x65,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x42, 0x86, 0x01, 0x0a, 0x40, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x50, 0x01, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_goTypes = []interface{}{
	(*RecordConsentProfileUpdateScreenOptions)(nil),         // 0: api.typesv2.deeplink_screen_option.alfred.RecordConsentProfileUpdateScreenOptions
	(*SaveDobForProfileUpdateScreenOptions)(nil),            // 1: api.typesv2.deeplink_screen_option.alfred.SaveDobForProfileUpdateScreenOptions
	(*RequestChoiceBottomSheetScreenOptions)(nil),           // 2: api.typesv2.deeplink_screen_option.alfred.RequestChoiceBottomSheetScreenOptions
	(*RequestChoice)(nil),                                   // 3: api.typesv2.deeplink_screen_option.alfred.RequestChoice
	(*ProvisionNewRequestScreenOptions)(nil),                // 4: api.typesv2.deeplink_screen_option.alfred.ProvisionNewRequestScreenOptions
	(*RequestChoiceScrollableBottomSheetScreenOptions)(nil), // 5: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions
	(*RequestChoiceV2)(nil),                                 // 6: api.typesv2.deeplink_screen_option.alfred.RequestChoiceV2
	(*InfoAcknowledgementBottomSheetScreenOption)(nil),      // 7: api.typesv2.deeplink_screen_option.alfred.InfoAcknowledgementBottomSheetScreenOption
	(*AlfredRequestChoicesScreenOptions)(nil),               // 8: api.typesv2.deeplink_screen_option.alfred.AlfredRequestChoicesScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil),       // 9: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*date.Date)(nil),                                       // 10: google.type.Date
	(*common.Text)(nil),                                     // 11: api.typesv2.common.Text
	(*deeplink.Cta)(nil),                                    // 12: frontend.deeplink.Cta
	(*ui.IconTextComponent)(nil),                            // 13: api.typesv2.ui.IconTextComponent
	(*common.VisualElement)(nil),                            // 14: api.typesv2.common.VisualElement
}
var file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_depIdxs = []int32{
	9,  // 0: api.typesv2.deeplink_screen_option.alfred.RecordConsentProfileUpdateScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	9,  // 1: api.typesv2.deeplink_screen_option.alfred.SaveDobForProfileUpdateScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	10, // 2: api.typesv2.deeplink_screen_option.alfred.SaveDobForProfileUpdateScreenOptions.dob:type_name -> google.type.Date
	9,  // 3: api.typesv2.deeplink_screen_option.alfred.RequestChoiceBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	11, // 4: api.typesv2.deeplink_screen_option.alfred.RequestChoiceBottomSheetScreenOptions.title:type_name -> api.typesv2.common.Text
	3,  // 5: api.typesv2.deeplink_screen_option.alfred.RequestChoiceBottomSheetScreenOptions.request_choices:type_name -> api.typesv2.deeplink_screen_option.alfred.RequestChoice
	11, // 6: api.typesv2.deeplink_screen_option.alfred.RequestChoice.title:type_name -> api.typesv2.common.Text
	11, // 7: api.typesv2.deeplink_screen_option.alfred.RequestChoice.subtitle:type_name -> api.typesv2.common.Text
	11, // 8: api.typesv2.deeplink_screen_option.alfred.RequestChoice.price:type_name -> api.typesv2.common.Text
	12, // 9: api.typesv2.deeplink_screen_option.alfred.RequestChoice.ctas:type_name -> frontend.deeplink.Cta
	9,  // 10: api.typesv2.deeplink_screen_option.alfred.ProvisionNewRequestScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	9,  // 11: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	13, // 12: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions.title:type_name -> api.typesv2.ui.IconTextComponent
	13, // 13: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions.choices_description:type_name -> api.typesv2.ui.IconTextComponent
	6,  // 14: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions.choices:type_name -> api.typesv2.deeplink_screen_option.alfred.RequestChoiceV2
	13, // 15: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions.footer_info:type_name -> api.typesv2.ui.IconTextComponent
	12, // 16: api.typesv2.deeplink_screen_option.alfred.RequestChoiceScrollableBottomSheetScreenOptions.cta:type_name -> frontend.deeplink.Cta
	13, // 17: api.typesv2.deeplink_screen_option.alfred.RequestChoiceV2.choice_text:type_name -> api.typesv2.ui.IconTextComponent
	12, // 18: api.typesv2.deeplink_screen_option.alfred.RequestChoiceV2.cta:type_name -> frontend.deeplink.Cta
	9,  // 19: api.typesv2.deeplink_screen_option.alfred.InfoAcknowledgementBottomSheetScreenOption.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	13, // 20: api.typesv2.deeplink_screen_option.alfred.InfoAcknowledgementBottomSheetScreenOption.title:type_name -> api.typesv2.ui.IconTextComponent
	14, // 21: api.typesv2.deeplink_screen_option.alfred.InfoAcknowledgementBottomSheetScreenOption.icon:type_name -> api.typesv2.common.VisualElement
	13, // 22: api.typesv2.deeplink_screen_option.alfred.InfoAcknowledgementBottomSheetScreenOption.description:type_name -> api.typesv2.ui.IconTextComponent
	12, // 23: api.typesv2.deeplink_screen_option.alfred.InfoAcknowledgementBottomSheetScreenOption.ctas:type_name -> frontend.deeplink.Cta
	9,  // 24: api.typesv2.deeplink_screen_option.alfred.AlfredRequestChoicesScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordConsentProfileUpdateScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveDobForProfileUpdateScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestChoiceBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestChoice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProvisionNewRequestScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestChoiceScrollableBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestChoiceV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoAcknowledgementBottomSheetScreenOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlfredRequestChoicesScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_alfred_alfred_screen_options_proto_depIdxs = nil
}
