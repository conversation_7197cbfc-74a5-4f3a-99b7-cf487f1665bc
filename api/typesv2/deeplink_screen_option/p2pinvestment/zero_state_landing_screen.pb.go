// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/p2pinvestment/zero_state_landing_screen.proto

package p2pinvestment

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// screen: P2P_ZERO_STATE_LANDING_SCREEN
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=18678-11323&mode=design&t=YRDwhdw2amsS9yUn-0
type P2PZeroStateLandingScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// ordered list of components to be displayed from top to bottom
	LandingComponents []*LandingComponent `protobuf:"bytes,2,rep,name=landing_components,json=landingComponents,proto3" json:"landing_components,omitempty"`
	// event properties that should be sent by the client along with P2PZeroStateLoaded, P2PPlanListActioned event
	// ref - https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit#gid=1102015672
	EventProperties map[string]string `protobuf:"bytes,3,rep,name=event_properties,json=eventProperties,proto3" json:"event_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *P2PZeroStateLandingScreenOptions) Reset() {
	*x = P2PZeroStateLandingScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PZeroStateLandingScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PZeroStateLandingScreenOptions) ProtoMessage() {}

func (x *P2PZeroStateLandingScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PZeroStateLandingScreenOptions.ProtoReflect.Descriptor instead.
func (*P2PZeroStateLandingScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescGZIP(), []int{0}
}

func (x *P2PZeroStateLandingScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *P2PZeroStateLandingScreenOptions) GetLandingComponents() []*LandingComponent {
	if x != nil {
		return x.LandingComponents
	}
	return nil
}

func (x *P2PZeroStateLandingScreenOptions) GetEventProperties() map[string]string {
	if x != nil {
		return x.EventProperties
	}
	return nil
}

type LandingComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Component:
	//
	//	*LandingComponent_ChoosePlanTile
	//	*LandingComponent_InfoTile
	Component isLandingComponent_Component `protobuf_oneof:"component"`
}

func (x *LandingComponent) Reset() {
	*x = LandingComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LandingComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandingComponent) ProtoMessage() {}

func (x *LandingComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandingComponent.ProtoReflect.Descriptor instead.
func (*LandingComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescGZIP(), []int{1}
}

func (m *LandingComponent) GetComponent() isLandingComponent_Component {
	if m != nil {
		return m.Component
	}
	return nil
}

func (x *LandingComponent) GetChoosePlanTile() *deeplink.P2PChoosePlanTile {
	if x, ok := x.GetComponent().(*LandingComponent_ChoosePlanTile); ok {
		return x.ChoosePlanTile
	}
	return nil
}

func (x *LandingComponent) GetInfoTile() *ui.IconTextComponent {
	if x, ok := x.GetComponent().(*LandingComponent_InfoTile); ok {
		return x.InfoTile
	}
	return nil
}

type isLandingComponent_Component interface {
	isLandingComponent_Component()
}

type LandingComponent_ChoosePlanTile struct {
	// existing component from P2P_INVESTMENT_CHOOSE_PLAN
	ChoosePlanTile *deeplink.P2PChoosePlanTile `protobuf:"bytes,1,opt,name=choose_plan_tile,json=choosePlanTile,proto3,oneof"`
}

type LandingComponent_InfoTile struct {
	// tiles to provide additional info about jump - learn more about jump etc
	InfoTile *ui.IconTextComponent `protobuf:"bytes,2,opt,name=info_tile,json=infoTile,proto3,oneof"`
}

func (*LandingComponent_ChoosePlanTile) isLandingComponent_Component() {}

func (*LandingComponent_InfoTile) isLandingComponent_Component() {}

var File_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDesc = []byte{
	0x0a, 0x50, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x32, 0x70, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x30, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x32, 0x70, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbe, 0x03, 0x0a, 0x20, 0x50, 0x32, 0x50, 0x5a, 0x65, 0x72,
	0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x71, 0x0a, 0x12, 0x6c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x32, 0x70, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x6c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x92, 0x01,
	0x0a, 0x10, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x67, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x32,
	0x70, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x32, 0x50, 0x5a,
	0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x1a, 0x42, 0x0a, 0x14, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb3, 0x01, 0x0a, 0x10, 0x4c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x10, 0x63,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74, 0x69, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x50, 0x32, 0x50, 0x43, 0x68, 0x6f,
	0x6f, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x69, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x63,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x69, 0x6c, 0x65, 0x12, 0x40, 0x0a,
	0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x54, 0x69, 0x6c, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x94, 0x01, 0x0a,
	0x47, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x32, 0x70, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x01, 0x5a, 0x47, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x32, 0x70, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescData = file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_goTypes = []interface{}{
	(*P2PZeroStateLandingScreenOptions)(nil), // 0: api.typesv2.deeplink_screen_option.p2pinvestment.P2PZeroStateLandingScreenOptions
	(*LandingComponent)(nil),                 // 1: api.typesv2.deeplink_screen_option.p2pinvestment.LandingComponent
	nil,                                      // 2: api.typesv2.deeplink_screen_option.p2pinvestment.P2PZeroStateLandingScreenOptions.EventPropertiesEntry
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 3: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.P2PChoosePlanTile)(nil),                // 4: frontend.deeplink.P2PChoosePlanTile
	(*ui.IconTextComponent)(nil),                      // 5: api.typesv2.ui.IconTextComponent
}
var file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_depIdxs = []int32{
	3, // 0: api.typesv2.deeplink_screen_option.p2pinvestment.P2PZeroStateLandingScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	1, // 1: api.typesv2.deeplink_screen_option.p2pinvestment.P2PZeroStateLandingScreenOptions.landing_components:type_name -> api.typesv2.deeplink_screen_option.p2pinvestment.LandingComponent
	2, // 2: api.typesv2.deeplink_screen_option.p2pinvestment.P2PZeroStateLandingScreenOptions.event_properties:type_name -> api.typesv2.deeplink_screen_option.p2pinvestment.P2PZeroStateLandingScreenOptions.EventPropertiesEntry
	4, // 3: api.typesv2.deeplink_screen_option.p2pinvestment.LandingComponent.choose_plan_tile:type_name -> frontend.deeplink.P2PChoosePlanTile
	5, // 4: api.typesv2.deeplink_screen_option.p2pinvestment.LandingComponent.info_tile:type_name -> api.typesv2.ui.IconTextComponent
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() {
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_init()
}
func file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_init() {
	if File_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PZeroStateLandingScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LandingComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*LandingComponent_ChoosePlanTile)(nil),
		(*LandingComponent_InfoTile)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto = out.File
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_p2pinvestment_zero_state_landing_screen_proto_depIdxs = nil
}
