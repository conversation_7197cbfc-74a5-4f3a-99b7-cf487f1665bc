// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/grid/vertical_key_value_grid.proto

package grid

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on VerticalKeyValueGrid with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerticalKeyValueGrid) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerticalKeyValueGrid with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerticalKeyValueGridMultiError, or nil if none found.
func (m *VerticalKeyValueGrid) ValidateAll() error {
	return m.validate(true)
}

func (m *VerticalKeyValueGrid) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLines() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VerticalKeyValueGridValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VerticalKeyValueGridValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VerticalKeyValueGridValidationError{
					field:  fmt.Sprintf("Lines[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return VerticalKeyValueGridMultiError(errors)
	}

	return nil
}

// VerticalKeyValueGridMultiError is an error wrapping multiple validation
// errors returned by VerticalKeyValueGrid.ValidateAll() if the designated
// constraints aren't met.
type VerticalKeyValueGridMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerticalKeyValueGridMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerticalKeyValueGridMultiError) AllErrors() []error { return m }

// VerticalKeyValueGridValidationError is the validation error returned by
// VerticalKeyValueGrid.Validate if the designated constraints aren't met.
type VerticalKeyValueGridValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerticalKeyValueGridValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerticalKeyValueGridValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerticalKeyValueGridValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerticalKeyValueGridValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerticalKeyValueGridValidationError) ErrorName() string {
	return "VerticalKeyValueGridValidationError"
}

// Error satisfies the builtin error interface
func (e VerticalKeyValueGridValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerticalKeyValueGrid.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerticalKeyValueGridValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerticalKeyValueGridValidationError{}

// Validate checks the field values on VerticalKeyValueGridLine with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerticalKeyValueGridLine) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerticalKeyValueGridLine with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerticalKeyValueGridLineMultiError, or nil if none found.
func (m *VerticalKeyValueGridLine) ValidateAll() error {
	return m.validate(true)
}

func (m *VerticalKeyValueGridLine) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LineAlignment

	for idx, item := range m.GetPairs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VerticalKeyValueGridLineValidationError{
						field:  fmt.Sprintf("Pairs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VerticalKeyValueGridLineValidationError{
						field:  fmt.Sprintf("Pairs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VerticalKeyValueGridLineValidationError{
					field:  fmt.Sprintf("Pairs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return VerticalKeyValueGridLineMultiError(errors)
	}

	return nil
}

// VerticalKeyValueGridLineMultiError is an error wrapping multiple validation
// errors returned by VerticalKeyValueGridLine.ValidateAll() if the designated
// constraints aren't met.
type VerticalKeyValueGridLineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerticalKeyValueGridLineMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerticalKeyValueGridLineMultiError) AllErrors() []error { return m }

// VerticalKeyValueGridLineValidationError is the validation error returned by
// VerticalKeyValueGridLine.Validate if the designated constraints aren't met.
type VerticalKeyValueGridLineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerticalKeyValueGridLineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerticalKeyValueGridLineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerticalKeyValueGridLineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerticalKeyValueGridLineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerticalKeyValueGridLineValidationError) ErrorName() string {
	return "VerticalKeyValueGridLineValidationError"
}

// Error satisfies the builtin error interface
func (e VerticalKeyValueGridLineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerticalKeyValueGridLine.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerticalKeyValueGridLineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerticalKeyValueGridLineValidationError{}
