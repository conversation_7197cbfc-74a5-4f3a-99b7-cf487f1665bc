// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/swipe_button.proto

package ui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SwipeButton with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SwipeButton) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SwipeButton with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SwipeButtonMultiError, or
// nil if none found.
func (m *SwipeButton) ValidateAll() error {
	return m.validate(true)
}

func (m *SwipeButton) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDisplayString()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwipeButtonValidationError{
					field:  "DisplayString",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwipeButtonValidationError{
					field:  "DisplayString",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayString()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwipeButtonValidationError{
				field:  "DisplayString",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwipeButtonValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwipeButtonValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwipeButtonValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SwipeButtonMultiError(errors)
	}

	return nil
}

// SwipeButtonMultiError is an error wrapping multiple validation errors
// returned by SwipeButton.ValidateAll() if the designated constraints aren't met.
type SwipeButtonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SwipeButtonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SwipeButtonMultiError) AllErrors() []error { return m }

// SwipeButtonValidationError is the validation error returned by
// SwipeButton.Validate if the designated constraints aren't met.
type SwipeButtonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SwipeButtonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SwipeButtonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SwipeButtonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SwipeButtonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SwipeButtonValidationError) ErrorName() string { return "SwipeButtonValidationError" }

// Error satisfies the builtin error interface
func (e SwipeButtonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSwipeButton.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SwipeButtonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SwipeButtonValidationError{}
