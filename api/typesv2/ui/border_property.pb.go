// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/border_property.proto

package ui

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BorderStyle int32

const (
	BorderStyle_UNDEFINED   BorderStyle = 0
	BorderStyle_SOLID_LINE  BorderStyle = 1
	BorderStyle_DASHED_LINE BorderStyle = 2
)

// Enum value maps for BorderStyle.
var (
	BorderStyle_name = map[int32]string{
		0: "UNDEFINED",
		1: "SOLID_LINE",
		2: "DASHED_LINE",
	}
	BorderStyle_value = map[string]int32{
		"UNDEFINED":   0,
		"SOLID_LINE":  1,
		"DASHED_LINE": 2,
	}
)

func (x BorderStyle) Enum() *BorderStyle {
	p := new(BorderStyle)
	*p = x
	return p
}

func (x BorderStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BorderStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_ui_border_property_proto_enumTypes[0].Descriptor()
}

func (BorderStyle) Type() protoreflect.EnumType {
	return &file_api_typesv2_ui_border_property_proto_enumTypes[0]
}

func (x BorderStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BorderStyle.Descriptor instead.
func (BorderStyle) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_ui_border_property_proto_rawDescGZIP(), []int{0}
}

type BorderProperty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BorderThickness int32       `protobuf:"varint,1,opt,name=border_thickness,json=borderThickness,proto3" json:"border_thickness,omitempty"`
	BorderColor     string      `protobuf:"bytes,2,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	BorderType      BorderStyle `protobuf:"varint,3,opt,name=border_type,json=borderType,proto3,enum=api.typesv2.ui.BorderStyle" json:"border_type,omitempty"`
}

func (x *BorderProperty) Reset() {
	*x = BorderProperty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_border_property_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BorderProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorderProperty) ProtoMessage() {}

func (x *BorderProperty) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_border_property_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorderProperty.ProtoReflect.Descriptor instead.
func (*BorderProperty) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_border_property_proto_rawDescGZIP(), []int{0}
}

func (x *BorderProperty) GetBorderThickness() int32 {
	if x != nil {
		return x.BorderThickness
	}
	return 0
}

func (x *BorderProperty) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *BorderProperty) GetBorderType() BorderStyle {
	if x != nil {
		return x.BorderType
	}
	return BorderStyle_UNDEFINED
}

var File_api_typesv2_ui_border_property_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_border_property_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x22, 0x9c, 0x01, 0x0a, 0x0e, 0x42, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x69, 0x63, 0x6b, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x68, 0x69, 0x63, 0x6b,
	0x6e, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x0a, 0x62, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x3d, 0x0a, 0x0b, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x79, 0x6c, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x4f, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x49, 0x4e,
	0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x41, 0x53, 0x48, 0x45, 0x44, 0x5f, 0x4c, 0x49,
	0x4e, 0x45, 0x10, 0x02, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x5a, 0x25, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_border_property_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_border_property_proto_rawDescData = file_api_typesv2_ui_border_property_proto_rawDesc
)

func file_api_typesv2_ui_border_property_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_border_property_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_border_property_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_border_property_proto_rawDescData)
	})
	return file_api_typesv2_ui_border_property_proto_rawDescData
}

var file_api_typesv2_ui_border_property_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_ui_border_property_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_typesv2_ui_border_property_proto_goTypes = []interface{}{
	(BorderStyle)(0),       // 0: api.typesv2.ui.BorderStyle
	(*BorderProperty)(nil), // 1: api.typesv2.ui.BorderProperty
}
var file_api_typesv2_ui_border_property_proto_depIdxs = []int32{
	0, // 0: api.typesv2.ui.BorderProperty.border_type:type_name -> api.typesv2.ui.BorderStyle
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_border_property_proto_init() }
func file_api_typesv2_ui_border_property_proto_init() {
	if File_api_typesv2_ui_border_property_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_border_property_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BorderProperty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_border_property_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_border_property_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_border_property_proto_depIdxs,
		EnumInfos:         file_api_typesv2_ui_border_property_proto_enumTypes,
		MessageInfos:      file_api_typesv2_ui_border_property_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_border_property_proto = out.File
	file_api_typesv2_ui_border_property_proto_rawDesc = nil
	file_api_typesv2_ui_border_property_proto_goTypes = nil
	file_api_typesv2_ui_border_property_proto_depIdxs = nil
}
