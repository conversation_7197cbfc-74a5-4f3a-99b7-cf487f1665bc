// Code generated by MockGen. DO NOT EDIT.
// Source: api/preapprovedloan/inbound_notification/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	inbound_notification "github.com/epifi/gamma/api/preapprovedloan/inbound_notification"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLoanInboundNotificationClient is a mock of LoanInboundNotificationClient interface.
type MockLoanInboundNotificationClient struct {
	ctrl     *gomock.Controller
	recorder *MockLoanInboundNotificationClientMockRecorder
}

// MockLoanInboundNotificationClientMockRecorder is the mock recorder for MockLoanInboundNotificationClient.
type MockLoanInboundNotificationClientMockRecorder struct {
	mock *MockLoanInboundNotificationClient
}

// NewMockLoanInboundNotificationClient creates a new mock instance.
func NewMockLoanInboundNotificationClient(ctrl *gomock.Controller) *MockLoanInboundNotificationClient {
	mock := &MockLoanInboundNotificationClient{ctrl: ctrl}
	mock.recorder = &MockLoanInboundNotificationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanInboundNotificationClient) EXPECT() *MockLoanInboundNotificationClientMockRecorder {
	return m.recorder
}

// ProcessLoanInboundTransaction mocks base method.
func (m *MockLoanInboundNotificationClient) ProcessLoanInboundTransaction(ctx context.Context, in *inbound_notification.ProcessLoanInboundTransactionRequest, opts ...grpc.CallOption) (*inbound_notification.ProcessLoanInboundTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessLoanInboundTransaction", varargs...)
	ret0, _ := ret[0].(*inbound_notification.ProcessLoanInboundTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessLoanInboundTransaction indicates an expected call of ProcessLoanInboundTransaction.
func (mr *MockLoanInboundNotificationClientMockRecorder) ProcessLoanInboundTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessLoanInboundTransaction", reflect.TypeOf((*MockLoanInboundNotificationClient)(nil).ProcessLoanInboundTransaction), varargs...)
}

// MockLoanInboundNotificationServer is a mock of LoanInboundNotificationServer interface.
type MockLoanInboundNotificationServer struct {
	ctrl     *gomock.Controller
	recorder *MockLoanInboundNotificationServerMockRecorder
}

// MockLoanInboundNotificationServerMockRecorder is the mock recorder for MockLoanInboundNotificationServer.
type MockLoanInboundNotificationServerMockRecorder struct {
	mock *MockLoanInboundNotificationServer
}

// NewMockLoanInboundNotificationServer creates a new mock instance.
func NewMockLoanInboundNotificationServer(ctrl *gomock.Controller) *MockLoanInboundNotificationServer {
	mock := &MockLoanInboundNotificationServer{ctrl: ctrl}
	mock.recorder = &MockLoanInboundNotificationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanInboundNotificationServer) EXPECT() *MockLoanInboundNotificationServerMockRecorder {
	return m.recorder
}

// ProcessLoanInboundTransaction mocks base method.
func (m *MockLoanInboundNotificationServer) ProcessLoanInboundTransaction(arg0 context.Context, arg1 *inbound_notification.ProcessLoanInboundTransactionRequest) (*inbound_notification.ProcessLoanInboundTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessLoanInboundTransaction", arg0, arg1)
	ret0, _ := ret[0].(*inbound_notification.ProcessLoanInboundTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessLoanInboundTransaction indicates an expected call of ProcessLoanInboundTransaction.
func (mr *MockLoanInboundNotificationServerMockRecorder) ProcessLoanInboundTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessLoanInboundTransaction", reflect.TypeOf((*MockLoanInboundNotificationServer)(nil).ProcessLoanInboundTransaction), arg0, arg1)
}

// MockUnsafeLoanInboundNotificationServer is a mock of UnsafeLoanInboundNotificationServer interface.
type MockUnsafeLoanInboundNotificationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLoanInboundNotificationServerMockRecorder
}

// MockUnsafeLoanInboundNotificationServerMockRecorder is the mock recorder for MockUnsafeLoanInboundNotificationServer.
type MockUnsafeLoanInboundNotificationServerMockRecorder struct {
	mock *MockUnsafeLoanInboundNotificationServer
}

// NewMockUnsafeLoanInboundNotificationServer creates a new mock instance.
func NewMockUnsafeLoanInboundNotificationServer(ctrl *gomock.Controller) *MockUnsafeLoanInboundNotificationServer {
	mock := &MockUnsafeLoanInboundNotificationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLoanInboundNotificationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLoanInboundNotificationServer) EXPECT() *MockUnsafeLoanInboundNotificationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLoanInboundNotificationServer mocks base method.
func (m *MockUnsafeLoanInboundNotificationServer) mustEmbedUnimplementedLoanInboundNotificationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLoanInboundNotificationServer")
}

// mustEmbedUnimplementedLoanInboundNotificationServer indicates an expected call of mustEmbedUnimplementedLoanInboundNotificationServer.
func (mr *MockUnsafeLoanInboundNotificationServerMockRecorder) mustEmbedUnimplementedLoanInboundNotificationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLoanInboundNotificationServer", reflect.TypeOf((*MockUnsafeLoanInboundNotificationServer)(nil).mustEmbedUnimplementedLoanInboundNotificationServer))
}
