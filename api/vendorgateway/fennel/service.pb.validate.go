// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/fennel/service.proto

package fennel

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LogDatasetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogDatasetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogDatasetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogDatasetsRequestMultiError, or nil if none found.
func (m *LogDatasetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LogDatasetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogDatasetsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogDatasetsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogDatasetsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DatasetName

	// no validation rules for Workflow

	if len(errors) > 0 {
		return LogDatasetsRequestMultiError(errors)
	}

	return nil
}

// LogDatasetsRequestMultiError is an error wrapping multiple validation errors
// returned by LogDatasetsRequest.ValidateAll() if the designated constraints
// aren't met.
type LogDatasetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogDatasetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogDatasetsRequestMultiError) AllErrors() []error { return m }

// LogDatasetsRequestValidationError is the validation error returned by
// LogDatasetsRequest.Validate if the designated constraints aren't met.
type LogDatasetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogDatasetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogDatasetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogDatasetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogDatasetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogDatasetsRequestValidationError) ErrorName() string {
	return "LogDatasetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LogDatasetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogDatasetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogDatasetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogDatasetsRequestValidationError{}

// Validate checks the field values on LogDatasetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogDatasetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogDatasetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogDatasetsResponseMultiError, or nil if none found.
func (m *LogDatasetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LogDatasetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogDatasetsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogDatasetsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogDatasetsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Response

	if len(errors) > 0 {
		return LogDatasetsResponseMultiError(errors)
	}

	return nil
}

// LogDatasetsResponseMultiError is an error wrapping multiple validation
// errors returned by LogDatasetsResponse.ValidateAll() if the designated
// constraints aren't met.
type LogDatasetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogDatasetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogDatasetsResponseMultiError) AllErrors() []error { return m }

// LogDatasetsResponseValidationError is the validation error returned by
// LogDatasetsResponse.Validate if the designated constraints aren't met.
type LogDatasetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogDatasetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogDatasetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogDatasetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogDatasetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogDatasetsResponseValidationError) ErrorName() string {
	return "LogDatasetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LogDatasetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogDatasetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogDatasetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogDatasetsResponseValidationError{}

// Validate checks the field values on ExtractFeatureSetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExtractFeatureSetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtractFeatureSetsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtractFeatureSetsRequestMultiError, or nil if none found.
func (m *ExtractFeatureSetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtractFeatureSetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExtractFeatureSetsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExtractFeatureSetsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExtractFeatureSetsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIdentifierList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExtractFeatureSetsRequestValidationError{
						field:  fmt.Sprintf("IdentifierList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExtractFeatureSetsRequestValidationError{
						field:  fmt.Sprintf("IdentifierList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExtractFeatureSetsRequestValidationError{
					field:  fmt.Sprintf("IdentifierList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Workflow

	if len(errors) > 0 {
		return ExtractFeatureSetsRequestMultiError(errors)
	}

	return nil
}

// ExtractFeatureSetsRequestMultiError is an error wrapping multiple validation
// errors returned by ExtractFeatureSetsRequest.ValidateAll() if the
// designated constraints aren't met.
type ExtractFeatureSetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtractFeatureSetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtractFeatureSetsRequestMultiError) AllErrors() []error { return m }

// ExtractFeatureSetsRequestValidationError is the validation error returned by
// ExtractFeatureSetsRequest.Validate if the designated constraints aren't met.
type ExtractFeatureSetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtractFeatureSetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtractFeatureSetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtractFeatureSetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtractFeatureSetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtractFeatureSetsRequestValidationError) ErrorName() string {
	return "ExtractFeatureSetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExtractFeatureSetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtractFeatureSetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtractFeatureSetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtractFeatureSetsRequestValidationError{}

// Validate checks the field values on ExtractFeatureSetsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExtractFeatureSetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtractFeatureSetsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtractFeatureSetsResponseMultiError, or nil if none found.
func (m *ExtractFeatureSetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtractFeatureSetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExtractFeatureSetsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExtractFeatureSetsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExtractFeatureSetsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFeatureMap()))
		i := 0
		for key := range m.GetFeatureMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureMap()[key]
			_ = val

			// no validation rules for FeatureMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ExtractFeatureSetsResponseValidationError{
							field:  fmt.Sprintf("FeatureMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ExtractFeatureSetsResponseValidationError{
							field:  fmt.Sprintf("FeatureMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ExtractFeatureSetsResponseValidationError{
						field:  fmt.Sprintf("FeatureMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ExtractFeatureSetsResponseMultiError(errors)
	}

	return nil
}

// ExtractFeatureSetsResponseMultiError is an error wrapping multiple
// validation errors returned by ExtractFeatureSetsResponse.ValidateAll() if
// the designated constraints aren't met.
type ExtractFeatureSetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtractFeatureSetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtractFeatureSetsResponseMultiError) AllErrors() []error { return m }

// ExtractFeatureSetsResponseValidationError is the validation error returned
// by ExtractFeatureSetsResponse.Validate if the designated constraints aren't met.
type ExtractFeatureSetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtractFeatureSetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtractFeatureSetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtractFeatureSetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtractFeatureSetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtractFeatureSetsResponseValidationError) ErrorName() string {
	return "ExtractFeatureSetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExtractFeatureSetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtractFeatureSetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtractFeatureSetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtractFeatureSetsResponseValidationError{}

// Validate checks the field values on GetPdScoreRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPdScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdScoreRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdScoreRequestMultiError, or nil if none found.
func (m *GetPdScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPdScoreRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPdScoreRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPdScoreRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for RawCreditReport

	if len(errors) > 0 {
		return GetPdScoreRequestMultiError(errors)
	}

	return nil
}

// GetPdScoreRequestMultiError is an error wrapping multiple validation errors
// returned by GetPdScoreRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPdScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdScoreRequestMultiError) AllErrors() []error { return m }

// GetPdScoreRequestValidationError is the validation error returned by
// GetPdScoreRequest.Validate if the designated constraints aren't met.
type GetPdScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdScoreRequestValidationError) ErrorName() string {
	return "GetPdScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdScoreRequestValidationError{}

// Validate checks the field values on GetPdScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPdScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPdScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPdScoreResponseMultiError, or nil if none found.
func (m *GetPdScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPdScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPdScoreResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPdScoreResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPdScoreResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Score

	// no validation rules for ModelContext

	// no validation rules for ModelVersion

	if len(errors) > 0 {
		return GetPdScoreResponseMultiError(errors)
	}

	return nil
}

// GetPdScoreResponseMultiError is an error wrapping multiple validation errors
// returned by GetPdScoreResponse.ValidateAll() if the designated constraints
// aren't met.
type GetPdScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPdScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPdScoreResponseMultiError) AllErrors() []error { return m }

// GetPdScoreResponseValidationError is the validation error returned by
// GetPdScoreResponse.Validate if the designated constraints aren't met.
type GetPdScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPdScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPdScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPdScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPdScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPdScoreResponseValidationError) ErrorName() string {
	return "GetPdScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPdScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPdScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPdScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPdScoreResponseValidationError{}
