// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/extvalidate/service.proto

package extvalidate

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on VerifyBankAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyBankAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyBankAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyBankAccountRequestMultiError, or nil if none found.
func (m *VerifyBankAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBankAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBankAccountRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBankAccountRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBankAccountRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Ifsc

	// no validation rules for AccountNumber

	if len(errors) > 0 {
		return VerifyBankAccountRequestMultiError(errors)
	}

	return nil
}

// VerifyBankAccountRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyBankAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyBankAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBankAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBankAccountRequestMultiError) AllErrors() []error { return m }

// VerifyBankAccountRequestValidationError is the validation error returned by
// VerifyBankAccountRequest.Validate if the designated constraints aren't met.
type VerifyBankAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBankAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBankAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBankAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBankAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBankAccountRequestValidationError) ErrorName() string {
	return "VerifyBankAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBankAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBankAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBankAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBankAccountRequestValidationError{}

// Validate checks the field values on VerifyBankAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyBankAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyBankAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyBankAccountResponseMultiError, or nil if none found.
func (m *VerifyBankAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyBankAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBankAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBankAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBankAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBankAccountResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBankAccountResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBankAccountResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsVerified

	// no validation rules for AccountNumber

	// no validation rules for Ifsc

	if all {
		switch v := interface{}(m.GetAccountHolderName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyBankAccountResponseValidationError{
					field:  "AccountHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyBankAccountResponseValidationError{
					field:  "AccountHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountHolderName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyBankAccountResponseValidationError{
				field:  "AccountHolderName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return VerifyBankAccountResponseMultiError(errors)
	}

	return nil
}

// VerifyBankAccountResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyBankAccountResponse.ValidateAll() if the
// designated constraints aren't met.
type VerifyBankAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyBankAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyBankAccountResponseMultiError) AllErrors() []error { return m }

// VerifyBankAccountResponseValidationError is the validation error returned by
// VerifyBankAccountResponse.Validate if the designated constraints aren't met.
type VerifyBankAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyBankAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyBankAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyBankAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyBankAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyBankAccountResponseValidationError) ErrorName() string {
	return "VerifyBankAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyBankAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyBankAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyBankAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyBankAccountResponseValidationError{}
