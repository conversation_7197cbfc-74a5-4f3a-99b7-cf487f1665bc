// Code generated by MockGen. DO NOT EDIT.
// Source: api/./vendornotification/cx/chatbot/workflow/senseforth/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	senseforth "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChatBotWorkflowClient is a mock of ChatBotWorkflowClient interface.
type MockChatBotWorkflowClient struct {
	ctrl     *gomock.Controller
	recorder *MockChatBotWorkflowClientMockRecorder
}

// MockChatBotWorkflowClientMockRecorder is the mock recorder for MockChatBotWorkflowClient.
type MockChatBotWorkflowClientMockRecorder struct {
	mock *MockChatBotWorkflowClient
}

// NewMockChatBotWorkflowClient creates a new mock instance.
func NewMockChatBotWorkflowClient(ctrl *gomock.Controller) *MockChatBotWorkflowClient {
	mock := &MockChatBotWorkflowClient{ctrl: ctrl}
	mock.recorder = &MockChatBotWorkflowClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatBotWorkflowClient) EXPECT() *MockChatBotWorkflowClientMockRecorder {
	return m.recorder
}

// ExecuteAction mocks base method.
func (m *MockChatBotWorkflowClient) ExecuteAction(ctx context.Context, in *senseforth.ExecuteActionRequest, opts ...grpc.CallOption) (*senseforth.ExecuteActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteAction", varargs...)
	ret0, _ := ret[0].(*senseforth.ExecuteActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteAction indicates an expected call of ExecuteAction.
func (mr *MockChatBotWorkflowClientMockRecorder) ExecuteAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteAction", reflect.TypeOf((*MockChatBotWorkflowClient)(nil).ExecuteAction), varargs...)
}

// FetchData mocks base method.
func (m *MockChatBotWorkflowClient) FetchData(ctx context.Context, in *senseforth.FetchDataRequest, opts ...grpc.CallOption) (*senseforth.FetchDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchData", varargs...)
	ret0, _ := ret[0].(*senseforth.FetchDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchData indicates an expected call of FetchData.
func (mr *MockChatBotWorkflowClientMockRecorder) FetchData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchData", reflect.TypeOf((*MockChatBotWorkflowClient)(nil).FetchData), varargs...)
}

// MockChatBotWorkflowServer is a mock of ChatBotWorkflowServer interface.
type MockChatBotWorkflowServer struct {
	ctrl     *gomock.Controller
	recorder *MockChatBotWorkflowServerMockRecorder
}

// MockChatBotWorkflowServerMockRecorder is the mock recorder for MockChatBotWorkflowServer.
type MockChatBotWorkflowServerMockRecorder struct {
	mock *MockChatBotWorkflowServer
}

// NewMockChatBotWorkflowServer creates a new mock instance.
func NewMockChatBotWorkflowServer(ctrl *gomock.Controller) *MockChatBotWorkflowServer {
	mock := &MockChatBotWorkflowServer{ctrl: ctrl}
	mock.recorder = &MockChatBotWorkflowServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatBotWorkflowServer) EXPECT() *MockChatBotWorkflowServerMockRecorder {
	return m.recorder
}

// ExecuteAction mocks base method.
func (m *MockChatBotWorkflowServer) ExecuteAction(arg0 context.Context, arg1 *senseforth.ExecuteActionRequest) (*senseforth.ExecuteActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteAction", arg0, arg1)
	ret0, _ := ret[0].(*senseforth.ExecuteActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteAction indicates an expected call of ExecuteAction.
func (mr *MockChatBotWorkflowServerMockRecorder) ExecuteAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteAction", reflect.TypeOf((*MockChatBotWorkflowServer)(nil).ExecuteAction), arg0, arg1)
}

// FetchData mocks base method.
func (m *MockChatBotWorkflowServer) FetchData(arg0 context.Context, arg1 *senseforth.FetchDataRequest) (*senseforth.FetchDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchData", arg0, arg1)
	ret0, _ := ret[0].(*senseforth.FetchDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchData indicates an expected call of FetchData.
func (mr *MockChatBotWorkflowServerMockRecorder) FetchData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchData", reflect.TypeOf((*MockChatBotWorkflowServer)(nil).FetchData), arg0, arg1)
}

// MockUnsafeChatBotWorkflowServer is a mock of UnsafeChatBotWorkflowServer interface.
type MockUnsafeChatBotWorkflowServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeChatBotWorkflowServerMockRecorder
}

// MockUnsafeChatBotWorkflowServerMockRecorder is the mock recorder for MockUnsafeChatBotWorkflowServer.
type MockUnsafeChatBotWorkflowServerMockRecorder struct {
	mock *MockUnsafeChatBotWorkflowServer
}

// NewMockUnsafeChatBotWorkflowServer creates a new mock instance.
func NewMockUnsafeChatBotWorkflowServer(ctrl *gomock.Controller) *MockUnsafeChatBotWorkflowServer {
	mock := &MockUnsafeChatBotWorkflowServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeChatBotWorkflowServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeChatBotWorkflowServer) EXPECT() *MockUnsafeChatBotWorkflowServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedChatBotWorkflowServer mocks base method.
func (m *MockUnsafeChatBotWorkflowServer) mustEmbedUnimplementedChatBotWorkflowServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedChatBotWorkflowServer")
}

// mustEmbedUnimplementedChatBotWorkflowServer indicates an expected call of mustEmbedUnimplementedChatBotWorkflowServer.
func (mr *MockUnsafeChatBotWorkflowServerMockRecorder) mustEmbedUnimplementedChatBotWorkflowServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedChatBotWorkflowServer", reflect.TypeOf((*MockUnsafeChatBotWorkflowServer)(nil).mustEmbedUnimplementedChatBotWorkflowServer))
}
