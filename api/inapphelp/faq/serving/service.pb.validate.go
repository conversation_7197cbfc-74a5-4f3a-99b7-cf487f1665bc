// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/inapphelp/faq/serving/service.proto

package serving

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/inapphelp/faq/enums"

	solutions "github.com/epifi/gamma/api/vendorgateway/cx/solutions"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.FaqFetchSource(0)

	_ = solutions.CategoryVisibility(0)
)

// Validate checks the field values on GetAllCategoriesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllCategoriesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllCategoriesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllCategoriesRequestMultiError, or nil if none found.
func (m *GetAllCategoriesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllCategoriesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FaqFetchSource

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetAllCategoriesRequestMultiError(errors)
	}

	return nil
}

// GetAllCategoriesRequestMultiError is an error wrapping multiple validation
// errors returned by GetAllCategoriesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAllCategoriesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllCategoriesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllCategoriesRequestMultiError) AllErrors() []error { return m }

// GetAllCategoriesRequestValidationError is the validation error returned by
// GetAllCategoriesRequest.Validate if the designated constraints aren't met.
type GetAllCategoriesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllCategoriesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllCategoriesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllCategoriesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllCategoriesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllCategoriesRequestValidationError) ErrorName() string {
	return "GetAllCategoriesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllCategoriesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllCategoriesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllCategoriesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllCategoriesRequestValidationError{}

// Validate checks the field values on GetAllCategoriesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllCategoriesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllCategoriesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllCategoriesResponseMultiError, or nil if none found.
func (m *GetAllCategoriesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllCategoriesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllCategoriesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllCategoriesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllCategoriesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllCategoriesResponseValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllCategoriesResponseValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllCategoriesResponseValidationError{
					field:  fmt.Sprintf("Categories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllCategoriesResponseMultiError(errors)
	}

	return nil
}

// GetAllCategoriesResponseMultiError is an error wrapping multiple validation
// errors returned by GetAllCategoriesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAllCategoriesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllCategoriesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllCategoriesResponseMultiError) AllErrors() []error { return m }

// GetAllCategoriesResponseValidationError is the validation error returned by
// GetAllCategoriesResponse.Validate if the designated constraints aren't met.
type GetAllCategoriesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllCategoriesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllCategoriesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllCategoriesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllCategoriesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllCategoriesResponseValidationError) ErrorName() string {
	return "GetAllCategoriesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllCategoriesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllCategoriesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllCategoriesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllCategoriesResponseValidationError{}

// Validate checks the field values on GetAllFoldersInCategoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllFoldersInCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllFoldersInCategoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllFoldersInCategoryRequestMultiError, or nil if none found.
func (m *GetAllFoldersInCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllFoldersInCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FaqFetchSource

	// no validation rules for CategoryId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetAllFoldersInCategoryRequestMultiError(errors)
	}

	return nil
}

// GetAllFoldersInCategoryRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllFoldersInCategoryRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAllFoldersInCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllFoldersInCategoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllFoldersInCategoryRequestMultiError) AllErrors() []error { return m }

// GetAllFoldersInCategoryRequestValidationError is the validation error
// returned by GetAllFoldersInCategoryRequest.Validate if the designated
// constraints aren't met.
type GetAllFoldersInCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllFoldersInCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllFoldersInCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllFoldersInCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllFoldersInCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllFoldersInCategoryRequestValidationError) ErrorName() string {
	return "GetAllFoldersInCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllFoldersInCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllFoldersInCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllFoldersInCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllFoldersInCategoryRequestValidationError{}

// Validate checks the field values on GetAllFoldersInCategoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllFoldersInCategoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllFoldersInCategoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllFoldersInCategoryResponseMultiError, or nil if none found.
func (m *GetAllFoldersInCategoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllFoldersInCategoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllFoldersInCategoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllFoldersInCategoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllFoldersInCategoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFolders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllFoldersInCategoryResponseValidationError{
						field:  fmt.Sprintf("Folders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllFoldersInCategoryResponseValidationError{
						field:  fmt.Sprintf("Folders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllFoldersInCategoryResponseValidationError{
					field:  fmt.Sprintf("Folders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllFoldersInCategoryResponseMultiError(errors)
	}

	return nil
}

// GetAllFoldersInCategoryResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllFoldersInCategoryResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAllFoldersInCategoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllFoldersInCategoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllFoldersInCategoryResponseMultiError) AllErrors() []error { return m }

// GetAllFoldersInCategoryResponseValidationError is the validation error
// returned by GetAllFoldersInCategoryResponse.Validate if the designated
// constraints aren't met.
type GetAllFoldersInCategoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllFoldersInCategoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllFoldersInCategoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllFoldersInCategoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllFoldersInCategoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllFoldersInCategoryResponseValidationError) ErrorName() string {
	return "GetAllFoldersInCategoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllFoldersInCategoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllFoldersInCategoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllFoldersInCategoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllFoldersInCategoryResponseValidationError{}

// Validate checks the field values on GetAllArticlesInFolderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllArticlesInFolderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllArticlesInFolderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllArticlesInFolderRequestMultiError, or nil if none found.
func (m *GetAllArticlesInFolderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllArticlesInFolderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FaqFetchSource

	// no validation rules for FolderId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetAllArticlesInFolderRequestMultiError(errors)
	}

	return nil
}

// GetAllArticlesInFolderRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllArticlesInFolderRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAllArticlesInFolderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllArticlesInFolderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllArticlesInFolderRequestMultiError) AllErrors() []error { return m }

// GetAllArticlesInFolderRequestValidationError is the validation error
// returned by GetAllArticlesInFolderRequest.Validate if the designated
// constraints aren't met.
type GetAllArticlesInFolderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllArticlesInFolderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllArticlesInFolderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllArticlesInFolderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllArticlesInFolderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllArticlesInFolderRequestValidationError) ErrorName() string {
	return "GetAllArticlesInFolderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllArticlesInFolderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllArticlesInFolderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllArticlesInFolderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllArticlesInFolderRequestValidationError{}

// Validate checks the field values on GetAllArticlesInFolderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllArticlesInFolderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllArticlesInFolderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllArticlesInFolderResponseMultiError, or nil if none found.
func (m *GetAllArticlesInFolderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllArticlesInFolderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllArticlesInFolderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllArticlesInFolderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllArticlesInFolderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetArticles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllArticlesInFolderResponseValidationError{
						field:  fmt.Sprintf("Articles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllArticlesInFolderResponseValidationError{
						field:  fmt.Sprintf("Articles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllArticlesInFolderResponseValidationError{
					field:  fmt.Sprintf("Articles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllArticlesInFolderResponseMultiError(errors)
	}

	return nil
}

// GetAllArticlesInFolderResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllArticlesInFolderResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAllArticlesInFolderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllArticlesInFolderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllArticlesInFolderResponseMultiError) AllErrors() []error { return m }

// GetAllArticlesInFolderResponseValidationError is the validation error
// returned by GetAllArticlesInFolderResponse.Validate if the designated
// constraints aren't met.
type GetAllArticlesInFolderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllArticlesInFolderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllArticlesInFolderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllArticlesInFolderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllArticlesInFolderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllArticlesInFolderResponseValidationError) ErrorName() string {
	return "GetAllArticlesInFolderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllArticlesInFolderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllArticlesInFolderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllArticlesInFolderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllArticlesInFolderResponseValidationError{}

// Validate checks the field values on GetFAQByContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFAQByContextRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFAQByContextRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFAQByContextRequestMultiError, or nil if none found.
func (m *GetFAQByContextRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFAQByContextRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFaqContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFAQByContextRequestValidationError{
					field:  "FaqContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFAQByContextRequestValidationError{
					field:  "FaqContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFaqContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFAQByContextRequestValidationError{
				field:  "FaqContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFAQByContextRequestMultiError(errors)
	}

	return nil
}

// GetFAQByContextRequestMultiError is an error wrapping multiple validation
// errors returned by GetFAQByContextRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFAQByContextRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFAQByContextRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFAQByContextRequestMultiError) AllErrors() []error { return m }

// GetFAQByContextRequestValidationError is the validation error returned by
// GetFAQByContextRequest.Validate if the designated constraints aren't met.
type GetFAQByContextRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFAQByContextRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFAQByContextRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFAQByContextRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFAQByContextRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFAQByContextRequestValidationError) ErrorName() string {
	return "GetFAQByContextRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFAQByContextRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFAQByContextRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFAQByContextRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFAQByContextRequestValidationError{}

// Validate checks the field values on GetFAQByContextResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFAQByContextResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFAQByContextResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFAQByContextResponseMultiError, or nil if none found.
func (m *GetFAQByContextResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFAQByContextResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFAQByContextResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFAQByContextResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFAQByContextResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFaqList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFAQByContextResponseValidationError{
						field:  fmt.Sprintf("FaqList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFAQByContextResponseValidationError{
						field:  fmt.Sprintf("FaqList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFAQByContextResponseValidationError{
					field:  fmt.Sprintf("FaqList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFAQByContextResponseMultiError(errors)
	}

	return nil
}

// GetFAQByContextResponseMultiError is an error wrapping multiple validation
// errors returned by GetFAQByContextResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFAQByContextResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFAQByContextResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFAQByContextResponseMultiError) AllErrors() []error { return m }

// GetFAQByContextResponseValidationError is the validation error returned by
// GetFAQByContextResponse.Validate if the designated constraints aren't met.
type GetFAQByContextResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFAQByContextResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFAQByContextResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFAQByContextResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFAQByContextResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFAQByContextResponseValidationError) ErrorName() string {
	return "GetFAQByContextResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFAQByContextResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFAQByContextResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFAQByContextResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFAQByContextResponseValidationError{}

// Validate checks the field values on GetFAQByIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFAQByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFAQByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFAQByIdRequestMultiError, or nil if none found.
func (m *GetFAQByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFAQByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FaqId

	// no validation rules for FaqType

	// no validation rules for FaqFetchSource

	if len(errors) > 0 {
		return GetFAQByIdRequestMultiError(errors)
	}

	return nil
}

// GetFAQByIdRequestMultiError is an error wrapping multiple validation errors
// returned by GetFAQByIdRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFAQByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFAQByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFAQByIdRequestMultiError) AllErrors() []error { return m }

// GetFAQByIdRequestValidationError is the validation error returned by
// GetFAQByIdRequest.Validate if the designated constraints aren't met.
type GetFAQByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFAQByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFAQByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFAQByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFAQByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFAQByIdRequestValidationError) ErrorName() string {
	return "GetFAQByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFAQByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFAQByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFAQByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFAQByIdRequestValidationError{}

// Validate checks the field values on GetFAQByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFAQByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFAQByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFAQByIdResponseMultiError, or nil if none found.
func (m *GetFAQByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFAQByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFAQByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFAQByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFAQByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FaqType

	switch v := m.Faq.(type) {
	case *GetFAQByIdResponse_Category:
		if v == nil {
			err := GetFAQByIdResponseValidationError{
				field:  "Faq",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCategory()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFAQByIdResponseValidationError{
						field:  "Category",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFAQByIdResponseValidationError{
						field:  "Category",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCategory()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFAQByIdResponseValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetFAQByIdResponse_Folder:
		if v == nil {
			err := GetFAQByIdResponseValidationError{
				field:  "Faq",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFolder()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFAQByIdResponseValidationError{
						field:  "Folder",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFAQByIdResponseValidationError{
						field:  "Folder",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFolder()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFAQByIdResponseValidationError{
					field:  "Folder",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetFAQByIdResponse_Article:
		if v == nil {
			err := GetFAQByIdResponseValidationError{
				field:  "Faq",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetArticle()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFAQByIdResponseValidationError{
						field:  "Article",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFAQByIdResponseValidationError{
						field:  "Article",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetArticle()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFAQByIdResponseValidationError{
					field:  "Article",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetFAQByIdResponseMultiError(errors)
	}

	return nil
}

// GetFAQByIdResponseMultiError is an error wrapping multiple validation errors
// returned by GetFAQByIdResponse.ValidateAll() if the designated constraints
// aren't met.
type GetFAQByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFAQByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFAQByIdResponseMultiError) AllErrors() []error { return m }

// GetFAQByIdResponseValidationError is the validation error returned by
// GetFAQByIdResponse.Validate if the designated constraints aren't met.
type GetFAQByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFAQByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFAQByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFAQByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFAQByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFAQByIdResponseValidationError) ErrorName() string {
	return "GetFAQByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFAQByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFAQByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFAQByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFAQByIdResponseValidationError{}

// Validate checks the field values on GetCategoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCategoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCategoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCategoryRequestMultiError, or nil if none found.
func (m *GetCategoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCategoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CategoryId

	// no validation rules for FaqFetchSource

	if len(errors) > 0 {
		return GetCategoryRequestMultiError(errors)
	}

	return nil
}

// GetCategoryRequestMultiError is an error wrapping multiple validation errors
// returned by GetCategoryRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCategoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCategoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCategoryRequestMultiError) AllErrors() []error { return m }

// GetCategoryRequestValidationError is the validation error returned by
// GetCategoryRequest.Validate if the designated constraints aren't met.
type GetCategoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCategoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCategoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCategoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCategoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCategoryRequestValidationError) ErrorName() string {
	return "GetCategoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCategoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCategoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCategoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCategoryRequestValidationError{}

// Validate checks the field values on GetCategoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCategoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCategoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCategoryResponseMultiError, or nil if none found.
func (m *GetCategoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCategoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCategoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCategoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCategoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCategory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCategoryResponseValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCategoryResponseValidationError{
					field:  "Category",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCategory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCategoryResponseValidationError{
				field:  "Category",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCategoryResponseMultiError(errors)
	}

	return nil
}

// GetCategoryResponseMultiError is an error wrapping multiple validation
// errors returned by GetCategoryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCategoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCategoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCategoryResponseMultiError) AllErrors() []error { return m }

// GetCategoryResponseValidationError is the validation error returned by
// GetCategoryResponse.Validate if the designated constraints aren't met.
type GetCategoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCategoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCategoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCategoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCategoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCategoryResponseValidationError) ErrorName() string {
	return "GetCategoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCategoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCategoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCategoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCategoryResponseValidationError{}

// Validate checks the field values on GetFolderRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFolderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolderRequestMultiError, or nil if none found.
func (m *GetFolderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FolderId

	// no validation rules for FaqFetchSource

	if len(errors) > 0 {
		return GetFolderRequestMultiError(errors)
	}

	return nil
}

// GetFolderRequestMultiError is an error wrapping multiple validation errors
// returned by GetFolderRequest.ValidateAll() if the designated constraints
// aren't met.
type GetFolderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolderRequestMultiError) AllErrors() []error { return m }

// GetFolderRequestValidationError is the validation error returned by
// GetFolderRequest.Validate if the designated constraints aren't met.
type GetFolderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolderRequestValidationError) ErrorName() string { return "GetFolderRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetFolderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolderRequestValidationError{}

// Validate checks the field values on GetFolderResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFolderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFolderResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFolderResponseMultiError, or nil if none found.
func (m *GetFolderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFolderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFolder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFolderResponseValidationError{
					field:  "Folder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFolderResponseValidationError{
					field:  "Folder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFolder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFolderResponseValidationError{
				field:  "Folder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFolderResponseMultiError(errors)
	}

	return nil
}

// GetFolderResponseMultiError is an error wrapping multiple validation errors
// returned by GetFolderResponse.ValidateAll() if the designated constraints
// aren't met.
type GetFolderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFolderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFolderResponseMultiError) AllErrors() []error { return m }

// GetFolderResponseValidationError is the validation error returned by
// GetFolderResponse.Validate if the designated constraints aren't met.
type GetFolderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFolderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFolderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFolderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFolderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFolderResponseValidationError) ErrorName() string {
	return "GetFolderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFolderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFolderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFolderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFolderResponseValidationError{}

// Validate checks the field values on GetArticleRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetArticleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetArticleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetArticleRequestMultiError, or nil if none found.
func (m *GetArticleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetArticleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ArticleId

	// no validation rules for FaqFetchSource

	if len(errors) > 0 {
		return GetArticleRequestMultiError(errors)
	}

	return nil
}

// GetArticleRequestMultiError is an error wrapping multiple validation errors
// returned by GetArticleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetArticleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetArticleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetArticleRequestMultiError) AllErrors() []error { return m }

// GetArticleRequestValidationError is the validation error returned by
// GetArticleRequest.Validate if the designated constraints aren't met.
type GetArticleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetArticleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetArticleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetArticleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetArticleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetArticleRequestValidationError) ErrorName() string {
	return "GetArticleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetArticleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetArticleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetArticleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetArticleRequestValidationError{}

// Validate checks the field values on GetArticleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetArticleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetArticleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetArticleResponseMultiError, or nil if none found.
func (m *GetArticleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetArticleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetArticleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetArticleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetArticleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetArticle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetArticleResponseValidationError{
					field:  "Article",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetArticleResponseValidationError{
					field:  "Article",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetArticle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetArticleResponseValidationError{
				field:  "Article",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetArticleResponseMultiError(errors)
	}

	return nil
}

// GetArticleResponseMultiError is an error wrapping multiple validation errors
// returned by GetArticleResponse.ValidateAll() if the designated constraints
// aren't met.
type GetArticleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetArticleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetArticleResponseMultiError) AllErrors() []error { return m }

// GetArticleResponseValidationError is the validation error returned by
// GetArticleResponse.Validate if the designated constraints aren't met.
type GetArticleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetArticleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetArticleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetArticleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetArticleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetArticleResponseValidationError) ErrorName() string {
	return "GetArticleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetArticleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetArticleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetArticleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetArticleResponseValidationError{}

// Validate checks the field values on SubmitArticleFeedbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitArticleFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitArticleFeedbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitArticleFeedbackRequestMultiError, or nil if none found.
func (m *SubmitArticleFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitArticleFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ArticleId

	// no validation rules for FeedbackResponse

	if all {
		switch v := interface{}(m.GetSourceIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitArticleFeedbackRequestValidationError{
					field:  "SourceIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitArticleFeedbackRequestValidationError{
					field:  "SourceIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitArticleFeedbackRequestValidationError{
				field:  "SourceIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitArticleFeedbackRequestMultiError(errors)
	}

	return nil
}

// SubmitArticleFeedbackRequestMultiError is an error wrapping multiple
// validation errors returned by SubmitArticleFeedbackRequest.ValidateAll() if
// the designated constraints aren't met.
type SubmitArticleFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitArticleFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitArticleFeedbackRequestMultiError) AllErrors() []error { return m }

// SubmitArticleFeedbackRequestValidationError is the validation error returned
// by SubmitArticleFeedbackRequest.Validate if the designated constraints
// aren't met.
type SubmitArticleFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitArticleFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitArticleFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitArticleFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitArticleFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitArticleFeedbackRequestValidationError) ErrorName() string {
	return "SubmitArticleFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitArticleFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitArticleFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitArticleFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitArticleFeedbackRequestValidationError{}

// Validate checks the field values on SubmitArticleFeedbackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitArticleFeedbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitArticleFeedbackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SubmitArticleFeedbackResponseMultiError, or nil if none found.
func (m *SubmitArticleFeedbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitArticleFeedbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitArticleFeedbackResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeedbackId

	if all {
		switch v := interface{}(m.GetBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitArticleFeedbackResponseValidationError{
				field:  "BottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitArticleFeedbackResponseMultiError(errors)
	}

	return nil
}

// SubmitArticleFeedbackResponseMultiError is an error wrapping multiple
// validation errors returned by SubmitArticleFeedbackResponse.ValidateAll()
// if the designated constraints aren't met.
type SubmitArticleFeedbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitArticleFeedbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitArticleFeedbackResponseMultiError) AllErrors() []error { return m }

// SubmitArticleFeedbackResponseValidationError is the validation error
// returned by SubmitArticleFeedbackResponse.Validate if the designated
// constraints aren't met.
type SubmitArticleFeedbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitArticleFeedbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitArticleFeedbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitArticleFeedbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitArticleFeedbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitArticleFeedbackResponseValidationError) ErrorName() string {
	return "SubmitArticleFeedbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitArticleFeedbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitArticleFeedbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitArticleFeedbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitArticleFeedbackResponseValidationError{}

// Validate checks the field values on UpdateFeedbackReasonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFeedbackReasonRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFeedbackReasonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFeedbackReasonRequestMultiError, or nil if none found.
func (m *UpdateFeedbackReasonRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFeedbackReasonRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeedbackId

	// no validation rules for Reason

	if len(errors) > 0 {
		return UpdateFeedbackReasonRequestMultiError(errors)
	}

	return nil
}

// UpdateFeedbackReasonRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateFeedbackReasonRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateFeedbackReasonRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFeedbackReasonRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFeedbackReasonRequestMultiError) AllErrors() []error { return m }

// UpdateFeedbackReasonRequestValidationError is the validation error returned
// by UpdateFeedbackReasonRequest.Validate if the designated constraints
// aren't met.
type UpdateFeedbackReasonRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFeedbackReasonRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFeedbackReasonRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFeedbackReasonRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFeedbackReasonRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFeedbackReasonRequestValidationError) ErrorName() string {
	return "UpdateFeedbackReasonRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFeedbackReasonRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFeedbackReasonRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFeedbackReasonRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFeedbackReasonRequestValidationError{}

// Validate checks the field values on UpdateFeedbackReasonResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFeedbackReasonResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFeedbackReasonResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFeedbackReasonResponseMultiError, or nil if none found.
func (m *UpdateFeedbackReasonResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFeedbackReasonResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFeedbackReasonResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFeedbackReasonResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFeedbackReasonResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFeedbackReasonResponseMultiError(errors)
	}

	return nil
}

// UpdateFeedbackReasonResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateFeedbackReasonResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateFeedbackReasonResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFeedbackReasonResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFeedbackReasonResponseMultiError) AllErrors() []error { return m }

// UpdateFeedbackReasonResponseValidationError is the validation error returned
// by UpdateFeedbackReasonResponse.Validate if the designated constraints
// aren't met.
type UpdateFeedbackReasonResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFeedbackReasonResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFeedbackReasonResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFeedbackReasonResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFeedbackReasonResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFeedbackReasonResponseValidationError) ErrorName() string {
	return "UpdateFeedbackReasonResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFeedbackReasonResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFeedbackReasonResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFeedbackReasonResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFeedbackReasonResponseValidationError{}

// Validate checks the field values on UpdateFAQRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateFAQRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFAQRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFAQRequestMultiError, or nil if none found.
func (m *UpdateFAQRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFAQRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UpdateFaqType

	// no validation rules for FaqId

	// no validation rules for FaqVisibility

	// no validation rules for CategoryRank

	// no validation rules for CategoryIconUrl

	if all {
		switch v := interface{}(m.GetUpdatedFaqFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFAQRequestValidationError{
					field:  "UpdatedFaqFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFAQRequestValidationError{
					field:  "UpdatedFaqFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedFaqFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFAQRequestValidationError{
				field:  "UpdatedFaqFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBulkUpdateRequiredForCategoryHierarchy

	if len(errors) > 0 {
		return UpdateFAQRequestMultiError(errors)
	}

	return nil
}

// UpdateFAQRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateFAQRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateFAQRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFAQRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFAQRequestMultiError) AllErrors() []error { return m }

// UpdateFAQRequestValidationError is the validation error returned by
// UpdateFAQRequest.Validate if the designated constraints aren't met.
type UpdateFAQRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFAQRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFAQRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFAQRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFAQRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFAQRequestValidationError) ErrorName() string { return "UpdateFAQRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateFAQRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFAQRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFAQRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFAQRequestValidationError{}

// Validate checks the field values on UpdateFAQResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateFAQResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFAQResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFAQResponseMultiError, or nil if none found.
func (m *UpdateFAQResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFAQResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFAQResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFAQResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFAQResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFAQResponseMultiError(errors)
	}

	return nil
}

// UpdateFAQResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateFAQResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateFAQResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFAQResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFAQResponseMultiError) AllErrors() []error { return m }

// UpdateFAQResponseValidationError is the validation error returned by
// UpdateFAQResponse.Validate if the designated constraints aren't met.
type UpdateFAQResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFAQResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFAQResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFAQResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFAQResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFAQResponseValidationError) ErrorName() string {
	return "UpdateFAQResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFAQResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFAQResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFAQResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFAQResponseValidationError{}

// Validate checks the field values on SubmitArticleFeedbackRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitArticleFeedbackRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitArticleFeedbackRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SubmitArticleFeedbackRequestV2MultiError, or nil if none found.
func (m *SubmitArticleFeedbackRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitArticleFeedbackRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ArticleId

	// no validation rules for FeedbackResponse

	if len(errors) > 0 {
		return SubmitArticleFeedbackRequestV2MultiError(errors)
	}

	return nil
}

// SubmitArticleFeedbackRequestV2MultiError is an error wrapping multiple
// validation errors returned by SubmitArticleFeedbackRequestV2.ValidateAll()
// if the designated constraints aren't met.
type SubmitArticleFeedbackRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitArticleFeedbackRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitArticleFeedbackRequestV2MultiError) AllErrors() []error { return m }

// SubmitArticleFeedbackRequestV2ValidationError is the validation error
// returned by SubmitArticleFeedbackRequestV2.Validate if the designated
// constraints aren't met.
type SubmitArticleFeedbackRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitArticleFeedbackRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitArticleFeedbackRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitArticleFeedbackRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitArticleFeedbackRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitArticleFeedbackRequestV2ValidationError) ErrorName() string {
	return "SubmitArticleFeedbackRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitArticleFeedbackRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitArticleFeedbackRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitArticleFeedbackRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitArticleFeedbackRequestV2ValidationError{}

// Validate checks the field values on SubmitArticleFeedbackResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitArticleFeedbackResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitArticleFeedbackResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SubmitArticleFeedbackResponseV2MultiError, or nil if none found.
func (m *SubmitArticleFeedbackResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitArticleFeedbackResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseV2ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseV2ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitArticleFeedbackResponseV2ValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeedbackId

	if all {
		switch v := interface{}(m.GetBottomSheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseV2ValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitArticleFeedbackResponseV2ValidationError{
					field:  "BottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitArticleFeedbackResponseV2ValidationError{
				field:  "BottomSheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitArticleFeedbackResponseV2MultiError(errors)
	}

	return nil
}

// SubmitArticleFeedbackResponseV2MultiError is an error wrapping multiple
// validation errors returned by SubmitArticleFeedbackResponseV2.ValidateAll()
// if the designated constraints aren't met.
type SubmitArticleFeedbackResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitArticleFeedbackResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitArticleFeedbackResponseV2MultiError) AllErrors() []error { return m }

// SubmitArticleFeedbackResponseV2ValidationError is the validation error
// returned by SubmitArticleFeedbackResponseV2.Validate if the designated
// constraints aren't met.
type SubmitArticleFeedbackResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitArticleFeedbackResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitArticleFeedbackResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitArticleFeedbackResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitArticleFeedbackResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitArticleFeedbackResponseV2ValidationError) ErrorName() string {
	return "SubmitArticleFeedbackResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitArticleFeedbackResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitArticleFeedbackResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitArticleFeedbackResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitArticleFeedbackResponseV2ValidationError{}

// Validate checks the field values on UpdateFeedbackReasonRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFeedbackReasonRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFeedbackReasonRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFeedbackReasonRequestV2MultiError, or nil if none found.
func (m *UpdateFeedbackReasonRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFeedbackReasonRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeedbackId

	// no validation rules for UserComment

	if len(errors) > 0 {
		return UpdateFeedbackReasonRequestV2MultiError(errors)
	}

	return nil
}

// UpdateFeedbackReasonRequestV2MultiError is an error wrapping multiple
// validation errors returned by UpdateFeedbackReasonRequestV2.ValidateAll()
// if the designated constraints aren't met.
type UpdateFeedbackReasonRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFeedbackReasonRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFeedbackReasonRequestV2MultiError) AllErrors() []error { return m }

// UpdateFeedbackReasonRequestV2ValidationError is the validation error
// returned by UpdateFeedbackReasonRequestV2.Validate if the designated
// constraints aren't met.
type UpdateFeedbackReasonRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFeedbackReasonRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFeedbackReasonRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFeedbackReasonRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFeedbackReasonRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFeedbackReasonRequestV2ValidationError) ErrorName() string {
	return "UpdateFeedbackReasonRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFeedbackReasonRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFeedbackReasonRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFeedbackReasonRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFeedbackReasonRequestV2ValidationError{}

// Validate checks the field values on UpdateFeedbackReasonResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFeedbackReasonResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFeedbackReasonResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateFeedbackReasonResponseV2MultiError, or nil if none found.
func (m *UpdateFeedbackReasonResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFeedbackReasonResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFeedbackReasonResponseV2ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFeedbackReasonResponseV2ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFeedbackReasonResponseV2ValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateFeedbackReasonResponseV2MultiError(errors)
	}

	return nil
}

// UpdateFeedbackReasonResponseV2MultiError is an error wrapping multiple
// validation errors returned by UpdateFeedbackReasonResponseV2.ValidateAll()
// if the designated constraints aren't met.
type UpdateFeedbackReasonResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFeedbackReasonResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFeedbackReasonResponseV2MultiError) AllErrors() []error { return m }

// UpdateFeedbackReasonResponseV2ValidationError is the validation error
// returned by UpdateFeedbackReasonResponseV2.Validate if the designated
// constraints aren't met.
type UpdateFeedbackReasonResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFeedbackReasonResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFeedbackReasonResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFeedbackReasonResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFeedbackReasonResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFeedbackReasonResponseV2ValidationError) ErrorName() string {
	return "UpdateFeedbackReasonResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFeedbackReasonResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFeedbackReasonResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFeedbackReasonResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFeedbackReasonResponseV2ValidationError{}

// Validate checks the field values on GetPopularFAQListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPopularFAQListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPopularFAQListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPopularFAQListRequestMultiError, or nil if none found.
func (m *GetPopularFAQListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPopularFAQListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for FetchSource

	if len(errors) > 0 {
		return GetPopularFAQListRequestMultiError(errors)
	}

	return nil
}

// GetPopularFAQListRequestMultiError is an error wrapping multiple validation
// errors returned by GetPopularFAQListRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPopularFAQListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPopularFAQListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPopularFAQListRequestMultiError) AllErrors() []error { return m }

// GetPopularFAQListRequestValidationError is the validation error returned by
// GetPopularFAQListRequest.Validate if the designated constraints aren't met.
type GetPopularFAQListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPopularFAQListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPopularFAQListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPopularFAQListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPopularFAQListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPopularFAQListRequestValidationError) ErrorName() string {
	return "GetPopularFAQListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPopularFAQListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPopularFAQListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPopularFAQListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPopularFAQListRequestValidationError{}

// Validate checks the field values on GetPopularFAQListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPopularFAQListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPopularFAQListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPopularFAQListResponseMultiError, or nil if none found.
func (m *GetPopularFAQListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPopularFAQListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPopularFAQListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPopularFAQListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPopularFAQListResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetArticleList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPopularFAQListResponseValidationError{
						field:  fmt.Sprintf("ArticleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPopularFAQListResponseValidationError{
						field:  fmt.Sprintf("ArticleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPopularFAQListResponseValidationError{
					field:  fmt.Sprintf("ArticleList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPopularFAQListResponseMultiError(errors)
	}

	return nil
}

// GetPopularFAQListResponseMultiError is an error wrapping multiple validation
// errors returned by GetPopularFAQListResponse.ValidateAll() if the
// designated constraints aren't met.
type GetPopularFAQListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPopularFAQListResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPopularFAQListResponseMultiError) AllErrors() []error { return m }

// GetPopularFAQListResponseValidationError is the validation error returned by
// GetPopularFAQListResponse.Validate if the designated constraints aren't met.
type GetPopularFAQListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPopularFAQListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPopularFAQListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPopularFAQListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPopularFAQListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPopularFAQListResponseValidationError) ErrorName() string {
	return "GetPopularFAQListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPopularFAQListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPopularFAQListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPopularFAQListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPopularFAQListResponseValidationError{}

// Validate checks the field values on CreateBulkFAQContextMappingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateBulkFAQContextMappingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBulkFAQContextMappingRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateBulkFAQContextMappingRequestMultiError, or nil if none found.
func (m *CreateBulkFAQContextMappingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBulkFAQContextMappingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFaqContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBulkFAQContextMappingRequestValidationError{
					field:  "FaqContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBulkFAQContextMappingRequestValidationError{
					field:  "FaqContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFaqContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBulkFAQContextMappingRequestValidationError{
				field:  "FaqContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetArticleIds()) > 50 {
		err := CreateBulkFAQContextMappingRequestValidationError{
			field:  "ArticleIds",
			reason: "value must contain no more than 50 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateBulkFAQContextMappingRequestMultiError(errors)
	}

	return nil
}

// CreateBulkFAQContextMappingRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateBulkFAQContextMappingRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateBulkFAQContextMappingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBulkFAQContextMappingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBulkFAQContextMappingRequestMultiError) AllErrors() []error { return m }

// CreateBulkFAQContextMappingRequestValidationError is the validation error
// returned by CreateBulkFAQContextMappingRequest.Validate if the designated
// constraints aren't met.
type CreateBulkFAQContextMappingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBulkFAQContextMappingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBulkFAQContextMappingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBulkFAQContextMappingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBulkFAQContextMappingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBulkFAQContextMappingRequestValidationError) ErrorName() string {
	return "CreateBulkFAQContextMappingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBulkFAQContextMappingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBulkFAQContextMappingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBulkFAQContextMappingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBulkFAQContextMappingRequestValidationError{}

// Validate checks the field values on CreateBulkFAQContextMappingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateBulkFAQContextMappingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBulkFAQContextMappingResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateBulkFAQContextMappingResponseMultiError, or nil if none found.
func (m *CreateBulkFAQContextMappingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBulkFAQContextMappingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBulkFAQContextMappingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBulkFAQContextMappingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBulkFAQContextMappingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateBulkFAQContextMappingResponseMultiError(errors)
	}

	return nil
}

// CreateBulkFAQContextMappingResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateBulkFAQContextMappingResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateBulkFAQContextMappingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBulkFAQContextMappingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBulkFAQContextMappingResponseMultiError) AllErrors() []error { return m }

// CreateBulkFAQContextMappingResponseValidationError is the validation error
// returned by CreateBulkFAQContextMappingResponse.Validate if the designated
// constraints aren't met.
type CreateBulkFAQContextMappingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBulkFAQContextMappingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBulkFAQContextMappingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBulkFAQContextMappingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBulkFAQContextMappingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBulkFAQContextMappingResponseValidationError) ErrorName() string {
	return "CreateBulkFAQContextMappingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBulkFAQContextMappingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBulkFAQContextMappingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBulkFAQContextMappingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBulkFAQContextMappingResponseValidationError{}
