// Code generated by MockGen. DO NOT EDIT.
// Source: api/inapphelp/feedback_engine/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	feedback_engine "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockFeedbackEngineClient is a mock of FeedbackEngineClient interface.
type MockFeedbackEngineClient struct {
	ctrl     *gomock.Controller
	recorder *MockFeedbackEngineClientMockRecorder
}

// MockFeedbackEngineClientMockRecorder is the mock recorder for MockFeedbackEngineClient.
type MockFeedbackEngineClientMockRecorder struct {
	mock *MockFeedbackEngineClient
}

// NewMockFeedbackEngineClient creates a new mock instance.
func NewMockFeedbackEngineClient(ctrl *gomock.Controller) *MockFeedbackEngineClient {
	mock := &MockFeedbackEngineClient{ctrl: ctrl}
	mock.recorder = &MockFeedbackEngineClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeedbackEngineClient) EXPECT() *MockFeedbackEngineClientMockRecorder {
	return m.recorder
}

// GetFirstFeedbackQuestion mocks base method.
func (m *MockFeedbackEngineClient) GetFirstFeedbackQuestion(ctx context.Context, in *feedback_engine.GetFirstFeedbackQuestionRequest, opts ...grpc.CallOption) (*feedback_engine.GetFirstFeedbackQuestionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstFeedbackQuestion", varargs...)
	ret0, _ := ret[0].(*feedback_engine.GetFirstFeedbackQuestionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstFeedbackQuestion indicates an expected call of GetFirstFeedbackQuestion.
func (mr *MockFeedbackEngineClientMockRecorder) GetFirstFeedbackQuestion(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstFeedbackQuestion", reflect.TypeOf((*MockFeedbackEngineClient)(nil).GetFirstFeedbackQuestion), varargs...)
}

// GetNextFeedbackQuestion mocks base method.
func (m *MockFeedbackEngineClient) GetNextFeedbackQuestion(ctx context.Context, in *feedback_engine.GetNextFeedbackQuestionRequest, opts ...grpc.CallOption) (*feedback_engine.GetNextFeedbackQuestionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNextFeedbackQuestion", varargs...)
	ret0, _ := ret[0].(*feedback_engine.GetNextFeedbackQuestionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextFeedbackQuestion indicates an expected call of GetNextFeedbackQuestion.
func (mr *MockFeedbackEngineClientMockRecorder) GetNextFeedbackQuestion(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextFeedbackQuestion", reflect.TypeOf((*MockFeedbackEngineClient)(nil).GetNextFeedbackQuestion), varargs...)
}

// MockFeedbackEngineServer is a mock of FeedbackEngineServer interface.
type MockFeedbackEngineServer struct {
	ctrl     *gomock.Controller
	recorder *MockFeedbackEngineServerMockRecorder
}

// MockFeedbackEngineServerMockRecorder is the mock recorder for MockFeedbackEngineServer.
type MockFeedbackEngineServerMockRecorder struct {
	mock *MockFeedbackEngineServer
}

// NewMockFeedbackEngineServer creates a new mock instance.
func NewMockFeedbackEngineServer(ctrl *gomock.Controller) *MockFeedbackEngineServer {
	mock := &MockFeedbackEngineServer{ctrl: ctrl}
	mock.recorder = &MockFeedbackEngineServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeedbackEngineServer) EXPECT() *MockFeedbackEngineServerMockRecorder {
	return m.recorder
}

// GetFirstFeedbackQuestion mocks base method.
func (m *MockFeedbackEngineServer) GetFirstFeedbackQuestion(arg0 context.Context, arg1 *feedback_engine.GetFirstFeedbackQuestionRequest) (*feedback_engine.GetFirstFeedbackQuestionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstFeedbackQuestion", arg0, arg1)
	ret0, _ := ret[0].(*feedback_engine.GetFirstFeedbackQuestionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstFeedbackQuestion indicates an expected call of GetFirstFeedbackQuestion.
func (mr *MockFeedbackEngineServerMockRecorder) GetFirstFeedbackQuestion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstFeedbackQuestion", reflect.TypeOf((*MockFeedbackEngineServer)(nil).GetFirstFeedbackQuestion), arg0, arg1)
}

// GetNextFeedbackQuestion mocks base method.
func (m *MockFeedbackEngineServer) GetNextFeedbackQuestion(arg0 context.Context, arg1 *feedback_engine.GetNextFeedbackQuestionRequest) (*feedback_engine.GetNextFeedbackQuestionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextFeedbackQuestion", arg0, arg1)
	ret0, _ := ret[0].(*feedback_engine.GetNextFeedbackQuestionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextFeedbackQuestion indicates an expected call of GetNextFeedbackQuestion.
func (mr *MockFeedbackEngineServerMockRecorder) GetNextFeedbackQuestion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextFeedbackQuestion", reflect.TypeOf((*MockFeedbackEngineServer)(nil).GetNextFeedbackQuestion), arg0, arg1)
}

// MockUnsafeFeedbackEngineServer is a mock of UnsafeFeedbackEngineServer interface.
type MockUnsafeFeedbackEngineServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeFeedbackEngineServerMockRecorder
}

// MockUnsafeFeedbackEngineServerMockRecorder is the mock recorder for MockUnsafeFeedbackEngineServer.
type MockUnsafeFeedbackEngineServerMockRecorder struct {
	mock *MockUnsafeFeedbackEngineServer
}

// NewMockUnsafeFeedbackEngineServer creates a new mock instance.
func NewMockUnsafeFeedbackEngineServer(ctrl *gomock.Controller) *MockUnsafeFeedbackEngineServer {
	mock := &MockUnsafeFeedbackEngineServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeFeedbackEngineServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeFeedbackEngineServer) EXPECT() *MockUnsafeFeedbackEngineServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedFeedbackEngineServer mocks base method.
func (m *MockUnsafeFeedbackEngineServer) mustEmbedUnimplementedFeedbackEngineServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedFeedbackEngineServer")
}

// mustEmbedUnimplementedFeedbackEngineServer indicates an expected call of mustEmbedUnimplementedFeedbackEngineServer.
func (mr *MockUnsafeFeedbackEngineServerMockRecorder) mustEmbedUnimplementedFeedbackEngineServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedFeedbackEngineServer", reflect.TypeOf((*MockUnsafeFeedbackEngineServer)(nil).mustEmbedUnimplementedFeedbackEngineServer))
}
