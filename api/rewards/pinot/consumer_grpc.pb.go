//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/rewards/pinot/consumer.proto

package pinot

import (
	context "context"
	events "github.com/epifi/gamma/api/rewards/events"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Consumer_ProcessTerminalRewardEventsForPinot_FullMethodName = "/rewards.pinot.Consumer/ProcessTerminalRewardEventsForPinot"
	Consumer_ProcessProjectionEventForPinot_FullMethodName      = "/rewards.pinot.Consumer/ProcessProjectionEventForPinot"
)

// ConsumerClient is the client API for Consumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsumerClient interface {
	// rewards pinot consumer which consumes the terminal state reward (PROCESSED, EXPIRED)
	// which are pushed to RewardStatusUpdateEvent sns topic,stitches other information
	// and publishes them to the kafka stream for uploading to realtime table for terminal rewards (rewards_terminal)
	// Also publishes to kafka stream of realtime table for non_terminal rewards with delete_entry set to true,
	// so that any reward which may be present in non-terminal realtime table is soft-deleted for querying
	ProcessTerminalRewardEventsForPinot(ctx context.Context, in *events.RewardStatusUpdateEvent, opts ...grpc.CallOption) (*ConsumerResponse, error)
	// projections pinot consumer which consumes projection generation/update events,
	// which are pushed to ProjectionEvent sns topic,stitches other information
	// and publishes them to kafka topic for uploading to realtime table for projections (reward_projections)
	ProcessProjectionEventForPinot(ctx context.Context, in *events.ProjectionEvent, opts ...grpc.CallOption) (*ConsumerResponse, error)
}

type consumerClient struct {
	cc grpc.ClientConnInterface
}

func NewConsumerClient(cc grpc.ClientConnInterface) ConsumerClient {
	return &consumerClient{cc}
}

func (c *consumerClient) ProcessTerminalRewardEventsForPinot(ctx context.Context, in *events.RewardStatusUpdateEvent, opts ...grpc.CallOption) (*ConsumerResponse, error) {
	out := new(ConsumerResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessTerminalRewardEventsForPinot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessProjectionEventForPinot(ctx context.Context, in *events.ProjectionEvent, opts ...grpc.CallOption) (*ConsumerResponse, error) {
	out := new(ConsumerResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessProjectionEventForPinot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsumerServer is the server API for Consumer service.
// All implementations should embed UnimplementedConsumerServer
// for forward compatibility
type ConsumerServer interface {
	// rewards pinot consumer which consumes the terminal state reward (PROCESSED, EXPIRED)
	// which are pushed to RewardStatusUpdateEvent sns topic,stitches other information
	// and publishes them to the kafka stream for uploading to realtime table for terminal rewards (rewards_terminal)
	// Also publishes to kafka stream of realtime table for non_terminal rewards with delete_entry set to true,
	// so that any reward which may be present in non-terminal realtime table is soft-deleted for querying
	ProcessTerminalRewardEventsForPinot(context.Context, *events.RewardStatusUpdateEvent) (*ConsumerResponse, error)
	// projections pinot consumer which consumes projection generation/update events,
	// which are pushed to ProjectionEvent sns topic,stitches other information
	// and publishes them to kafka topic for uploading to realtime table for projections (reward_projections)
	ProcessProjectionEventForPinot(context.Context, *events.ProjectionEvent) (*ConsumerResponse, error)
}

// UnimplementedConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedConsumerServer struct {
}

func (UnimplementedConsumerServer) ProcessTerminalRewardEventsForPinot(context.Context, *events.RewardStatusUpdateEvent) (*ConsumerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessTerminalRewardEventsForPinot not implemented")
}
func (UnimplementedConsumerServer) ProcessProjectionEventForPinot(context.Context, *events.ProjectionEvent) (*ConsumerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessProjectionEventForPinot not implemented")
}

// UnsafeConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsumerServer will
// result in compilation errors.
type UnsafeConsumerServer interface {
	mustEmbedUnimplementedConsumerServer()
}

func RegisterConsumerServer(s grpc.ServiceRegistrar, srv ConsumerServer) {
	s.RegisterService(&Consumer_ServiceDesc, srv)
}

func _Consumer_ProcessTerminalRewardEventsForPinot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(events.RewardStatusUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessTerminalRewardEventsForPinot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessTerminalRewardEventsForPinot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessTerminalRewardEventsForPinot(ctx, req.(*events.RewardStatusUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessProjectionEventForPinot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(events.ProjectionEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessProjectionEventForPinot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessProjectionEventForPinot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessProjectionEventForPinot(ctx, req.(*events.ProjectionEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// Consumer_ServiceDesc is the grpc.ServiceDesc for Consumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rewards.pinot.Consumer",
	HandlerType: (*ConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessTerminalRewardEventsForPinot",
			Handler:    _Consumer_ProcessTerminalRewardEventsForPinot_Handler,
		},
		{
			MethodName: "ProcessProjectionEventForPinot",
			Handler:    _Consumer_ProcessProjectionEventForPinot_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/rewards/pinot/consumer.proto",
}
