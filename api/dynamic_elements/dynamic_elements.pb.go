// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/dynamic_elements/dynamic_elements.proto

package dynamic_elements

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	components "github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DcDashboardScreenAdditionalInfo_Section int32

const (
	DcDashboardScreenAdditionalInfo_SECTION_UNSPECIFIED DcDashboardScreenAdditionalInfo_Section = 0
	// Section for showing widget in offers & promotions section
	DcDashboardScreenAdditionalInfo_SECTION_DC_DASHBOARD_PROMOTIONAL_WIDGET DcDashboardScreenAdditionalInfo_Section = 1
	// Section for large banner
	DcDashboardScreenAdditionalInfo_SECTION_DC_DASHBOARD_LARGE_BANNER DcDashboardScreenAdditionalInfo_Section = 2
	// Section for GTM POPUP
	DcDashboardScreenAdditionalInfo_SECTION_DC_DASHBOARD_GTM_POPUP DcDashboardScreenAdditionalInfo_Section = 3
)

// Enum value maps for DcDashboardScreenAdditionalInfo_Section.
var (
	DcDashboardScreenAdditionalInfo_Section_name = map[int32]string{
		0: "SECTION_UNSPECIFIED",
		1: "SECTION_DC_DASHBOARD_PROMOTIONAL_WIDGET",
		2: "SECTION_DC_DASHBOARD_LARGE_BANNER",
		3: "SECTION_DC_DASHBOARD_GTM_POPUP",
	}
	DcDashboardScreenAdditionalInfo_Section_value = map[string]int32{
		"SECTION_UNSPECIFIED":                     0,
		"SECTION_DC_DASHBOARD_PROMOTIONAL_WIDGET": 1,
		"SECTION_DC_DASHBOARD_LARGE_BANNER":       2,
		"SECTION_DC_DASHBOARD_GTM_POPUP":          3,
	}
)

func (x DcDashboardScreenAdditionalInfo_Section) Enum() *DcDashboardScreenAdditionalInfo_Section {
	p := new(DcDashboardScreenAdditionalInfo_Section)
	*p = x
	return p
}

func (x DcDashboardScreenAdditionalInfo_Section) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DcDashboardScreenAdditionalInfo_Section) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[0].Descriptor()
}

func (DcDashboardScreenAdditionalInfo_Section) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[0]
}

func (x DcDashboardScreenAdditionalInfo_Section) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DcDashboardScreenAdditionalInfo_Section.Descriptor instead.
func (DcDashboardScreenAdditionalInfo_Section) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{5, 0}
}

type PostPaymentScreenAdditionalInfo_Section int32

const (
	PostPaymentScreenAdditionalInfo_SECTION_UNSPECIFIED PostPaymentScreenAdditionalInfo_Section = 0
	// Section for showing promotional banner
	PostPaymentScreenAdditionalInfo_SECTION_POST_PAYMENT_PROMOTIONAL_BANNER PostPaymentScreenAdditionalInfo_Section = 1
)

// Enum value maps for PostPaymentScreenAdditionalInfo_Section.
var (
	PostPaymentScreenAdditionalInfo_Section_name = map[int32]string{
		0: "SECTION_UNSPECIFIED",
		1: "SECTION_POST_PAYMENT_PROMOTIONAL_BANNER",
	}
	PostPaymentScreenAdditionalInfo_Section_value = map[string]int32{
		"SECTION_UNSPECIFIED":                     0,
		"SECTION_POST_PAYMENT_PROMOTIONAL_BANNER": 1,
	}
)

func (x PostPaymentScreenAdditionalInfo_Section) Enum() *PostPaymentScreenAdditionalInfo_Section {
	p := new(PostPaymentScreenAdditionalInfo_Section)
	*p = x
	return p
}

func (x PostPaymentScreenAdditionalInfo_Section) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PostPaymentScreenAdditionalInfo_Section) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[1].Descriptor()
}

func (PostPaymentScreenAdditionalInfo_Section) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[1]
}

func (x PostPaymentScreenAdditionalInfo_Section) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PostPaymentScreenAdditionalInfo_Section.Descriptor instead.
func (PostPaymentScreenAdditionalInfo_Section) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{6, 0}
}

type HomeScreenAdditionalInfo_Section int32

const (
	HomeScreenAdditionalInfo_SECTION_UNSPECIFIED HomeScreenAdditionalInfo_Section = 0
	// to identify the critical banner section on the top bar
	HomeScreenAdditionalInfo_SECTION_TOP_BAR HomeScreenAdditionalInfo_Section = 1
	// to identify the banner section in the body of Home screen
	HomeScreenAdditionalInfo_SECTION_BODY HomeScreenAdditionalInfo_Section = 2
	// to identify lending related promotional banner section in the body of Home screen
	HomeScreenAdditionalInfo_SECTION_BODY2 HomeScreenAdditionalInfo_Section = 3
	// To identify whether the request is made from Home V2 screen for GTM Popup
	HomeScreenAdditionalInfo_SECTION_GTM_POPUP HomeScreenAdditionalInfo_Section = 4
	// to identify the primary feature section in middle widgets section of home v2
	HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY HomeScreenAdditionalInfo_Section = 5
	// to identify the secondary feature section in middle widgets section of home v2
	// this might be a list of feature widgets coming from different services
	HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY HomeScreenAdditionalInfo_Section = 6
	// Display content within a card.
	// Optionally include tabs for switching between different tabs content views if available.
	// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	HomeScreenAdditionalInfo_SECTION_TABBED_CARD HomeScreenAdditionalInfo_Section = 7
)

// Enum value maps for HomeScreenAdditionalInfo_Section.
var (
	HomeScreenAdditionalInfo_Section_name = map[int32]string{
		0: "SECTION_UNSPECIFIED",
		1: "SECTION_TOP_BAR",
		2: "SECTION_BODY",
		3: "SECTION_BODY2",
		4: "SECTION_GTM_POPUP",
		5: "SECTION_FEATURE_PRIMARY",
		6: "SECTION_FEATURE_SECONDARY",
		7: "SECTION_TABBED_CARD",
	}
	HomeScreenAdditionalInfo_Section_value = map[string]int32{
		"SECTION_UNSPECIFIED":       0,
		"SECTION_TOP_BAR":           1,
		"SECTION_BODY":              2,
		"SECTION_BODY2":             3,
		"SECTION_GTM_POPUP":         4,
		"SECTION_FEATURE_PRIMARY":   5,
		"SECTION_FEATURE_SECONDARY": 6,
		"SECTION_TABBED_CARD":       7,
	}
)

func (x HomeScreenAdditionalInfo_Section) Enum() *HomeScreenAdditionalInfo_Section {
	p := new(HomeScreenAdditionalInfo_Section)
	*p = x
	return p
}

func (x HomeScreenAdditionalInfo_Section) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HomeScreenAdditionalInfo_Section) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[2].Descriptor()
}

func (HomeScreenAdditionalInfo_Section) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[2]
}

func (x HomeScreenAdditionalInfo_Section) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HomeScreenAdditionalInfo_Section.Descriptor instead.
func (HomeScreenAdditionalInfo_Section) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{9, 0}
}

type HomeScreenAdditionalInfo_Version int32

const (
	HomeScreenAdditionalInfo_VERSION_UNSPECIFIED HomeScreenAdditionalInfo_Version = 0
	// to identify v2 version of home screen
	HomeScreenAdditionalInfo_VERSION_V2 HomeScreenAdditionalInfo_Version = 1
)

// Enum value maps for HomeScreenAdditionalInfo_Version.
var (
	HomeScreenAdditionalInfo_Version_name = map[int32]string{
		0: "VERSION_UNSPECIFIED",
		1: "VERSION_V2",
	}
	HomeScreenAdditionalInfo_Version_value = map[string]int32{
		"VERSION_UNSPECIFIED": 0,
		"VERSION_V2":          1,
	}
)

func (x HomeScreenAdditionalInfo_Version) Enum() *HomeScreenAdditionalInfo_Version {
	p := new(HomeScreenAdditionalInfo_Version)
	*p = x
	return p
}

func (x HomeScreenAdditionalInfo_Version) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HomeScreenAdditionalInfo_Version) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[3].Descriptor()
}

func (HomeScreenAdditionalInfo_Version) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[3]
}

func (x HomeScreenAdditionalInfo_Version) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HomeScreenAdditionalInfo_Version.Descriptor instead.
func (HomeScreenAdditionalInfo_Version) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{9, 1}
}

type InvestmentLandingScreenAdditionalInfo_Section int32

const (
	InvestmentLandingScreenAdditionalInfo_SECTION_UNSPECIFIED InvestmentLandingScreenAdditionalInfo_Section = 0
	// Section for showing banner in Investment landing
	InvestmentLandingScreenAdditionalInfo_SECTION_INVEST_LANDING_BANNER InvestmentLandingScreenAdditionalInfo_Section = 1
	// Section for GTM POPUP in Investment landing
	InvestmentLandingScreenAdditionalInfo_SECTION_INVEST_GTM_POPUP InvestmentLandingScreenAdditionalInfo_Section = 2
)

// Enum value maps for InvestmentLandingScreenAdditionalInfo_Section.
var (
	InvestmentLandingScreenAdditionalInfo_Section_name = map[int32]string{
		0: "SECTION_UNSPECIFIED",
		1: "SECTION_INVEST_LANDING_BANNER",
		2: "SECTION_INVEST_GTM_POPUP",
	}
	InvestmentLandingScreenAdditionalInfo_Section_value = map[string]int32{
		"SECTION_UNSPECIFIED":           0,
		"SECTION_INVEST_LANDING_BANNER": 1,
		"SECTION_INVEST_GTM_POPUP":      2,
	}
)

func (x InvestmentLandingScreenAdditionalInfo_Section) Enum() *InvestmentLandingScreenAdditionalInfo_Section {
	p := new(InvestmentLandingScreenAdditionalInfo_Section)
	*p = x
	return p
}

func (x InvestmentLandingScreenAdditionalInfo_Section) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentLandingScreenAdditionalInfo_Section) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[4].Descriptor()
}

func (InvestmentLandingScreenAdditionalInfo_Section) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[4]
}

func (x InvestmentLandingScreenAdditionalInfo_Section) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentLandingScreenAdditionalInfo_Section.Descriptor instead.
func (InvestmentLandingScreenAdditionalInfo_Section) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{10, 0}
}

// Enum for specifying the various types of UI variants to render for this Banner
type BannerElementContentV2_BannerElementContentV2UiVariant int32

const (
	// Default handling of the UI for Home Promo banner v2. Ref:
	// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21086-48823&mode=dev
	BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED BannerElementContentV2_BannerElementContentV2UiVariant = 0
	// V2 UI handling for the Home Promo banner v2. Ref:
	// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24366&mode=dev
	BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2 BannerElementContentV2_BannerElementContentV2UiVariant = 1
)

// Enum value maps for BannerElementContentV2_BannerElementContentV2UiVariant.
var (
	BannerElementContentV2_BannerElementContentV2UiVariant_name = map[int32]string{
		0: "BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED",
		1: "BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2",
	}
	BannerElementContentV2_BannerElementContentV2UiVariant_value = map[string]int32{
		"BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED": 0,
		"BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2":          1,
	}
)

func (x BannerElementContentV2_BannerElementContentV2UiVariant) Enum() *BannerElementContentV2_BannerElementContentV2UiVariant {
	p := new(BannerElementContentV2_BannerElementContentV2UiVariant)
	*p = x
	return p
}

func (x BannerElementContentV2_BannerElementContentV2UiVariant) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BannerElementContentV2_BannerElementContentV2UiVariant) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[5].Descriptor()
}

func (BannerElementContentV2_BannerElementContentV2UiVariant) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[5]
}

func (x BannerElementContentV2_BannerElementContentV2UiVariant) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BannerElementContentV2_BannerElementContentV2UiVariant.Descriptor instead.
func (BannerElementContentV2_BannerElementContentV2UiVariant) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{16, 0}
}

type DynamicElementCta_Type int32

const (
	// No action can be taken on the event.
	DynamicElementCta_TYPE_UNSPECIFIED DynamicElementCta_Type = 0
	// user will be shown a popup element on clicking
	DynamicElementCta_TYPE_SHOW_POP_UP DynamicElementCta_Type = 1
	// lets the user to get notified later
	DynamicElementCta_TYPE_NOTIFY_ME DynamicElementCta_Type = 2
	// on this Cta, User will not be shown this element later
	DynamicElementCta_TYPE_DISMISSED DynamicElementCta_Type = 3
)

// Enum value maps for DynamicElementCta_Type.
var (
	DynamicElementCta_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "TYPE_SHOW_POP_UP",
		2: "TYPE_NOTIFY_ME",
		3: "TYPE_DISMISSED",
	}
	DynamicElementCta_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"TYPE_SHOW_POP_UP": 1,
		"TYPE_NOTIFY_ME":   2,
		"TYPE_DISMISSED":   3,
	}
)

func (x DynamicElementCta_Type) Enum() *DynamicElementCta_Type {
	p := new(DynamicElementCta_Type)
	*p = x
	return p
}

func (x DynamicElementCta_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DynamicElementCta_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[6].Descriptor()
}

func (DynamicElementCta_Type) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[6]
}

func (x DynamicElementCta_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DynamicElementCta_Type.Descriptor instead.
func (DynamicElementCta_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{20, 0}
}

// Refer this link for possible shapes
// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0-to-2.1?node-id=12197%3A101109&t=KcJwVz1S5DrrCFqO-1
type BannerSingleShapeElement_Shape int32

const (
	BannerSingleShapeElement_SHAPE_UNSPECIFIED BannerSingleShapeElement_Shape = 0
	// Oval shape
	BannerSingleShapeElement_SHAPE_STAMP_1 BannerSingleShapeElement_Shape = 1
	// Milestone: Circular top section, rounded square bottom section
	BannerSingleShapeElement_SHAPE_STAMP_2 BannerSingleShapeElement_Shape = 2
	// Pentagon: Rounded triangular top section, rounded square bottom section
	BannerSingleShapeElement_SHAPE_STAMP_3 BannerSingleShapeElement_Shape = 3
	// Square: Rounded square shape
	BannerSingleShapeElement_SHAPE_STAMP_4 BannerSingleShapeElement_Shape = 4
)

// Enum value maps for BannerSingleShapeElement_Shape.
var (
	BannerSingleShapeElement_Shape_name = map[int32]string{
		0: "SHAPE_UNSPECIFIED",
		1: "SHAPE_STAMP_1",
		2: "SHAPE_STAMP_2",
		3: "SHAPE_STAMP_3",
		4: "SHAPE_STAMP_4",
	}
	BannerSingleShapeElement_Shape_value = map[string]int32{
		"SHAPE_UNSPECIFIED": 0,
		"SHAPE_STAMP_1":     1,
		"SHAPE_STAMP_2":     2,
		"SHAPE_STAMP_3":     3,
		"SHAPE_STAMP_4":     4,
	}
)

func (x BannerSingleShapeElement_Shape) Enum() *BannerSingleShapeElement_Shape {
	p := new(BannerSingleShapeElement_Shape)
	*p = x
	return p
}

func (x BannerSingleShapeElement_Shape) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BannerSingleShapeElement_Shape) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_dynamic_elements_proto_enumTypes[7].Descriptor()
}

func (BannerSingleShapeElement_Shape) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_dynamic_elements_proto_enumTypes[7]
}

func (x BannerSingleShapeElement_Shape) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BannerSingleShapeElement_Shape.Descriptor instead.
func (BannerSingleShapeElement_Shape) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{27, 0}
}

// Fetch request to be used for integration with individual BE services
type FetchDynamicElementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId       string         `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientContext *ClientContext `protobuf:"bytes,2,opt,name=client_context,json=clientContext,proto3" json:"client_context,omitempty"`
}

func (x *FetchDynamicElementsRequest) Reset() {
	*x = FetchDynamicElementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDynamicElementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDynamicElementsRequest) ProtoMessage() {}

func (x *FetchDynamicElementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDynamicElementsRequest.ProtoReflect.Descriptor instead.
func (*FetchDynamicElementsRequest) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{0}
}

func (x *FetchDynamicElementsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *FetchDynamicElementsRequest) GetClientContext() *ClientContext {
	if x != nil {
		return x.ClientContext
	}
	return nil
}

// Fetch response to be used for integration with individual services
type FetchDynamicElementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ElementsList []*DynamicElement `protobuf:"bytes,2,rep,name=elements_list,json=elementsList,proto3" json:"elements_list,omitempty"`
}

func (x *FetchDynamicElementsResponse) Reset() {
	*x = FetchDynamicElementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDynamicElementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDynamicElementsResponse) ProtoMessage() {}

func (x *FetchDynamicElementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDynamicElementsResponse.ProtoReflect.Descriptor instead.
func (*FetchDynamicElementsResponse) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{1}
}

func (x *FetchDynamicElementsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchDynamicElementsResponse) GetElementsList() []*DynamicElement {
	if x != nil {
		return x.ElementsList
	}
	return nil
}

// Callback request to be used for integration with individual services
type DynamicElementCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId         string           `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ElementId       string           `protobuf:"bytes,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	CallbackPayload *CallbackPayload `protobuf:"bytes,3,opt,name=callback_payload,json=callbackPayload,proto3" json:"callback_payload,omitempty"`
}

func (x *DynamicElementCallbackRequest) Reset() {
	*x = DynamicElementCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicElementCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicElementCallbackRequest) ProtoMessage() {}

func (x *DynamicElementCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicElementCallbackRequest.ProtoReflect.Descriptor instead.
func (*DynamicElementCallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{2}
}

func (x *DynamicElementCallbackRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DynamicElementCallbackRequest) GetElementId() string {
	if x != nil {
		return x.ElementId
	}
	return ""
}

func (x *DynamicElementCallbackRequest) GetCallbackPayload() *CallbackPayload {
	if x != nil {
		return x.CallbackPayload
	}
	return nil
}

// Callback response to be used for integration with individual services
type DynamicElementCallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DynamicElementCallbackResponse) Reset() {
	*x = DynamicElementCallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicElementCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicElementCallbackResponse) ProtoMessage() {}

func (x *DynamicElementCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicElementCallbackResponse.ProtoReflect.Descriptor instead.
func (*DynamicElementCallbackResponse) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{3}
}

func (x *DynamicElementCallbackResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Contains the details of the client app required to fetch dynamic elements
// Apart from screen_name other contexts can be used by the BE services
// eg: Insights service may use location, time, etc to give relevant insights
type ClientContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScreenName deeplink.Screen `protobuf:"varint,1,opt,name=screen_name,json=screenName,proto3,enum=frontend.deeplink.Screen" json:"screen_name,omitempty"`
	// additional info required for dynamically rendered screens
	// eg: help category screens
	//
	// Types that are assignable to ScreenAdditionalInfo:
	//
	//	*ClientContext_FaqCategory
	//	*ClientContext_HomeInfo
	//	*ClientContext_InvestLandingInfo
	//	*ClientContext_AnalyserScreenInfo
	//	*ClientContext_DcDashboardScreenAdditionalInfo
	//	*ClientContext_PostPaymentScreenAdditionalInfo
	ScreenAdditionalInfo isClientContext_ScreenAdditionalInfo `protobuf_oneof:"screen_additional_info"`
	// app platform of the client eg: Android/Ios
	AppPlatform common.Platform `protobuf:"varint,14,opt,name=app_platform,json=appPlatform,proto3,enum=api.typesv2.common.Platform" json:"app_platform,omitempty"`
	// app version of the client
	AppVersion int32 `protobuf:"varint,15,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *ClientContext) Reset() {
	*x = ClientContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientContext) ProtoMessage() {}

func (x *ClientContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientContext.ProtoReflect.Descriptor instead.
func (*ClientContext) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{4}
}

func (x *ClientContext) GetScreenName() deeplink.Screen {
	if x != nil {
		return x.ScreenName
	}
	return deeplink.Screen(0)
}

func (m *ClientContext) GetScreenAdditionalInfo() isClientContext_ScreenAdditionalInfo {
	if m != nil {
		return m.ScreenAdditionalInfo
	}
	return nil
}

func (x *ClientContext) GetFaqCategory() *FAQCategoryScreenAdditionalInfo {
	if x, ok := x.GetScreenAdditionalInfo().(*ClientContext_FaqCategory); ok {
		return x.FaqCategory
	}
	return nil
}

func (x *ClientContext) GetHomeInfo() *HomeScreenAdditionalInfo {
	if x, ok := x.GetScreenAdditionalInfo().(*ClientContext_HomeInfo); ok {
		return x.HomeInfo
	}
	return nil
}

func (x *ClientContext) GetInvestLandingInfo() *InvestmentLandingScreenAdditionalInfo {
	if x, ok := x.GetScreenAdditionalInfo().(*ClientContext_InvestLandingInfo); ok {
		return x.InvestLandingInfo
	}
	return nil
}

func (x *ClientContext) GetAnalyserScreenInfo() *AnalyserScreenAdditionalInfo {
	if x, ok := x.GetScreenAdditionalInfo().(*ClientContext_AnalyserScreenInfo); ok {
		return x.AnalyserScreenInfo
	}
	return nil
}

func (x *ClientContext) GetDcDashboardScreenAdditionalInfo() *DcDashboardScreenAdditionalInfo {
	if x, ok := x.GetScreenAdditionalInfo().(*ClientContext_DcDashboardScreenAdditionalInfo); ok {
		return x.DcDashboardScreenAdditionalInfo
	}
	return nil
}

func (x *ClientContext) GetPostPaymentScreenAdditionalInfo() *PostPaymentScreenAdditionalInfo {
	if x, ok := x.GetScreenAdditionalInfo().(*ClientContext_PostPaymentScreenAdditionalInfo); ok {
		return x.PostPaymentScreenAdditionalInfo
	}
	return nil
}

func (x *ClientContext) GetAppPlatform() common.Platform {
	if x != nil {
		return x.AppPlatform
	}
	return common.Platform(0)
}

func (x *ClientContext) GetAppVersion() int32 {
	if x != nil {
		return x.AppVersion
	}
	return 0
}

type isClientContext_ScreenAdditionalInfo interface {
	isClientContext_ScreenAdditionalInfo()
}

type ClientContext_FaqCategory struct {
	FaqCategory *FAQCategoryScreenAdditionalInfo `protobuf:"bytes,2,opt,name=faq_category,json=faqCategory,proto3,oneof"`
}

type ClientContext_HomeInfo struct {
	// For backward compatibility the default is promotions section(SECTION_BODY) of the screen
	// i.e. if no additional info is passed it is considered as promotions section of the screen
	HomeInfo *HomeScreenAdditionalInfo `protobuf:"bytes,3,opt,name=home_info,json=homeInfo,proto3,oneof"`
}

type ClientContext_InvestLandingInfo struct {
	// Additional info for fetching different sections on Invest landing screen powered by dynamic elements
	InvestLandingInfo *InvestmentLandingScreenAdditionalInfo `protobuf:"bytes,4,opt,name=invest_landing_info,json=investLandingInfo,proto3,oneof"`
}

type ClientContext_AnalyserScreenInfo struct {
	// Additional info for analyser screen.
	AnalyserScreenInfo *AnalyserScreenAdditionalInfo `protobuf:"bytes,5,opt,name=analyser_screen_info,json=analyserScreenInfo,proto3,oneof"`
}

type ClientContext_DcDashboardScreenAdditionalInfo struct {
	// Additional info for DC dashboard
	DcDashboardScreenAdditionalInfo *DcDashboardScreenAdditionalInfo `protobuf:"bytes,6,opt,name=dc_dashboard_screen_additional_info,json=dcDashboardScreenAdditionalInfo,proto3,oneof"`
}

type ClientContext_PostPaymentScreenAdditionalInfo struct {
	// Additional info for post payment screen
	PostPaymentScreenAdditionalInfo *PostPaymentScreenAdditionalInfo `protobuf:"bytes,7,opt,name=post_payment_screen_additional_info,json=postPaymentScreenAdditionalInfo,proto3,oneof"`
}

func (*ClientContext_FaqCategory) isClientContext_ScreenAdditionalInfo() {}

func (*ClientContext_HomeInfo) isClientContext_ScreenAdditionalInfo() {}

func (*ClientContext_InvestLandingInfo) isClientContext_ScreenAdditionalInfo() {}

func (*ClientContext_AnalyserScreenInfo) isClientContext_ScreenAdditionalInfo() {}

func (*ClientContext_DcDashboardScreenAdditionalInfo) isClientContext_ScreenAdditionalInfo() {}

func (*ClientContext_PostPaymentScreenAdditionalInfo) isClientContext_ScreenAdditionalInfo() {}

type DcDashboardScreenAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Section DcDashboardScreenAdditionalInfo_Section `protobuf:"varint,1,opt,name=section,proto3,enum=dynamic_elements.DcDashboardScreenAdditionalInfo_Section" json:"section,omitempty"`
}

func (x *DcDashboardScreenAdditionalInfo) Reset() {
	*x = DcDashboardScreenAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DcDashboardScreenAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DcDashboardScreenAdditionalInfo) ProtoMessage() {}

func (x *DcDashboardScreenAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DcDashboardScreenAdditionalInfo.ProtoReflect.Descriptor instead.
func (*DcDashboardScreenAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{5}
}

func (x *DcDashboardScreenAdditionalInfo) GetSection() DcDashboardScreenAdditionalInfo_Section {
	if x != nil {
		return x.Section
	}
	return DcDashboardScreenAdditionalInfo_SECTION_UNSPECIFIED
}

type PostPaymentScreenAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Section PostPaymentScreenAdditionalInfo_Section `protobuf:"varint,1,opt,name=section,proto3,enum=dynamic_elements.PostPaymentScreenAdditionalInfo_Section" json:"section,omitempty"`
}

func (x *PostPaymentScreenAdditionalInfo) Reset() {
	*x = PostPaymentScreenAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostPaymentScreenAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostPaymentScreenAdditionalInfo) ProtoMessage() {}

func (x *PostPaymentScreenAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostPaymentScreenAdditionalInfo.ProtoReflect.Descriptor instead.
func (*PostPaymentScreenAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{6}
}

func (x *PostPaymentScreenAdditionalInfo) GetSection() PostPaymentScreenAdditionalInfo_Section {
	if x != nil {
		return x.Section
	}
	return PostPaymentScreenAdditionalInfo_SECTION_UNSPECIFIED
}

type AnalyserScreenAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnalyserName string `protobuf:"bytes,1,opt,name=analyser_name,json=analyserName,proto3" json:"analyser_name,omitempty"`
}

func (x *AnalyserScreenAdditionalInfo) Reset() {
	*x = AnalyserScreenAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalyserScreenAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyserScreenAdditionalInfo) ProtoMessage() {}

func (x *AnalyserScreenAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyserScreenAdditionalInfo.ProtoReflect.Descriptor instead.
func (*AnalyserScreenAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{7}
}

func (x *AnalyserScreenAdditionalInfo) GetAnalyserName() string {
	if x != nil {
		return x.AnalyserName
	}
	return ""
}

// additional info required to identify FAQ category screens
type FAQCategoryScreenAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryId   int64  `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
}

func (x *FAQCategoryScreenAdditionalInfo) Reset() {
	*x = FAQCategoryScreenAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FAQCategoryScreenAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FAQCategoryScreenAdditionalInfo) ProtoMessage() {}

func (x *FAQCategoryScreenAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FAQCategoryScreenAdditionalInfo.ProtoReflect.Descriptor instead.
func (*FAQCategoryScreenAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{8}
}

func (x *FAQCategoryScreenAdditionalInfo) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *FAQCategoryScreenAdditionalInfo) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

// additional info required to identify the section on home screen
type HomeScreenAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Section HomeScreenAdditionalInfo_Section `protobuf:"varint,1,opt,name=section,proto3,enum=dynamic_elements.HomeScreenAdditionalInfo_Section" json:"section,omitempty"`
	Version HomeScreenAdditionalInfo_Version `protobuf:"varint,2,opt,name=version,proto3,enum=dynamic_elements.HomeScreenAdditionalInfo_Version" json:"version,omitempty"`
}

func (x *HomeScreenAdditionalInfo) Reset() {
	*x = HomeScreenAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HomeScreenAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomeScreenAdditionalInfo) ProtoMessage() {}

func (x *HomeScreenAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomeScreenAdditionalInfo.ProtoReflect.Descriptor instead.
func (*HomeScreenAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{9}
}

func (x *HomeScreenAdditionalInfo) GetSection() HomeScreenAdditionalInfo_Section {
	if x != nil {
		return x.Section
	}
	return HomeScreenAdditionalInfo_SECTION_UNSPECIFIED
}

func (x *HomeScreenAdditionalInfo) GetVersion() HomeScreenAdditionalInfo_Version {
	if x != nil {
		return x.Version
	}
	return HomeScreenAdditionalInfo_VERSION_UNSPECIFIED
}

// additional info required to identify the section on invest landing screen
type InvestmentLandingScreenAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Section InvestmentLandingScreenAdditionalInfo_Section `protobuf:"varint,1,opt,name=section,proto3,enum=dynamic_elements.InvestmentLandingScreenAdditionalInfo_Section" json:"section,omitempty"`
}

func (x *InvestmentLandingScreenAdditionalInfo) Reset() {
	*x = InvestmentLandingScreenAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentLandingScreenAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentLandingScreenAdditionalInfo) ProtoMessage() {}

func (x *InvestmentLandingScreenAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentLandingScreenAdditionalInfo.ProtoReflect.Descriptor instead.
func (*InvestmentLandingScreenAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{10}
}

func (x *InvestmentLandingScreenAdditionalInfo) GetSection() InvestmentLandingScreenAdditionalInfo_Section {
	if x != nil {
		return x.Section
	}
	return InvestmentLandingScreenAdditionalInfo_SECTION_UNSPECIFIED
}

type DynamicElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// BE service owning this dynamic element
	OwnerService typesv2.ServiceName `protobuf:"varint,1,opt,name=owner_service,json=ownerService,proto3,enum=api.typesv2.ServiceName" json:"owner_service,omitempty"`
	// id of this dynamic element within the owner service
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	// A dynamic element can be used for alert, marketing, insights etc.
	// Not used by the client as of now.
	UtilityType ElementUtilityType `protobuf:"varint,3,opt,name=utility_type,json=utilityType,proto3,enum=dynamic_elements.ElementUtilityType" json:"utility_type,omitempty"`
	// A dynamic element can be a banner, bottom sheet, pop up etc.
	StructureType ElementStructureType `protobuf:"varint,4,opt,name=structure_type,json=structureType,proto3,enum=dynamic_elements.ElementStructureType" json:"structure_type,omitempty"`
	// its parameters are dependent on ElementStructureType
	Content *ElementContent `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// map of meta data required for biz analytics
	BizAnalyticsData map[string]string `protobuf:"bytes,6,rep,name=biz_analytics_data,json=bizAnalyticsData,proto3" json:"biz_analytics_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Endtime of the element
	EndTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *DynamicElement) Reset() {
	*x = DynamicElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicElement) ProtoMessage() {}

func (x *DynamicElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicElement.ProtoReflect.Descriptor instead.
func (*DynamicElement) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{11}
}

func (x *DynamicElement) GetOwnerService() typesv2.ServiceName {
	if x != nil {
		return x.OwnerService
	}
	return typesv2.ServiceName(0)
}

func (x *DynamicElement) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DynamicElement) GetUtilityType() ElementUtilityType {
	if x != nil {
		return x.UtilityType
	}
	return ElementUtilityType_ELEMENT_UTILITY_TYPE_UNSPECIFIED
}

func (x *DynamicElement) GetStructureType() ElementStructureType {
	if x != nil {
		return x.StructureType
	}
	return ElementStructureType_ELEMENT_STRUCTURE_TYPE_UNSPECIFIED
}

func (x *DynamicElement) GetContent() *ElementContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *DynamicElement) GetBizAnalyticsData() map[string]string {
	if x != nil {
		return x.BizAnalyticsData
	}
	return nil
}

func (x *DynamicElement) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// Contains the params required to render a dynamic element.
// the content of a targeted comms element is dependent on its structure type
// e.g., banner, bottom sheets etc.
type ElementContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Content:
	//
	//	*ElementContent_Banner
	//	*ElementContent_BottomSheet
	//	*ElementContent_PopUp
	//	*ElementContent_BannerV2
	//	*ElementContent_ScrollableBanner
	//	*ElementContent_GtmPopUpBanner
	//	*ElementContent_FeatureWidgetWithFourPoints
	//	*ElementContent_FeatureWidgetWithThreePoints
	//	*ElementContent_FeatureWidgetWithTwoPoints
	//	*ElementContent_TabbedCard
	//	*ElementContent_RedirectElement
	//	*ElementContent_ProgressBarElement
	//	*ElementContent_BannerV3
	Content isElementContent_Content `protobuf_oneof:"content"`
}

func (x *ElementContent) Reset() {
	*x = ElementContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ElementContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ElementContent) ProtoMessage() {}

func (x *ElementContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ElementContent.ProtoReflect.Descriptor instead.
func (*ElementContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{12}
}

func (m *ElementContent) GetContent() isElementContent_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (x *ElementContent) GetBanner() *BannerElementContent {
	if x, ok := x.GetContent().(*ElementContent_Banner); ok {
		return x.Banner
	}
	return nil
}

func (x *ElementContent) GetBottomSheet() *BottomSheetElementContent {
	if x, ok := x.GetContent().(*ElementContent_BottomSheet); ok {
		return x.BottomSheet
	}
	return nil
}

func (x *ElementContent) GetPopUp() *PopUpElementContent {
	if x, ok := x.GetContent().(*ElementContent_PopUp); ok {
		return x.PopUp
	}
	return nil
}

func (x *ElementContent) GetBannerV2() *BannerElementContentV2 {
	if x, ok := x.GetContent().(*ElementContent_BannerV2); ok {
		return x.BannerV2
	}
	return nil
}

func (x *ElementContent) GetScrollableBanner() *ScrollableBannerElementContent {
	if x, ok := x.GetContent().(*ElementContent_ScrollableBanner); ok {
		return x.ScrollableBanner
	}
	return nil
}

func (x *ElementContent) GetGtmPopUpBanner() *GTMPopUpBanner {
	if x, ok := x.GetContent().(*ElementContent_GtmPopUpBanner); ok {
		return x.GtmPopUpBanner
	}
	return nil
}

func (x *ElementContent) GetFeatureWidgetWithFourPoints() *FeatureWidgetWithFourPoints {
	if x, ok := x.GetContent().(*ElementContent_FeatureWidgetWithFourPoints); ok {
		return x.FeatureWidgetWithFourPoints
	}
	return nil
}

func (x *ElementContent) GetFeatureWidgetWithThreePoints() *FeatureWidgetWithThreePoints {
	if x, ok := x.GetContent().(*ElementContent_FeatureWidgetWithThreePoints); ok {
		return x.FeatureWidgetWithThreePoints
	}
	return nil
}

func (x *ElementContent) GetFeatureWidgetWithTwoPoints() *FeatureWidgetWithTwoPoints {
	if x, ok := x.GetContent().(*ElementContent_FeatureWidgetWithTwoPoints); ok {
		return x.FeatureWidgetWithTwoPoints
	}
	return nil
}

func (x *ElementContent) GetTabbedCard() *TabbedCard {
	if x, ok := x.GetContent().(*ElementContent_TabbedCard); ok {
		return x.TabbedCard
	}
	return nil
}

func (x *ElementContent) GetRedirectElement() *RedirectElementContent {
	if x, ok := x.GetContent().(*ElementContent_RedirectElement); ok {
		return x.RedirectElement
	}
	return nil
}

func (x *ElementContent) GetProgressBarElement() *ProgressBarCardContent {
	if x, ok := x.GetContent().(*ElementContent_ProgressBarElement); ok {
		return x.ProgressBarElement
	}
	return nil
}

func (x *ElementContent) GetBannerV3() *BannerElementContentV3 {
	if x, ok := x.GetContent().(*ElementContent_BannerV3); ok {
		return x.BannerV3
	}
	return nil
}

type isElementContent_Content interface {
	isElementContent_Content()
}

type ElementContent_Banner struct {
	Banner *BannerElementContent `protobuf:"bytes,1,opt,name=banner,proto3,oneof"`
}

type ElementContent_BottomSheet struct {
	BottomSheet *BottomSheetElementContent `protobuf:"bytes,2,opt,name=bottom_sheet,json=bottomSheet,proto3,oneof"`
}

type ElementContent_PopUp struct {
	PopUp *PopUpElementContent `protobuf:"bytes,3,opt,name=pop_up,json=popUp,proto3,oneof"`
}

type ElementContent_BannerV2 struct {
	BannerV2 *BannerElementContentV2 `protobuf:"bytes,4,opt,name=banner_v2,json=bannerV2,proto3,oneof"`
}

type ElementContent_ScrollableBanner struct {
	ScrollableBanner *ScrollableBannerElementContent `protobuf:"bytes,5,opt,name=scrollable_banner,json=scrollableBanner,proto3,oneof"`
}

type ElementContent_GtmPopUpBanner struct {
	GtmPopUpBanner *GTMPopUpBanner `protobuf:"bytes,6,opt,name=gtm_pop_up_banner,json=gtmPopUpBanner,proto3,oneof"`
}

type ElementContent_FeatureWidgetWithFourPoints struct {
	FeatureWidgetWithFourPoints *FeatureWidgetWithFourPoints `protobuf:"bytes,7,opt,name=feature_widget_with_four_points,json=featureWidgetWithFourPoints,proto3,oneof"`
}

type ElementContent_FeatureWidgetWithThreePoints struct {
	FeatureWidgetWithThreePoints *FeatureWidgetWithThreePoints `protobuf:"bytes,8,opt,name=feature_widget_with_three_points,json=featureWidgetWithThreePoints,proto3,oneof"`
}

type ElementContent_FeatureWidgetWithTwoPoints struct {
	FeatureWidgetWithTwoPoints *FeatureWidgetWithTwoPoints `protobuf:"bytes,9,opt,name=feature_widget_with_two_points,json=featureWidgetWithTwoPoints,proto3,oneof"`
}

type ElementContent_TabbedCard struct {
	TabbedCard *TabbedCard `protobuf:"bytes,10,opt,name=tabbed_card,json=tabbedCard,proto3,oneof"`
}

type ElementContent_RedirectElement struct {
	RedirectElement *RedirectElementContent `protobuf:"bytes,11,opt,name=redirect_element,json=redirectElement,proto3,oneof"`
}

type ElementContent_ProgressBarElement struct {
	ProgressBarElement *ProgressBarCardContent `protobuf:"bytes,12,opt,name=progress_bar_element,json=progressBarElement,proto3,oneof"`
}

type ElementContent_BannerV3 struct {
	BannerV3 *BannerElementContentV3 `protobuf:"bytes,13,opt,name=banner_v3,json=bannerV3,proto3,oneof"`
}

func (*ElementContent_Banner) isElementContent_Content() {}

func (*ElementContent_BottomSheet) isElementContent_Content() {}

func (*ElementContent_PopUp) isElementContent_Content() {}

func (*ElementContent_BannerV2) isElementContent_Content() {}

func (*ElementContent_ScrollableBanner) isElementContent_Content() {}

func (*ElementContent_GtmPopUpBanner) isElementContent_Content() {}

func (*ElementContent_FeatureWidgetWithFourPoints) isElementContent_Content() {}

func (*ElementContent_FeatureWidgetWithThreePoints) isElementContent_Content() {}

func (*ElementContent_FeatureWidgetWithTwoPoints) isElementContent_Content() {}

func (*ElementContent_TabbedCard) isElementContent_Content() {}

func (*ElementContent_RedirectElement) isElementContent_Content() {}

func (*ElementContent_ProgressBarElement) isElementContent_Content() {}

func (*ElementContent_BannerV3) isElementContent_Content() {}

// redirects user to a deeplink
type RedirectElementContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// redirect to deeplink
	Deeplink *deeplink.Deeplink `protobuf:"bytes,1,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *RedirectElementContent) Reset() {
	*x = RedirectElementContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedirectElementContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedirectElementContent) ProtoMessage() {}

func (x *RedirectElementContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedirectElementContent.ProtoReflect.Descriptor instead.
func (*RedirectElementContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{13}
}

func (x *RedirectElementContent) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// Display content within a card.
// Optionally include tabs for switching between different tabs content views if available.
// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
type TabbedCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional Title for the whole Tabbed Card View
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Tabs for the Widget tapping on which we show the respective card for the tab
	Tabs []*TabbedCard_Tab `protobuf:"bytes,2,rep,name=tabs,proto3" json:"tabs,omitempty"`
	// Background color for tab when selected e.g white
	SelectedTabBgColor *ui.BackgroundColour `protobuf:"bytes,3,opt,name=selected_tab_bg_color,json=selectedTabBgColor,proto3" json:"selected_tab_bg_color,omitempty"`
	// Background color for tab when selected e.g gray
	UnselectedTabBgColor *ui.BackgroundColour `protobuf:"bytes,4,opt,name=unselected_tab_bg_color,json=unselectedTabBgColor,proto3" json:"unselected_tab_bg_color,omitempty"`
}

func (x *TabbedCard) Reset() {
	*x = TabbedCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TabbedCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TabbedCard) ProtoMessage() {}

func (x *TabbedCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TabbedCard.ProtoReflect.Descriptor instead.
func (*TabbedCard) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{14}
}

func (x *TabbedCard) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *TabbedCard) GetTabs() []*TabbedCard_Tab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *TabbedCard) GetSelectedTabBgColor() *ui.BackgroundColour {
	if x != nil {
		return x.SelectedTabBgColor
	}
	return nil
}

func (x *TabbedCard) GetUnselectedTabBgColor() *ui.BackgroundColour {
	if x != nil {
		return x.UnselectedTabBgColor
	}
	return nil
}

// Contains data/params required to render a Banner element
type BannerElementContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the banner
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// body of the banner with detailed description
	Body string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// url for the icon to be shown on the banner
	// deprecated in favour of visual_element
	//
	// Deprecated: Marked as deprecated in api/dynamic_elements/dynamic_elements.proto.
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// hex encoded background color for the banner.
	BackgroundColor string `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// list of CTAs to be rendered on the banner
	CtaList []*DynamicElementCta `protobuf:"bytes,5,rep,name=cta_list,json=ctaList,proto3" json:"cta_list,omitempty"`
	// Deeplink for click on banner
	Deeplink *deeplink.Deeplink `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// text color for title
	TitleTextColor string `protobuf:"bytes,7,opt,name=title_text_color,json=titleTextColor,proto3" json:"title_text_color,omitempty"`
	// text color for body
	BodyTextColor string                `protobuf:"bytes,8,opt,name=body_text_color,json=bodyTextColor,proto3" json:"body_text_color,omitempty"`
	VisualElement *common.VisualElement `protobuf:"bytes,9,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
}

func (x *BannerElementContent) Reset() {
	*x = BannerElementContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerElementContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerElementContent) ProtoMessage() {}

func (x *BannerElementContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerElementContent.ProtoReflect.Descriptor instead.
func (*BannerElementContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{15}
}

func (x *BannerElementContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BannerElementContent) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

// Deprecated: Marked as deprecated in api/dynamic_elements/dynamic_elements.proto.
func (x *BannerElementContent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *BannerElementContent) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *BannerElementContent) GetCtaList() []*DynamicElementCta {
	if x != nil {
		return x.CtaList
	}
	return nil
}

func (x *BannerElementContent) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *BannerElementContent) GetTitleTextColor() string {
	if x != nil {
		return x.TitleTextColor
	}
	return ""
}

func (x *BannerElementContent) GetBodyTextColor() string {
	if x != nil {
		return x.BodyTextColor
	}
	return ""
}

func (x *BannerElementContent) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

// Contains data/params required to render a Banner element on home v2
type BannerElementContentV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the banner
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// image to be shown on the banner
	// deprecated in favour of visual_element
	//
	// Deprecated: Marked as deprecated in api/dynamic_elements/dynamic_elements.proto.
	Image *common.Image `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	// background color for the banner.
	BackgroundColor *ui.BackgroundColour `protobuf:"bytes,3,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// list of CTAs to be rendered on the banner
	CtaList []*DynamicElementCta `protobuf:"bytes,4,rep,name=cta_list,json=ctaList,proto3" json:"cta_list,omitempty"`
	// Deeplink for click on banner
	Deeplink *deeplink.Deeplink `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// shadows to be shown on the banner
	Shadows []*ui.Shadow `protobuf:"bytes,6,rep,name=shadows,proto3" json:"shadows,omitempty"`
	// body of the banner with detailed description
	// Optional field
	Body *common.Text `protobuf:"bytes,7,opt,name=body,proto3" json:"body,omitempty"`
	// Parameters to decide whether to show time counter or not
	TimeCounterParams *TimeCounterParams    `protobuf:"bytes,8,opt,name=time_counter_params,json=timeCounterParams,proto3" json:"time_counter_params,omitempty"`
	VisualElement     *common.VisualElement `protobuf:"bytes,9,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	// The Visual to render on the Full Banner space. If this is set, Banner will only display the Full visual and no other
	// elements will be visible, apart from timer and the Indicator points when applicable. Ref:
	// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-27875&mode=dev
	VisualElementFullBanner *common.VisualElement `protobuf:"bytes,10,opt,name=visual_element_full_banner,json=visualElementFullBanner,proto3" json:"visual_element_full_banner,omitempty"`
	// The background color of the selected indicator view/dot, when this banner element is visible. Ref:
	// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24373&mode=dev
	IndicatorSelectedColor *ui.BackgroundColour `protobuf:"bytes,11,opt,name=indicator_selected_color,json=indicatorSelectedColor,proto3" json:"indicator_selected_color,omitempty"`
	// The background color of the default views/dots, when this banner element is visible. Ref:
	// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24373&mode=dev
	IndicatorDefaultColor *ui.BackgroundColour `protobuf:"bytes,12,opt,name=indicator_default_color,json=indicatorDefaultColor,proto3" json:"indicator_default_color,omitempty"`
	// [Optional] Ui variant type used for rendering [BannerElementContentV2]
	// Clients should render the entire list of Promo banners in either V2 or existing Ui, depending on the
	// 1st element's banner_element_content_v2_ui_variant
	BannerElementContentV2UiVariant BannerElementContentV2_BannerElementContentV2UiVariant `protobuf:"varint,13,opt,name=banner_element_content_v2_ui_variant,json=bannerElementContentV2UiVariant,proto3,enum=dynamic_elements.BannerElementContentV2_BannerElementContentV2UiVariant" json:"banner_element_content_v2_ui_variant,omitempty"`
}

func (x *BannerElementContentV2) Reset() {
	*x = BannerElementContentV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerElementContentV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerElementContentV2) ProtoMessage() {}

func (x *BannerElementContentV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerElementContentV2.ProtoReflect.Descriptor instead.
func (*BannerElementContentV2) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{16}
}

func (x *BannerElementContentV2) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

// Deprecated: Marked as deprecated in api/dynamic_elements/dynamic_elements.proto.
func (x *BannerElementContentV2) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BannerElementContentV2) GetBackgroundColor() *ui.BackgroundColour {
	if x != nil {
		return x.BackgroundColor
	}
	return nil
}

func (x *BannerElementContentV2) GetCtaList() []*DynamicElementCta {
	if x != nil {
		return x.CtaList
	}
	return nil
}

func (x *BannerElementContentV2) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *BannerElementContentV2) GetShadows() []*ui.Shadow {
	if x != nil {
		return x.Shadows
	}
	return nil
}

func (x *BannerElementContentV2) GetBody() *common.Text {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *BannerElementContentV2) GetTimeCounterParams() *TimeCounterParams {
	if x != nil {
		return x.TimeCounterParams
	}
	return nil
}

func (x *BannerElementContentV2) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *BannerElementContentV2) GetVisualElementFullBanner() *common.VisualElement {
	if x != nil {
		return x.VisualElementFullBanner
	}
	return nil
}

func (x *BannerElementContentV2) GetIndicatorSelectedColor() *ui.BackgroundColour {
	if x != nil {
		return x.IndicatorSelectedColor
	}
	return nil
}

func (x *BannerElementContentV2) GetIndicatorDefaultColor() *ui.BackgroundColour {
	if x != nil {
		return x.IndicatorDefaultColor
	}
	return nil
}

func (x *BannerElementContentV2) GetBannerElementContentV2UiVariant() BannerElementContentV2_BannerElementContentV2UiVariant {
	if x != nil {
		return x.BannerElementContentV2UiVariant
	}
	return BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED
}

type TimeCounterParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether to show time counter in banner or not
	ShowTimeCounter bool `protobuf:"varint,1,opt,name=show_time_counter,json=showTimeCounter,proto3" json:"show_time_counter,omitempty"`
	// Text parameters to be used for time counter text
	TextParams *common.Text `protobuf:"bytes,2,opt,name=text_params,json=textParams,proto3" json:"text_params,omitempty"`
	// BG colour to be used in time counter section
	BgColour string `protobuf:"bytes,3,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *TimeCounterParams) Reset() {
	*x = TimeCounterParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeCounterParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeCounterParams) ProtoMessage() {}

func (x *TimeCounterParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeCounterParams.ProtoReflect.Descriptor instead.
func (*TimeCounterParams) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{17}
}

func (x *TimeCounterParams) GetShowTimeCounter() bool {
	if x != nil {
		return x.ShowTimeCounter
	}
	return false
}

func (x *TimeCounterParams) GetTextParams() *common.Text {
	if x != nil {
		return x.TextParams
	}
	return nil
}

func (x *TimeCounterParams) GetBgColour() string {
	if x != nil {
		return x.BgColour
	}
	return ""
}

// Contains data/params required to render a Bottom Sheet element
// TODO: [V2] Bottom sheet element is not fully scoped out. Not used as of now.
type BottomSheetElementContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the bottom sheet
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// body of the bottom sheet with detailed description
	Body string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// url for the icon to be shown on the bottom sheet
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// list of CTAs to be rendered on the bottom sheet
	CtaList []*DynamicElementCta `protobuf:"bytes,4,rep,name=cta_list,json=ctaList,proto3" json:"cta_list,omitempty"`
}

func (x *BottomSheetElementContent) Reset() {
	*x = BottomSheetElementContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BottomSheetElementContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BottomSheetElementContent) ProtoMessage() {}

func (x *BottomSheetElementContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BottomSheetElementContent.ProtoReflect.Descriptor instead.
func (*BottomSheetElementContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{18}
}

func (x *BottomSheetElementContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BottomSheetElementContent) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *BottomSheetElementContent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *BottomSheetElementContent) GetCtaList() []*DynamicElementCta {
	if x != nil {
		return x.CtaList
	}
	return nil
}

// Contains data/params required to render a Pop Up element
type PopUpElementContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the pop up
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// body of the bottom sheet with detailed description
	Body string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// url for the icon to be shown on the pop up
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// hex encoded background color for the banner.
	BackgroundColor string `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// list of CTAs to be rendered on the pop up
	CtaList []*DynamicElementCta `protobuf:"bytes,5,rep,name=cta_list,json=ctaList,proto3" json:"cta_list,omitempty"`
	// A section with extra text content. Example, starts/ends text seen here:
	// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=2017%3A46103&t=YI8R7iqkhCKhLutT-0
	AdditionalTextSection *PopUpElementContent_AdditionalTextSection `protobuf:"bytes,6,opt,name=additional_text_section,json=additionalTextSection,proto3" json:"additional_text_section,omitempty"`
}

func (x *PopUpElementContent) Reset() {
	*x = PopUpElementContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PopUpElementContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PopUpElementContent) ProtoMessage() {}

func (x *PopUpElementContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PopUpElementContent.ProtoReflect.Descriptor instead.
func (*PopUpElementContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{19}
}

func (x *PopUpElementContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PopUpElementContent) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *PopUpElementContent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *PopUpElementContent) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *PopUpElementContent) GetCtaList() []*DynamicElementCta {
	if x != nil {
		return x.CtaList
	}
	return nil
}

func (x *PopUpElementContent) GetAdditionalTextSection() *PopUpElementContent_AdditionalTextSection {
	if x != nil {
		return x.AdditionalTextSection
	}
	return nil
}

type DynamicElementCta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of CTA
	Type DynamicElementCta_Type `protobuf:"varint,1,opt,name=type,proto3,enum=dynamic_elements.DynamicElementCta_Type" json:"type,omitempty"`
	Text string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// hex encoded background color for the cta.
	BackgroundColor string `protobuf:"bytes,3,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// options required for different types of CTAs
	//
	// Types that are assignable to Options:
	//
	//	*DynamicElementCta_PopUpOptions
	Options isDynamicElementCta_Options `protobuf_oneof:"options"`
	// Deeplink for click on CTA
	Deeplink *deeplink.Deeplink `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// image url for the cta
	CtaImageUrl string `protobuf:"bytes,6,opt,name=cta_image_url,json=ctaImageUrl,proto3" json:"cta_image_url,omitempty"`
	// hex encoded color for the cta text.
	TextColor string `protobuf:"bytes,7,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
}

func (x *DynamicElementCta) Reset() {
	*x = DynamicElementCta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicElementCta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicElementCta) ProtoMessage() {}

func (x *DynamicElementCta) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicElementCta.ProtoReflect.Descriptor instead.
func (*DynamicElementCta) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{20}
}

func (x *DynamicElementCta) GetType() DynamicElementCta_Type {
	if x != nil {
		return x.Type
	}
	return DynamicElementCta_TYPE_UNSPECIFIED
}

func (x *DynamicElementCta) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *DynamicElementCta) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (m *DynamicElementCta) GetOptions() isDynamicElementCta_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (x *DynamicElementCta) GetPopUpOptions() *PopUpElementContent {
	if x, ok := x.GetOptions().(*DynamicElementCta_PopUpOptions); ok {
		return x.PopUpOptions
	}
	return nil
}

func (x *DynamicElementCta) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *DynamicElementCta) GetCtaImageUrl() string {
	if x != nil {
		return x.CtaImageUrl
	}
	return ""
}

func (x *DynamicElementCta) GetTextColor() string {
	if x != nil {
		return x.TextColor
	}
	return ""
}

type isDynamicElementCta_Options interface {
	isDynamicElementCta_Options()
}

type DynamicElementCta_PopUpOptions struct {
	// for TYPE_SHOW_POP_UP
	PopUpOptions *PopUpElementContent `protobuf:"bytes,4,opt,name=pop_up_options,json=popUpOptions,proto3,oneof"`
}

func (*DynamicElementCta_PopUpOptions) isDynamicElementCta_Options() {}

// The callback parameters depending upon the owner service
// TODO: [V2] to define the service specific payloads
type CallbackPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Payload:
	//
	//	*CallbackPayload_InAppTargetedComms
	//	*CallbackPayload_Insights
	//	*CallbackPayload_VkycNudge
	Payload isCallbackPayload_Payload `protobuf_oneof:"payload"`
}

func (x *CallbackPayload) Reset() {
	*x = CallbackPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackPayload) ProtoMessage() {}

func (x *CallbackPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackPayload.ProtoReflect.Descriptor instead.
func (*CallbackPayload) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{21}
}

func (m *CallbackPayload) GetPayload() isCallbackPayload_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *CallbackPayload) GetInAppTargetedComms() *InAppTargetedCommsCallbackPayload {
	if x, ok := x.GetPayload().(*CallbackPayload_InAppTargetedComms); ok {
		return x.InAppTargetedComms
	}
	return nil
}

func (x *CallbackPayload) GetInsights() *InsightsCallbackPayload {
	if x, ok := x.GetPayload().(*CallbackPayload_Insights); ok {
		return x.Insights
	}
	return nil
}

func (x *CallbackPayload) GetVkycNudge() *VKYCNudgeCallbackPayload {
	if x, ok := x.GetPayload().(*CallbackPayload_VkycNudge); ok {
		return x.VkycNudge
	}
	return nil
}

type isCallbackPayload_Payload interface {
	isCallbackPayload_Payload()
}

type CallbackPayload_InAppTargetedComms struct {
	InAppTargetedComms *InAppTargetedCommsCallbackPayload `protobuf:"bytes,1,opt,name=in_app_targeted_comms,json=inAppTargetedComms,proto3,oneof"`
}

type CallbackPayload_Insights struct {
	Insights *InsightsCallbackPayload `protobuf:"bytes,2,opt,name=insights,proto3,oneof"`
}

type CallbackPayload_VkycNudge struct {
	VkycNudge *VKYCNudgeCallbackPayload `protobuf:"bytes,3,opt,name=vkyc_nudge,json=vkycNudge,proto3,oneof"`
}

func (*CallbackPayload_InAppTargetedComms) isCallbackPayload_Payload() {}

func (*CallbackPayload_Insights) isCallbackPayload_Payload() {}

func (*CallbackPayload_VkycNudge) isCallbackPayload_Payload() {}

type InAppTargetedCommsCallbackPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsDismissed bool `protobuf:"varint,1,opt,name=is_dismissed,json=isDismissed,proto3" json:"is_dismissed,omitempty"`
}

func (x *InAppTargetedCommsCallbackPayload) Reset() {
	*x = InAppTargetedCommsCallbackPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InAppTargetedCommsCallbackPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InAppTargetedCommsCallbackPayload) ProtoMessage() {}

func (x *InAppTargetedCommsCallbackPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InAppTargetedCommsCallbackPayload.ProtoReflect.Descriptor instead.
func (*InAppTargetedCommsCallbackPayload) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{22}
}

func (x *InAppTargetedCommsCallbackPayload) GetIsDismissed() bool {
	if x != nil {
		return x.IsDismissed
	}
	return false
}

type InsightsCallbackPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InsightsCallbackPayload) Reset() {
	*x = InsightsCallbackPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsightsCallbackPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsightsCallbackPayload) ProtoMessage() {}

func (x *InsightsCallbackPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsightsCallbackPayload.ProtoReflect.Descriptor instead.
func (*InsightsCallbackPayload) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{23}
}

// update the last callback received
type VKYCNudgeCallbackPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastCallbackTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=last_callback_time,json=lastCallbackTime,proto3" json:"last_callback_time,omitempty"`
}

func (x *VKYCNudgeCallbackPayload) Reset() {
	*x = VKYCNudgeCallbackPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VKYCNudgeCallbackPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VKYCNudgeCallbackPayload) ProtoMessage() {}

func (x *VKYCNudgeCallbackPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VKYCNudgeCallbackPayload.ProtoReflect.Descriptor instead.
func (*VKYCNudgeCallbackPayload) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{24}
}

func (x *VKYCNudgeCallbackPayload) GetLastCallbackTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCallbackTime
	}
	return nil
}

// A third type of banner that shows scrolling content on the right
type ScrollableBannerElementContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Header will be static in the banner
	Header *BannerHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Scrollable part of the banner
	ScrollingElements []*BannerSingleShapeElement `protobuf:"bytes,2,rep,name=scrolling_elements,json=scrollingElements,proto3" json:"scrolling_elements,omitempty"`
	// bg colour of the full widget
	BgColour *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *ScrollableBannerElementContent) Reset() {
	*x = ScrollableBannerElementContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScrollableBannerElementContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrollableBannerElementContent) ProtoMessage() {}

func (x *ScrollableBannerElementContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrollableBannerElementContent.ProtoReflect.Descriptor instead.
func (*ScrollableBannerElementContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{25}
}

func (x *ScrollableBannerElementContent) GetHeader() *BannerHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ScrollableBannerElementContent) GetScrollingElements() []*BannerSingleShapeElement {
	if x != nil {
		return x.ScrollingElements
	}
	return nil
}

func (x *ScrollableBannerElementContent) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

type BannerHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Title to be shown in the header
	Title []*common.Text `protobuf:"bytes,1,rep,name=title,proto3" json:"title,omitempty"`
	// To redirect the user on appropriate landing screen for banner
	Cta *ui.IconTextComponent `protobuf:"bytes,2,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *BannerHeader) Reset() {
	*x = BannerHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerHeader) ProtoMessage() {}

func (x *BannerHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerHeader.ProtoReflect.Descriptor instead.
func (*BannerHeader) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{26}
}

func (x *BannerHeader) GetTitle() []*common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BannerHeader) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

type BannerSingleShapeElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Shape BannerSingleShapeElement_Shape `protobuf:"varint,1,opt,name=shape,proto3,enum=dynamic_elements.BannerSingleShapeElement_Shape" json:"shape,omitempty"`
	// Image to be shown inside each shaped element
	Image *common.Image `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	// Title of shaped element
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// Bg colour of each shape element
	BgColour *widget.BackgroundColour `protobuf:"bytes,4,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	// Shadow of shape element
	Shadow []*widget.Shadow `protobuf:"bytes,5,rep,name=shadow,proto3" json:"shadow,omitempty"`
	// Deeplink for click on banner
	Deeplink *deeplink.Deeplink `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *BannerSingleShapeElement) Reset() {
	*x = BannerSingleShapeElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerSingleShapeElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerSingleShapeElement) ProtoMessage() {}

func (x *BannerSingleShapeElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerSingleShapeElement.ProtoReflect.Descriptor instead.
func (*BannerSingleShapeElement) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{27}
}

func (x *BannerSingleShapeElement) GetShape() BannerSingleShapeElement_Shape {
	if x != nil {
		return x.Shape
	}
	return BannerSingleShapeElement_SHAPE_UNSPECIFIED
}

func (x *BannerSingleShapeElement) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BannerSingleShapeElement) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BannerSingleShapeElement) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *BannerSingleShapeElement) GetShadow() []*widget.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

func (x *BannerSingleShapeElement) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// GTMPopUpBanner defines the parameters required to show GTM pop up banner on home load
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=995-51039&t=Ff1iXdLHD498gai1-0
type GTMPopUpBanner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Body can have multiple layouts
	//
	// Types that are assignable to Body:
	//
	//	*GTMPopUpBanner_BodyLayoutParagraph_
	//	*GTMPopUpBanner_BodyLayoutBulletPoints_
	//	*GTMPopUpBanner_BodyLayoutFullLottie_
	Body isGTMPopUpBanner_Body `protobuf_oneof:"Body"`
	// Background colour of the pop up container
	BgColour *widget.BackgroundColour `protobuf:"bytes,4,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	// List of ctas to be shown
	Ctas []*DynamicElementCta `protobuf:"bytes,5,rep,name=ctas,proto3" json:"ctas,omitempty"`
	// Deeplink for click on the banner
	Deeplink *deeplink.Deeplink `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// background lotte
	BgVisualElement *common.VisualElement `protobuf:"bytes,7,opt,name=bg_visual_element,json=bgVisualElement,proto3" json:"bg_visual_element,omitempty"`
	// time duration after which the lotte is to be loaded
	StartPopUpAfter *durationpb.Duration `protobuf:"bytes,8,opt,name=start_pop_up_after,json=startPopUpAfter,proto3" json:"start_pop_up_after,omitempty"`
	// parameter to decide whether to dismiss on clicking outside the banner
	DismissOnClickOutsidePopUp bool `protobuf:"varint,9,opt,name=dismiss_on_click_outside_pop_up,json=dismissOnClickOutsidePopUp,proto3" json:"dismiss_on_click_outside_pop_up,omitempty"`
}

func (x *GTMPopUpBanner) Reset() {
	*x = GTMPopUpBanner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GTMPopUpBanner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GTMPopUpBanner) ProtoMessage() {}

func (x *GTMPopUpBanner) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GTMPopUpBanner.ProtoReflect.Descriptor instead.
func (*GTMPopUpBanner) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{28}
}

func (m *GTMPopUpBanner) GetBody() isGTMPopUpBanner_Body {
	if m != nil {
		return m.Body
	}
	return nil
}

func (x *GTMPopUpBanner) GetBodyLayoutParagraph() *GTMPopUpBanner_BodyLayoutParagraph {
	if x, ok := x.GetBody().(*GTMPopUpBanner_BodyLayoutParagraph_); ok {
		return x.BodyLayoutParagraph
	}
	return nil
}

func (x *GTMPopUpBanner) GetBodyLayoutBulletPoints() *GTMPopUpBanner_BodyLayoutBulletPoints {
	if x, ok := x.GetBody().(*GTMPopUpBanner_BodyLayoutBulletPoints_); ok {
		return x.BodyLayoutBulletPoints
	}
	return nil
}

func (x *GTMPopUpBanner) GetBodyLayoutFullLottie() *GTMPopUpBanner_BodyLayoutFullLottie {
	if x, ok := x.GetBody().(*GTMPopUpBanner_BodyLayoutFullLottie_); ok {
		return x.BodyLayoutFullLottie
	}
	return nil
}

func (x *GTMPopUpBanner) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *GTMPopUpBanner) GetCtas() []*DynamicElementCta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

func (x *GTMPopUpBanner) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *GTMPopUpBanner) GetBgVisualElement() *common.VisualElement {
	if x != nil {
		return x.BgVisualElement
	}
	return nil
}

func (x *GTMPopUpBanner) GetStartPopUpAfter() *durationpb.Duration {
	if x != nil {
		return x.StartPopUpAfter
	}
	return nil
}

func (x *GTMPopUpBanner) GetDismissOnClickOutsidePopUp() bool {
	if x != nil {
		return x.DismissOnClickOutsidePopUp
	}
	return false
}

type isGTMPopUpBanner_Body interface {
	isGTMPopUpBanner_Body()
}

type GTMPopUpBanner_BodyLayoutParagraph_ struct {
	BodyLayoutParagraph *GTMPopUpBanner_BodyLayoutParagraph `protobuf:"bytes,1,opt,name=body_layout_paragraph,json=bodyLayoutParagraph,proto3,oneof"`
}

type GTMPopUpBanner_BodyLayoutBulletPoints_ struct {
	BodyLayoutBulletPoints *GTMPopUpBanner_BodyLayoutBulletPoints `protobuf:"bytes,2,opt,name=body_layout_bullet_points,json=bodyLayoutBulletPoints,proto3,oneof"`
}

type GTMPopUpBanner_BodyLayoutFullLottie_ struct {
	BodyLayoutFullLottie *GTMPopUpBanner_BodyLayoutFullLottie `protobuf:"bytes,3,opt,name=body_layout_full_lottie,json=bodyLayoutFullLottie,proto3,oneof"`
}

func (*GTMPopUpBanner_BodyLayoutParagraph_) isGTMPopUpBanner_Body() {}

func (*GTMPopUpBanner_BodyLayoutBulletPoints_) isGTMPopUpBanner_Body() {}

func (*GTMPopUpBanner_BodyLayoutFullLottie_) isGTMPopUpBanner_Body() {}

// FeatureWidgetWithFourPoints defines the parameters required to feature widget on home which highlights 4 points
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29100&mode=design&t=BS8f7qemLTGYtAp6-1
type FeatureWidgetWithFourPoints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Card:
	//
	//	*FeatureWidgetWithFourPoints_TextVisualElementCard_
	//	*FeatureWidgetWithFourPoints_FullVisualElementCard_
	Card isFeatureWidgetWithFourPoints_Card `protobuf_oneof:"Card"`
	// boolean indicator for specifying whether this card should only be used for carousel
	IsCarouselVariant bool `protobuf:"varint,3,opt,name=is_carousel_variant,json=isCarouselVariant,proto3" json:"is_carousel_variant,omitempty"`
	// title to be shown on top of the dynamic element
	Title *common.Text `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// border color for the card
	BorderColor *widget.BackgroundColour `protobuf:"bytes,5,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
}

func (x *FeatureWidgetWithFourPoints) Reset() {
	*x = FeatureWidgetWithFourPoints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithFourPoints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithFourPoints) ProtoMessage() {}

func (x *FeatureWidgetWithFourPoints) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithFourPoints.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithFourPoints) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{29}
}

func (m *FeatureWidgetWithFourPoints) GetCard() isFeatureWidgetWithFourPoints_Card {
	if m != nil {
		return m.Card
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints) GetTextVisualElementCard() *FeatureWidgetWithFourPoints_TextVisualElementCard {
	if x, ok := x.GetCard().(*FeatureWidgetWithFourPoints_TextVisualElementCard_); ok {
		return x.TextVisualElementCard
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints) GetFullVisualElementCard() *FeatureWidgetWithFourPoints_FullVisualElementCard {
	if x, ok := x.GetCard().(*FeatureWidgetWithFourPoints_FullVisualElementCard_); ok {
		return x.FullVisualElementCard
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints) GetIsCarouselVariant() bool {
	if x != nil {
		return x.IsCarouselVariant
	}
	return false
}

func (x *FeatureWidgetWithFourPoints) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

type isFeatureWidgetWithFourPoints_Card interface {
	isFeatureWidgetWithFourPoints_Card()
}

type FeatureWidgetWithFourPoints_TextVisualElementCard_ struct {
	TextVisualElementCard *FeatureWidgetWithFourPoints_TextVisualElementCard `protobuf:"bytes,1,opt,name=text_visual_element_card,json=textVisualElementCard,proto3,oneof"`
}

type FeatureWidgetWithFourPoints_FullVisualElementCard_ struct {
	FullVisualElementCard *FeatureWidgetWithFourPoints_FullVisualElementCard `protobuf:"bytes,2,opt,name=full_visual_element_card,json=fullVisualElementCard,proto3,oneof"`
}

func (*FeatureWidgetWithFourPoints_TextVisualElementCard_) isFeatureWidgetWithFourPoints_Card() {}

func (*FeatureWidgetWithFourPoints_FullVisualElementCard_) isFeatureWidgetWithFourPoints_Card() {}

// FeatureWidgetWithThreePoints defines the parameters required to feature widget on home which highlights 3 points
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29002&mode=design&t=BS8f7qemLTGYtAp6-1
type FeatureWidgetWithThreePoints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftVerticalFlyer     *FeatureWidgetWithThreePoints_LeftVerticalFlyer      `protobuf:"bytes,1,opt,name=left_vertical_flyer,json=leftVerticalFlyer,proto3" json:"left_vertical_flyer,omitempty"`
	RightHorizontalFlyers []*FeatureWidgetWithThreePoints_RightHorizontalFlyer `protobuf:"bytes,2,rep,name=right_horizontal_flyers,json=rightHorizontalFlyers,proto3" json:"right_horizontal_flyers,omitempty"`
	// title to be shown on top of the dynamic element
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// border color for the card
	BorderColor *widget.BackgroundColour `protobuf:"bytes,4,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
}

func (x *FeatureWidgetWithThreePoints) Reset() {
	*x = FeatureWidgetWithThreePoints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithThreePoints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithThreePoints) ProtoMessage() {}

func (x *FeatureWidgetWithThreePoints) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithThreePoints.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithThreePoints) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{30}
}

func (x *FeatureWidgetWithThreePoints) GetLeftVerticalFlyer() *FeatureWidgetWithThreePoints_LeftVerticalFlyer {
	if x != nil {
		return x.LeftVerticalFlyer
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints) GetRightHorizontalFlyers() []*FeatureWidgetWithThreePoints_RightHorizontalFlyer {
	if x != nil {
		return x.RightHorizontalFlyers
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

// FeatureWidgetWithTwoPoints defines the parameters required to feature widget on home which highlights 2 points
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29103&mode=design&t=BS8f7qemLTGYtAp6-1
type FeatureWidgetWithTwoPoints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopHorizontalFlyer     *FeatureWidgetWithTwoPoints_TopHorizontalFlyer      `protobuf:"bytes,1,opt,name=top_horizontal_flyer,json=topHorizontalFlyer,proto3" json:"top_horizontal_flyer,omitempty"`
	BottomHorizontalFlyers []*FeatureWidgetWithTwoPoints_BottomHorizontalFlyer `protobuf:"bytes,2,rep,name=bottom_horizontal_flyers,json=bottomHorizontalFlyers,proto3" json:"bottom_horizontal_flyers,omitempty"`
	// title to be shown on top of the dynamic element
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// border color for the card
	BorderColor *widget.BackgroundColour `protobuf:"bytes,4,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
}

func (x *FeatureWidgetWithTwoPoints) Reset() {
	*x = FeatureWidgetWithTwoPoints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithTwoPoints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithTwoPoints) ProtoMessage() {}

func (x *FeatureWidgetWithTwoPoints) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithTwoPoints.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithTwoPoints) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{31}
}

func (x *FeatureWidgetWithTwoPoints) GetTopHorizontalFlyer() *FeatureWidgetWithTwoPoints_TopHorizontalFlyer {
	if x != nil {
		return x.TopHorizontalFlyer
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints) GetBottomHorizontalFlyers() []*FeatureWidgetWithTwoPoints_BottomHorizontalFlyer {
	if x != nil {
		return x.BottomHorizontalFlyers
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=34292-15842&t=rU7IEj5cPd2bq243-1
// For displaying progress-based features. e.g., To show user's tier benefits, Fi coins earned etc.
type ProgressBarCardContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title to be shown above the progress bar. e.g., 3% back on all transactions
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// optional, visual element to be shown on the left side of the banner
	LeftVisualElement *common.VisualElement `protobuf:"bytes,2,opt,name=left_visual_element,json=leftVisualElement,proto3" json:"left_visual_element,omitempty"`
	// progress bar component
	ProgressBar *components.LinearProgressBar `protobuf:"bytes,3,opt,name=progress_bar,json=progressBar,proto3" json:"progress_bar,omitempty"`
	// visual element to be shown on the right side of the banner. e.g., money plant image
	RightVisualElement *common.VisualElement `protobuf:"bytes,4,opt,name=right_visual_element,json=rightVisualElement,proto3" json:"right_visual_element,omitempty"`
	// content to be shown below the progress bar. e.g., `<Fi coin icon> 3000 / <Fi coin icon> 5000`
	SubtitleContent []*ProgressBarCardContent_SubtitleContent `protobuf:"bytes,5,rep,name=subtitle_content,json=subtitleContent,proto3" json:"subtitle_content,omitempty"`
	// optional, background colour of the entire card
	BgColour *widget.BackgroundColour `protobuf:"bytes,6,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *ProgressBarCardContent) Reset() {
	*x = ProgressBarCardContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressBarCardContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressBarCardContent) ProtoMessage() {}

func (x *ProgressBarCardContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressBarCardContent.ProtoReflect.Descriptor instead.
func (*ProgressBarCardContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{32}
}

func (x *ProgressBarCardContent) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ProgressBarCardContent) GetLeftVisualElement() *common.VisualElement {
	if x != nil {
		return x.LeftVisualElement
	}
	return nil
}

func (x *ProgressBarCardContent) GetProgressBar() *components.LinearProgressBar {
	if x != nil {
		return x.ProgressBar
	}
	return nil
}

func (x *ProgressBarCardContent) GetRightVisualElement() *common.VisualElement {
	if x != nil {
		return x.RightVisualElement
	}
	return nil
}

func (x *ProgressBarCardContent) GetSubtitleContent() []*ProgressBarCardContent_SubtitleContent {
	if x != nil {
		return x.SubtitleContent
	}
	return nil
}

func (x *ProgressBarCardContent) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=33339-48137&t=fuPV9532WdSfWeRF-1
// Although many of these fields are also present in BannerElementContentV2, client has logic to display only certain elements in that case.
// For example, client only shows the title, body or CTA even if BE sends all of them. Hence, defined this separate message.
type BannerElementContentV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the banner. e.g., `PAYING A BUSINESS?`
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// e.g., `Get 3% back as Fi-Coins on this payment`, `Upgrade to Prime now` cta with a deeplink
	Body *ui.IconTextComponent `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// To be rendered on the left side of the banner
	LeftVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=left_visual_element,json=leftVisualElement,proto3" json:"left_visual_element,omitempty"`
	// To be rendered on the right side of the banner
	RightVisualElement *common.VisualElement `protobuf:"bytes,4,opt,name=right_visual_element,json=rightVisualElement,proto3" json:"right_visual_element,omitempty"`
	// background colour of the banner
	BgColour *widget.BackgroundColour `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	// Deeplink to redirect the user on click of the banner
	Deeplink *deeplink.Deeplink `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Border color for the banners
	// For Example - pay landing screen banner, supporting both solid and gradient styles.
	BorderColour *widget.BackgroundColour `protobuf:"bytes,7,opt,name=borderColour,proto3" json:"borderColour,omitempty"`
}

func (x *BannerElementContentV3) Reset() {
	*x = BannerElementContentV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerElementContentV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerElementContentV3) ProtoMessage() {}

func (x *BannerElementContentV3) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerElementContentV3.ProtoReflect.Descriptor instead.
func (*BannerElementContentV3) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{33}
}

func (x *BannerElementContentV3) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BannerElementContentV3) GetBody() *ui.IconTextComponent {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *BannerElementContentV3) GetLeftVisualElement() *common.VisualElement {
	if x != nil {
		return x.LeftVisualElement
	}
	return nil
}

func (x *BannerElementContentV3) GetRightVisualElement() *common.VisualElement {
	if x != nil {
		return x.RightVisualElement
	}
	return nil
}

func (x *BannerElementContentV3) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *BannerElementContentV3) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *BannerElementContentV3) GetBorderColour() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColour
	}
	return nil
}

type TabbedCard_Tab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional value of tab
	// If we want to show only one tab don't send this so we just show the card
	// If more than one tabs always send this tab
	Tab *ui.IconTextComponent `protobuf:"bytes,1,opt,name=tab,proto3" json:"tab,omitempty"`
	// Card content for particular tab
	Card *TabbedCard_Card `protobuf:"bytes,2,opt,name=card,proto3" json:"card,omitempty"`
}

func (x *TabbedCard_Tab) Reset() {
	*x = TabbedCard_Tab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TabbedCard_Tab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TabbedCard_Tab) ProtoMessage() {}

func (x *TabbedCard_Tab) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TabbedCard_Tab.ProtoReflect.Descriptor instead.
func (*TabbedCard_Tab) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{14, 0}
}

func (x *TabbedCard_Tab) GetTab() *ui.IconTextComponent {
	if x != nil {
		return x.Tab
	}
	return nil
}

func (x *TabbedCard_Tab) GetCard() *TabbedCard_Card {
	if x != nil {
		return x.Card
	}
	return nil
}

type TabbedCard_Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-17385&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	CollectiveInfo *TabbedCard_Card_CollectiveInfoView `protobuf:"bytes,1,opt,name=collective_info,json=collectiveInfo,proto3" json:"collective_info,omitempty"`
	// Description for the card content
	// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	Description *ui.IconTextComponent `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Number of chips binding it to be max as per the requirement
	Chips []*TabbedCard_Card_Chip `protobuf:"bytes,3,rep,name=chips,proto3" json:"chips,omitempty"`
	// Footer with deeplink to land on respective screen
	// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-17385&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	Footer *ui.IconTextComponent `protobuf:"bytes,4,opt,name=footer,proto3" json:"footer,omitempty"`
	// Background Color of the card i.e white in designs
	BgColor *ui.BackgroundColour `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// Corner Radius of the card
	CornerRadius int32 `protobuf:"varint,6,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
}

func (x *TabbedCard_Card) Reset() {
	*x = TabbedCard_Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TabbedCard_Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TabbedCard_Card) ProtoMessage() {}

func (x *TabbedCard_Card) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TabbedCard_Card.ProtoReflect.Descriptor instead.
func (*TabbedCard_Card) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{14, 1}
}

func (x *TabbedCard_Card) GetCollectiveInfo() *TabbedCard_Card_CollectiveInfoView {
	if x != nil {
		return x.CollectiveInfo
	}
	return nil
}

func (x *TabbedCard_Card) GetDescription() *ui.IconTextComponent {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *TabbedCard_Card) GetChips() []*TabbedCard_Card_Chip {
	if x != nil {
		return x.Chips
	}
	return nil
}

func (x *TabbedCard_Card) GetFooter() *ui.IconTextComponent {
	if x != nil {
		return x.Footer
	}
	return nil
}

func (x *TabbedCard_Card) GetBgColor() *ui.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *TabbedCard_Card) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

type TabbedCard_Card_CollectiveInfoView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Background colour for https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	BgColour *ui.BackgroundColour `protobuf:"bytes,1,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	// Array of collective informations like Portfolio, Today etc in figma
	CollectiveInfos []*TabbedCard_Card_CollectiveInfo `protobuf:"bytes,2,rep,name=collective_infos,json=collectiveInfos,proto3" json:"collective_infos,omitempty"`
	// Corner Radius of the card
	CornerRadius int32 `protobuf:"varint,3,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
}

func (x *TabbedCard_Card_CollectiveInfoView) Reset() {
	*x = TabbedCard_Card_CollectiveInfoView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TabbedCard_Card_CollectiveInfoView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TabbedCard_Card_CollectiveInfoView) ProtoMessage() {}

func (x *TabbedCard_Card_CollectiveInfoView) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TabbedCard_Card_CollectiveInfoView.ProtoReflect.Descriptor instead.
func (*TabbedCard_Card_CollectiveInfoView) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{14, 1, 0}
}

func (x *TabbedCard_Card_CollectiveInfoView) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *TabbedCard_Card_CollectiveInfoView) GetCollectiveInfos() []*TabbedCard_Card_CollectiveInfo {
	if x != nil {
		return x.CollectiveInfos
	}
	return nil
}

func (x *TabbedCard_Card_CollectiveInfoView) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

type TabbedCard_Card_CollectiveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	// primary value e.g Portfolio $125.3 or Today
	Primary *ui.IconTextComponent `protobuf:"bytes,1,opt,name=primary,proto3" json:"primary,omitempty"`
	// secondary value e.g ^ 4.5% shows the change in the overall values
	Secondary *ui.IconTextComponent `protobuf:"bytes,2,opt,name=secondary,proto3" json:"secondary,omitempty"`
}

func (x *TabbedCard_Card_CollectiveInfo) Reset() {
	*x = TabbedCard_Card_CollectiveInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TabbedCard_Card_CollectiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TabbedCard_Card_CollectiveInfo) ProtoMessage() {}

func (x *TabbedCard_Card_CollectiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TabbedCard_Card_CollectiveInfo.ProtoReflect.Descriptor instead.
func (*TabbedCard_Card_CollectiveInfo) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{14, 1, 1}
}

func (x *TabbedCard_Card_CollectiveInfo) GetPrimary() *ui.IconTextComponent {
	if x != nil {
		return x.Primary
	}
	return nil
}

func (x *TabbedCard_Card_CollectiveInfo) GetSecondary() *ui.IconTextComponent {
	if x != nil {
		return x.Secondary
	}
	return nil
}

// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-17334&mode=design&t=Q1lU7tXjuFhkQ8R7-0
type TabbedCard_Card_Chip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Image of the Chip e.g Amazon image
	Image *common.VisualElement `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	// Title of the Chip e.g Name of Stocks
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// This is either the change in value for stocks or mutual funds else just a simple itc for other views
	Subtitle *ui.IconTextComponent `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// Boolean that shows if some chip change in values has to be tracked realtime
	// This will make the subtitle to track realtime or not
	ShouldTrackRealtime bool `protobuf:"varint,4,opt,name=should_track_realtime,json=shouldTrackRealtime,proto3" json:"should_track_realtime,omitempty"`
	// Stock Id for which to track real time
	// Will be used only when real time tracking is enabled
	StockId string `protobuf:"bytes,5,opt,name=stock_id,json=stockId,proto3" json:"stock_id,omitempty"`
	// deeplink during chips click action
	Deeplink *deeplink.Deeplink `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *TabbedCard_Card_Chip) Reset() {
	*x = TabbedCard_Card_Chip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TabbedCard_Card_Chip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TabbedCard_Card_Chip) ProtoMessage() {}

func (x *TabbedCard_Card_Chip) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TabbedCard_Card_Chip.ProtoReflect.Descriptor instead.
func (*TabbedCard_Card_Chip) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{14, 1, 2}
}

func (x *TabbedCard_Card_Chip) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *TabbedCard_Card_Chip) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *TabbedCard_Card_Chip) GetSubtitle() *ui.IconTextComponent {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *TabbedCard_Card_Chip) GetShouldTrackRealtime() bool {
	if x != nil {
		return x.ShouldTrackRealtime
	}
	return false
}

func (x *TabbedCard_Card_Chip) GetStockId() string {
	if x != nil {
		return x.StockId
	}
	return ""
}

func (x *TabbedCard_Card_Chip) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type PopUpElementContent_AdditionalTextSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Background color of the box
	BackgroundColor *ui.BackgroundColour `protobuf:"bytes,1,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// A list of text values to be displayed vertically
	Texts []*common.Text `protobuf:"bytes,2,rep,name=texts,proto3" json:"texts,omitempty"`
}

func (x *PopUpElementContent_AdditionalTextSection) Reset() {
	*x = PopUpElementContent_AdditionalTextSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PopUpElementContent_AdditionalTextSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PopUpElementContent_AdditionalTextSection) ProtoMessage() {}

func (x *PopUpElementContent_AdditionalTextSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PopUpElementContent_AdditionalTextSection.ProtoReflect.Descriptor instead.
func (*PopUpElementContent_AdditionalTextSection) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{19, 0}
}

func (x *PopUpElementContent_AdditionalTextSection) GetBackgroundColor() *ui.BackgroundColour {
	if x != nil {
		return x.BackgroundColor
	}
	return nil
}

func (x *PopUpElementContent_AdditionalTextSection) GetTexts() []*common.Text {
	if x != nil {
		return x.Texts
	}
	return nil
}

type GTMPopUpBanner_BodyLayoutParagraph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	BodyContent *common.Text `protobuf:"bytes,2,opt,name=body_content,json=bodyContent,proto3" json:"body_content,omitempty"`
	// Image/lotte to be shown on pop up
	PopUpVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=pop_up_visual_element,json=popUpVisualElement,proto3" json:"pop_up_visual_element,omitempty"`
}

func (x *GTMPopUpBanner_BodyLayoutParagraph) Reset() {
	*x = GTMPopUpBanner_BodyLayoutParagraph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GTMPopUpBanner_BodyLayoutParagraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GTMPopUpBanner_BodyLayoutParagraph) ProtoMessage() {}

func (x *GTMPopUpBanner_BodyLayoutParagraph) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GTMPopUpBanner_BodyLayoutParagraph.ProtoReflect.Descriptor instead.
func (*GTMPopUpBanner_BodyLayoutParagraph) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{28, 0}
}

func (x *GTMPopUpBanner_BodyLayoutParagraph) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GTMPopUpBanner_BodyLayoutParagraph) GetBodyContent() *common.Text {
	if x != nil {
		return x.BodyContent
	}
	return nil
}

func (x *GTMPopUpBanner_BodyLayoutParagraph) GetPopUpVisualElement() *common.VisualElement {
	if x != nil {
		return x.PopUpVisualElement
	}
	return nil
}

type GTMPopUpBanner_BodyLayoutBulletPoints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title        *common.Text                                               `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	BulletPoints []*GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint `protobuf:"bytes,2,rep,name=bullet_points,json=bulletPoints,proto3" json:"bullet_points,omitempty"`
	// Image/lotte to be shown on pop up
	PopUpVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=pop_up_visual_element,json=popUpVisualElement,proto3" json:"pop_up_visual_element,omitempty"`
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints) Reset() {
	*x = GTMPopUpBanner_BodyLayoutBulletPoints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GTMPopUpBanner_BodyLayoutBulletPoints) ProtoMessage() {}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GTMPopUpBanner_BodyLayoutBulletPoints.ProtoReflect.Descriptor instead.
func (*GTMPopUpBanner_BodyLayoutBulletPoints) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{28, 1}
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints) GetBulletPoints() []*GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint {
	if x != nil {
		return x.BulletPoints
	}
	return nil
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints) GetPopUpVisualElement() *common.VisualElement {
	if x != nil {
		return x.PopUpVisualElement
	}
	return nil
}

type GTMPopUpBanner_BodyLayoutFullLottie struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PopUpVisualElement *common.VisualElement `protobuf:"bytes,1,opt,name=pop_up_visual_element,json=popUpVisualElement,proto3" json:"pop_up_visual_element,omitempty"`
}

func (x *GTMPopUpBanner_BodyLayoutFullLottie) Reset() {
	*x = GTMPopUpBanner_BodyLayoutFullLottie{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GTMPopUpBanner_BodyLayoutFullLottie) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GTMPopUpBanner_BodyLayoutFullLottie) ProtoMessage() {}

func (x *GTMPopUpBanner_BodyLayoutFullLottie) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GTMPopUpBanner_BodyLayoutFullLottie.ProtoReflect.Descriptor instead.
func (*GTMPopUpBanner_BodyLayoutFullLottie) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{28, 2}
}

func (x *GTMPopUpBanner_BodyLayoutFullLottie) GetPopUpVisualElement() *common.VisualElement {
	if x != nil {
		return x.PopUpVisualElement
	}
	return nil
}

type GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image *common.Image `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Text  *common.Text  `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) Reset() {
	*x = GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) ProtoMessage() {}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint.ProtoReflect.Descriptor instead.
func (*GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{28, 1, 0}
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29100&mode=design&t=mkvx9Q4hMXzHNgwQ-1
type FeatureWidgetWithFourPoints_TextVisualElementCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopSection    *FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection    `protobuf:"bytes,1,opt,name=top_section,json=topSection,proto3" json:"top_section,omitempty"`
	MiddleSection *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection `protobuf:"bytes,2,opt,name=middle_section,json=middleSection,proto3" json:"middle_section,omitempty"`
	// cta below the middle section
	Cta      *ui.IconTextComponent `protobuf:"bytes,3,opt,name=cta,proto3" json:"cta,omitempty"`
	BgColour *ui.BackgroundColour  `protobuf:"bytes,4,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Shadow   *ui.Shadow            `protobuf:"bytes,5,opt,name=shadow,proto3" json:"shadow,omitempty"`
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) Reset() {
	*x = FeatureWidgetWithFourPoints_TextVisualElementCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithFourPoints_TextVisualElementCard) ProtoMessage() {}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithFourPoints_TextVisualElementCard.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithFourPoints_TextVisualElementCard) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{29, 0}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) GetTopSection() *FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection {
	if x != nil {
		return x.TopSection
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) GetMiddleSection() *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection {
	if x != nil {
		return x.MiddleSection
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7120%3A35948&mode=design&t=HYWSznO6nH0xMGNX-1
type FeatureWidgetWithFourPoints_FullVisualElementCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// visual element to be shown on the entire card
	VisualElement *common.VisualElement `protobuf:"bytes,1,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	// cta on top of the visual element
	Cta      *ui.IconTextComponent `protobuf:"bytes,2,opt,name=cta,proto3" json:"cta,omitempty"`
	BgColour *ui.BackgroundColour  `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Shadow   *ui.Shadow            `protobuf:"bytes,6,opt,name=shadow,proto3" json:"shadow,omitempty"`
}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) Reset() {
	*x = FeatureWidgetWithFourPoints_FullVisualElementCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithFourPoints_FullVisualElementCard) ProtoMessage() {}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithFourPoints_FullVisualElementCard.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithFourPoints_FullVisualElementCard) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{29, 1}
}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_FullVisualElementCard) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

type FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// image spanning the entire top section
	VisualElement *common.VisualElement `protobuf:"bytes,1,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) Reset() {
	*x = FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) ProtoMessage() {}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{29, 0, 0}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

type FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HighlightedPoints []*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint `protobuf:"bytes,1,rep,name=highlighted_points,json=highlightedPoints,proto3" json:"highlighted_points,omitempty"`
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) Reset() {
	*x = FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) ProtoMessage() {}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{29, 0, 1}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection) GetHighlightedPoints() []*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint {
	if x != nil {
		return x.HighlightedPoints
	}
	return nil
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29100&mode=design&t=TNslpsfJ95lnpl7E-1
type FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftIcon *common.VisualElement `protobuf:"bytes,1,opt,name=left_icon,json=leftIcon,proto3" json:"left_icon,omitempty"`
	PreText  *common.Text          `protobuf:"bytes,2,opt,name=pre_text,json=preText,proto3" json:"pre_text,omitempty"`
	Text     *common.Text          `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) Reset() {
	*x = FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) ProtoMessage() {
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{29, 0, 1, 0}
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) GetLeftIcon() *common.VisualElement {
	if x != nil {
		return x.LeftIcon
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) GetPreText() *common.Text {
	if x != nil {
		return x.PreText
	}
	return nil
}

func (x *FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29002&mode=design&t=mkvx9Q4hMXzHNgwQ-1
type FeatureWidgetWithThreePoints_LeftVerticalFlyer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// visual element occupying entire flyer
	VisualElement *common.VisualElement `protobuf:"bytes,1,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	// cta on top of the visual element
	Cta      *ui.IconTextComponent `protobuf:"bytes,2,opt,name=cta,proto3" json:"cta,omitempty"`
	BgColour *ui.BackgroundColour  `protobuf:"bytes,3,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Shadow   *ui.Shadow            `protobuf:"bytes,4,opt,name=shadow,proto3" json:"shadow,omitempty"`
}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) Reset() {
	*x = FeatureWidgetWithThreePoints_LeftVerticalFlyer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithThreePoints_LeftVerticalFlyer) ProtoMessage() {}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithThreePoints_LeftVerticalFlyer.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithThreePoints_LeftVerticalFlyer) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{30, 0}
}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_LeftVerticalFlyer) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29002&mode=design&t=mkvx9Q4hMXzHNgwQ-1
type FeatureWidgetWithThreePoints_RightHorizontalFlyer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreText   *common.Text          `protobuf:"bytes,1,opt,name=pre_text,json=preText,proto3" json:"pre_text,omitempty"`
	Text      *common.Text          `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	RightIcon *common.VisualElement `protobuf:"bytes,3,opt,name=right_icon,json=rightIcon,proto3" json:"right_icon,omitempty"`
	Deeplink  *deeplink.Deeplink    `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	BgColour  *ui.BackgroundColour  `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Shadow    *ui.Shadow            `protobuf:"bytes,6,opt,name=shadow,proto3" json:"shadow,omitempty"`
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) Reset() {
	*x = FeatureWidgetWithThreePoints_RightHorizontalFlyer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithThreePoints_RightHorizontalFlyer) ProtoMessage() {}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithThreePoints_RightHorizontalFlyer.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithThreePoints_RightHorizontalFlyer) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{30, 1}
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) GetPreText() *common.Text {
	if x != nil {
		return x.PreText
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) GetRightIcon() *common.VisualElement {
	if x != nil {
		return x.RightIcon
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *FeatureWidgetWithThreePoints_RightHorizontalFlyer) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29103&mode=design&t=mkvx9Q4hMXzHNgwQ-1
type FeatureWidgetWithTwoPoints_TopHorizontalFlyer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pre heading can be nil
	PreHeading *common.Text `protobuf:"bytes,1,opt,name=pre_heading,json=preHeading,proto3" json:"pre_heading,omitempty"`
	// can either be text or image
	//
	// Types that are assignable to Heading:
	//
	//	*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text
	//	*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image
	Heading isFeatureWidgetWithTwoPoints_TopHorizontalFlyer_Heading `protobuf_oneof:"heading"`
	// image next to the heading
	VisualElement *common.VisualElement `protobuf:"bytes,4,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	// cta below the heading
	Cta      *ui.IconTextComponent `protobuf:"bytes,5,opt,name=cta,proto3" json:"cta,omitempty"`
	BgColour *ui.BackgroundColour  `protobuf:"bytes,6,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Shadow   *ui.Shadow            `protobuf:"bytes,7,opt,name=shadow,proto3" json:"shadow,omitempty"`
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) Reset() {
	*x = FeatureWidgetWithTwoPoints_TopHorizontalFlyer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithTwoPoints_TopHorizontalFlyer) ProtoMessage() {}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithTwoPoints_TopHorizontalFlyer.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithTwoPoints_TopHorizontalFlyer) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{31, 0}
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetPreHeading() *common.Text {
	if x != nil {
		return x.PreHeading
	}
	return nil
}

func (m *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetHeading() isFeatureWidgetWithTwoPoints_TopHorizontalFlyer_Heading {
	if m != nil {
		return m.Heading
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetText() *common.Text {
	if x, ok := x.GetHeading().(*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text); ok {
		return x.Text
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetImage() *common.VisualElement {
	if x, ok := x.GetHeading().(*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image); ok {
		return x.Image
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_TopHorizontalFlyer) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

type isFeatureWidgetWithTwoPoints_TopHorizontalFlyer_Heading interface {
	isFeatureWidgetWithTwoPoints_TopHorizontalFlyer_Heading()
}

type FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text struct {
	Text *common.Text `protobuf:"bytes,2,opt,name=text,proto3,oneof"`
}

type FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image struct {
	Image *common.VisualElement `protobuf:"bytes,3,opt,name=image,proto3,oneof"`
}

func (*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text) isFeatureWidgetWithTwoPoints_TopHorizontalFlyer_Heading() {
}

func (*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image) isFeatureWidgetWithTwoPoints_TopHorizontalFlyer_Heading() {
}

// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29103&mode=design&t=mkvx9Q4hMXzHNgwQ-1
type FeatureWidgetWithTwoPoints_BottomHorizontalFlyer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreText   *common.Text          `protobuf:"bytes,1,opt,name=pre_text,json=preText,proto3" json:"pre_text,omitempty"`
	Text      *common.Text          `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	RightIcon *common.VisualElement `protobuf:"bytes,3,opt,name=right_icon,json=rightIcon,proto3" json:"right_icon,omitempty"`
	Deeplink  *deeplink.Deeplink    `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	BgColour  *ui.BackgroundColour  `protobuf:"bytes,5,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	Shadow    *ui.Shadow            `protobuf:"bytes,6,opt,name=shadow,proto3" json:"shadow,omitempty"`
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) Reset() {
	*x = FeatureWidgetWithTwoPoints_BottomHorizontalFlyer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) ProtoMessage() {}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureWidgetWithTwoPoints_BottomHorizontalFlyer.ProtoReflect.Descriptor instead.
func (*FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{31, 1}
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) GetPreText() *common.Text {
	if x != nil {
		return x.PreText
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) GetRightIcon() *common.VisualElement {
	if x != nil {
		return x.RightIcon
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) GetBgColour() *ui.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *FeatureWidgetWithTwoPoints_BottomHorizontalFlyer) GetShadow() *ui.Shadow {
	if x != nil {
		return x.Shadow
	}
	return nil
}

// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=34292-15842&t=bOkq4gEXz4iUysyg-1
type ProgressBarCardContent_SubtitleContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subtitle can contain a mix of images and text, e.g., `<Fi coin icon> 3000 / <Fi coin icon> 5000`.
	// If a text element within the subtitle requires a gradient color, backend should send a single Text in the `subtitle` field,
	// and the `text_colour` that client should apply to the Text.
	Subtitle *ui.IconTextComponent `protobuf:"bytes,1,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// optional, should be sent only if a gradient color needs to be applied to the subtitle text.
	TextColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=text_colour,json=textColour,proto3" json:"text_colour,omitempty"`
}

func (x *ProgressBarCardContent_SubtitleContent) Reset() {
	*x = ProgressBarCardContent_SubtitleContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressBarCardContent_SubtitleContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressBarCardContent_SubtitleContent) ProtoMessage() {}

func (x *ProgressBarCardContent_SubtitleContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_dynamic_elements_dynamic_elements_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressBarCardContent_SubtitleContent.ProtoReflect.Descriptor instead.
func (*ProgressBarCardContent_SubtitleContent) Descriptor() ([]byte, []int) {
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP(), []int{32, 0}
}

func (x *ProgressBarCardContent_SubtitleContent) GetSubtitle() *ui.IconTextComponent {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *ProgressBarCardContent_SubtitleContent) GetTextColour() *widget.BackgroundColour {
	if x != nil {
		return x.TextColour
	}
	return nil
}

var File_api_dynamic_elements_dynamic_elements_proto protoreflect.FileDescriptor

var file_api_dynamic_elements_dynamic_elements_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x1a,
	0x20, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x75, 0x69, 0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x2f, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x01, 0x0a, 0x1b, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0d,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x8a, 0x01,
	0x0a, 0x1c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x1d, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x22, 0x45, 0x0a, 0x1e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc1, 0x06, 0x0a, 0x0d,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3a, 0x0a,
	0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0a, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x0c, 0x66, 0x61, 0x71,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x46, 0x41, 0x51, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x61, 0x71, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x49, 0x0a, 0x09, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x08, 0x68, 0x6f, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x69, 0x0a, 0x13,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x62, 0x0a, 0x14, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x65, 0x72, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x81, 0x01, 0x0a, 0x23,
	0x64, 0x63, 0x5f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x63, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x1f,
	0x64, 0x63, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x81, 0x01, 0x0a, 0x23, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x2e, 0x50, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x1f, 0x70, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x18, 0x0a, 0x16, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22,
	0x93, 0x02, 0x0a, 0x1f, 0x44, 0x63, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x53, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x63, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a,
	0x27, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x43, 0x5f, 0x44, 0x41, 0x53, 0x48,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x43, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10,
	0x02, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x43, 0x5f,
	0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x47, 0x54, 0x4d, 0x5f, 0x50, 0x4f,
	0x50, 0x55, 0x50, 0x10, 0x03, 0x22, 0xc7, 0x01, 0x0a, 0x1f, 0x50, 0x6f, 0x73, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x53, 0x0a, 0x07, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4f,
	0x0a, 0x07, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f,
	0x53, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x01, 0x22,
	0x43, 0x0a, 0x1c, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x67, 0x0a, 0x1f, 0x46, 0x41, 0x51, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb5, 0x03,
	0x0a, 0x18, 0x48, 0x6f, 0x6d, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x07, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x48,
	0x6f, 0x6d, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x48, 0x6f, 0x6d,
	0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc8, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x01,
	0x12, 0x10, 0x0a, 0x0c, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x44, 0x59,
	0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f,
	0x44, 0x59, 0x32, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x47, 0x54, 0x4d, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17,
	0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43,
	0x4f, 0x4e, 0x44, 0x41, 0x52, 0x59, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x42, 0x42, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x07, 0x22, 0x32, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x13,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x56, 0x32, 0x10, 0x01, 0x22, 0xe7, 0x01, 0x0a, 0x25, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x59, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3f, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x07, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21,
	0x0a, 0x1d, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10,
	0x01, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56,
	0x45, 0x53, 0x54, 0x5f, 0x47, 0x54, 0x4d, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x02, 0x22,
	0x95, 0x04, 0x0a, 0x0e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0c, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x47, 0x0a, 0x0c, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x75,
	0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x64, 0x0a, 0x12, 0x62, 0x69, 0x7a, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x42, 0x69, 0x7a, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x62, 0x69, 0x7a, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x1a, 0x43, 0x0a, 0x15, 0x42, 0x69, 0x7a, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8c, 0x09, 0x0a, 0x0e, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x0c,
	0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65,
	0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x12, 0x3e,
	0x0a, 0x06, 0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f, 0x70, 0x55, 0x70, 0x12, 0x47,
	0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x76, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x32, 0x48, 0x00, 0x52, 0x08, 0x62,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x56, 0x32, 0x12, 0x5f, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x6f, 0x6c,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x10, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x74, 0x6d, 0x5f,
	0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x47, 0x54, 0x4d, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x67, 0x74, 0x6d, 0x50, 0x6f, 0x70, 0x55,
	0x70, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x75, 0x0a, 0x1f, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x66,
	0x6f, 0x75, 0x72, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x48,
	0x00, 0x52, 0x1b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x78,
	0x0a, 0x20, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x48, 0x00, 0x52, 0x1c, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x72,
	0x65, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x72, 0x0a, 0x1e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f,
	0x74, 0x77, 0x6f, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x54, 0x77, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x48, 0x00,
	0x52, 0x1a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x54, 0x77, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x0b,
	0x74, 0x61, 0x62, 0x62, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x48,
	0x00, 0x52, 0x0a, 0x74, 0x61, 0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x12, 0x55, 0x0a,
	0x10, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x62, 0x61, 0x72, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61,
	0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x12,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x47, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x76, 0x33, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x33, 0x48,
	0x00, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x56, 0x33, 0x42, 0x09, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xd0, 0x0b, 0x0a, 0x0a, 0x54, 0x61,
	0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x54, 0x61, 0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x54, 0x61,
	0x62, 0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x12, 0x53, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x62, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x12, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x54, 0x61, 0x62, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x57, 0x0a, 0x17,
	0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x62, 0x5f, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x14, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x61, 0x62, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0x71, 0x0a, 0x03, 0x54, 0x61, 0x62, 0x12, 0x33, 0x0a, 0x03,
	0x74, 0x61, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x74, 0x61,
	0x62, 0x12, 0x35, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x54, 0x61, 0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0xb1, 0x08, 0x0a, 0x04, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x5d, 0x0a, 0x0f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61,
	0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x43, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x70, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x62, 0x62, 0x65, 0x64, 0x43, 0x61,
	0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x68, 0x69, 0x70, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x10, 0x04, 0x52, 0x05, 0x63, 0x68, 0x69, 0x70, 0x73, 0x12, 0x39, 0x0a,
	0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f,
	0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f,
	0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x1a, 0xd5, 0x01, 0x0a, 0x12, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x3d, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x12, 0x5b, 0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x61,
	0x62, 0x62, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x1a, 0x8e, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x3f, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x61, 0x72, 0x79, 0x1a, 0xb6, 0x02, 0x0a, 0x04, 0x43, 0x68, 0x69, 0x70, 0x12, 0x37, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x9f, 0x03, 0x0a,
	0x14, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12,
	0x1d, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x29,
	0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x08, 0x63, 0x74, 0x61,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x74, 0x61,
	0x52, 0x07, 0x63, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x0f,
	0x62, 0x6f, 0x64, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x6f, 0x64, 0x79, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xf9,
	0x08, 0x0a, 0x16, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x32, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x4b,
	0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x08, 0x63,
	0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x74, 0x61, 0x52, 0x07, 0x63, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x30, 0x0a, 0x07, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x07, 0x73,
	0x68, 0x61, 0x64, 0x6f, 0x77, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x12, 0x53, 0x0a, 0x13, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x5e, 0x0a, 0x1a, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x17, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x18, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x16, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x58, 0x0a, 0x17, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x15, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x97, 0x01, 0x0a, 0x24, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x32, 0x5f, 0x75, 0x69, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x48, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x56, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x32, 0x55, 0x69, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x6e, 0x74, 0x52, 0x1f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x32, 0x55, 0x69, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x6e, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x1f, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x32, 0x55, 0x69,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x30, 0x42, 0x41, 0x4e, 0x4e, 0x45,
	0x52, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x49, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x4e, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a,
	0x27, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x49, 0x5f, 0x56, 0x41,
	0x52, 0x49, 0x41, 0x4e, 0x54, 0x5f, 0x56, 0x32, 0x10, 0x01, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x54,
	0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x68, 0x6f,
	0x77, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0a, 0x74, 0x65, 0x78,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x22, 0xa0, 0x01, 0x0a, 0x19, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53,
	0x68, 0x65, 0x65, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x3e, 0x0a, 0x08, 0x63, 0x74, 0x61, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x74, 0x61, 0x52, 0x07,
	0x63, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xd1, 0x03, 0x0a, 0x13, 0x50, 0x6f, 0x70, 0x55,
	0x70, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x3e, 0x0a, 0x08, 0x63, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x74, 0x61, 0x52, 0x07, 0x63, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x73, 0x0a, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x94, 0x01, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b,
	0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x65, 0x78, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x65, 0x78, 0x74, 0x73, 0x22, 0xc2, 0x03, 0x0a, 0x11,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x74,
	0x61, 0x12, 0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x74, 0x61, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4d,
	0x0a, 0x0e, 0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x0c, 0x70, 0x6f, 0x70, 0x55, 0x70, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x74, 0x61, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x74, 0x61, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x5a, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x48, 0x4f, 0x57, 0x5f, 0x50, 0x4f, 0x50, 0x5f, 0x55, 0x50, 0x10, 0x01, 0x12, 0x12, 0x0a,
	0x0e, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x4d, 0x45, 0x10,
	0x02, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x4d, 0x49, 0x53,
	0x53, 0x45, 0x44, 0x10, 0x03, 0x42, 0x09, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x9c, 0x02, 0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x68, 0x0a, 0x15, 0x69, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x49, 0x6e, 0x41, 0x70, 0x70, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x41, 0x70,
	0x70, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x12, 0x47,
	0x0a, 0x08, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x08, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x76, 0x6b, 0x79, 0x63, 0x5f,
	0x6e, 0x75, 0x64, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x56,
	0x4b, 0x59, 0x43, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x09, 0x76, 0x6b, 0x79, 0x63, 0x4e,
	0x75, 0x64, 0x67, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22,
	0x46, 0x0a, 0x21, 0x49, 0x6e, 0x41, 0x70, 0x70, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x65, 0x64,
	0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x6d, 0x69,
	0x73, 0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44, 0x69,
	0x73, 0x6d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x49, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0x64, 0x0a, 0x18, 0x56, 0x4b, 0x59, 0x43, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x48,
	0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x80, 0x02, 0x0a, 0x1e, 0x53, 0x63, 0x72,
	0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x12, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x53,
	0x68, 0x61, 0x70, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x73, 0x63, 0x72,
	0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x4b,
	0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0x73, 0x0a, 0x0c, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x03, 0x63,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61,
	0x22, 0xf3, 0x03, 0x0a, 0x18, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x53, 0x68, 0x61, 0x70, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a,
	0x05, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x70,
	0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x68, 0x61, 0x70, 0x65, 0x52, 0x05,
	0x73, 0x68, 0x61, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x12, 0x3c, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f,
	0x77, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x6a, 0x0a, 0x05, 0x53, 0x68,
	0x61, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x48, 0x41, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x48,
	0x41, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x11, 0x0a,
	0x0d, 0x53, 0x48, 0x41, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x5f, 0x32, 0x10, 0x02,
	0x12, 0x11, 0x0a, 0x0d, 0x53, 0x48, 0x41, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x5f,
	0x33, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x48, 0x41, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x4d, 0x50, 0x5f, 0x34, 0x10, 0x04, 0x22, 0xd3, 0x0b, 0x0a, 0x0e, 0x47, 0x54, 0x4d, 0x50, 0x6f,
	0x70, 0x55, 0x70, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x6a, 0x0a, 0x15, 0x62, 0x6f, 0x64,
	0x79, 0x5f, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x47, 0x54, 0x4d, 0x50,
	0x6f, 0x70, 0x55, 0x70, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x4c,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x67, 0x72, 0x61, 0x70, 0x68, 0x48, 0x00,
	0x52, 0x13, 0x62, 0x6f, 0x64, 0x79, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x74, 0x0a, 0x19, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x6c, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x5f, 0x62, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x47, 0x54, 0x4d, 0x50,
	0x6f, 0x70, 0x55, 0x70, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x4c,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x48, 0x00, 0x52, 0x16, 0x62, 0x6f, 0x64, 0x79, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x42,
	0x75, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x6e, 0x0a, 0x17, 0x62,
	0x6f, 0x64, 0x79, 0x5f, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f,
	0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x47, 0x54, 0x4d, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x42,
	0x6f, 0x64, 0x79, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x4c, 0x6f, 0x74,
	0x74, 0x69, 0x65, 0x48, 0x00, 0x52, 0x14, 0x62, 0x6f, 0x64, 0x79, 0x4c, 0x61, 0x79, 0x6f, 0x75,
	0x74, 0x46, 0x75, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x37, 0x0a, 0x04, 0x63, 0x74, 0x61, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x74, 0x61, 0x52, 0x04, 0x63, 0x74, 0x61,
	0x73, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x4d, 0x0a, 0x11, 0x62, 0x67,
	0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x62, 0x67, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x12, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x12, 0x43, 0x0a, 0x1f, 0x64, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x5f, 0x6f, 0x6e, 0x5f,
	0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x70, 0x6f,
	0x70, 0x5f, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x64, 0x69, 0x73, 0x6d,
	0x69, 0x73, 0x73, 0x4f, 0x6e, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x73, 0x69, 0x64,
	0x65, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x1a, 0xd8, 0x01, 0x0a, 0x13, 0x42, 0x6f, 0x64, 0x79, 0x4c,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x67, 0x72, 0x61, 0x70, 0x68, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3b,
	0x0a, 0x0c, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b,
	0x62, 0x6f, 0x64, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x15, 0x70,
	0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x70,
	0x6f, 0x70, 0x55, 0x70, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x1a, 0x82, 0x03, 0x0a, 0x16, 0x42, 0x6f, 0x64, 0x79, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x6e, 0x0a, 0x0d,
	0x62, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x47, 0x54, 0x4d, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x64, 0x79, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0c,
	0x62, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x54, 0x0a, 0x15,
	0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12,
	0x70, 0x6f, 0x70, 0x55, 0x70, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x1a, 0x72, 0x0a, 0x11, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x42, 0x75, 0x6c, 0x6c,
	0x65, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x6c, 0x0a, 0x14, 0x42, 0x6f, 0x64, 0x79, 0x4c, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x12, 0x54,
	0x0a, 0x15, 0x70, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x12, 0x70, 0x6f, 0x70, 0x55, 0x70, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x22, 0xbf, 0x0c, 0x0a,
	0x1b, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x7e, 0x0a, 0x18,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x48, 0x00, 0x52, 0x15, 0x74, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x7e, 0x0a, 0x18,
	0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x75,
	0x6c, 0x6c, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x48, 0x00, 0x52, 0x15, 0x66, 0x75, 0x6c, 0x6c, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x13,
	0x69, 0x73, 0x5f, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x5f, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x43, 0x61, 0x72,
	0x6f, 0x75, 0x73, 0x65, 0x6c, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x0c,
	0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a,
	0xdc, 0x06, 0x0a, 0x15, 0x54, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x6f, 0x0a, 0x0b, 0x74, 0x6f, 0x70,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x2e, 0x54, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x74, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x78, 0x0a, 0x0e, 0x6d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x51, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x09, 0x62, 0x67, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x64,
	0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x1a, 0x56, 0x0a, 0x0a, 0x54, 0x6f, 0x70, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x1a, 0xdb, 0x02, 0x0a, 0x0d, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x91, 0x01, 0x0a, 0x12, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74,
	0x65, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x62, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x46, 0x6f, 0x75, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x2e, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x11, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x1a, 0xb5, 0x01, 0x0a, 0x10, 0x48, 0x69, 0x67, 0x68, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x6c,
	0x65, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x08, 0x70,
	0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x70, 0x72, 0x65, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x85,
	0x02, 0x0a, 0x15, 0x46, 0x75, 0x6c, 0x6c, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x06,
	0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x42, 0x06, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x22, 0xfa,
	0x07, 0x0a, 0x1c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x72, 0x65, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12,
	0x70, 0x0a, 0x13, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x5f, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x54, 0x68, 0x72, 0x65, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x4c, 0x65, 0x66,
	0x74, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x52, 0x11,
	0x6c, 0x65, 0x66, 0x74, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65,
	0x72, 0x12, 0x7b, 0x0a, 0x17, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x43, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x72, 0x65, 0x65, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x2e, 0x52, 0x69, 0x67, 0x68, 0x74, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74,
	0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x52, 0x15, 0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x6f,
	0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x73, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x51,
	0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x1a, 0x81, 0x02, 0x0a, 0x11, 0x4c, 0x65, 0x66, 0x74, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63,
	0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x06, 0x73,
	0x68, 0x61, 0x64, 0x6f, 0x77, 0x1a, 0xe3, 0x02, 0x0a, 0x14, 0x52, 0x69, 0x67, 0x68, 0x74, 0x48,
	0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x12, 0x33,
	0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x70, 0x72, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x40, 0x0a, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3d, 0x0a, 0x09,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x64, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61,
	0x64, 0x6f, 0x77, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x22, 0xad, 0x09, 0x0a, 0x1a,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x54, 0x77, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x71, 0x0a, 0x14, 0x74, 0x6f,
	0x70, 0x5f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x79,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x54, 0x77, 0x6f,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x6f, 0x70, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f,
	0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x52, 0x12, 0x74, 0x6f, 0x70, 0x48, 0x6f,
	0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x12, 0x7c, 0x0a,
	0x18, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74,
	0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x79, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x77, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c,
	0x79, 0x65, 0x72, 0x52, 0x16, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x48, 0x6f, 0x72, 0x69, 0x7a,
	0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x62,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0xb3,
	0x03, 0x0a, 0x12, 0x54, 0x6f, 0x70, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c,
	0x46, 0x6c, 0x79, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2e, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x39, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x09, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x68, 0x61,
	0x64, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f,
	0x77, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x42, 0x09, 0x0a, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x1a, 0xe4, 0x02, 0x0a, 0x15, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x48,
	0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x6c, 0x79, 0x65, 0x72, 0x12, 0x33,
	0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x70, 0x72, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x40, 0x0a, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3d, 0x0a, 0x09,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x64, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61,
	0x64, 0x6f, 0x77, 0x52, 0x06, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x22, 0x9c, 0x05, 0x0a, 0x16,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x13, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73,
	0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42,
	0x61, 0x72, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x12,
	0x53, 0x0a, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x43, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x1a, 0xa1, 0x01, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x08, 0x73, 0x75,
	0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x0b, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0a,
	0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x22, 0x81, 0x04, 0x0a, 0x16, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x56, 0x33, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x51, 0x0a, 0x13,
	0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x6c, 0x65,
	0x66, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x53, 0x0a, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x52, 0x0a, 0x0c, 0x62, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x42, 0x5a,
	0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_dynamic_elements_dynamic_elements_proto_rawDescOnce sync.Once
	file_api_dynamic_elements_dynamic_elements_proto_rawDescData = file_api_dynamic_elements_dynamic_elements_proto_rawDesc
)

func file_api_dynamic_elements_dynamic_elements_proto_rawDescGZIP() []byte {
	file_api_dynamic_elements_dynamic_elements_proto_rawDescOnce.Do(func() {
		file_api_dynamic_elements_dynamic_elements_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_dynamic_elements_dynamic_elements_proto_rawDescData)
	})
	return file_api_dynamic_elements_dynamic_elements_proto_rawDescData
}

var file_api_dynamic_elements_dynamic_elements_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_dynamic_elements_dynamic_elements_proto_msgTypes = make([]protoimpl.MessageInfo, 55)
var file_api_dynamic_elements_dynamic_elements_proto_goTypes = []interface{}{
	(DcDashboardScreenAdditionalInfo_Section)(0),                            // 0: dynamic_elements.DcDashboardScreenAdditionalInfo.Section
	(PostPaymentScreenAdditionalInfo_Section)(0),                            // 1: dynamic_elements.PostPaymentScreenAdditionalInfo.Section
	(HomeScreenAdditionalInfo_Section)(0),                                   // 2: dynamic_elements.HomeScreenAdditionalInfo.Section
	(HomeScreenAdditionalInfo_Version)(0),                                   // 3: dynamic_elements.HomeScreenAdditionalInfo.Version
	(InvestmentLandingScreenAdditionalInfo_Section)(0),                      // 4: dynamic_elements.InvestmentLandingScreenAdditionalInfo.Section
	(BannerElementContentV2_BannerElementContentV2UiVariant)(0),             // 5: dynamic_elements.BannerElementContentV2.BannerElementContentV2UiVariant
	(DynamicElementCta_Type)(0),                                             // 6: dynamic_elements.DynamicElementCta.Type
	(BannerSingleShapeElement_Shape)(0),                                     // 7: dynamic_elements.BannerSingleShapeElement.Shape
	(*FetchDynamicElementsRequest)(nil),                                     // 8: dynamic_elements.FetchDynamicElementsRequest
	(*FetchDynamicElementsResponse)(nil),                                    // 9: dynamic_elements.FetchDynamicElementsResponse
	(*DynamicElementCallbackRequest)(nil),                                   // 10: dynamic_elements.DynamicElementCallbackRequest
	(*DynamicElementCallbackResponse)(nil),                                  // 11: dynamic_elements.DynamicElementCallbackResponse
	(*ClientContext)(nil),                                                   // 12: dynamic_elements.ClientContext
	(*DcDashboardScreenAdditionalInfo)(nil),                                 // 13: dynamic_elements.DcDashboardScreenAdditionalInfo
	(*PostPaymentScreenAdditionalInfo)(nil),                                 // 14: dynamic_elements.PostPaymentScreenAdditionalInfo
	(*AnalyserScreenAdditionalInfo)(nil),                                    // 15: dynamic_elements.AnalyserScreenAdditionalInfo
	(*FAQCategoryScreenAdditionalInfo)(nil),                                 // 16: dynamic_elements.FAQCategoryScreenAdditionalInfo
	(*HomeScreenAdditionalInfo)(nil),                                        // 17: dynamic_elements.HomeScreenAdditionalInfo
	(*InvestmentLandingScreenAdditionalInfo)(nil),                           // 18: dynamic_elements.InvestmentLandingScreenAdditionalInfo
	(*DynamicElement)(nil),                                                  // 19: dynamic_elements.DynamicElement
	(*ElementContent)(nil),                                                  // 20: dynamic_elements.ElementContent
	(*RedirectElementContent)(nil),                                          // 21: dynamic_elements.RedirectElementContent
	(*TabbedCard)(nil),                                                      // 22: dynamic_elements.TabbedCard
	(*BannerElementContent)(nil),                                            // 23: dynamic_elements.BannerElementContent
	(*BannerElementContentV2)(nil),                                          // 24: dynamic_elements.BannerElementContentV2
	(*TimeCounterParams)(nil),                                               // 25: dynamic_elements.TimeCounterParams
	(*BottomSheetElementContent)(nil),                                       // 26: dynamic_elements.BottomSheetElementContent
	(*PopUpElementContent)(nil),                                             // 27: dynamic_elements.PopUpElementContent
	(*DynamicElementCta)(nil),                                               // 28: dynamic_elements.DynamicElementCta
	(*CallbackPayload)(nil),                                                 // 29: dynamic_elements.CallbackPayload
	(*InAppTargetedCommsCallbackPayload)(nil),                               // 30: dynamic_elements.InAppTargetedCommsCallbackPayload
	(*InsightsCallbackPayload)(nil),                                         // 31: dynamic_elements.InsightsCallbackPayload
	(*VKYCNudgeCallbackPayload)(nil),                                        // 32: dynamic_elements.VKYCNudgeCallbackPayload
	(*ScrollableBannerElementContent)(nil),                                  // 33: dynamic_elements.ScrollableBannerElementContent
	(*BannerHeader)(nil),                                                    // 34: dynamic_elements.BannerHeader
	(*BannerSingleShapeElement)(nil),                                        // 35: dynamic_elements.BannerSingleShapeElement
	(*GTMPopUpBanner)(nil),                                                  // 36: dynamic_elements.GTMPopUpBanner
	(*FeatureWidgetWithFourPoints)(nil),                                     // 37: dynamic_elements.FeatureWidgetWithFourPoints
	(*FeatureWidgetWithThreePoints)(nil),                                    // 38: dynamic_elements.FeatureWidgetWithThreePoints
	(*FeatureWidgetWithTwoPoints)(nil),                                      // 39: dynamic_elements.FeatureWidgetWithTwoPoints
	(*ProgressBarCardContent)(nil),                                          // 40: dynamic_elements.ProgressBarCardContent
	(*BannerElementContentV3)(nil),                                          // 41: dynamic_elements.BannerElementContentV3
	nil,                                                                     // 42: dynamic_elements.DynamicElement.BizAnalyticsDataEntry
	(*TabbedCard_Tab)(nil),                                                  // 43: dynamic_elements.TabbedCard.Tab
	(*TabbedCard_Card)(nil),                                                 // 44: dynamic_elements.TabbedCard.Card
	(*TabbedCard_Card_CollectiveInfoView)(nil),                              // 45: dynamic_elements.TabbedCard.Card.CollectiveInfoView
	(*TabbedCard_Card_CollectiveInfo)(nil),                                  // 46: dynamic_elements.TabbedCard.Card.CollectiveInfo
	(*TabbedCard_Card_Chip)(nil),                                            // 47: dynamic_elements.TabbedCard.Card.Chip
	(*PopUpElementContent_AdditionalTextSection)(nil),                       // 48: dynamic_elements.PopUpElementContent.AdditionalTextSection
	(*GTMPopUpBanner_BodyLayoutParagraph)(nil),                              // 49: dynamic_elements.GTMPopUpBanner.BodyLayoutParagraph
	(*GTMPopUpBanner_BodyLayoutBulletPoints)(nil),                           // 50: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints
	(*GTMPopUpBanner_BodyLayoutFullLottie)(nil),                             // 51: dynamic_elements.GTMPopUpBanner.BodyLayoutFullLottie
	(*GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint)(nil),         // 52: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.SingleBulletPoint
	(*FeatureWidgetWithFourPoints_TextVisualElementCard)(nil),               // 53: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard
	(*FeatureWidgetWithFourPoints_FullVisualElementCard)(nil),               // 54: dynamic_elements.FeatureWidgetWithFourPoints.FullVisualElementCard
	(*FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection)(nil),    // 55: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.TopSection
	(*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection)(nil), // 56: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection
	(*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint)(nil), // 57: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection.HighlightedPoint
	(*FeatureWidgetWithThreePoints_LeftVerticalFlyer)(nil),                                   // 58: dynamic_elements.FeatureWidgetWithThreePoints.LeftVerticalFlyer
	(*FeatureWidgetWithThreePoints_RightHorizontalFlyer)(nil),                                // 59: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer
	(*FeatureWidgetWithTwoPoints_TopHorizontalFlyer)(nil),                                    // 60: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer
	(*FeatureWidgetWithTwoPoints_BottomHorizontalFlyer)(nil),                                 // 61: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer
	(*ProgressBarCardContent_SubtitleContent)(nil),                                           // 62: dynamic_elements.ProgressBarCardContent.SubtitleContent
	(*rpc.Status)(nil),                   // 63: rpc.Status
	(deeplink.Screen)(0),                 // 64: frontend.deeplink.Screen
	(common.Platform)(0),                 // 65: api.typesv2.common.Platform
	(typesv2.ServiceName)(0),             // 66: api.typesv2.ServiceName
	(ElementUtilityType)(0),              // 67: dynamic_elements.ElementUtilityType
	(ElementStructureType)(0),            // 68: dynamic_elements.ElementStructureType
	(*timestamppb.Timestamp)(nil),        // 69: google.protobuf.Timestamp
	(*deeplink.Deeplink)(nil),            // 70: frontend.deeplink.Deeplink
	(*ui.IconTextComponent)(nil),         // 71: api.typesv2.ui.IconTextComponent
	(*ui.BackgroundColour)(nil),          // 72: api.typesv2.ui.BackgroundColour
	(*common.VisualElement)(nil),         // 73: api.typesv2.common.VisualElement
	(*common.Text)(nil),                  // 74: api.typesv2.common.Text
	(*common.Image)(nil),                 // 75: api.typesv2.common.Image
	(*ui.Shadow)(nil),                    // 76: api.typesv2.ui.Shadow
	(*widget.BackgroundColour)(nil),      // 77: api.typesv2.common.ui.widget.BackgroundColour
	(*widget.Shadow)(nil),                // 78: api.typesv2.common.ui.widget.Shadow
	(*durationpb.Duration)(nil),          // 79: google.protobuf.Duration
	(*components.LinearProgressBar)(nil), // 80: api.typesv2.ui.sdui.components.LinearProgressBar
}
var file_api_dynamic_elements_dynamic_elements_proto_depIdxs = []int32{
	12,  // 0: dynamic_elements.FetchDynamicElementsRequest.client_context:type_name -> dynamic_elements.ClientContext
	63,  // 1: dynamic_elements.FetchDynamicElementsResponse.status:type_name -> rpc.Status
	19,  // 2: dynamic_elements.FetchDynamicElementsResponse.elements_list:type_name -> dynamic_elements.DynamicElement
	29,  // 3: dynamic_elements.DynamicElementCallbackRequest.callback_payload:type_name -> dynamic_elements.CallbackPayload
	63,  // 4: dynamic_elements.DynamicElementCallbackResponse.status:type_name -> rpc.Status
	64,  // 5: dynamic_elements.ClientContext.screen_name:type_name -> frontend.deeplink.Screen
	16,  // 6: dynamic_elements.ClientContext.faq_category:type_name -> dynamic_elements.FAQCategoryScreenAdditionalInfo
	17,  // 7: dynamic_elements.ClientContext.home_info:type_name -> dynamic_elements.HomeScreenAdditionalInfo
	18,  // 8: dynamic_elements.ClientContext.invest_landing_info:type_name -> dynamic_elements.InvestmentLandingScreenAdditionalInfo
	15,  // 9: dynamic_elements.ClientContext.analyser_screen_info:type_name -> dynamic_elements.AnalyserScreenAdditionalInfo
	13,  // 10: dynamic_elements.ClientContext.dc_dashboard_screen_additional_info:type_name -> dynamic_elements.DcDashboardScreenAdditionalInfo
	14,  // 11: dynamic_elements.ClientContext.post_payment_screen_additional_info:type_name -> dynamic_elements.PostPaymentScreenAdditionalInfo
	65,  // 12: dynamic_elements.ClientContext.app_platform:type_name -> api.typesv2.common.Platform
	0,   // 13: dynamic_elements.DcDashboardScreenAdditionalInfo.section:type_name -> dynamic_elements.DcDashboardScreenAdditionalInfo.Section
	1,   // 14: dynamic_elements.PostPaymentScreenAdditionalInfo.section:type_name -> dynamic_elements.PostPaymentScreenAdditionalInfo.Section
	2,   // 15: dynamic_elements.HomeScreenAdditionalInfo.section:type_name -> dynamic_elements.HomeScreenAdditionalInfo.Section
	3,   // 16: dynamic_elements.HomeScreenAdditionalInfo.version:type_name -> dynamic_elements.HomeScreenAdditionalInfo.Version
	4,   // 17: dynamic_elements.InvestmentLandingScreenAdditionalInfo.section:type_name -> dynamic_elements.InvestmentLandingScreenAdditionalInfo.Section
	66,  // 18: dynamic_elements.DynamicElement.owner_service:type_name -> api.typesv2.ServiceName
	67,  // 19: dynamic_elements.DynamicElement.utility_type:type_name -> dynamic_elements.ElementUtilityType
	68,  // 20: dynamic_elements.DynamicElement.structure_type:type_name -> dynamic_elements.ElementStructureType
	20,  // 21: dynamic_elements.DynamicElement.content:type_name -> dynamic_elements.ElementContent
	42,  // 22: dynamic_elements.DynamicElement.biz_analytics_data:type_name -> dynamic_elements.DynamicElement.BizAnalyticsDataEntry
	69,  // 23: dynamic_elements.DynamicElement.end_time:type_name -> google.protobuf.Timestamp
	23,  // 24: dynamic_elements.ElementContent.banner:type_name -> dynamic_elements.BannerElementContent
	26,  // 25: dynamic_elements.ElementContent.bottom_sheet:type_name -> dynamic_elements.BottomSheetElementContent
	27,  // 26: dynamic_elements.ElementContent.pop_up:type_name -> dynamic_elements.PopUpElementContent
	24,  // 27: dynamic_elements.ElementContent.banner_v2:type_name -> dynamic_elements.BannerElementContentV2
	33,  // 28: dynamic_elements.ElementContent.scrollable_banner:type_name -> dynamic_elements.ScrollableBannerElementContent
	36,  // 29: dynamic_elements.ElementContent.gtm_pop_up_banner:type_name -> dynamic_elements.GTMPopUpBanner
	37,  // 30: dynamic_elements.ElementContent.feature_widget_with_four_points:type_name -> dynamic_elements.FeatureWidgetWithFourPoints
	38,  // 31: dynamic_elements.ElementContent.feature_widget_with_three_points:type_name -> dynamic_elements.FeatureWidgetWithThreePoints
	39,  // 32: dynamic_elements.ElementContent.feature_widget_with_two_points:type_name -> dynamic_elements.FeatureWidgetWithTwoPoints
	22,  // 33: dynamic_elements.ElementContent.tabbed_card:type_name -> dynamic_elements.TabbedCard
	21,  // 34: dynamic_elements.ElementContent.redirect_element:type_name -> dynamic_elements.RedirectElementContent
	40,  // 35: dynamic_elements.ElementContent.progress_bar_element:type_name -> dynamic_elements.ProgressBarCardContent
	41,  // 36: dynamic_elements.ElementContent.banner_v3:type_name -> dynamic_elements.BannerElementContentV3
	70,  // 37: dynamic_elements.RedirectElementContent.deeplink:type_name -> frontend.deeplink.Deeplink
	71,  // 38: dynamic_elements.TabbedCard.title:type_name -> api.typesv2.ui.IconTextComponent
	43,  // 39: dynamic_elements.TabbedCard.tabs:type_name -> dynamic_elements.TabbedCard.Tab
	72,  // 40: dynamic_elements.TabbedCard.selected_tab_bg_color:type_name -> api.typesv2.ui.BackgroundColour
	72,  // 41: dynamic_elements.TabbedCard.unselected_tab_bg_color:type_name -> api.typesv2.ui.BackgroundColour
	28,  // 42: dynamic_elements.BannerElementContent.cta_list:type_name -> dynamic_elements.DynamicElementCta
	70,  // 43: dynamic_elements.BannerElementContent.deeplink:type_name -> frontend.deeplink.Deeplink
	73,  // 44: dynamic_elements.BannerElementContent.visual_element:type_name -> api.typesv2.common.VisualElement
	74,  // 45: dynamic_elements.BannerElementContentV2.title:type_name -> api.typesv2.common.Text
	75,  // 46: dynamic_elements.BannerElementContentV2.image:type_name -> api.typesv2.common.Image
	72,  // 47: dynamic_elements.BannerElementContentV2.background_color:type_name -> api.typesv2.ui.BackgroundColour
	28,  // 48: dynamic_elements.BannerElementContentV2.cta_list:type_name -> dynamic_elements.DynamicElementCta
	70,  // 49: dynamic_elements.BannerElementContentV2.deeplink:type_name -> frontend.deeplink.Deeplink
	76,  // 50: dynamic_elements.BannerElementContentV2.shadows:type_name -> api.typesv2.ui.Shadow
	74,  // 51: dynamic_elements.BannerElementContentV2.body:type_name -> api.typesv2.common.Text
	25,  // 52: dynamic_elements.BannerElementContentV2.time_counter_params:type_name -> dynamic_elements.TimeCounterParams
	73,  // 53: dynamic_elements.BannerElementContentV2.visual_element:type_name -> api.typesv2.common.VisualElement
	73,  // 54: dynamic_elements.BannerElementContentV2.visual_element_full_banner:type_name -> api.typesv2.common.VisualElement
	72,  // 55: dynamic_elements.BannerElementContentV2.indicator_selected_color:type_name -> api.typesv2.ui.BackgroundColour
	72,  // 56: dynamic_elements.BannerElementContentV2.indicator_default_color:type_name -> api.typesv2.ui.BackgroundColour
	5,   // 57: dynamic_elements.BannerElementContentV2.banner_element_content_v2_ui_variant:type_name -> dynamic_elements.BannerElementContentV2.BannerElementContentV2UiVariant
	74,  // 58: dynamic_elements.TimeCounterParams.text_params:type_name -> api.typesv2.common.Text
	28,  // 59: dynamic_elements.BottomSheetElementContent.cta_list:type_name -> dynamic_elements.DynamicElementCta
	28,  // 60: dynamic_elements.PopUpElementContent.cta_list:type_name -> dynamic_elements.DynamicElementCta
	48,  // 61: dynamic_elements.PopUpElementContent.additional_text_section:type_name -> dynamic_elements.PopUpElementContent.AdditionalTextSection
	6,   // 62: dynamic_elements.DynamicElementCta.type:type_name -> dynamic_elements.DynamicElementCta.Type
	27,  // 63: dynamic_elements.DynamicElementCta.pop_up_options:type_name -> dynamic_elements.PopUpElementContent
	70,  // 64: dynamic_elements.DynamicElementCta.deeplink:type_name -> frontend.deeplink.Deeplink
	30,  // 65: dynamic_elements.CallbackPayload.in_app_targeted_comms:type_name -> dynamic_elements.InAppTargetedCommsCallbackPayload
	31,  // 66: dynamic_elements.CallbackPayload.insights:type_name -> dynamic_elements.InsightsCallbackPayload
	32,  // 67: dynamic_elements.CallbackPayload.vkyc_nudge:type_name -> dynamic_elements.VKYCNudgeCallbackPayload
	69,  // 68: dynamic_elements.VKYCNudgeCallbackPayload.last_callback_time:type_name -> google.protobuf.Timestamp
	34,  // 69: dynamic_elements.ScrollableBannerElementContent.header:type_name -> dynamic_elements.BannerHeader
	35,  // 70: dynamic_elements.ScrollableBannerElementContent.scrolling_elements:type_name -> dynamic_elements.BannerSingleShapeElement
	77,  // 71: dynamic_elements.ScrollableBannerElementContent.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	74,  // 72: dynamic_elements.BannerHeader.title:type_name -> api.typesv2.common.Text
	71,  // 73: dynamic_elements.BannerHeader.cta:type_name -> api.typesv2.ui.IconTextComponent
	7,   // 74: dynamic_elements.BannerSingleShapeElement.shape:type_name -> dynamic_elements.BannerSingleShapeElement.Shape
	75,  // 75: dynamic_elements.BannerSingleShapeElement.image:type_name -> api.typesv2.common.Image
	74,  // 76: dynamic_elements.BannerSingleShapeElement.title:type_name -> api.typesv2.common.Text
	77,  // 77: dynamic_elements.BannerSingleShapeElement.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	78,  // 78: dynamic_elements.BannerSingleShapeElement.shadow:type_name -> api.typesv2.common.ui.widget.Shadow
	70,  // 79: dynamic_elements.BannerSingleShapeElement.deeplink:type_name -> frontend.deeplink.Deeplink
	49,  // 80: dynamic_elements.GTMPopUpBanner.body_layout_paragraph:type_name -> dynamic_elements.GTMPopUpBanner.BodyLayoutParagraph
	50,  // 81: dynamic_elements.GTMPopUpBanner.body_layout_bullet_points:type_name -> dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints
	51,  // 82: dynamic_elements.GTMPopUpBanner.body_layout_full_lottie:type_name -> dynamic_elements.GTMPopUpBanner.BodyLayoutFullLottie
	77,  // 83: dynamic_elements.GTMPopUpBanner.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	28,  // 84: dynamic_elements.GTMPopUpBanner.ctas:type_name -> dynamic_elements.DynamicElementCta
	70,  // 85: dynamic_elements.GTMPopUpBanner.deeplink:type_name -> frontend.deeplink.Deeplink
	73,  // 86: dynamic_elements.GTMPopUpBanner.bg_visual_element:type_name -> api.typesv2.common.VisualElement
	79,  // 87: dynamic_elements.GTMPopUpBanner.start_pop_up_after:type_name -> google.protobuf.Duration
	53,  // 88: dynamic_elements.FeatureWidgetWithFourPoints.text_visual_element_card:type_name -> dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard
	54,  // 89: dynamic_elements.FeatureWidgetWithFourPoints.full_visual_element_card:type_name -> dynamic_elements.FeatureWidgetWithFourPoints.FullVisualElementCard
	74,  // 90: dynamic_elements.FeatureWidgetWithFourPoints.title:type_name -> api.typesv2.common.Text
	77,  // 91: dynamic_elements.FeatureWidgetWithFourPoints.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	58,  // 92: dynamic_elements.FeatureWidgetWithThreePoints.left_vertical_flyer:type_name -> dynamic_elements.FeatureWidgetWithThreePoints.LeftVerticalFlyer
	59,  // 93: dynamic_elements.FeatureWidgetWithThreePoints.right_horizontal_flyers:type_name -> dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer
	74,  // 94: dynamic_elements.FeatureWidgetWithThreePoints.title:type_name -> api.typesv2.common.Text
	77,  // 95: dynamic_elements.FeatureWidgetWithThreePoints.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	60,  // 96: dynamic_elements.FeatureWidgetWithTwoPoints.top_horizontal_flyer:type_name -> dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer
	61,  // 97: dynamic_elements.FeatureWidgetWithTwoPoints.bottom_horizontal_flyers:type_name -> dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer
	74,  // 98: dynamic_elements.FeatureWidgetWithTwoPoints.title:type_name -> api.typesv2.common.Text
	77,  // 99: dynamic_elements.FeatureWidgetWithTwoPoints.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	74,  // 100: dynamic_elements.ProgressBarCardContent.title:type_name -> api.typesv2.common.Text
	73,  // 101: dynamic_elements.ProgressBarCardContent.left_visual_element:type_name -> api.typesv2.common.VisualElement
	80,  // 102: dynamic_elements.ProgressBarCardContent.progress_bar:type_name -> api.typesv2.ui.sdui.components.LinearProgressBar
	73,  // 103: dynamic_elements.ProgressBarCardContent.right_visual_element:type_name -> api.typesv2.common.VisualElement
	62,  // 104: dynamic_elements.ProgressBarCardContent.subtitle_content:type_name -> dynamic_elements.ProgressBarCardContent.SubtitleContent
	77,  // 105: dynamic_elements.ProgressBarCardContent.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	74,  // 106: dynamic_elements.BannerElementContentV3.title:type_name -> api.typesv2.common.Text
	71,  // 107: dynamic_elements.BannerElementContentV3.body:type_name -> api.typesv2.ui.IconTextComponent
	73,  // 108: dynamic_elements.BannerElementContentV3.left_visual_element:type_name -> api.typesv2.common.VisualElement
	73,  // 109: dynamic_elements.BannerElementContentV3.right_visual_element:type_name -> api.typesv2.common.VisualElement
	77,  // 110: dynamic_elements.BannerElementContentV3.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	70,  // 111: dynamic_elements.BannerElementContentV3.deeplink:type_name -> frontend.deeplink.Deeplink
	77,  // 112: dynamic_elements.BannerElementContentV3.borderColour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	71,  // 113: dynamic_elements.TabbedCard.Tab.tab:type_name -> api.typesv2.ui.IconTextComponent
	44,  // 114: dynamic_elements.TabbedCard.Tab.card:type_name -> dynamic_elements.TabbedCard.Card
	45,  // 115: dynamic_elements.TabbedCard.Card.collective_info:type_name -> dynamic_elements.TabbedCard.Card.CollectiveInfoView
	71,  // 116: dynamic_elements.TabbedCard.Card.description:type_name -> api.typesv2.ui.IconTextComponent
	47,  // 117: dynamic_elements.TabbedCard.Card.chips:type_name -> dynamic_elements.TabbedCard.Card.Chip
	71,  // 118: dynamic_elements.TabbedCard.Card.footer:type_name -> api.typesv2.ui.IconTextComponent
	72,  // 119: dynamic_elements.TabbedCard.Card.bg_color:type_name -> api.typesv2.ui.BackgroundColour
	72,  // 120: dynamic_elements.TabbedCard.Card.CollectiveInfoView.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	46,  // 121: dynamic_elements.TabbedCard.Card.CollectiveInfoView.collective_infos:type_name -> dynamic_elements.TabbedCard.Card.CollectiveInfo
	71,  // 122: dynamic_elements.TabbedCard.Card.CollectiveInfo.primary:type_name -> api.typesv2.ui.IconTextComponent
	71,  // 123: dynamic_elements.TabbedCard.Card.CollectiveInfo.secondary:type_name -> api.typesv2.ui.IconTextComponent
	73,  // 124: dynamic_elements.TabbedCard.Card.Chip.image:type_name -> api.typesv2.common.VisualElement
	74,  // 125: dynamic_elements.TabbedCard.Card.Chip.title:type_name -> api.typesv2.common.Text
	71,  // 126: dynamic_elements.TabbedCard.Card.Chip.subtitle:type_name -> api.typesv2.ui.IconTextComponent
	70,  // 127: dynamic_elements.TabbedCard.Card.Chip.deeplink:type_name -> frontend.deeplink.Deeplink
	72,  // 128: dynamic_elements.PopUpElementContent.AdditionalTextSection.background_color:type_name -> api.typesv2.ui.BackgroundColour
	74,  // 129: dynamic_elements.PopUpElementContent.AdditionalTextSection.texts:type_name -> api.typesv2.common.Text
	74,  // 130: dynamic_elements.GTMPopUpBanner.BodyLayoutParagraph.title:type_name -> api.typesv2.common.Text
	74,  // 131: dynamic_elements.GTMPopUpBanner.BodyLayoutParagraph.body_content:type_name -> api.typesv2.common.Text
	73,  // 132: dynamic_elements.GTMPopUpBanner.BodyLayoutParagraph.pop_up_visual_element:type_name -> api.typesv2.common.VisualElement
	74,  // 133: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.title:type_name -> api.typesv2.common.Text
	52,  // 134: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.bullet_points:type_name -> dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.SingleBulletPoint
	73,  // 135: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.pop_up_visual_element:type_name -> api.typesv2.common.VisualElement
	73,  // 136: dynamic_elements.GTMPopUpBanner.BodyLayoutFullLottie.pop_up_visual_element:type_name -> api.typesv2.common.VisualElement
	75,  // 137: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.SingleBulletPoint.image:type_name -> api.typesv2.common.Image
	74,  // 138: dynamic_elements.GTMPopUpBanner.BodyLayoutBulletPoints.SingleBulletPoint.text:type_name -> api.typesv2.common.Text
	55,  // 139: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.top_section:type_name -> dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.TopSection
	56,  // 140: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.middle_section:type_name -> dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection
	71,  // 141: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.cta:type_name -> api.typesv2.ui.IconTextComponent
	72,  // 142: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	76,  // 143: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.shadow:type_name -> api.typesv2.ui.Shadow
	73,  // 144: dynamic_elements.FeatureWidgetWithFourPoints.FullVisualElementCard.visual_element:type_name -> api.typesv2.common.VisualElement
	71,  // 145: dynamic_elements.FeatureWidgetWithFourPoints.FullVisualElementCard.cta:type_name -> api.typesv2.ui.IconTextComponent
	72,  // 146: dynamic_elements.FeatureWidgetWithFourPoints.FullVisualElementCard.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	76,  // 147: dynamic_elements.FeatureWidgetWithFourPoints.FullVisualElementCard.shadow:type_name -> api.typesv2.ui.Shadow
	73,  // 148: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.TopSection.visual_element:type_name -> api.typesv2.common.VisualElement
	57,  // 149: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection.highlighted_points:type_name -> dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection.HighlightedPoint
	73,  // 150: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection.HighlightedPoint.left_icon:type_name -> api.typesv2.common.VisualElement
	74,  // 151: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection.HighlightedPoint.pre_text:type_name -> api.typesv2.common.Text
	74,  // 152: dynamic_elements.FeatureWidgetWithFourPoints.TextVisualElementCard.MiddleSection.HighlightedPoint.text:type_name -> api.typesv2.common.Text
	73,  // 153: dynamic_elements.FeatureWidgetWithThreePoints.LeftVerticalFlyer.visual_element:type_name -> api.typesv2.common.VisualElement
	71,  // 154: dynamic_elements.FeatureWidgetWithThreePoints.LeftVerticalFlyer.cta:type_name -> api.typesv2.ui.IconTextComponent
	72,  // 155: dynamic_elements.FeatureWidgetWithThreePoints.LeftVerticalFlyer.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	76,  // 156: dynamic_elements.FeatureWidgetWithThreePoints.LeftVerticalFlyer.shadow:type_name -> api.typesv2.ui.Shadow
	74,  // 157: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer.pre_text:type_name -> api.typesv2.common.Text
	74,  // 158: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer.text:type_name -> api.typesv2.common.Text
	73,  // 159: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer.right_icon:type_name -> api.typesv2.common.VisualElement
	70,  // 160: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer.deeplink:type_name -> frontend.deeplink.Deeplink
	72,  // 161: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	76,  // 162: dynamic_elements.FeatureWidgetWithThreePoints.RightHorizontalFlyer.shadow:type_name -> api.typesv2.ui.Shadow
	74,  // 163: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.pre_heading:type_name -> api.typesv2.common.Text
	74,  // 164: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.text:type_name -> api.typesv2.common.Text
	73,  // 165: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.image:type_name -> api.typesv2.common.VisualElement
	73,  // 166: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.visual_element:type_name -> api.typesv2.common.VisualElement
	71,  // 167: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.cta:type_name -> api.typesv2.ui.IconTextComponent
	72,  // 168: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	76,  // 169: dynamic_elements.FeatureWidgetWithTwoPoints.TopHorizontalFlyer.shadow:type_name -> api.typesv2.ui.Shadow
	74,  // 170: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer.pre_text:type_name -> api.typesv2.common.Text
	74,  // 171: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer.text:type_name -> api.typesv2.common.Text
	73,  // 172: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer.right_icon:type_name -> api.typesv2.common.VisualElement
	70,  // 173: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer.deeplink:type_name -> frontend.deeplink.Deeplink
	72,  // 174: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	76,  // 175: dynamic_elements.FeatureWidgetWithTwoPoints.BottomHorizontalFlyer.shadow:type_name -> api.typesv2.ui.Shadow
	71,  // 176: dynamic_elements.ProgressBarCardContent.SubtitleContent.subtitle:type_name -> api.typesv2.ui.IconTextComponent
	77,  // 177: dynamic_elements.ProgressBarCardContent.SubtitleContent.text_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	178, // [178:178] is the sub-list for method output_type
	178, // [178:178] is the sub-list for method input_type
	178, // [178:178] is the sub-list for extension type_name
	178, // [178:178] is the sub-list for extension extendee
	0,   // [0:178] is the sub-list for field type_name
}

func init() { file_api_dynamic_elements_dynamic_elements_proto_init() }
func file_api_dynamic_elements_dynamic_elements_proto_init() {
	if File_api_dynamic_elements_dynamic_elements_proto != nil {
		return
	}
	file_api_dynamic_elements_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDynamicElementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDynamicElementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicElementCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicElementCallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DcDashboardScreenAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostPaymentScreenAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalyserScreenAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FAQCategoryScreenAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HomeScreenAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentLandingScreenAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ElementContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedirectElementContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TabbedCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerElementContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerElementContentV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeCounterParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BottomSheetElementContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PopUpElementContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicElementCta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InAppTargetedCommsCallbackPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsightsCallbackPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VKYCNudgeCallbackPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScrollableBannerElementContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerSingleShapeElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GTMPopUpBanner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithFourPoints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithThreePoints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithTwoPoints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressBarCardContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerElementContentV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TabbedCard_Tab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TabbedCard_Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TabbedCard_Card_CollectiveInfoView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TabbedCard_Card_CollectiveInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TabbedCard_Card_Chip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PopUpElementContent_AdditionalTextSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GTMPopUpBanner_BodyLayoutParagraph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GTMPopUpBanner_BodyLayoutBulletPoints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GTMPopUpBanner_BodyLayoutFullLottie); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GTMPopUpBanner_BodyLayoutBulletPoints_SingleBulletPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithFourPoints_TextVisualElementCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithFourPoints_FullVisualElementCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithThreePoints_LeftVerticalFlyer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithThreePoints_RightHorizontalFlyer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithTwoPoints_TopHorizontalFlyer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureWidgetWithTwoPoints_BottomHorizontalFlyer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_dynamic_elements_dynamic_elements_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressBarCardContent_SubtitleContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ClientContext_FaqCategory)(nil),
		(*ClientContext_HomeInfo)(nil),
		(*ClientContext_InvestLandingInfo)(nil),
		(*ClientContext_AnalyserScreenInfo)(nil),
		(*ClientContext_DcDashboardScreenAdditionalInfo)(nil),
		(*ClientContext_PostPaymentScreenAdditionalInfo)(nil),
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*ElementContent_Banner)(nil),
		(*ElementContent_BottomSheet)(nil),
		(*ElementContent_PopUp)(nil),
		(*ElementContent_BannerV2)(nil),
		(*ElementContent_ScrollableBanner)(nil),
		(*ElementContent_GtmPopUpBanner)(nil),
		(*ElementContent_FeatureWidgetWithFourPoints)(nil),
		(*ElementContent_FeatureWidgetWithThreePoints)(nil),
		(*ElementContent_FeatureWidgetWithTwoPoints)(nil),
		(*ElementContent_TabbedCard)(nil),
		(*ElementContent_RedirectElement)(nil),
		(*ElementContent_ProgressBarElement)(nil),
		(*ElementContent_BannerV3)(nil),
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*DynamicElementCta_PopUpOptions)(nil),
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*CallbackPayload_InAppTargetedComms)(nil),
		(*CallbackPayload_Insights)(nil),
		(*CallbackPayload_VkycNudge)(nil),
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[28].OneofWrappers = []interface{}{
		(*GTMPopUpBanner_BodyLayoutParagraph_)(nil),
		(*GTMPopUpBanner_BodyLayoutBulletPoints_)(nil),
		(*GTMPopUpBanner_BodyLayoutFullLottie_)(nil),
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[29].OneofWrappers = []interface{}{
		(*FeatureWidgetWithFourPoints_TextVisualElementCard_)(nil),
		(*FeatureWidgetWithFourPoints_FullVisualElementCard_)(nil),
	}
	file_api_dynamic_elements_dynamic_elements_proto_msgTypes[52].OneofWrappers = []interface{}{
		(*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Text)(nil),
		(*FeatureWidgetWithTwoPoints_TopHorizontalFlyer_Image)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_dynamic_elements_dynamic_elements_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   55,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_dynamic_elements_dynamic_elements_proto_goTypes,
		DependencyIndexes: file_api_dynamic_elements_dynamic_elements_proto_depIdxs,
		EnumInfos:         file_api_dynamic_elements_dynamic_elements_proto_enumTypes,
		MessageInfos:      file_api_dynamic_elements_dynamic_elements_proto_msgTypes,
	}.Build()
	File_api_dynamic_elements_dynamic_elements_proto = out.File
	file_api_dynamic_elements_dynamic_elements_proto_rawDesc = nil
	file_api_dynamic_elements_dynamic_elements_proto_goTypes = nil
	file_api_dynamic_elements_dynamic_elements_proto_depIdxs = nil
}
