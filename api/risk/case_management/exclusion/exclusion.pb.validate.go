// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/exclusion/exclusion.proto

package exclusion

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExclusionRequestElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExclusionRequestElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExclusionRequestElement with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExclusionRequestElementMultiError, or nil if none found.
func (m *ExclusionRequestElement) ValidateAll() error {
	return m.validate(true)
}

func (m *ExclusionRequestElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Type.(type) {
	case *ExclusionRequestElement_AlertTypeParams:
		if v == nil {
			err := ExclusionRequestElementValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAlertTypeParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExclusionRequestElementValidationError{
						field:  "AlertTypeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExclusionRequestElementValidationError{
						field:  "AlertTypeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAlertTypeParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExclusionRequestElementValidationError{
					field:  "AlertTypeParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ExclusionRequestElement_ActorTypeParams:
		if v == nil {
			err := ExclusionRequestElementValidationError{
				field:  "Type",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorTypeParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExclusionRequestElementValidationError{
						field:  "ActorTypeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExclusionRequestElementValidationError{
						field:  "ActorTypeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorTypeParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExclusionRequestElementValidationError{
					field:  "ActorTypeParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ExclusionRequestElementMultiError(errors)
	}

	return nil
}

// ExclusionRequestElementMultiError is an error wrapping multiple validation
// errors returned by ExclusionRequestElement.ValidateAll() if the designated
// constraints aren't met.
type ExclusionRequestElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExclusionRequestElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExclusionRequestElementMultiError) AllErrors() []error { return m }

// ExclusionRequestElementValidationError is the validation error returned by
// ExclusionRequestElement.Validate if the designated constraints aren't met.
type ExclusionRequestElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExclusionRequestElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExclusionRequestElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExclusionRequestElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExclusionRequestElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExclusionRequestElementValidationError) ErrorName() string {
	return "ExclusionRequestElementValidationError"
}

// Error satisfies the builtin error interface
func (e ExclusionRequestElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExclusionRequestElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExclusionRequestElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExclusionRequestElementValidationError{}

// Validate checks the field values on AlertTypeParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AlertTypeParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AlertTypeParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AlertTypeParamsMultiError, or nil if none found.
func (m *AlertTypeParams) ValidateAll() error {
	return m.validate(true)
}

func (m *AlertTypeParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAlerts()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AlertTypeParamsValidationError{
					field:  "Alerts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AlertTypeParamsValidationError{
					field:  "Alerts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlerts()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AlertTypeParamsValidationError{
				field:  "Alerts",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AlertTypeParamsMultiError(errors)
	}

	return nil
}

// AlertTypeParamsMultiError is an error wrapping multiple validation errors
// returned by AlertTypeParams.ValidateAll() if the designated constraints
// aren't met.
type AlertTypeParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AlertTypeParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AlertTypeParamsMultiError) AllErrors() []error { return m }

// AlertTypeParamsValidationError is the validation error returned by
// AlertTypeParams.Validate if the designated constraints aren't met.
type AlertTypeParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AlertTypeParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AlertTypeParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AlertTypeParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AlertTypeParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AlertTypeParamsValidationError) ErrorName() string { return "AlertTypeParamsValidationError" }

// Error satisfies the builtin error interface
func (e AlertTypeParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAlertTypeParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AlertTypeParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AlertTypeParamsValidationError{}

// Validate checks the field values on ActorTypeParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActorTypeParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorTypeParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActorTypeParamsMultiError, or nil if none found.
func (m *ActorTypeParams) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorTypeParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ActorTypeParamsMultiError(errors)
	}

	return nil
}

// ActorTypeParamsMultiError is an error wrapping multiple validation errors
// returned by ActorTypeParams.ValidateAll() if the designated constraints
// aren't met.
type ActorTypeParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorTypeParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorTypeParamsMultiError) AllErrors() []error { return m }

// ActorTypeParamsValidationError is the validation error returned by
// ActorTypeParams.Validate if the designated constraints aren't met.
type ActorTypeParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorTypeParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorTypeParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorTypeParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorTypeParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorTypeParamsValidationError) ErrorName() string { return "ActorTypeParamsValidationError" }

// Error satisfies the builtin error interface
func (e ActorTypeParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorTypeParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorTypeParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorTypeParamsValidationError{}
