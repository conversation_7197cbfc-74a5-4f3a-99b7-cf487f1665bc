// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/onsurity/download_document_vendor.proto

package onsurity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DownloadDocumentsApiRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadDocumentsApiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadDocumentsApiRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadDocumentsApiRequestMultiError, or nil if none found.
func (m *DownloadDocumentsApiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadDocumentsApiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DownloadDocumentsApiRequestMultiError(errors)
	}

	return nil
}

// DownloadDocumentsApiRequestMultiError is an error wrapping multiple
// validation errors returned by DownloadDocumentsApiRequest.ValidateAll() if
// the designated constraints aren't met.
type DownloadDocumentsApiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadDocumentsApiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadDocumentsApiRequestMultiError) AllErrors() []error { return m }

// DownloadDocumentsApiRequestValidationError is the validation error returned
// by DownloadDocumentsApiRequest.Validate if the designated constraints
// aren't met.
type DownloadDocumentsApiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadDocumentsApiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadDocumentsApiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadDocumentsApiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadDocumentsApiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadDocumentsApiRequestValidationError) ErrorName() string {
	return "DownloadDocumentsApiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadDocumentsApiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadDocumentsApiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadDocumentsApiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadDocumentsApiRequestValidationError{}

// Validate checks the field values on DownloadDocumentsApiResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadDocumentsApiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadDocumentsApiResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadDocumentsApiResponseMultiError, or nil if none found.
func (m *DownloadDocumentsApiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadDocumentsApiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadDocumentsApiResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadDocumentsApiResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadDocumentsApiResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadDocumentsApiResponseValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadDocumentsApiResponseValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadDocumentsApiResponseValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DownloadDocumentsApiResponseMultiError(errors)
	}

	return nil
}

// DownloadDocumentsApiResponseMultiError is an error wrapping multiple
// validation errors returned by DownloadDocumentsApiResponse.ValidateAll() if
// the designated constraints aren't met.
type DownloadDocumentsApiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadDocumentsApiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadDocumentsApiResponseMultiError) AllErrors() []error { return m }

// DownloadDocumentsApiResponseValidationError is the validation error returned
// by DownloadDocumentsApiResponse.Validate if the designated constraints
// aren't met.
type DownloadDocumentsApiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadDocumentsApiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadDocumentsApiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadDocumentsApiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadDocumentsApiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadDocumentsApiResponseValidationError) ErrorName() string {
	return "DownloadDocumentsApiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadDocumentsApiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadDocumentsApiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadDocumentsApiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadDocumentsApiResponseValidationError{}

// Validate checks the field values on DownloadDocumentsApiResponse_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DownloadDocumentsApiResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadDocumentsApiResponse_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DownloadDocumentsApiResponse_DataMultiError, or nil if none found.
func (m *DownloadDocumentsApiResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadDocumentsApiResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileUrl

	if len(errors) > 0 {
		return DownloadDocumentsApiResponse_DataMultiError(errors)
	}

	return nil
}

// DownloadDocumentsApiResponse_DataMultiError is an error wrapping multiple
// validation errors returned by
// DownloadDocumentsApiResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type DownloadDocumentsApiResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadDocumentsApiResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadDocumentsApiResponse_DataMultiError) AllErrors() []error { return m }

// DownloadDocumentsApiResponse_DataValidationError is the validation error
// returned by DownloadDocumentsApiResponse_Data.Validate if the designated
// constraints aren't met.
type DownloadDocumentsApiResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadDocumentsApiResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadDocumentsApiResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadDocumentsApiResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadDocumentsApiResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadDocumentsApiResponse_DataValidationError) ErrorName() string {
	return "DownloadDocumentsApiResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadDocumentsApiResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadDocumentsApiResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadDocumentsApiResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadDocumentsApiResponse_DataValidationError{}

// Validate checks the field values on DownloadDocumentsApiResponse_Meta with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DownloadDocumentsApiResponse_Meta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadDocumentsApiResponse_Meta
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DownloadDocumentsApiResponse_MetaMultiError, or nil if none found.
func (m *DownloadDocumentsApiResponse_Meta) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadDocumentsApiResponse_Meta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsSuccess

	// no validation rules for StatusCode

	// no validation rules for ResponseMessage

	// no validation rules for DisplayMessage

	if len(errors) > 0 {
		return DownloadDocumentsApiResponse_MetaMultiError(errors)
	}

	return nil
}

// DownloadDocumentsApiResponse_MetaMultiError is an error wrapping multiple
// validation errors returned by
// DownloadDocumentsApiResponse_Meta.ValidateAll() if the designated
// constraints aren't met.
type DownloadDocumentsApiResponse_MetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadDocumentsApiResponse_MetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadDocumentsApiResponse_MetaMultiError) AllErrors() []error { return m }

// DownloadDocumentsApiResponse_MetaValidationError is the validation error
// returned by DownloadDocumentsApiResponse_Meta.Validate if the designated
// constraints aren't met.
type DownloadDocumentsApiResponse_MetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadDocumentsApiResponse_MetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadDocumentsApiResponse_MetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadDocumentsApiResponse_MetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadDocumentsApiResponse_MetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadDocumentsApiResponse_MetaValidationError) ErrorName() string {
	return "DownloadDocumentsApiResponse_MetaValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadDocumentsApiResponse_MetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadDocumentsApiResponse_Meta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadDocumentsApiResponse_MetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadDocumentsApiResponse_MetaValidationError{}
