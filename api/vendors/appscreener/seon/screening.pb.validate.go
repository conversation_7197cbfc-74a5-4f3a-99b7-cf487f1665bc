// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/seon/screening.proto

package seon

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FraudScoreRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreRequestMultiError, or nil if none found.
func (m *FraudScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreRequestValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for PhoneNumber

	if len(errors) > 0 {
		return FraudScoreRequestMultiError(errors)
	}

	return nil
}

// FraudScoreRequestMultiError is an error wrapping multiple validation errors
// returned by FraudScoreRequest.ValidateAll() if the designated constraints
// aren't met.
type FraudScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreRequestMultiError) AllErrors() []error { return m }

// FraudScoreRequestValidationError is the validation error returned by
// FraudScoreRequest.Validate if the designated constraints aren't met.
type FraudScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreRequestValidationError) ErrorName() string {
	return "FraudScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreRequestValidationError{}

// Validate checks the field values on FraudScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreResponseMultiError, or nil if none found.
func (m *FraudScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	if all {
		switch v := interface{}(m.GetError()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponseValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponseValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetError()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponseValidationError{
				field:  "Error",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FraudScoreResponseMultiError(errors)
	}

	return nil
}

// FraudScoreResponseMultiError is an error wrapping multiple validation errors
// returned by FraudScoreResponse.ValidateAll() if the designated constraints
// aren't met.
type FraudScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponseMultiError) AllErrors() []error { return m }

// FraudScoreResponseValidationError is the validation error returned by
// FraudScoreResponse.Validate if the designated constraints aren't met.
type FraudScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponseValidationError) ErrorName() string {
	return "FraudScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponseValidationError{}

// Validate checks the field values on FraudScoreRequest_Config with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreRequest_Config) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreRequest_Config with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreRequest_ConfigMultiError, or nil if none found.
func (m *FraudScoreRequest_Config) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreRequest_Config) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEmail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreRequest_ConfigValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreRequest_ConfigValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreRequest_ConfigValidationError{
				field:  "Email",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhone()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreRequest_ConfigValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreRequest_ConfigValidationError{
					field:  "Phone",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhone()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreRequest_ConfigValidationError{
				field:  "Phone",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IpApi

	// no validation rules for EmailApi

	// no validation rules for PhoneApi

	// no validation rules for DeviceFingerprinting

	if len(errors) > 0 {
		return FraudScoreRequest_ConfigMultiError(errors)
	}

	return nil
}

// FraudScoreRequest_ConfigMultiError is an error wrapping multiple validation
// errors returned by FraudScoreRequest_Config.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreRequest_ConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreRequest_ConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreRequest_ConfigMultiError) AllErrors() []error { return m }

// FraudScoreRequest_ConfigValidationError is the validation error returned by
// FraudScoreRequest_Config.Validate if the designated constraints aren't met.
type FraudScoreRequest_ConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreRequest_ConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreRequest_ConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreRequest_ConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreRequest_ConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreRequest_ConfigValidationError) ErrorName() string {
	return "FraudScoreRequest_ConfigValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreRequest_ConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreRequest_Config.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreRequest_ConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreRequest_ConfigValidationError{}

// Validate checks the field values on FraudScoreRequest_Config_Email with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreRequest_Config_Email) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreRequest_Config_Email with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FraudScoreRequest_Config_EmailMultiError, or nil if none found.
func (m *FraudScoreRequest_Config_Email) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreRequest_Config_Email) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Include

	// no validation rules for Version

	if len(errors) > 0 {
		return FraudScoreRequest_Config_EmailMultiError(errors)
	}

	return nil
}

// FraudScoreRequest_Config_EmailMultiError is an error wrapping multiple
// validation errors returned by FraudScoreRequest_Config_Email.ValidateAll()
// if the designated constraints aren't met.
type FraudScoreRequest_Config_EmailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreRequest_Config_EmailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreRequest_Config_EmailMultiError) AllErrors() []error { return m }

// FraudScoreRequest_Config_EmailValidationError is the validation error
// returned by FraudScoreRequest_Config_Email.Validate if the designated
// constraints aren't met.
type FraudScoreRequest_Config_EmailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreRequest_Config_EmailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreRequest_Config_EmailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreRequest_Config_EmailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreRequest_Config_EmailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreRequest_Config_EmailValidationError) ErrorName() string {
	return "FraudScoreRequest_Config_EmailValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreRequest_Config_EmailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreRequest_Config_Email.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreRequest_Config_EmailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreRequest_Config_EmailValidationError{}

// Validate checks the field values on FraudScoreRequest_Config_Phone with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreRequest_Config_Phone) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreRequest_Config_Phone with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FraudScoreRequest_Config_PhoneMultiError, or nil if none found.
func (m *FraudScoreRequest_Config_Phone) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreRequest_Config_Phone) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Include

	// no validation rules for Version

	if len(errors) > 0 {
		return FraudScoreRequest_Config_PhoneMultiError(errors)
	}

	return nil
}

// FraudScoreRequest_Config_PhoneMultiError is an error wrapping multiple
// validation errors returned by FraudScoreRequest_Config_Phone.ValidateAll()
// if the designated constraints aren't met.
type FraudScoreRequest_Config_PhoneMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreRequest_Config_PhoneMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreRequest_Config_PhoneMultiError) AllErrors() []error { return m }

// FraudScoreRequest_Config_PhoneValidationError is the validation error
// returned by FraudScoreRequest_Config_Phone.Validate if the designated
// constraints aren't met.
type FraudScoreRequest_Config_PhoneValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreRequest_Config_PhoneValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreRequest_Config_PhoneValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreRequest_Config_PhoneValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreRequest_Config_PhoneValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreRequest_Config_PhoneValidationError) ErrorName() string {
	return "FraudScoreRequest_Config_PhoneValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreRequest_Config_PhoneValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreRequest_Config_Phone.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreRequest_Config_PhoneValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreRequest_Config_PhoneValidationError{}

// Validate checks the field values on FraudScoreResponse_Error with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreResponse_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_ErrorMultiError, or nil if none found.
func (m *FraudScoreResponse_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	// no validation rules for Code

	if len(errors) > 0 {
		return FraudScoreResponse_ErrorMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_ErrorMultiError is an error wrapping multiple validation
// errors returned by FraudScoreResponse_Error.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_ErrorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_ErrorMultiError) AllErrors() []error { return m }

// FraudScoreResponse_ErrorValidationError is the validation error returned by
// FraudScoreResponse_Error.Validate if the designated constraints aren't met.
type FraudScoreResponse_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_ErrorValidationError) ErrorName() string {
	return "FraudScoreResponse_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_ErrorValidationError{}

// Validate checks the field values on FraudScoreResponse_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreResponse_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_DataMultiError, or nil if none found.
func (m *FraudScoreResponse_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FraudScore

	// no validation rules for Version

	for idx, item := range m.GetAppliedRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FraudScoreResponse_DataValidationError{
						field:  fmt.Sprintf("AppliedRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FraudScoreResponse_DataValidationError{
						field:  fmt.Sprintf("AppliedRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FraudScoreResponse_DataValidationError{
					field:  fmt.Sprintf("AppliedRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CalculationTime

	// no validation rules for SeonId

	if all {
		switch v := interface{}(m.GetEmailDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_DataValidationError{
					field:  "EmailDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_DataValidationError{
					field:  "EmailDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmailDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_DataValidationError{
				field:  "EmailDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_DataValidationError{
					field:  "PhoneDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_DataValidationError{
					field:  "PhoneDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_DataValidationError{
				field:  "PhoneDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FraudScoreResponse_DataMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_DataMultiError is an error wrapping multiple validation
// errors returned by FraudScoreResponse_Data.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_DataMultiError) AllErrors() []error { return m }

// FraudScoreResponse_DataValidationError is the validation error returned by
// FraudScoreResponse_Data.Validate if the designated constraints aren't met.
type FraudScoreResponse_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_DataValidationError) ErrorName() string {
	return "FraudScoreResponse_DataValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_DataValidationError{}

// Validate checks the field values on FraudScoreResponse_Data_AppliedRules
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FraudScoreResponse_Data_AppliedRules) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreResponse_Data_AppliedRules
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_Data_AppliedRulesMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_AppliedRules) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_AppliedRules) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Operation

	// no validation rules for Score

	if len(errors) > 0 {
		return FraudScoreResponse_Data_AppliedRulesMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_AppliedRulesMultiError is an error wrapping multiple
// validation errors returned by
// FraudScoreResponse_Data_AppliedRules.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_Data_AppliedRulesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_AppliedRulesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_AppliedRulesMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_AppliedRulesValidationError is the validation error
// returned by FraudScoreResponse_Data_AppliedRules.Validate if the designated
// constraints aren't met.
type FraudScoreResponse_Data_AppliedRulesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_AppliedRulesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_AppliedRulesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_AppliedRulesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_AppliedRulesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_AppliedRulesValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_AppliedRulesValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_AppliedRulesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_AppliedRules.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_AppliedRulesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_AppliedRulesValidationError{}

// Validate checks the field values on FraudScoreResponse_Data_EmailDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreResponse_Data_EmailDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetailsMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_EmailDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Score

	// no validation rules for Deliverable

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetailsValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBreachDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  "BreachDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  "BreachDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBreachDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetailsValidationError{
				field:  "BreachDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  "History",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  "History",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetailsValidationError{
				field:  "History",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFlags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
						field:  fmt.Sprintf("Flags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FraudScoreResponse_Data_EmailDetailsValidationError{
						field:  fmt.Sprintf("Flags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FraudScoreResponse_Data_EmailDetailsValidationError{
					field:  fmt.Sprintf("Flags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Id

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetailsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetailsMultiError is an error wrapping multiple
// validation errors returned by
// FraudScoreResponse_Data_EmailDetails.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_Data_EmailDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetailsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_EmailDetailsValidationError is the validation error
// returned by FraudScoreResponse_Data_EmailDetails.Validate if the designated
// constraints aren't met.
type FraudScoreResponse_Data_EmailDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetailsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetailsValidationError{}

// Validate checks the field values on FraudScoreResponse_Data_PhoneDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FraudScoreResponse_Data_PhoneDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FraudScoreResponse_Data_PhoneDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_Data_PhoneDetailsMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_PhoneDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_PhoneDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Number

	// no validation rules for Valid

	// no validation rules for Disposable

	// no validation rules for Type

	// no validation rules for Country

	// no validation rules for Carrier

	// no validation rules for Score

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetailsValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetailsValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetailsValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHistory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetailsValidationError{
					field:  "History",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetailsValidationError{
					field:  "History",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHistory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetailsValidationError{
				field:  "History",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFlags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FraudScoreResponse_Data_PhoneDetailsValidationError{
						field:  fmt.Sprintf("Flags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FraudScoreResponse_Data_PhoneDetailsValidationError{
						field:  fmt.Sprintf("Flags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FraudScoreResponse_Data_PhoneDetailsValidationError{
					field:  fmt.Sprintf("Flags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Id

	if len(errors) > 0 {
		return FraudScoreResponse_Data_PhoneDetailsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_PhoneDetailsMultiError is an error wrapping multiple
// validation errors returned by
// FraudScoreResponse_Data_PhoneDetails.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_Data_PhoneDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_PhoneDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_PhoneDetailsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_PhoneDetailsValidationError is the validation error
// returned by FraudScoreResponse_Data_PhoneDetails.Validate if the designated
// constraints aren't met.
type FraudScoreResponse_Data_PhoneDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_PhoneDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_PhoneDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_PhoneDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_PhoneDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_PhoneDetailsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_PhoneDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_PhoneDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_PhoneDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_PhoneDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_PhoneDetailsValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_EmailDetails_AccountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails_AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_EmailDetails_AccountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetails_AccountDetailsMultiError, or nil if
// none found.
func (m *FraudScoreResponse_Data_EmailDetails_AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails_AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFacebook()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Facebook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Facebook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacebook()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Facebook",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGoogle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Google",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Google",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoogle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Google",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetApple()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Apple",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Apple",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApple()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Apple",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTwitter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Twitter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Twitter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTwitter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Twitter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMicrosoft()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Microsoft",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Microsoft",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMicrosoft()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Microsoft",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetYahoo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Yahoo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Yahoo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetYahoo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Yahoo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstagram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Instagram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Instagram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstagram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Instagram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSpotify()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Spotify",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Spotify",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSpotify()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Spotify",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLinkedin()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Linkedin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Linkedin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinkedin()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Linkedin",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDiscord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Discord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Discord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Discord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSkype()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Skype",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
					field:  "Skype",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkype()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{
				field:  "Skype",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetails_AccountDetailsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetails_AccountDetailsMultiError is an error
// wrapping multiple validation errors returned by
// FraudScoreResponse_Data_EmailDetails_AccountDetails.ValidateAll() if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetails_AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetails_AccountDetailsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError is the
// validation error returned by
// FraudScoreResponse_Data_EmailDetails_AccountDetails.Validate if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails_AccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetails_AccountDetailsValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_EmailDetails_BreachDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails_BreachDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_EmailDetails_BreachDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetails_BreachDetailsMultiError, or nil if
// none found.
func (m *FraudScoreResponse_Data_EmailDetails_BreachDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails_BreachDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HaveibeenpwnedListed

	// no validation rules for NumberOfBreaches

	// no validation rules for FirstBreach

	for idx, item := range m.GetBreaches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError{
						field:  fmt.Sprintf("Breaches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError{
						field:  fmt.Sprintf("Breaches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError{
					field:  fmt.Sprintf("Breaches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetails_BreachDetailsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetails_BreachDetailsMultiError is an error
// wrapping multiple validation errors returned by
// FraudScoreResponse_Data_EmailDetails_BreachDetails.ValidateAll() if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_BreachDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetails_BreachDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetails_BreachDetailsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError is the
// validation error returned by
// FraudScoreResponse_Data_EmailDetails_BreachDetails.Validate if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails_BreachDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetails_BreachDetailsValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_EmailDetails_History with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails_History) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_EmailDetails_History with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetails_HistoryMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_EmailDetails_History) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails_History) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Hits

	// no validation rules for CustomerHits

	// no validation rules for FirstSeen

	// no validation rules for LastSeen

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetails_HistoryMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetails_HistoryMultiError is an error wrapping
// multiple validation errors returned by
// FraudScoreResponse_Data_EmailDetails_History.ValidateAll() if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_HistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetails_HistoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetails_HistoryMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_EmailDetails_HistoryValidationError is the
// validation error returned by
// FraudScoreResponse_Data_EmailDetails_History.Validate if the designated
// constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_HistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetails_HistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetails_HistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetails_HistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetails_HistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetails_HistoryValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetails_HistoryValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetails_HistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails_History.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetails_HistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetails_HistoryValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_EmailDetails_Flags with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails_Flags) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_EmailDetails_Flags with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetails_FlagsMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_EmailDetails_Flags) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails_Flags) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Note

	// no validation rules for Date

	// no validation rules for Industry

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetails_FlagsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetails_FlagsMultiError is an error wrapping
// multiple validation errors returned by
// FraudScoreResponse_Data_EmailDetails_Flags.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_FlagsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetails_FlagsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetails_FlagsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_EmailDetails_FlagsValidationError is the validation
// error returned by FraudScoreResponse_Data_EmailDetails_Flags.Validate if
// the designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_FlagsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetails_FlagsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetails_FlagsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetails_FlagsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetails_FlagsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetails_FlagsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetails_FlagsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetails_FlagsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails_Flags.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetails_FlagsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetails_FlagsValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_EmailDetails_AccountDetails_Account with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails_AccountDetails_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_EmailDetails_AccountDetails_Account with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountMultiError, or
// nil if none found.
func (m *FraudScoreResponse_Data_EmailDetails_AccountDetails_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails_AccountDetails_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Registered

	// no validation rules for Photo

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountMultiError is an
// error wrapping multiple validation errors returned by
// FraudScoreResponse_Data_EmailDetails_AccountDetails_Account.ValidateAll()
// if the designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountMultiError) AllErrors() []error {
	return m
}

// FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError
// is the validation error returned by
// FraudScoreResponse_Data_EmailDetails_AccountDetails_Account.Validate if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails_AccountDetails_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetails_AccountDetails_AccountValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesMultiError, or
// nil if none found.
func (m *FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Domain

	// no validation rules for Date

	if len(errors) > 0 {
		return FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesMultiError is an
// error wrapping multiple validation errors returned by
// FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches.ValidateAll()
// if the designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesMultiError) AllErrors() []error {
	return m
}

// FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError
// is the validation error returned by
// FraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches.Validate if the
// designated constraints aren't met.
type FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_EmailDetails_BreachDetails_Breaches.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_EmailDetails_BreachDetails_BreachesValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_PhoneDetails_AccountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_PhoneDetails_AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_PhoneDetails_AccountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// FraudScoreResponse_Data_PhoneDetails_AccountDetailsMultiError, or nil if
// none found.
func (m *FraudScoreResponse_Data_PhoneDetails_AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_PhoneDetails_AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFacebook()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Facebook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Facebook",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFacebook()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Facebook",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGoogle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Google",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Google",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGoogle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Google",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLine()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Line",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Line",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLine()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Line",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTwitter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Twitter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Twitter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTwitter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Twitter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMicrosoft()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Microsoft",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Microsoft",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMicrosoft()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Microsoft",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetYahoo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Yahoo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Yahoo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetYahoo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Yahoo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstagram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Instagram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Instagram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstagram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Instagram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTelegram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Telegram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Telegram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTelegram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Telegram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWhatsapp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Whatsapp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
					field:  "Whatsapp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWhatsapp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{
				field:  "Whatsapp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FraudScoreResponse_Data_PhoneDetails_AccountDetailsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_PhoneDetails_AccountDetailsMultiError is an error
// wrapping multiple validation errors returned by
// FraudScoreResponse_Data_PhoneDetails_AccountDetails.ValidateAll() if the
// designated constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_PhoneDetails_AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_PhoneDetails_AccountDetailsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError is the
// validation error returned by
// FraudScoreResponse_Data_PhoneDetails_AccountDetails.Validate if the
// designated constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_PhoneDetails_AccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_PhoneDetails_AccountDetailsValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_PhoneDetails_History with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_PhoneDetails_History) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_PhoneDetails_History with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FraudScoreResponse_Data_PhoneDetails_HistoryMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_PhoneDetails_History) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_PhoneDetails_History) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Hits

	// no validation rules for CustomerHits

	// no validation rules for FirstSeen

	// no validation rules for LastSeen

	if len(errors) > 0 {
		return FraudScoreResponse_Data_PhoneDetails_HistoryMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_PhoneDetails_HistoryMultiError is an error wrapping
// multiple validation errors returned by
// FraudScoreResponse_Data_PhoneDetails_History.ValidateAll() if the
// designated constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_HistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_PhoneDetails_HistoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_PhoneDetails_HistoryMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_PhoneDetails_HistoryValidationError is the
// validation error returned by
// FraudScoreResponse_Data_PhoneDetails_History.Validate if the designated
// constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_HistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_PhoneDetails_HistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_PhoneDetails_HistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_PhoneDetails_HistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_PhoneDetails_HistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_PhoneDetails_HistoryValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_PhoneDetails_HistoryValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_PhoneDetails_HistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_PhoneDetails_History.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_PhoneDetails_HistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_PhoneDetails_HistoryValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_PhoneDetails_Flags with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_PhoneDetails_Flags) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_PhoneDetails_Flags with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FraudScoreResponse_Data_PhoneDetails_FlagsMultiError, or nil if none found.
func (m *FraudScoreResponse_Data_PhoneDetails_Flags) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_PhoneDetails_Flags) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Note

	// no validation rules for Date

	// no validation rules for Industry

	if len(errors) > 0 {
		return FraudScoreResponse_Data_PhoneDetails_FlagsMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_PhoneDetails_FlagsMultiError is an error wrapping
// multiple validation errors returned by
// FraudScoreResponse_Data_PhoneDetails_Flags.ValidateAll() if the designated
// constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_FlagsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_PhoneDetails_FlagsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_PhoneDetails_FlagsMultiError) AllErrors() []error { return m }

// FraudScoreResponse_Data_PhoneDetails_FlagsValidationError is the validation
// error returned by FraudScoreResponse_Data_PhoneDetails_Flags.Validate if
// the designated constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_FlagsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_PhoneDetails_FlagsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FraudScoreResponse_Data_PhoneDetails_FlagsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FraudScoreResponse_Data_PhoneDetails_FlagsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FraudScoreResponse_Data_PhoneDetails_FlagsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FraudScoreResponse_Data_PhoneDetails_FlagsValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_PhoneDetails_FlagsValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_PhoneDetails_FlagsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_PhoneDetails_Flags.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_PhoneDetails_FlagsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_PhoneDetails_FlagsValidationError{}

// Validate checks the field values on
// FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountMultiError, or
// nil if none found.
func (m *FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Registered

	// no validation rules for Photo

	// no validation rules for LastSeen

	if len(errors) > 0 {
		return FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountMultiError(errors)
	}

	return nil
}

// FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountMultiError is an
// error wrapping multiple validation errors returned by
// FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account.ValidateAll()
// if the designated constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountMultiError) AllErrors() []error {
	return m
}

// FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError
// is the validation error returned by
// FraudScoreResponse_Data_PhoneDetails_AccountDetails_Account.Validate if the
// designated constraints aren't met.
type FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError) ErrorName() string {
	return "FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFraudScoreResponse_Data_PhoneDetails_AccountDetails_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FraudScoreResponse_Data_PhoneDetails_AccountDetails_AccountValidationError{}
