// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendors/inhouse/merchantnamecategoriser.proto

package inhouse

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MerchantNameCategoriser_MerchantNameCategoriser_FullMethodName = "/vendors.inhouse.MerchantNameCategoriser/MerchantNameCategoriser"
)

// MerchantNameCategoriserClient is the client API for MerchantNameCategoriser service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MerchantNameCategoriserClient interface {
	// inhouse merchant name categoriser API to check whether the passed name is a merchant or not
	MerchantNameCategoriser(ctx context.Context, in *MerchantNameCategoriserRequest, opts ...grpc.CallOption) (*MerchantNameCategoriserResponse, error)
}

type merchantNameCategoriserClient struct {
	cc grpc.ClientConnInterface
}

func NewMerchantNameCategoriserClient(cc grpc.ClientConnInterface) MerchantNameCategoriserClient {
	return &merchantNameCategoriserClient{cc}
}

func (c *merchantNameCategoriserClient) MerchantNameCategoriser(ctx context.Context, in *MerchantNameCategoriserRequest, opts ...grpc.CallOption) (*MerchantNameCategoriserResponse, error) {
	out := new(MerchantNameCategoriserResponse)
	err := c.cc.Invoke(ctx, MerchantNameCategoriser_MerchantNameCategoriser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MerchantNameCategoriserServer is the server API for MerchantNameCategoriser service.
// All implementations should embed UnimplementedMerchantNameCategoriserServer
// for forward compatibility
type MerchantNameCategoriserServer interface {
	// inhouse merchant name categoriser API to check whether the passed name is a merchant or not
	MerchantNameCategoriser(context.Context, *MerchantNameCategoriserRequest) (*MerchantNameCategoriserResponse, error)
}

// UnimplementedMerchantNameCategoriserServer should be embedded to have forward compatible implementations.
type UnimplementedMerchantNameCategoriserServer struct {
}

func (UnimplementedMerchantNameCategoriserServer) MerchantNameCategoriser(context.Context, *MerchantNameCategoriserRequest) (*MerchantNameCategoriserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MerchantNameCategoriser not implemented")
}

// UnsafeMerchantNameCategoriserServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MerchantNameCategoriserServer will
// result in compilation errors.
type UnsafeMerchantNameCategoriserServer interface {
	mustEmbedUnimplementedMerchantNameCategoriserServer()
}

func RegisterMerchantNameCategoriserServer(s grpc.ServiceRegistrar, srv MerchantNameCategoriserServer) {
	s.RegisterService(&MerchantNameCategoriser_ServiceDesc, srv)
}

func _MerchantNameCategoriser_MerchantNameCategoriser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MerchantNameCategoriserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MerchantNameCategoriserServer).MerchantNameCategoriser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MerchantNameCategoriser_MerchantNameCategoriser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MerchantNameCategoriserServer).MerchantNameCategoriser(ctx, req.(*MerchantNameCategoriserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MerchantNameCategoriser_ServiceDesc is the grpc.ServiceDesc for MerchantNameCategoriser service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MerchantNameCategoriser_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendors.inhouse.MerchantNameCategoriser",
	HandlerType: (*MerchantNameCategoriserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MerchantNameCategoriser",
			Handler:    _MerchantNameCategoriser_MerchantNameCategoriser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendors/inhouse/merchantnamecategoriser.proto",
}
