// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/lentra/preapproved_loan.proto

package lentra

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LoanEligibilityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequestMultiError, or nil if none found.
func (m *LoanEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityRequestValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityRequestValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityRequestValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanApplication()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityRequestValidationError{
					field:  "LoanApplication",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityRequestValidationError{
					field:  "LoanApplication",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanApplication()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityRequestValidationError{
				field:  "LoanApplication",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIntegrations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityRequestValidationError{
					field:  "Integrations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityRequestValidationError{
					field:  "Integrations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntegrations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityRequestValidationError{
				field:  "Integrations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SDedupeId

	if len(errors) > 0 {
		return LoanEligibilityRequestMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequestMultiError is an error wrapping multiple validation
// errors returned by LoanEligibilityRequest.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequestMultiError) AllErrors() []error { return m }

// LoanEligibilityRequestValidationError is the validation error returned by
// LoanEligibilityRequest.Validate if the designated constraints aren't met.
type LoanEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityRequestValidationError) ErrorName() string {
	return "LoanEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequestValidationError{}

// Validate checks the field values on LoanEligibilityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponseMultiError, or nil if none found.
func (m *LoanEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReferenceId

	// no validation rules for TrackingId

	// no validation rules for DateTime

	if all {
		switch v := interface{}(m.GetResponseDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponseValidationError{
					field:  "ResponseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponseValidationError{
					field:  "ResponseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponseValidationError{
				field:  "ResponseDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanEligibilityResponseMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponseMultiError is an error wrapping multiple validation
// errors returned by LoanEligibilityResponse.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponseMultiError) AllErrors() []error { return m }

// LoanEligibilityResponseValidationError is the validation error returned by
// LoanEligibilityResponse.Validate if the designated constraints aren't met.
type LoanEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityResponseValidationError) ErrorName() string {
	return "LoanEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponseValidationError{}

// Validate checks the field values on LoanEligibilityAuthTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityAuthTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityAuthTokenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LoanEligibilityAuthTokenRequestMultiError, or nil if none found.
func (m *LoanEligibilityAuthTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SClientIdentifier

	// no validation rules for SClientSecret

	// no validation rules for SInstitutionName

	// no validation rules for SApplication

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenRequestMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenRequestMultiError is an error wrapping multiple
// validation errors returned by LoanEligibilityAuthTokenRequest.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityAuthTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenRequestMultiError) AllErrors() []error { return m }

// LoanEligibilityAuthTokenRequestValidationError is the validation error
// returned by LoanEligibilityAuthTokenRequest.Validate if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityAuthTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenRequestValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenRequestValidationError{}

// Validate checks the field values on LoanEligibilityAuthTokenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoanEligibilityAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityAuthTokenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LoanEligibilityAuthTokenResponseMultiError, or nil if none found.
func (m *LoanEligibilityAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponseValidationError{
					field:  "OStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponseValidationError{
					field:  "OStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityAuthTokenResponseValidationError{
				field:  "OStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponseValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponseValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityAuthTokenResponseValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetError()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponseValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponseValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetError()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityAuthTokenResponseValidationError{
				field:  "Error",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenResponseMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenResponseMultiError is an error wrapping multiple
// validation errors returned by
// LoanEligibilityAuthTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenResponseMultiError) AllErrors() []error { return m }

// LoanEligibilityAuthTokenResponseValidationError is the validation error
// returned by LoanEligibilityAuthTokenResponse.Validate if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenResponseValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenResponseValidationError{}

// Validate checks the field values on LoanEligibilityRequest_Integrations with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoanEligibilityRequest_Integrations) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityRequest_Integrations
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_IntegrationsMultiError, or nil if none found.
func (m *LoanEligibilityRequest_Integrations) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_Integrations) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Multibureau

	// no validation rules for FraudCheck

	// no validation rules for LimitCheck

	if len(errors) > 0 {
		return LoanEligibilityRequest_IntegrationsMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_IntegrationsMultiError is an error wrapping multiple
// validation errors returned by
// LoanEligibilityRequest_Integrations.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityRequest_IntegrationsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_IntegrationsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_IntegrationsMultiError) AllErrors() []error { return m }

// LoanEligibilityRequest_IntegrationsValidationError is the validation error
// returned by LoanEligibilityRequest_Integrations.Validate if the designated
// constraints aren't met.
type LoanEligibilityRequest_IntegrationsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_IntegrationsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityRequest_IntegrationsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityRequest_IntegrationsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityRequest_IntegrationsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityRequest_IntegrationsValidationError) ErrorName() string {
	return "LoanEligibilityRequest_IntegrationsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_IntegrationsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_Integrations.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_IntegrationsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_IntegrationsValidationError{}

// Validate checks the field values on LoanEligibilityRequest_LoanApplication
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityRequest_LoanApplication) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplicationMultiError, or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for ApplicationType

	// no validation rules for AgencyId

	// no validation rules for MerchantId

	// no validation rules for ChannelId

	// no validation rules for CategoryId

	// no validation rules for Currency

	// no validation rules for LoanAmount

	if all {
		switch v := interface{}(m.GetBorrower()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityRequest_LoanApplicationValidationError{
					field:  "Borrower",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityRequest_LoanApplicationValidationError{
					field:  "Borrower",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorrower()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityRequest_LoanApplicationValidationError{
				field:  "Borrower",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Tenure

	// no validation rules for Lender

	// no validation rules for Source

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplicationMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplicationMultiError is an error wrapping
// multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityRequest_LoanApplicationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplicationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplicationMultiError) AllErrors() []error { return m }

// LoanEligibilityRequest_LoanApplicationValidationError is the validation
// error returned by LoanEligibilityRequest_LoanApplication.Validate if the
// designated constraints aren't met.
type LoanEligibilityRequest_LoanApplicationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplicationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplicationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplicationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplicationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplicationValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplicationValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplicationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplicationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplicationValidationError{}

// Validate checks the field values on LoanEligibilityRequest_Metadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityRequest_Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityRequest_Metadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_MetadataMultiError, or nil if none found.
func (m *LoanEligibilityRequest_Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for Timestamp

	// no validation rules for TraceId

	if len(errors) > 0 {
		return LoanEligibilityRequest_MetadataMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_MetadataMultiError is an error wrapping multiple
// validation errors returned by LoanEligibilityRequest_Metadata.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_MetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_MetadataMultiError) AllErrors() []error { return m }

// LoanEligibilityRequest_MetadataValidationError is the validation error
// returned by LoanEligibilityRequest_Metadata.Validate if the designated
// constraints aren't met.
type LoanEligibilityRequest_MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityRequest_MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityRequest_MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityRequest_MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityRequest_MetadataValidationError) ErrorName() string {
	return "LoanEligibilityRequest_MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_MetadataValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_BorrowerMultiError, or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomerIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
						field:  fmt.Sprintf("CustomerIdentifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
						field:  fmt.Sprintf("CustomerIdentifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
					field:  fmt.Sprintf("CustomerIdentifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Name

	// no validation rules for SourceIpv4

	if all {
		switch v := interface{}(m.GetContactDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
					field:  "ContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
					field:  "ContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
				field:  "ContactDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Dob

	for idx, item := range m.GetEmploymentDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
						field:  fmt.Sprintf("EmploymentDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
						field:  fmt.Sprintf("EmploymentDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityRequest_LoanApplication_BorrowerValidationError{
					field:  fmt.Sprintf("EmploymentDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Qualification

	// no validation rules for MaritalStatus

	// no validation rules for Gender

	// no validation rules for ApplicantType

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_BorrowerMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_BorrowerMultiError is an error
// wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower.ValidateAll() if the
// designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_BorrowerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_BorrowerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_BorrowerMultiError) AllErrors() []error { return m }

// LoanEligibilityRequest_LoanApplication_BorrowerValidationError is the
// validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower.Validate if the designated
// constraints aren't met.
type LoanEligibilityRequest_LoanApplication_BorrowerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_BorrowerValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_BorrowerValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_BorrowerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_BorrowerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_BorrowerValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_BorrowerValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_BorrowerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_BorrowerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_BorrowerValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsMultiError,
// or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmploymentType

	// no validation rules for EmployerName

	// no validation rules for EmploymentCategory

	// no validation rules for NetMonthlySalary

	// no validation rules for GrossMonthlySalary

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError
// is the validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails.Validate
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_Borrower_EmploymentDetailsValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsMultiError,
// or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEmailLists() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
						field:  fmt.Sprintf("EmailLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
						field:  fmt.Sprintf("EmailLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
					field:  fmt.Sprintf("EmailLists[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPhoneLists() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
						field:  fmt.Sprintf("PhoneLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
						field:  fmt.Sprintf("PhoneLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{
					field:  fmt.Sprintf("PhoneLists[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsMultiError is
// an error wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError
// is the validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails.Validate if
// the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower_ContactDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetailsValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersMultiError,
// or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IdName

	// no validation rules for IdValue

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError
// is the validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers.Validate
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_Borrower_CustomerIdentifiersValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListMultiError,
// or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PhoneType

	// no validation rules for PhoneNumber

	// no validation rules for CountryCode

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError
// is the validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList.Validate
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_PhoneListValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListMultiError,
// or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmailType

	// no validation rules for EmailId

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError
// is the validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList.Validate
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_EmailListValidationError{}

// Validate checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesMultiError,
// or nil if none found.
func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Co

	// no validation rules for Hba

	// no validation rules for Srl

	// no validation rules for Landmark

	// no validation rules for Als

	// no validation rules for Vtc

	// no validation rules for Pincode

	// no validation rules for Po

	// no validation rules for District

	// no validation rules for State

	// no validation rules for Country

	// no validation rules for Uri

	// no validation rules for Latitude

	// no validation rules for Longitude

	// no validation rules for Type

	if len(errors) > 0 {
		return LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesMultiError(errors)
	}

	return nil
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError
// is the validation error returned by
// LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses.Validate
// if the designated constraints aren't met.
type LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError) ErrorName() string {
	return "LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_Addresses.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityRequest_LoanApplication_Borrower_ContactDetails_AddressesValidationError{}

// Validate checks the field values on LoanEligibilityResponse_ResponseDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetailsMultiError, or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResponseCode

	// no validation rules for ResponseDesc

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetailsValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetailsValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetailsValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetailsMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetailsMultiError is an error wrapping
// multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityResponse_ResponseDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetailsMultiError) AllErrors() []error { return m }

// LoanEligibilityResponse_ResponseDetailsValidationError is the validation
// error returned by LoanEligibilityResponse_ResponseDetails.Validate if the
// designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetailsValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetailsValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_ResponseMultiError, or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApprovedAmount

	// no validation rules for CibilStatus

	// no validation rules for CibilScore

	// no validation rules for FraudCheckDecision

	// no validation rules for FraudCheckStatus

	for idx, item := range m.GetDecisionResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_ResponseValidationError{
						field:  fmt.Sprintf("DecisionResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_ResponseValidationError{
						field:  fmt.Sprintf("DecisionResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityResponse_ResponseDetails_ResponseValidationError{
					field:  fmt.Sprintf("DecisionResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_ResponseMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_ResponseMultiError is an error
// wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response.ValidateAll() if the
// designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_ResponseMultiError) AllErrors() []error { return m }

// LoanEligibilityResponse_ResponseDetails_ResponseValidationError is the
// validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response.Validate if the designated
// constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_ResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_ResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_ResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_ResponseValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_ResponseValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetApplicantResult() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{
						field:  fmt.Sprintf("ApplicantResult[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{
						field:  fmt.Sprintf("ApplicantResult[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{
					field:  fmt.Sprintf("ApplicantResult[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Lender

	// no validation rules for InstitutionId

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponseValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationDecision

	// no validation rules for ApplicationApprovedAmount

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_Summary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_SummaryValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResponseDate

	// no validation rules for ScoringRefId

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetEligibilityResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
					field:  "EligibilityResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
					field:  "EligibilityResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEligibilityResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
				field:  "EligibilityResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDerivedFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
					field:  "DerivedFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
					field:  "DerivedFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDerivedFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
				field:  "DerivedFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
					field:  fmt.Sprintf("Rules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDeviationRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
						field:  fmt.Sprintf("DeviationRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
						field:  fmt.Sprintf("DeviationRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{
					field:  fmt.Sprintf("DeviationRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PolicyName

	// no validation rules for PolicyId

	// no validation rules for Decision

	// no validation rules for EligibilityAmount

	// no validation rules for EligibilityDecision

	// no validation rules for ApplicantId

	// no validation rules for EligibilityApprovedAmount

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResultValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomFieldsNtbetcCibilIdvValue

	// no validation rules for CalculatedFieldsNtbsalCreditLimit

	// no validation rules for CalculatedFieldsAvg_5PercentCc

	// no validation rules for CustomFieldsNtbEmplPeriod

	// no validation rules for CustomFieldsNtbHardEnqL_6M

	// no validation rules for CalculatedFieldsNtbSelfemplNetMthlyInc

	// no validation rules for FinancialFieldsNtbSelfemplNetMthlyInc

	// no validation rules for CustomFieldsNtbSalaryIncome

	// no validation rules for CustomFieldsMultiplierNtbSalaried

	// no validation rules for CustomFieldsNameDeclineNtb

	// no validation rules for CustomFieldsHunterScore

	// no validation rules for CustomFieldsCrchMaker

	// no validation rules for CustomFieldsNtbSmaNpaCheck

	// no validation rules for CustomFieldsNtbNpAge

	// no validation rules for CustomFieldsDpd_60To90L12M

	// no validation rules for CustomFieldsNtbDpd_5To30L6M

	// no validation rules for CalculatedFieldsNtbSalariedNetMthlyInc

	// no validation rules for CustomFieldsCrchChecker

	// no validation rules for CustomFieldsDpd_90Plus

	// no validation rules for FinancialFieldsNtbsalCreditLimit

	// no validation rules for FinancialFieldsNetmonthlyincSelfempl

	// no validation rules for CalculatedFieldsNtbCreditcardUtilization

	// no validation rules for CustomFieldsTotalCcOutstandingCibil

	// no validation rules for CustomFieldsCibilWriteoffStatus

	// no validation rules for CustomFieldsSalaryCount

	// no validation rules for CustomFieldsMinOfMthlyInc

	// no validation rules for CalculatedFieldsNtbFoirCc

	// no validation rules for CustomFieldsNtbMinOfMthlyInc

	// no validation rules for CalculatedFieldsNetmonthlyincSelfempl

	// no validation rules for CustomFieldsNtbNpCibilScore

	// no validation rules for MatchingFieldsPerfiosNameMatch

	// no validation rules for FinancialFieldsAvg_5PercentCc

	// no validation rules for FinancialFieldsNtbFoirCc

	// no validation rules for CustomFieldsAvgCcUtilize

	// no validation rules for FinancialFieldsNtbSalariedNetMthlyInc

	// no validation rules for CustomFieldsTotalCcSanctionedCibil

	// no validation rules for CustomFieldsNtbetcCibilIdv

	// no validation rules for CustomFieldsTotalObligationCibil

	// no validation rules for CustomFieldsAvgTotalincSelfempl

	// no validation rules for CustomFieldsNtbNpApproved

	// no validation rules for FinancialFieldsNtbCreditcardUtilization

	// no validation rules for CustomFieldsDpd_30To60L12M

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DerivedFieldsValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
				field:  "Values",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EligibilityId

	// no validation rules for GridId

	// no validation rules for ApprovedAmount

	// no validation rules for Decision

	// no validation rules for ComputeDisp

	// no validation rules for ComputeLogic

	// no validation rules for MaxAmount

	// no validation rules for MinAmount

	// no validation rules for Dp

	// no validation rules for MaxTenor

	// no validation rules for Remark

	// no validation rules for ComputedAmount

	// no validation rules for EligibilityAmount

	// no validation rules for Cnt

	// no validation rules for GridExp

	// no validation rules for MulProd

	if all {
		switch v := interface{}(m.GetAdditionalFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
					field:  "AdditionalFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
					field:  "AdditionalFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
				field:  "AdditionalFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EligibilityName

	for idx, item := range m.GetMatchedEligibility() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
						field:  fmt.Sprintf("MatchedEligibility[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
						field:  fmt.Sprintf("MatchedEligibility[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{
					field:  fmt.Sprintf("MatchedEligibility[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DtSubmit

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponseValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CriteriaId

	// no validation rules for RuleName

	// no validation rules for Outcome

	// no validation rules for Remark

	// no validation rules for Exp

	if all {
		switch v := interface{}(m.GetValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError{
				field:  "Values",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_RulesValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRules.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_DeviationRulesValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{
				field:  "Values",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EligibilityId

	// no validation rules for GridId

	// no validation rules for Decision

	// no validation rules for ComputeDisp

	// no validation rules for ComputeLogic

	// no validation rules for MaxAmount

	// no validation rules for MinAmount

	// no validation rules for Dp

	// no validation rules for MaxTenor

	// no validation rules for Remark

	// no validation rules for ComputedAmount

	// no validation rules for EligibilityAmount

	// no validation rules for Cnt

	// no validation rules for GridExp

	// no validation rules for MulProd

	// no validation rules for EligibilityName

	if all {
		switch v := interface{}(m.GetAdditionalFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{
					field:  "AdditionalFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{
					field:  "AdditionalFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{
				field:  "AdditionalFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DtSubmit

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibilityValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pct

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_AdditionalFieldsValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NtbsalCreditLimit

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_Values.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_ValuesValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Max

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_AdditionalFieldsValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FoirCc

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_Values.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_EligibilityResponse_MatchedEligibility_ValuesValidationError{}

// Validate checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesMultiError,
// or nil if none found.
func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NtbNpApproved

	if len(errors) > 0 {
		return LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesMultiError(errors)
	}

	return nil
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesMultiError
// is an error wrapping multiple validation errors returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values.ValidateAll()
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError
// is the validation error returned by
// LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values.Validate
// if the designated constraints aren't met.
type LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError) ErrorName() string {
	return "LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_Values.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityResponse_ResponseDetails_Response_DecisionResponse_ApplicantResult_Rules_ValuesValidationError{}

// Validate checks the field values on LoanEligibilityAuthTokenResponse_Error
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityAuthTokenResponse_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityAuthTokenResponse_Error with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// LoanEligibilityAuthTokenResponse_ErrorMultiError, or nil if none found.
func (m *LoanEligibilityAuthTokenResponse_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenResponse_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OStatus

	// no validation rules for ODeveloperMessage

	// no validation rules for SPath

	// no validation rules for DTimestamp

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenResponse_ErrorMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenResponse_ErrorMultiError is an error wrapping
// multiple validation errors returned by
// LoanEligibilityAuthTokenResponse_Error.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenResponse_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenResponse_ErrorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenResponse_ErrorMultiError) AllErrors() []error { return m }

// LoanEligibilityAuthTokenResponse_ErrorValidationError is the validation
// error returned by LoanEligibilityAuthTokenResponse_Error.Validate if the
// designated constraints aren't met.
type LoanEligibilityAuthTokenResponse_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenResponse_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenResponse_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenResponse_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityAuthTokenResponse_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenResponse_ErrorValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenResponse_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenResponse_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenResponse_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenResponse_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenResponse_ErrorValidationError{}

// Validate checks the field values on LoanEligibilityAuthTokenResponse_Body
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityAuthTokenResponse_Body) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanEligibilityAuthTokenResponse_Body
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanEligibilityAuthTokenResponse_BodyMultiError, or nil if none found.
func (m *LoanEligibilityAuthTokenResponse_Body) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenResponse_Body) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponse_BodyValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponse_BodyValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityAuthTokenResponse_BodyValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenResponse_BodyMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenResponse_BodyMultiError is an error wrapping
// multiple validation errors returned by
// LoanEligibilityAuthTokenResponse_Body.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenResponse_BodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenResponse_BodyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenResponse_BodyMultiError) AllErrors() []error { return m }

// LoanEligibilityAuthTokenResponse_BodyValidationError is the validation error
// returned by LoanEligibilityAuthTokenResponse_Body.Validate if the
// designated constraints aren't met.
type LoanEligibilityAuthTokenResponse_BodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenResponse_BodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenResponse_BodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenResponse_BodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityAuthTokenResponse_BodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenResponse_BodyValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenResponse_BodyValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenResponse_BodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenResponse_Body.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenResponse_BodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenResponse_BodyValidationError{}

// Validate checks the field values on LoanEligibilityAuthTokenResponse_Status
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanEligibilityAuthTokenResponse_Status) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityAuthTokenResponse_Status with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// LoanEligibilityAuthTokenResponse_StatusMultiError, or nil if none found.
func (m *LoanEligibilityAuthTokenResponse_Status) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenResponse_Status) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for HttpStatus

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenResponse_StatusMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenResponse_StatusMultiError is an error wrapping
// multiple validation errors returned by
// LoanEligibilityAuthTokenResponse_Status.ValidateAll() if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenResponse_StatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenResponse_StatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenResponse_StatusMultiError) AllErrors() []error { return m }

// LoanEligibilityAuthTokenResponse_StatusValidationError is the validation
// error returned by LoanEligibilityAuthTokenResponse_Status.Validate if the
// designated constraints aren't met.
type LoanEligibilityAuthTokenResponse_StatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenResponse_StatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenResponse_StatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenResponse_StatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityAuthTokenResponse_StatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenResponse_StatusValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenResponse_StatusValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenResponse_StatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenResponse_Status.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenResponse_StatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenResponse_StatusValidationError{}

// Validate checks the field values on
// LoanEligibilityAuthTokenResponse_Body_Payload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityAuthTokenResponse_Body_Payload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityAuthTokenResponse_Body_Payload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LoanEligibilityAuthTokenResponse_Body_PayloadMultiError, or nil if none found.
func (m *LoanEligibilityAuthTokenResponse_Body_Payload) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenResponse_Body_Payload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuthToken()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponse_Body_PayloadValidationError{
					field:  "AuthToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanEligibilityAuthTokenResponse_Body_PayloadValidationError{
					field:  "AuthToken",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthToken()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanEligibilityAuthTokenResponse_Body_PayloadValidationError{
				field:  "AuthToken",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusCode

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenResponse_Body_PayloadMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenResponse_Body_PayloadMultiError is an error wrapping
// multiple validation errors returned by
// LoanEligibilityAuthTokenResponse_Body_Payload.ValidateAll() if the
// designated constraints aren't met.
type LoanEligibilityAuthTokenResponse_Body_PayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenResponse_Body_PayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenResponse_Body_PayloadMultiError) AllErrors() []error { return m }

// LoanEligibilityAuthTokenResponse_Body_PayloadValidationError is the
// validation error returned by
// LoanEligibilityAuthTokenResponse_Body_Payload.Validate if the designated
// constraints aren't met.
type LoanEligibilityAuthTokenResponse_Body_PayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenResponse_Body_PayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenResponse_Body_PayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenResponse_Body_PayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanEligibilityAuthTokenResponse_Body_PayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenResponse_Body_PayloadValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenResponse_Body_PayloadValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenResponse_Body_PayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenResponse_Body_Payload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenResponse_Body_PayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenResponse_Body_PayloadValidationError{}

// Validate checks the field values on
// LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenMultiError, or nil
// if none found.
func (m *LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessToken

	// no validation rules for TokenType

	// no validation rules for RefreshToken

	// no validation rules for ExpiresIn

	// no validation rules for Scope

	if len(errors) > 0 {
		return LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenMultiError(errors)
	}

	return nil
}

// LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenMultiError is an
// error wrapping multiple validation errors returned by
// LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken.ValidateAll() if
// the designated constraints aren't met.
type LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenMultiError) AllErrors() []error {
	return m
}

// LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError is
// the validation error returned by
// LoanEligibilityAuthTokenResponse_Body_Payload_AuthToken.Validate if the
// designated constraints aren't met.
type LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError) ErrorName() string {
	return "LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError"
}

// Error satisfies the builtin error interface
func (e LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanEligibilityAuthTokenResponse_Body_Payload_AuthToken.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanEligibilityAuthTokenResponse_Body_Payload_AuthTokenValidationError{}
