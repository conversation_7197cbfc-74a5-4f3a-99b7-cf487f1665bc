// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/federal/internationalfundtransfer/gst.proto

package internationalfundtransfer

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// refer documentation provided by vendor
// https://docs.google.com/document/d/1hDCvZDQ9i1ckMURV66z71EMscO5W5H-N/edit?usp=sharing&ouid=106341874717564561814&rtpof=true&sd=true
type InquireOrReportGSTCollectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// A unique ID. Invoice reporting for GST will be against this Number
	// Alphanumeric
	InvoiceNo string `protobuf:"bytes,2,opt,name=invoice_no,json=invoiceNo,proto3" json:"invoice_no,omitempty"`
	// GST TTUM file Transaction ID which has this amount.
	ChargeTranId string `protobuf:"bytes,3,opt,name=charge_tran_id,json=chargeTranId,proto3" json:"charge_tran_id,omitempty"`
	// GST TTUM file Transaction date.
	ChargeTranDate string `protobuf:"bytes,4,opt,name=charge_tran_date,json=chargeTranDate,proto3" json:"charge_tran_date,omitempty"`
	// CBS Transaction ID + Transaction Date + serial number.
	// Alphanumeric number.
	// No special characters allowed.
	CompB2KId               string `protobuf:"bytes,5,opt,name=comp_b2k_id,json=compB2KId,proto3" json:"comp_b2k_id,omitempty"`
	ServiceSolId            string `protobuf:"bytes,6,opt,name=service_sol_id,json=serviceSolId,proto3" json:"service_sol_id,omitempty"`
	TargetAcid              string `protobuf:"bytes,7,opt,name=target_acid,json=targetAcid,proto3" json:"target_acid,omitempty"`
	CustomerId              string `protobuf:"bytes,8,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	IgstAcid                string `protobuf:"bytes,9,opt,name=igst_acid,json=IGSTAcid,proto3" json:"igst_acid,omitempty"`
	CgstAcid                string `protobuf:"bytes,10,opt,name=cgst_acid,json=CGSTAcid,proto3" json:"cgst_acid,omitempty"`
	SgstAcid                string `protobuf:"bytes,11,opt,name=sgst_acid,json=SGSTAcid,proto3" json:"sgst_acid,omitempty"`
	UgstAcid                string `protobuf:"bytes,12,opt,name=ugst_acid,json=UGSTAcid,proto3" json:"ugst_acid,omitempty"`
	NewIgstTaxAmount        string `protobuf:"bytes,13,opt,name=new_igst_tax_amount,json=newIGSTTaxAmount,proto3" json:"new_igst_tax_amount,omitempty"`
	NewCgstTaxAmount        string `protobuf:"bytes,14,opt,name=new_cgst_tax_amount,json=newCGSTTaxAmount,proto3" json:"new_cgst_tax_amount,omitempty"`
	NewSgstTaxAmount        string `protobuf:"bytes,15,opt,name=new_sgst_tax_amount,json=newSGSTTaxAmount,proto3" json:"new_sgst_tax_amount,omitempty"`
	NewUgstTaxAmount        string `protobuf:"bytes,16,opt,name=new_ugst_tax_amount,json=newUGSTTaxAmount,proto3" json:"new_ugst_tax_amount,omitempty"`
	CessAmount              string `protobuf:"bytes,17,opt,name=cess_amount,json=CESSAmount,proto3" json:"cess_amount,omitempty"`
	OrgGstNumber            string `protobuf:"bytes,18,opt,name=org_gst_number,json=orgGSTNumber,proto3" json:"org_gst_number,omitempty"`
	NewGstNumber            string `protobuf:"bytes,19,opt,name=new_gst_number,json=newGSTNumber,proto3" json:"new_gst_number,omitempty"`
	NewSrvProvdSt           string `protobuf:"bytes,20,opt,name=new_srv_provd_st,json=newSrvProvdST,proto3" json:"new_srv_provd_st,omitempty"`
	NewBenfSt               string `protobuf:"bytes,21,opt,name=new_benf_st,json=newBenfST,proto3" json:"new_benf_st,omitempty"`
	IgstTaxAmountDifference string `protobuf:"bytes,22,opt,name=igst_tax_amount_difference,json=IGSTTaxAmountDifference,proto3" json:"igst_tax_amount_difference,omitempty"`
	CgstTaxAmountDifference string `protobuf:"bytes,23,opt,name=cgst_tax_amount_difference,json=CGSTTaxAmountDifference,proto3" json:"cgst_tax_amount_difference,omitempty"`
	SgstTaxAmountDifference string `protobuf:"bytes,24,opt,name=sgst_tax_amount_difference,json=SGSTTaxAmountDifference,proto3" json:"sgst_tax_amount_difference,omitempty"`
	UgstTaxAmountDifference string `protobuf:"bytes,25,opt,name=ugst_tax_amount_difference,json=UGSTTaxAmountDifference,proto3" json:"ugst_tax_amount_difference,omitempty"`
	SrvProvSol              string `protobuf:"bytes,26,opt,name=srv_prov_sol,json=srvProvSol,proto3" json:"srv_prov_sol,omitempty"`
	BenfId                  string `protobuf:"bytes,27,opt,name=benf_id,json=benfID,proto3" json:"benf_id,omitempty"`
	OrigChargeAmt           string `protobuf:"bytes,28,opt,name=orig_charge_amt,json=origChargeAMT,proto3" json:"orig_charge_amt,omitempty"`
	OrigChargeCcy           string `protobuf:"bytes,29,opt,name=orig_charge_ccy,json=origChargeCCY,proto3" json:"orig_charge_ccy,omitempty"`
	FreeText                string `protobuf:"bytes,30,opt,name=free_text,json=freeText,proto3" json:"free_text,omitempty"`
	CompB2KType             string `protobuf:"bytes,31,opt,name=comp_b2k_type,json=compB2KType,proto3" json:"comp_b2k_type,omitempty"`
	Channel                 string `protobuf:"bytes,32,opt,name=channel,proto3" json:"channel,omitempty"`
	ReqType                 string `protobuf:"bytes,33,opt,name=req_type,json=req_Type,proto3" json:"req_type,omitempty"`
	FreeText1               string `protobuf:"bytes,34,opt,name=free_text1,json=freeText1,proto3" json:"free_text1,omitempty"`
	FreeText2               string `protobuf:"bytes,35,opt,name=free_text2,json=freeText2,proto3" json:"free_text2,omitempty"`
	FreeText3               string `protobuf:"bytes,36,opt,name=free_text3,json=freeText3,proto3" json:"free_text3,omitempty"`
	FreeText4               string `protobuf:"bytes,37,opt,name=free_text4,json=freeText4,proto3" json:"free_text4,omitempty"`
	FreeText5               string `protobuf:"bytes,38,opt,name=free_text5,json=freeText5,proto3" json:"free_text5,omitempty"`
	FreeText6               string `protobuf:"bytes,39,opt,name=free_text6,json=freeText6,proto3" json:"free_text6,omitempty"`
	FreeText7               string `protobuf:"bytes,40,opt,name=free_text7,json=freeText7,proto3" json:"free_text7,omitempty"`
	FreeText8               string `protobuf:"bytes,41,opt,name=free_text8,json=freeText8,proto3" json:"free_text8,omitempty"`
	FreeText9               string `protobuf:"bytes,42,opt,name=free_text9,json=freeText9,proto3" json:"free_text9,omitempty"`
	FreeText10              string `protobuf:"bytes,43,opt,name=free_text10,json=freeText10,proto3" json:"free_text10,omitempty"`
}

func (x *InquireOrReportGSTCollectionRequest) Reset() {
	*x = InquireOrReportGSTCollectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquireOrReportGSTCollectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquireOrReportGSTCollectionRequest) ProtoMessage() {}

func (x *InquireOrReportGSTCollectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquireOrReportGSTCollectionRequest.ProtoReflect.Descriptor instead.
func (*InquireOrReportGSTCollectionRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescGZIP(), []int{0}
}

func (x *InquireOrReportGSTCollectionRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetInvoiceNo() string {
	if x != nil {
		return x.InvoiceNo
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetChargeTranId() string {
	if x != nil {
		return x.ChargeTranId
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetChargeTranDate() string {
	if x != nil {
		return x.ChargeTranDate
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetCompB2KId() string {
	if x != nil {
		return x.CompB2KId
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetServiceSolId() string {
	if x != nil {
		return x.ServiceSolId
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetTargetAcid() string {
	if x != nil {
		return x.TargetAcid
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetIgstAcid() string {
	if x != nil {
		return x.IgstAcid
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetCgstAcid() string {
	if x != nil {
		return x.CgstAcid
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetSgstAcid() string {
	if x != nil {
		return x.SgstAcid
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetUgstAcid() string {
	if x != nil {
		return x.UgstAcid
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewIgstTaxAmount() string {
	if x != nil {
		return x.NewIgstTaxAmount
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewCgstTaxAmount() string {
	if x != nil {
		return x.NewCgstTaxAmount
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewSgstTaxAmount() string {
	if x != nil {
		return x.NewSgstTaxAmount
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewUgstTaxAmount() string {
	if x != nil {
		return x.NewUgstTaxAmount
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetCessAmount() string {
	if x != nil {
		return x.CessAmount
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetOrgGstNumber() string {
	if x != nil {
		return x.OrgGstNumber
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewGstNumber() string {
	if x != nil {
		return x.NewGstNumber
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewSrvProvdSt() string {
	if x != nil {
		return x.NewSrvProvdSt
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetNewBenfSt() string {
	if x != nil {
		return x.NewBenfSt
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetIgstTaxAmountDifference() string {
	if x != nil {
		return x.IgstTaxAmountDifference
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetCgstTaxAmountDifference() string {
	if x != nil {
		return x.CgstTaxAmountDifference
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetSgstTaxAmountDifference() string {
	if x != nil {
		return x.SgstTaxAmountDifference
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetUgstTaxAmountDifference() string {
	if x != nil {
		return x.UgstTaxAmountDifference
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetSrvProvSol() string {
	if x != nil {
		return x.SrvProvSol
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetBenfId() string {
	if x != nil {
		return x.BenfId
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetOrigChargeAmt() string {
	if x != nil {
		return x.OrigChargeAmt
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetOrigChargeCcy() string {
	if x != nil {
		return x.OrigChargeCcy
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText() string {
	if x != nil {
		return x.FreeText
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetCompB2KType() string {
	if x != nil {
		return x.CompB2KType
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetReqType() string {
	if x != nil {
		return x.ReqType
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText1() string {
	if x != nil {
		return x.FreeText1
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText2() string {
	if x != nil {
		return x.FreeText2
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText3() string {
	if x != nil {
		return x.FreeText3
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText4() string {
	if x != nil {
		return x.FreeText4
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText5() string {
	if x != nil {
		return x.FreeText5
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText6() string {
	if x != nil {
		return x.FreeText6
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText7() string {
	if x != nil {
		return x.FreeText7
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText8() string {
	if x != nil {
		return x.FreeText8
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText9() string {
	if x != nil {
		return x.FreeText9
	}
	return ""
}

func (x *InquireOrReportGSTCollectionRequest) GetFreeText10() string {
	if x != nil {
		return x.FreeText10
	}
	return ""
}

type InquireOrReportGSTCollectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      string         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message     string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Reserve1    string         `protobuf:"bytes,3,opt,name=reserve1,proto3" json:"reserve1,omitempty"`
	Reserve2    string         `protobuf:"bytes,4,opt,name=reserve2,proto3" json:"reserve2,omitempty"`
	Reserve3    string         `protobuf:"bytes,5,opt,name=reserve3,proto3" json:"reserve3,omitempty"`
	Reserve4    string         `protobuf:"bytes,6,opt,name=reserve4,proto3" json:"reserve4,omitempty"`
	Reserve5    string         `protobuf:"bytes,7,opt,name=reserve5,proto3" json:"reserve5,omitempty"`
	Reserve6    string         `protobuf:"bytes,8,opt,name=reserve6,proto3" json:"reserve6,omitempty"`
	Reserve7    string         `protobuf:"bytes,9,opt,name=reserve7,proto3" json:"reserve7,omitempty"`
	Reserve8    string         `protobuf:"bytes,10,opt,name=reserve8,proto3" json:"reserve8,omitempty"`
	Reserve9    string         `protobuf:"bytes,11,opt,name=reserve9,proto3" json:"reserve9,omitempty"`
	Reserve10   string         `protobuf:"bytes,12,opt,name=reserve10,proto3" json:"reserve10,omitempty"`
	ErrResponse *ErrorResponse `protobuf:"bytes,13,opt,name=err_response,json=errorResponse,proto3" json:"err_response,omitempty"`
}

func (x *InquireOrReportGSTCollectionResponse) Reset() {
	*x = InquireOrReportGSTCollectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquireOrReportGSTCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquireOrReportGSTCollectionResponse) ProtoMessage() {}

func (x *InquireOrReportGSTCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquireOrReportGSTCollectionResponse.ProtoReflect.Descriptor instead.
func (*InquireOrReportGSTCollectionResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescGZIP(), []int{1}
}

func (x *InquireOrReportGSTCollectionResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve1() string {
	if x != nil {
		return x.Reserve1
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve2() string {
	if x != nil {
		return x.Reserve2
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve3() string {
	if x != nil {
		return x.Reserve3
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve4() string {
	if x != nil {
		return x.Reserve4
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve5() string {
	if x != nil {
		return x.Reserve5
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve6() string {
	if x != nil {
		return x.Reserve6
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve7() string {
	if x != nil {
		return x.Reserve7
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve8() string {
	if x != nil {
		return x.Reserve8
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve9() string {
	if x != nil {
		return x.Reserve9
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetReserve10() string {
	if x != nil {
		return x.Reserve10
	}
	return ""
}

func (x *InquireOrReportGSTCollectionResponse) GetErrResponse() *ErrorResponse {
	if x != nil {
		return x.ErrResponse
	}
	return nil
}

type ErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatusCode   string `protobuf:"bytes,1,opt,name=status_code,json=statuscode,proto3" json:"status_code,omitempty"`
	StatusReason string `protobuf:"bytes,2,opt,name=status_reason,json=statusreason,proto3" json:"status_reason,omitempty"`
	CustomCode   string `protobuf:"bytes,3,opt,name=custom_code,json=customcode,proto3" json:"custom_code,omitempty"`
	CustomReason string `protobuf:"bytes,4,opt,name=custom_reason,json=customreason,proto3" json:"custom_reason,omitempty"`
	Description  string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescGZIP(), []int{2}
}

func (x *ErrorResponse) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *ErrorResponse) GetStatusReason() string {
	if x != nil {
		return x.StatusReason
	}
	return ""
}

func (x *ErrorResponse) GetCustomCode() string {
	if x != nil {
		return x.CustomCode
	}
	return ""
}

func (x *ErrorResponse) GetCustomReason() string {
	if x != nil {
		return x.CustomReason
	}
	return ""
}

func (x *ErrorResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_api_vendors_federal_internationalfundtransfer_gst_proto protoreflect.FileDescriptor

var file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2f,
	0x67, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x10,
	0x0a, 0x23, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x47, 0x53, 0x54, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x14, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x28, 0x52, 0x09,
	0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x4e, 0x6f, 0x12, 0x2f, 0x0a, 0x0e, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0c, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x10, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x0e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x29, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x5f, 0x62, 0x32, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x42, 0x32, 0x4b, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x41, 0x63, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x69, 0x67, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x08, 0x49, 0x47, 0x53, 0x54, 0x41, 0x63, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x63,
	0x67, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x08, 0x43, 0x47, 0x53, 0x54, 0x41,
	0x63, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x73, 0x67, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x08, 0x53, 0x47, 0x53, 0x54, 0x41, 0x63, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x75,
	0x67, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x08, 0x55, 0x47, 0x53, 0x54, 0x41,
	0x63, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x5f, 0x69, 0x67, 0x73, 0x74, 0x5f,
	0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x10, 0x6e, 0x65, 0x77,
	0x49, 0x47, 0x53, 0x54, 0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a,
	0x13, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x67, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x10, 0x6e, 0x65, 0x77, 0x43, 0x47, 0x53, 0x54, 0x54, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x5f, 0x73,
	0x67, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x10, 0x6e, 0x65, 0x77, 0x53, 0x47, 0x53, 0x54, 0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x38, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x5f, 0x75, 0x67, 0x73, 0x74, 0x5f, 0x74, 0x61,
	0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x10, 0x6e, 0x65, 0x77, 0x55, 0x47,
	0x53, 0x54, 0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x0b, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0a, 0x43, 0x45, 0x53,
	0x53, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x5f, 0x67,
	0x73, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0c, 0x6f, 0x72, 0x67, 0x47,
	0x53, 0x54, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x0e, 0x6e, 0x65, 0x77, 0x5f,
	0x67, 0x73, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0c, 0x6e, 0x65, 0x77,
	0x47, 0x53, 0x54, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x10, 0x6e, 0x65, 0x77,
	0x5f, 0x73, 0x72, 0x76, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x64, 0x5f, 0x73, 0x74, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0d,
	0x6e, 0x65, 0x77, 0x53, 0x72, 0x76, 0x50, 0x72, 0x6f, 0x76, 0x64, 0x53, 0x54, 0x12, 0x29, 0x0a,
	0x0b, 0x6e, 0x65, 0x77, 0x5f, 0x62, 0x65, 0x6e, 0x66, 0x5f, 0x73, 0x74, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x09, 0x6e,
	0x65, 0x77, 0x42, 0x65, 0x6e, 0x66, 0x53, 0x54, 0x12, 0x46, 0x0a, 0x1a, 0x69, 0x67, 0x73, 0x74,
	0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x66, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x17, 0x49, 0x47, 0x53, 0x54, 0x54, 0x61, 0x78,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x46, 0x0a, 0x1a, 0x63, 0x67, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x17, 0x43, 0x47, 0x53, 0x54, 0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69,
	0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x1a, 0x73, 0x67, 0x73, 0x74,
	0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x66, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x17, 0x53, 0x47, 0x53, 0x54, 0x54, 0x61, 0x78,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x46, 0x0a, 0x1a, 0x75, 0x67, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x17, 0x55, 0x47, 0x53, 0x54, 0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69,
	0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x0c, 0x73, 0x72, 0x76, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x5f, 0x73, 0x6f, 0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0a, 0x73, 0x72, 0x76, 0x50, 0x72,
	0x6f, 0x76, 0x53, 0x6f, 0x6c, 0x12, 0x22, 0x0a, 0x07, 0x62, 0x65, 0x6e, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x06, 0x62, 0x65, 0x6e, 0x66, 0x49, 0x44, 0x12, 0x31, 0x0a, 0x0f, 0x6f, 0x72, 0x69,
	0x67, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x61, 0x6d, 0x74, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0d, 0x6f,
	0x72, 0x69, 0x67, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x4d, 0x54, 0x12, 0x31, 0x0a, 0x0f,
	0x6f, 0x72, 0x69, 0x67, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x63, 0x63, 0x79, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x43, 0x59, 0x12,
	0x26, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x08, 0x66,
	0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x2d, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x5f,
	0x62, 0x32, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x42,
	0x32, 0x4b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x0a, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x08, 0x72,
	0x65, 0x71, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x31,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x0a, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x31, 0x12, 0x28, 0x0a, 0x0a,
	0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x32, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x09, 0x66, 0x72, 0x65,
	0x65, 0x54, 0x65, 0x78, 0x74, 0x32, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x33, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x33,
	0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x34, 0x18, 0x25,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52,
	0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x34, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x35, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x35, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x36, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x0a, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x36, 0x12, 0x28,
	0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x37, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x09, 0x66,
	0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x37, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x38, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78,
	0x74, 0x38, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x39,
	0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x0a, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x39, 0x12, 0x2a, 0x0a, 0x0b,
	0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x31, 0x30, 0x18, 0x2b, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x0a, 0x66, 0x72,
	0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x31, 0x30, 0x22, 0xd1, 0x03, 0x0a, 0x24, 0x49, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x53, 0x54, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x31, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x31, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x32, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x33, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x34, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x35, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x35, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x36, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x36, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x37, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x37, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x38, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x38, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x39, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x39, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x31, 0x30, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x31, 0x30, 0x12, 0x5d, 0x0a,
	0x0c, 0x65, 0x72, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0d, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xbd, 0x01, 0x0a,
	0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x8c, 0x01, 0x0a,
	0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x5a, 0x44, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescOnce sync.Once
	file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescData = file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDesc
)

func file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescGZIP() []byte {
	file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescOnce.Do(func() {
		file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescData)
	})
	return file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDescData
}

var file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_vendors_federal_internationalfundtransfer_gst_proto_goTypes = []interface{}{
	(*InquireOrReportGSTCollectionRequest)(nil),  // 0: vendors.federal.internationalfundtransfer.InquireOrReportGSTCollectionRequest
	(*InquireOrReportGSTCollectionResponse)(nil), // 1: vendors.federal.internationalfundtransfer.InquireOrReportGSTCollectionResponse
	(*ErrorResponse)(nil),                        // 2: vendors.federal.internationalfundtransfer.ErrorResponse
}
var file_api_vendors_federal_internationalfundtransfer_gst_proto_depIdxs = []int32{
	2, // 0: vendors.federal.internationalfundtransfer.InquireOrReportGSTCollectionResponse.err_response:type_name -> vendors.federal.internationalfundtransfer.ErrorResponse
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_vendors_federal_internationalfundtransfer_gst_proto_init() }
func file_api_vendors_federal_internationalfundtransfer_gst_proto_init() {
	if File_api_vendors_federal_internationalfundtransfer_gst_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquireOrReportGSTCollectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquireOrReportGSTCollectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_federal_internationalfundtransfer_gst_proto_goTypes,
		DependencyIndexes: file_api_vendors_federal_internationalfundtransfer_gst_proto_depIdxs,
		MessageInfos:      file_api_vendors_federal_internationalfundtransfer_gst_proto_msgTypes,
	}.Build()
	File_api_vendors_federal_internationalfundtransfer_gst_proto = out.File
	file_api_vendors_federal_internationalfundtransfer_gst_proto_rawDesc = nil
	file_api_vendors_federal_internationalfundtransfer_gst_proto_goTypes = nil
	file_api_vendors_federal_internationalfundtransfer_gst_proto_depIdxs = nil
}
