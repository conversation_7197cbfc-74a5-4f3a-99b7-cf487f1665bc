package mapper

import (
	"encoding/xml"

	"github.com/epifi/gamma/api/vendors/federal/upi"
)

type RespMapperConfirmationRequest struct {
	XMLName           xml.Name                                  `xml:"fed:UPIPaymentReq"`
	XmlnsFed          string                                    `xml:"xmlns:fed,attr"`
	XmlnsUpi          string                                    `xml:"xmlns:upi,attr"`
	XmlnsXsi          string                                    `xml:"xmlns:xsi,attr"`
	XsiSchemalocation string                                    `xml:"xsi:schemaLocation,attr"`
	MerchantHeader    upi.MerchantHeader                        `xml:"fed:MerchantHeader"`
	MerchantBody      respMapperConfirmationRequestMerchantBody `xml:"fed:MerchantBody"`
}

type respMapperConfirmationRequestMerchantBody struct {
	RespMapperConfirmation respMapperConfirmation `xml:"fed:RespMapperConfirmation"`
}

type respMapperConfirmation struct {
	XmlnsNS1 string          `xml:"xmlns:NS1,attr"`
	Head     upi.Head        `xml:"Head"`
	Txn      upi.Transaction `xml:"Txn"`
	Resp     upi.Resp        `xml:"Resp"`
}
