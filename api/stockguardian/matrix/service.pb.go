// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/matrix/service.proto

package matrix

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StartCustomerApplicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId     string            `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ClientReqId     string            `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	OrchFlow        OrchestrationFlow `protobuf:"varint,3,opt,name=orch_flow,json=orchFlow,proto3,enum=matrix.OrchestrationFlow" json:"orch_flow,omitempty"`
	OverrideOptions *OverrideOptions  `protobuf:"bytes,4,opt,name=override_options,json=overrideOptions,proto3" json:"override_options,omitempty"`
}

func (x *StartCustomerApplicationRequest) Reset() {
	*x = StartCustomerApplicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartCustomerApplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCustomerApplicationRequest) ProtoMessage() {}

func (x *StartCustomerApplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCustomerApplicationRequest.ProtoReflect.Descriptor instead.
func (*StartCustomerApplicationRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_service_proto_rawDescGZIP(), []int{0}
}

func (x *StartCustomerApplicationRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *StartCustomerApplicationRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *StartCustomerApplicationRequest) GetOrchFlow() OrchestrationFlow {
	if x != nil {
		return x.OrchFlow
	}
	return OrchestrationFlow_ORCHESTRATION_FLOW_UNSPECIFIED
}

func (x *StartCustomerApplicationRequest) GetOverrideOptions() *OverrideOptions {
	if x != nil {
		return x.OverrideOptions
	}
	return nil
}

type StartCustomerApplicationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	CurrentStage  Stage       `protobuf:"varint,3,opt,name=current_stage,json=currentStage,proto3,enum=matrix.Stage" json:"current_stage,omitempty"`
	StageStatus   StageStatus `protobuf:"varint,4,opt,name=stage_status,json=stageStatus,proto3,enum=matrix.StageStatus" json:"stage_status,omitempty"`
	NextAction    Action      `protobuf:"varint,5,opt,name=next_action,json=nextAction,proto3,enum=matrix.Action" json:"next_action,omitempty"`
}

func (x *StartCustomerApplicationResponse) Reset() {
	*x = StartCustomerApplicationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartCustomerApplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartCustomerApplicationResponse) ProtoMessage() {}

func (x *StartCustomerApplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartCustomerApplicationResponse.ProtoReflect.Descriptor instead.
func (*StartCustomerApplicationResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_service_proto_rawDescGZIP(), []int{1}
}

func (x *StartCustomerApplicationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *StartCustomerApplicationResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *StartCustomerApplicationResponse) GetCurrentStage() Stage {
	if x != nil {
		return x.CurrentStage
	}
	return Stage_STAGE_UNSPECIFIED
}

func (x *StartCustomerApplicationResponse) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *StartCustomerApplicationResponse) GetNextAction() Action {
	if x != nil {
		return x.NextAction
	}
	return Action_ACTION_UNSPECIFIED
}

type GetCustomerApplicationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ApplicationIdentifier:
	//	*GetCustomerApplicationStatusRequest_ApplicationId
	//	*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_
	ApplicationIdentifier isGetCustomerApplicationStatusRequest_ApplicationIdentifier `protobuf_oneof:"ApplicationIdentifier"`
}

func (x *GetCustomerApplicationStatusRequest) Reset() {
	*x = GetCustomerApplicationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationStatusRequest) ProtoMessage() {}

func (x *GetCustomerApplicationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationStatusRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_service_proto_rawDescGZIP(), []int{2}
}

func (m *GetCustomerApplicationStatusRequest) GetApplicationIdentifier() isGetCustomerApplicationStatusRequest_ApplicationIdentifier {
	if m != nil {
		return m.ApplicationIdentifier
	}
	return nil
}

func (x *GetCustomerApplicationStatusRequest) GetApplicationId() string {
	if x, ok := x.GetApplicationIdentifier().(*GetCustomerApplicationStatusRequest_ApplicationId); ok {
		return x.ApplicationId
	}
	return ""
}

func (x *GetCustomerApplicationStatusRequest) GetApplicantIdAndClientReqId() *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId {
	if x, ok := x.GetApplicationIdentifier().(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_); ok {
		return x.ApplicantIdAndClientReqId
	}
	return nil
}

type isGetCustomerApplicationStatusRequest_ApplicationIdentifier interface {
	isGetCustomerApplicationStatusRequest_ApplicationIdentifier()
}

type GetCustomerApplicationStatusRequest_ApplicationId struct {
	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3,oneof"`
}

type GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_ struct {
	ApplicantIdAndClientReqId *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId `protobuf:"bytes,2,opt,name=applicant_id_and_client_req_id,json=applicantIdAndClientReqId,proto3,oneof"`
}

func (*GetCustomerApplicationStatusRequest_ApplicationId) isGetCustomerApplicationStatusRequest_ApplicationIdentifier() {
}

func (*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_) isGetCustomerApplicationStatusRequest_ApplicationIdentifier() {
}

type GetCustomerApplicationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationStatus ApplicationStatus `protobuf:"varint,2,opt,name=application_status,json=applicationStatus,proto3,enum=matrix.ApplicationStatus" json:"application_status,omitempty"`
	CurrentStage      Stage             `protobuf:"varint,3,opt,name=current_stage,json=currentStage,proto3,enum=matrix.Stage" json:"current_stage,omitempty"`
	StageStatus       StageStatus       `protobuf:"varint,4,opt,name=stage_status,json=stageStatus,proto3,enum=matrix.StageStatus" json:"stage_status,omitempty"`
	NextAction        Action            `protobuf:"varint,5,opt,name=next_action,json=nextAction,proto3,enum=matrix.Action" json:"next_action,omitempty"`
}

func (x *GetCustomerApplicationStatusResponse) Reset() {
	*x = GetCustomerApplicationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationStatusResponse) ProtoMessage() {}

func (x *GetCustomerApplicationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationStatusResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCustomerApplicationStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCustomerApplicationStatusResponse) GetApplicationStatus() ApplicationStatus {
	if x != nil {
		return x.ApplicationStatus
	}
	return ApplicationStatus_APPLICATION_STATUS_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetCurrentStage() Stage {
	if x != nil {
		return x.CurrentStage
	}
	return Stage_STAGE_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetStageStatus() StageStatus {
	if x != nil {
		return x.StageStatus
	}
	return StageStatus_STAGE_STATUS_UNSPECIFIED
}

func (x *GetCustomerApplicationStatusResponse) GetNextAction() Action {
	if x != nil {
		return x.NextAction
	}
	return Action_ACTION_UNSPECIFIED
}

type GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicantId string `protobuf:"bytes,1,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) Reset() {
	*x = GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_matrix_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) ProtoMessage() {}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_matrix_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId.ProtoReflect.Descriptor instead.
func (*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_matrix_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

var File_api_stockguardian_matrix_service_proto protoreflect.FileDescriptor

var file_api_stockguardian_matrix_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x01, 0x0a, 0x1f, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69,
	0x78, 0x2e, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x08, 0x6f, 0x72, 0x63, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x42, 0x0a,
	0x10, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78,
	0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x8b, 0x02, 0x0a, 0x20, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f,
	0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xd8, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x8a, 0x01, 0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x48, 0x00, 0x52, 0x19, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x41,
	0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x1a, 0x62, 0x0a,
	0x19, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x42, 0x17, 0x0a, 0x15, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xb2, 0x02, 0x0a, 0x24, 0x47,
	0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x32, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x6d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f,
	0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0xf2, 0x01, 0x0a, 0x06, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x12, 0x6d, 0x0a, 0x18, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x2e, 0x6d, 0x61, 0x74, 0x72,
	0x69, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_matrix_service_proto_rawDescOnce sync.Once
	file_api_stockguardian_matrix_service_proto_rawDescData = file_api_stockguardian_matrix_service_proto_rawDesc
)

func file_api_stockguardian_matrix_service_proto_rawDescGZIP() []byte {
	file_api_stockguardian_matrix_service_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_matrix_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_matrix_service_proto_rawDescData)
	})
	return file_api_stockguardian_matrix_service_proto_rawDescData
}

var file_api_stockguardian_matrix_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_stockguardian_matrix_service_proto_goTypes = []interface{}{
	(*StartCustomerApplicationRequest)(nil),                               // 0: matrix.StartCustomerApplicationRequest
	(*StartCustomerApplicationResponse)(nil),                              // 1: matrix.StartCustomerApplicationResponse
	(*GetCustomerApplicationStatusRequest)(nil),                           // 2: matrix.GetCustomerApplicationStatusRequest
	(*GetCustomerApplicationStatusResponse)(nil),                          // 3: matrix.GetCustomerApplicationStatusResponse
	(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId)(nil), // 4: matrix.GetCustomerApplicationStatusRequest.ApplicantIdAndClientReqId
	(OrchestrationFlow)(0),                                                // 5: matrix.OrchestrationFlow
	(*OverrideOptions)(nil),                                               // 6: matrix.OverrideOptions
	(*rpc.Status)(nil),                                                    // 7: rpc.Status
	(Stage)(0),                                                            // 8: matrix.Stage
	(StageStatus)(0),                                                      // 9: matrix.StageStatus
	(Action)(0),                                                           // 10: matrix.Action
	(ApplicationStatus)(0),                                                // 11: matrix.ApplicationStatus
}
var file_api_stockguardian_matrix_service_proto_depIdxs = []int32{
	5,  // 0: matrix.StartCustomerApplicationRequest.orch_flow:type_name -> matrix.OrchestrationFlow
	6,  // 1: matrix.StartCustomerApplicationRequest.override_options:type_name -> matrix.OverrideOptions
	7,  // 2: matrix.StartCustomerApplicationResponse.status:type_name -> rpc.Status
	8,  // 3: matrix.StartCustomerApplicationResponse.current_stage:type_name -> matrix.Stage
	9,  // 4: matrix.StartCustomerApplicationResponse.stage_status:type_name -> matrix.StageStatus
	10, // 5: matrix.StartCustomerApplicationResponse.next_action:type_name -> matrix.Action
	4,  // 6: matrix.GetCustomerApplicationStatusRequest.applicant_id_and_client_req_id:type_name -> matrix.GetCustomerApplicationStatusRequest.ApplicantIdAndClientReqId
	7,  // 7: matrix.GetCustomerApplicationStatusResponse.status:type_name -> rpc.Status
	11, // 8: matrix.GetCustomerApplicationStatusResponse.application_status:type_name -> matrix.ApplicationStatus
	8,  // 9: matrix.GetCustomerApplicationStatusResponse.current_stage:type_name -> matrix.Stage
	9,  // 10: matrix.GetCustomerApplicationStatusResponse.stage_status:type_name -> matrix.StageStatus
	10, // 11: matrix.GetCustomerApplicationStatusResponse.next_action:type_name -> matrix.Action
	0,  // 12: matrix.Matrix.StartCustomerApplication:input_type -> matrix.StartCustomerApplicationRequest
	2,  // 13: matrix.Matrix.GetCustomerApplicationStatus:input_type -> matrix.GetCustomerApplicationStatusRequest
	1,  // 14: matrix.Matrix.StartCustomerApplication:output_type -> matrix.StartCustomerApplicationResponse
	3,  // 15: matrix.Matrix.GetCustomerApplicationStatus:output_type -> matrix.GetCustomerApplicationStatusResponse
	14, // [14:16] is the sub-list for method output_type
	12, // [12:14] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_stockguardian_matrix_service_proto_init() }
func file_api_stockguardian_matrix_service_proto_init() {
	if File_api_stockguardian_matrix_service_proto != nil {
		return
	}
	file_api_stockguardian_matrix_enums_proto_init()
	file_api_stockguardian_matrix_internal_customer_application_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_matrix_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartCustomerApplicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_matrix_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartCustomerApplicationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_matrix_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_matrix_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_matrix_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_stockguardian_matrix_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetCustomerApplicationStatusRequest_ApplicationId)(nil),
		(*GetCustomerApplicationStatusRequest_ApplicantIdAndClientReqId_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_matrix_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_stockguardian_matrix_service_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_matrix_service_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_matrix_service_proto_msgTypes,
	}.Build()
	File_api_stockguardian_matrix_service_proto = out.File
	file_api_stockguardian_matrix_service_proto_rawDesc = nil
	file_api_stockguardian_matrix_service_proto_goTypes = nil
	file_api_stockguardian_matrix_service_proto_depIdxs = nil
}
