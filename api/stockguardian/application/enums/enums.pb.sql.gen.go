// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/stockguardian/application/enums/enums.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the LoanApplicationStatus in string format in DB
func (p LoanApplicationStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationStatus while reading from DB
func (p *LoanApplicationStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationStatus value: %s", val)
	}
	*p = LoanApplicationStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationStatus
func (x LoanApplicationStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationStatus
func (x *LoanApplicationStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationStatus(LoanApplicationStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicationSubStatus in string format in DB
func (p LoanApplicationSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationSubStatus while reading from DB
func (p *LoanApplicationSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationSubStatus value: %s", val)
	}
	*p = LoanApplicationSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationSubStatus
func (x LoanApplicationSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationSubStatus
func (x *LoanApplicationSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationSubStatus(LoanApplicationSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicationStageName in string format in DB
func (p LoanApplicationStageName) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationStageName while reading from DB
func (p *LoanApplicationStageName) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationStageName_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationStageName value: %s", val)
	}
	*p = LoanApplicationStageName(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationStageName
func (x LoanApplicationStageName) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationStageName
func (x *LoanApplicationStageName) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationStageName(LoanApplicationStageName_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicationStageStatus in string format in DB
func (p LoanApplicationStageStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationStageStatus while reading from DB
func (p *LoanApplicationStageStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationStageStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationStageStatus value: %s", val)
	}
	*p = LoanApplicationStageStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationStageStatus
func (x LoanApplicationStageStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationStageStatus
func (x *LoanApplicationStageStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationStageStatus(LoanApplicationStageStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the LoanApplicationStageSubStatus in string format in DB
func (p LoanApplicationStageSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing LoanApplicationStageSubStatus while reading from DB
func (p *LoanApplicationStageSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := LoanApplicationStageSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected LoanApplicationStageSubStatus value: %s", val)
	}
	*p = LoanApplicationStageSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for LoanApplicationStageSubStatus
func (x LoanApplicationStageSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for LoanApplicationStageSubStatus
func (x *LoanApplicationStageSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = LoanApplicationStageSubStatus(LoanApplicationStageSubStatus_value[val])
	return nil
}
