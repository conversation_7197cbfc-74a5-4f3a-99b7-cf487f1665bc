// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/internationalfundtransfer/consumer/service.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GenerateSofLimitStrategiesValuesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateSofLimitStrategiesValuesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateSofLimitStrategiesValuesRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GenerateSofLimitStrategiesValuesRequestMultiError, or nil if none found.
func (m *GenerateSofLimitStrategiesValuesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateSofLimitStrategiesValuesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateSofLimitStrategiesValuesRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateSofLimitStrategiesValuesRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateSofLimitStrategiesValuesRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetSofId()) < 1 {
		err := GenerateSofLimitStrategiesValuesRequestValidationError{
			field:  "SofId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GenerateSofLimitStrategiesValuesRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GenerateSofLimitStrategiesValuesRequestMultiError(errors)
	}

	return nil
}

// GenerateSofLimitStrategiesValuesRequestMultiError is an error wrapping
// multiple validation errors returned by
// GenerateSofLimitStrategiesValuesRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateSofLimitStrategiesValuesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateSofLimitStrategiesValuesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateSofLimitStrategiesValuesRequestMultiError) AllErrors() []error { return m }

// GenerateSofLimitStrategiesValuesRequestValidationError is the validation
// error returned by GenerateSofLimitStrategiesValuesRequest.Validate if the
// designated constraints aren't met.
type GenerateSofLimitStrategiesValuesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateSofLimitStrategiesValuesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateSofLimitStrategiesValuesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateSofLimitStrategiesValuesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateSofLimitStrategiesValuesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateSofLimitStrategiesValuesRequestValidationError) ErrorName() string {
	return "GenerateSofLimitStrategiesValuesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateSofLimitStrategiesValuesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateSofLimitStrategiesValuesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateSofLimitStrategiesValuesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateSofLimitStrategiesValuesRequestValidationError{}

// Validate checks the field values on GenerateSofLimitStrategiesValuesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateSofLimitStrategiesValuesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateSofLimitStrategiesValuesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GenerateSofLimitStrategiesValuesResponseMultiError, or nil if none found.
func (m *GenerateSofLimitStrategiesValuesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateSofLimitStrategiesValuesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateSofLimitStrategiesValuesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateSofLimitStrategiesValuesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateSofLimitStrategiesValuesResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateSofLimitStrategiesValuesResponseMultiError(errors)
	}

	return nil
}

// GenerateSofLimitStrategiesValuesResponseMultiError is an error wrapping
// multiple validation errors returned by
// GenerateSofLimitStrategiesValuesResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateSofLimitStrategiesValuesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateSofLimitStrategiesValuesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateSofLimitStrategiesValuesResponseMultiError) AllErrors() []error { return m }

// GenerateSofLimitStrategiesValuesResponseValidationError is the validation
// error returned by GenerateSofLimitStrategiesValuesResponse.Validate if the
// designated constraints aren't met.
type GenerateSofLimitStrategiesValuesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateSofLimitStrategiesValuesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateSofLimitStrategiesValuesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateSofLimitStrategiesValuesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateSofLimitStrategiesValuesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateSofLimitStrategiesValuesResponseValidationError) ErrorName() string {
	return "GenerateSofLimitStrategiesValuesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateSofLimitStrategiesValuesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateSofLimitStrategiesValuesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateSofLimitStrategiesValuesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateSofLimitStrategiesValuesResponseValidationError{}
