package emailparser

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"log"
)

// MarshalJSON implements the Marshaler interface
func (m QueryType) MarshalJSON() ([]byte, error) {
	str := m.String()
	return json.Marshal(str)
}

// UnmarshalJSON implements the Unmarshaler interface
func (m *QueryType) UnmarshalJson(data []byte) error {
	var val string
	err := json.Unmarshal(data, &val)
	if err != nil {
		return err
	}

	valInt, ok := QueryType_value[val]
	if !ok {
		return fmt.Errorf("unexpected value: %s", val)
	}
	*m = QueryType(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (m QueryType) Value() (driver.Value, error) {
	return m.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (m *QueryType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := QueryType_value[val]
	if !ok {
		log.Printf("encountered unknown QueryType : %v", val)
	}
	*m = QueryType(valInt)
	return nil
}
