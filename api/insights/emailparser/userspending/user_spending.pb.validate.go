// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/emailparser/userspending/user_spending.proto

package userspending

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetAmountAndOrderCountQueryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAmountAndOrderCountQueryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmountAndOrderCountQueryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAmountAndOrderCountQueryResponseMultiError, or nil if none found.
func (m *GetAmountAndOrderCountQueryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmountAndOrderCountQueryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Merchant

	// no validation rules for TransactionType

	// no validation rules for Amount

	// no validation rules for Count

	if len(errors) > 0 {
		return GetAmountAndOrderCountQueryResponseMultiError(errors)
	}

	return nil
}

// GetAmountAndOrderCountQueryResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAmountAndOrderCountQueryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAmountAndOrderCountQueryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmountAndOrderCountQueryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmountAndOrderCountQueryResponseMultiError) AllErrors() []error { return m }

// GetAmountAndOrderCountQueryResponseValidationError is the validation error
// returned by GetAmountAndOrderCountQueryResponse.Validate if the designated
// constraints aren't met.
type GetAmountAndOrderCountQueryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmountAndOrderCountQueryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmountAndOrderCountQueryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmountAndOrderCountQueryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmountAndOrderCountQueryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmountAndOrderCountQueryResponseValidationError) ErrorName() string {
	return "GetAmountAndOrderCountQueryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmountAndOrderCountQueryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmountAndOrderCountQueryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmountAndOrderCountQueryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmountAndOrderCountQueryResponseValidationError{}
