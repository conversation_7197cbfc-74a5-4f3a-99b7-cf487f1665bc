package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "debitcard_auto_resolution")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToSecret, err1 := cfg.LoadSecrets(nil, conf.Application.Environment, conf.Aws.Region)
	if err1 != nil {
		return nil, err1
	}

	updateDefaultConfig(conf, keyToSecret)

	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) {
	readAndSetEnv(c)
	cfg.UpdateSecretValues(&cfg.DB{GormV2: &cfg.GormV2Conf{}}, c.Secrets, keyToSecret)
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}
}

type Config struct {
	Application            *application
	Secrets                *cfg.Secrets
	Aws                    *cfg.AWS
	ProcessTicketJobConfig *cfg.CxTicketAutoResolutionJobConfig
}

type application struct {
	Environment string
	Name        string
}
