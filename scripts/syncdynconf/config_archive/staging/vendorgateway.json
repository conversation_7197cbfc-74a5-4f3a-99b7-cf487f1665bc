{"DowntimeConfig": {"NSDL": {"Downtime": {"DailyDownTime": {"IsEnable": false, "StartTime": "00:30", "EndTime": "04:30", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}, "TimestampBasedDownTime": {"IsEnable": false, "StartTimestamp": "2022-03-15 15:04:05", "EndTimestamp": "2022-03-16 15:04:05", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}}}, "CVLKRA": {"Downtime": {"DailyDownTime": {"IsEnable": false, "StartTime": "00:30", "EndTime": "04:30", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}, "TimestampBasedDownTime": {"IsEnable": false, "StartTimestamp": "2021-11-01 15:04:05", "EndTimestamp": "2021-11-02 15:04:05", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}}}, "CKYC": {"Downtime": {"DailyDownTime": {"IsEnable": false, "StartTime": "00:30", "EndTime": "04:30", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}, "TimestampBasedDownTime": {"IsEnable": false, "StartTimestamp": "2021-03-15 15:04:0", "EndTimestamp": "2022-08-16 12:00:00", "Msg": "This is due to a downtime at our vendor partner. This should be up by %s. We will notify you once its up."}}}, "DIGILOCKER": {"Downtime": {"DailyDownTime": {"IsEnable": false, "StartTime": "00:30", "EndTime": "04:30", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}, "TimestampBasedDownTime": {"IsEnable": false, "StartTimestamp": "2021-11-01 15:04:05", "EndTimestamp": "2021-11-02 15:04:05", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}}}, "MANCH": {"Downtime": {"DailyDownTime": {"IsEnable": false, "StartTime": "00:30", "EndTime": "04:30", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}, "TimestampBasedDownTime": {"IsEnable": true, "StartTimestamp": "2022-09-26 11:00:00", "EndTimestamp": "2022-09-26 11:20:00", "Msg": "Our systems are under maintenance from %s to %s. Please retry after sometime."}}}}, "Application": {"AA": {"BaseURL": "https://simulator.staging.pointz.in:8080", "PostConsentURL": "/Consent", "ConsentStatusURL": "/Consent/handle", "ConsentArtefactURL": "/Consent", "RequestDataURL": "/FI/request", "FetchDataURL": "/FI/fetch", "GenerateAccessTokenURL": "https://uattokens.sahamati.org.in/auth/realms/sahamati/protocol/openid-connect/token", "FetchCrEntityDetailURL": "https://uatcr.sahamati.org.in/entityInfo/AA", "GetAccountLinkStatusURL": "/Account/link/status", "AccountDeLinkURL": "/account/delink", "ConsentUpdateURL": "/consent/update", "UseSahamatiCrAndToken": false, "OneMoneyCrId": "onemoney-aa", "FinvuCrId": "<EMAIL>", "GetAccountLinkStatusBulkURL": "/Account/link/Status", "GetHeartbeatStatusURL": "/Heartbeat", "GenerateFinvuJwtTokenURL": "/web/token", "GetBulkConsentRequestURL": "/Consent/status/bulk", "AAClientApiKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.A-VX3lgu6T_r2FWIp2bsDAQK9vll6p4uQC_D5LwXmdo", "SahamatiClientId": "EPIFIUAT", "EpifiAaKid": "654024c8-29c8-11e8-8868-0289437bf331", "AaSecretsVersionToUse": "V1"}}}