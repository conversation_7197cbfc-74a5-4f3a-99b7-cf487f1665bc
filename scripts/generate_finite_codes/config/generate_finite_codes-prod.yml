Application:
  Environment: "prod"
  Name: "generate_finite_codes"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 10
  MaxIdleConn: 10
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"
