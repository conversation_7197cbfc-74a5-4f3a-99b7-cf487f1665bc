#!/bin/bash
set -e

GOMODCACHE=$(go env GOMODCACHE)
BE_COMMON_VERSION=$(go list -m -f '{{.Version}}' github.com/epifi/be-common)
BE_COMMON_PATH=$GOMODCACHE/github.com/epifi/be-common@$BE_COMMON_VERSION

validate_go_changes() {
	# if modified files are not provided, use pr number
	if [ $# -eq 0 ]; then
		if [ -z "$PR_NUMBER" ]; then
			echo "PR_NUMBER is not set"
			exit 1
		fi
		echo "fetching PR diff for PR $PR_NUMBER..."
		modifiedDirs=$(gh pr diff "${PR_NUMBER}" --name-only | xargs -I % dirname % | uniq)
	else
		modifiedDirs=$(echo $@ | xargs -n1 dirname | uniq )
	fi
	modifiedDirs=$(echo "$modifiedDirs" | tr ' ' '\n')
	GOMAXPROCS=$(gomaxprocs)

	echo "found modified files: ${modifiedDirs}"

	echo "finding impacted packages..."
	# shellcheck disable=SC2128
    output=$(deps impacteddirs --modified_dirs="$modifiedDirs")

	# Check if the output is valid JSON
    if ! echo "$output" | jq empty; then
      echo "Error: Invalid JSON output"
      echo "output: $output"
      exit 1
    fi
	trimmedImpactedDirs=$(echo "$output" | jq -r '.trimmedImpactedDirs // empty')
	trimmedImpactedGoTestDirs=$(echo "$output" | jq -r '.trimmedImpactedGoTestDirs // empty')
	impactedDirs=$(echo "$output" | jq -r '.impactedDirs // [] | join(" ")')
	impactedGoTestDirs=$(echo "$output" | jq -r '.impactedGoTestDirs // [] | join(" ")')

    if [ -z "$trimmedImpactedDirs" ] && [ -z "$trimmedImpactedGoTestDirs" ]; then
    		echo "No impacted packages found... returning successfully"
    		return
	elif [ -z "$trimmedImpactedDirs" ]; then
		echo "No impacted go packages found..."
	elif [ -z "$trimmedImpactedGoTestDirs" ]; then
		echo "No impacted go test packages found..."
    fi
	echo "building impacted paths..."
	# For some reason, the command fails only when it run via makefile/script for mix of packages with/without main file
	# and not when run directly in terminal. TODO(sakthi) debug and understand this behavior.
	# So, using /dev/null as a short term fix not everyone in the community agrees to it. https://github.com/golang/go/issues/37378
	# TODO(sakthi) Fix this issue and remove /dev/null after golang supports better alternative
	if [ -n "$trimmedImpactedDirs" ]; then
		# Check if gofork is available
		IFS=' ' read -r -a impactedDirsArray <<< "$impactedDirs"

		batch=()
		for dir in "${impactedDirsArray[@]}"; do
		  batch+=("$dir")
		  if [ ${#batch[@]} -eq 50 ]; then
			  build_dirs_in_batch "${batch[@]}"
			  batch=()
		  fi
		done
		# Process any remaining directories
		if [ ${#batch[@]} -gt 0 ]; then
		  build_dirs_in_batch "${batch[@]}"
		fi
    fi

	# Commenting for now till we find better solution as its causing build app github action failure recently
	if [ -n "$trimmedImpactedGoTestDirs" ]; then
	  IFS=' ' read -r -a impactedGoTestDirsArray <<< "$impactedGoTestDirs"

	  batch=()
	  for dir in "${impactedGoTestDirsArray[@]}"; do
		batch+=("$dir")
		if [ ${#batch[@]} -eq 50 ]; then
		  build_test_dirs_in_batch "${batch[@]}"
		  batch=()
		fi
	  done

	  if [ ${#batch[@]} -gt 0 ]; then
		build_test_dirs_in_batch "${batch[@]}"
	  fi
    fi
}

build_dirs_in_batch() {
	local batch=("$@")
	local batchString="${batch[*]}"

	# Check if gofork is available
	if command -v gofork &> /dev/null; then
		# Check if GOFORKROOT environment variable is set
		if [ -z "$GOFORKROOT" ]; then
			# If GOFORKROOT is not set, use the hardcoded value
			GOFORKROOT=$(go env GOPATH)/src/github.com/epifi/go
		fi
		echo "GOMAXPROCS=$GOMAXPROCS GOROOT=$GOFORKROOT gofork install -v -checkerr -mod=readonly -ldflags '-s' $batchString"
		GOMAXPROCS=$GOMAXPROCS GOROOT=$GOFORKROOT gofork install -v -checkerr -mod=readonly -ldflags '-s' $batchString
	else
		echo "GOMAXPROCS=$GOMAXPROCS CGO_ENABLED=0 go build -v -mod=readonly -o /dev/null -ldflags '-s' $batchString"
		GOMAXPROCS=$GOMAXPROCS go build -v -mod=readonly -o /dev/null -ldflags '-s' $batchString
	fi
}

build_test_dirs_in_batch() {
  local batch=("$@")
  local batchString="${batch[*]}"
  echo "GOMAXPROCS=$GOMAXPROCS xgo validate -f=\"-mod=readonly\" $batchString"
  GOMAXPROCS=$GOMAXPROCS xgo validate -f="-mod=readonly" $batchString
}

build_servers() {
	servers_input_str=$1
	env=$2
    profileFilePath=$3
	echo "building servers"

	IFS=', ' read -r -a servers <<< "${servers_input_str}"
	serverString=""
	if [[ -z "${env}" ]]; then
		echo "Env not passed while building generated server...Exiting..."
		exit 1
	fi
	for server in "${servers[@]}"; do
		serverString+="./cmd/servers/${env}/$server "
		setup_server_output_dir "$server" "$env"
	done
	if [[ $profileFilePath ]]; then
	GOBIN=$(pwd)/output/binaries GOPROXY=https://goproxy.pointz.in CGO_ENABLED=0 go install -trimpath -pgo=$profileFilePath -mod=readonly -ldflags '-s' $serverString;
    else
	GOBIN=$(pwd)/output/binaries GOPROXY=https://goproxy.pointz.in CGO_ENABLED=0 go install -trimpath -mod=readonly -ldflags '-s' $serverString;
	fi
	for server in "${servers[@]}"; do
		mv ./output/binaries/$server ./output/$server/${server}_bin
	done

	set +x
}

build_go_cache() {
	set -x
	GOPROXY=https://goproxy.pointz.in CGO_ENABLED=0 go install -trimpath -mod=readonly -ldflags '-s' $(go list ./cmd/servers/qa/... ./cmd/worker/... | grep -E '(/server$|/worker$)');
	set +x
}


build_workers() {
	echo "building workers"
	set -x
	IFS=', ' read -r -a workers <<< "$1"
	workerString=""
	for worker in "${workers[@]}"; do
		workerString+="./cmd/worker/$worker "
		setup_worker_output_dir "$worker"
	done
	GOBIN=$(pwd)/output/worker/binaries CGO_ENABLED=0 go install -trimpath -mod=readonly -ldflags '-s' $workerString;
	for worker in "${workers[@]}"; do
		mv ./output/worker/binaries/$worker ./output/worker/$worker/${worker}_bin
	done

	set +x
}


# setup_server_output_dir creates an output directory for the server and copies all the config files
setup_server_output_dir() {
		server=$1
		env=$2

		# Create the target output directory
		mkdir -p ./output/$server/config
		mkdir -p ./output/$server/config/server
		mkdir -p ./output/$server/config/pkg/pay/payerrorcode
		mkdir -p ./output/$server/config/pkg/pay/pgauthkeys
		mkdir -p ./output/$server/config/pkg/pay/pgerrorcodes
		cp owners.yml ./output/$server/config/

		# Copy the required server and service group configs
		# Copy all server configs to config/server directory
		cp -rL ./cmd/servers/config/*.yml ./output/$server/config/server 2>/dev/null || :
		cp -rL ./cmd/servers/**/$server/config/*.yml ./output/$server/config/server 2>/dev/null || :
		# Tenant configs
		cp -rL ./cmd/servers/**/$server/config/**/*.yml ./output/$server/config/server 2>/dev/null || :

		# Copy the service group configs
        servergen_config_filepath="${BE_COMMON_PATH}/pkg/cfg/config/server-definition-${env}.yml"
		gamma_pkg_prefix="github.com/epifi/gamma/"
		n_service_groups=$(yq ".Servers.${server}.ServiceGroups | length" < "${servergen_config_filepath}")

		for ((i = 0; i < ${n_service_groups}; i++)); do
			service_group=$(yq ".Servers.${server}.ServiceGroups[${i}].ServiceGroup" < "${servergen_config_filepath}")
			config_dir_path=$(yq ".Servers.${server}.ServiceGroups[${i}].ConfPkgPath" < "${servergen_config_filepath}")

			if [[ "${config_dir_path}" == null ]]; then
				config_dir_path="./${service_group}/config/values"
			else
				gamma_prefix_len=${#gamma_pkg_prefix}
				config_dir_path="./${config_dir_path:${gamma_prefix_len}}/values"
			fi

			cp -rL ${config_dir_path}/*.yml ./output/$server/config/ 2>/dev/null || :
			# copy tenant configs
            cp -rL ${config_dir_path}/**/*.yml ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/*crt ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/*key ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/policy_model.conf ./output/$server/config/ 2>/dev/null || :

			cp -rL ${config_dir_path}/crdb-demo ./output/$server/config/crdb 2>/dev/null || :
			chmod 600 ./output/$server/config/crdb/* 2>/dev/null || :

			cp -rL ${config_dir_path}/rules ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/stubs ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/data ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/mappingCsv ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/mappingJson ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/skill_config_json ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/pdf-templates ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/slack-bot-reviewers ./output/$server/config/ 2>/dev/null || :
			cp -rL ${config_dir_path}/fonts ./output/$server/config/ 2>/dev/null || :
		done

    	# Copy common configs
    	cp -rL ./quest/sdk/init/config/*.yml ./output/$server/config/ 2>/dev/null || :
    	cp -rL ./pkg/cx/attachment/config/*.yml ./output/$server/config/ 2>/dev/null || :
    	cp -rL "${BE_COMMON_PATH}/pkg/cfg/config/"* ./output/$server/config/ || :
		chmod -R 755 ./output/$server/config/ || :
    	cp -rL ./pkg/pay/payerrorcode/payErrorViewMapping.json ./output/$server/config/pkg/pay/payerrorcode
    	cp -rL ./pkg/pay/pgauthkeys/*.json ./output/$server/config/pkg/pay/pgauthkeys/ 2>/dev/null || :
    	cp -rL "${BE_COMMON_PATH}/pkg/pay/pgerrorcodes/razorpayErrorResponseCodes.json" ./output/$server/config/pkg/pay/pgerrorcodes 2>/dev/null || :
}

# setup_worker_output_dir creates an output directory for the worker and copies all the config files
setup_worker_output_dir() {
		worker=$1
		echo "Building the target.."
		# the worker config files and server files will have collision if stored in the same directory
		# For this reason, config for workers are stored under <config_dir>/worker
		mkdir -p ./output/worker/${worker}/config/worker
		mkdir -p ./output/worker/${worker}/config/pkg/pay/pgerrorcodes
		cp -rL ./cmd/worker/${worker}/config/*.yml ./output/worker/${worker}/config/worker/ 2>/dev/null || :
		cp -rL ./quest/sdk/init/config/*.yml ./output/worker/${worker}/config/ 2>/dev/null || :
		cp -rL "${BE_COMMON_PATH}/pkg/cfg/config/"* ./output/worker/${worker}/config/ || :
		chmod -R 755 ./output/worker/${worker}/config/ || :
		cp -rL "${BE_COMMON_PATH}/pkg/pay/pgerrorcodes/razorpayErrorResponseCodes.json" ./output/worker/${worker}/config/pkg/pay/pgerrorcodes 2>/dev/null || :
}

# build_server_health_check build required servers and workers based on impacted paths
# binaries build here will be used for server health check
build_server_health_check(){
  modifiedDirsString=$(gh pr diff "${PR_NUMBER}" --name-only | xargs -I % dirname % | uniq)
  readarray -t modifiedDirs <<< "$modifiedDirsString"
  echo "Modified Directories: ${modifiedDirs[*]}"
  modifiedServers=()
  modifiedWorkers=()
  # extract servers based on directories impacted
  for dir in "${modifiedDirs[@]}"; do
    if [[ "$dir" == "cmd/"* ]]; then
      server=$(echo "$dir" | awk -F 'cmd/' '{print $2}' | awk -F '/' '{print $1}' | xargs )
      # ignoring workers
      if [[ "${server}" == "worker" || "${server}" == "servers" || "${server}" == "service_groups" ]]; then
        continue
      fi
      modifiedServers+=($server)
    fi
  done
  # remove duplicates
  modifiedServers=( `for server in ${modifiedServers[@]}; do echo $server; done | sort -u` )
  echo "Servers impacted: ${modifiedServers[*]}"
  # extract workers based on directories impacted
  for dir in "${modifiedDirs[@]}"; do
  	 if [[ "$dir" == "cmd/worker/pan"* ]]; then
               continue
      fi
     if [[ "$dir" == "cmd/worker/"* ]]; then
          worker=$(echo "$dir" | awk -F 'cmd/worker/' '{print $2}' | awk -F '/' '{print $1}')
          modifiedWorkers+=($worker)
      fi
  done
  # remove duplicates
  modifiedWorkers=( `for worker in ${modifiedWorkers[@]}; do echo $worker; done | sort -u` )
  echo "Workers impacted: ${modifiedWorkers[*]}"
  # create output directory for worker if not exists
  mkdir -p ./output/worker/${worker}
  modifiedServersString=$(IFS=, ; echo "${modifiedServers[*]}")
  modifiedWorkersString=$(IFS=, ; echo "${modifiedWorkers[*]}")
  echo "Servers string: ${modifiedServersString}"
  echo "Servers string: ${modifiedWorkersString}"
  if [[ "${modifiedServersString}" != "" ]]; then
  	build_servers "${modifiedServersString}" true test
  fi
  if [[ ${modifiedWorkersString} != "" ]]; then
  	build_workers "${modifiedWorkersString}"
  fi
}

case $1 in
validate_go_changes)
	# shellcheck disable=SC2068
	validate_go_changes ${@:2}
	;;
build_servers)
	build_servers ${@:2}
	;;
build_workers)
	build_workers ${@:2}
	;;
build_go_cache)
	build_go_cache
	;;
setup_server_output_dir)
	setup_server_output_dir ${@:2}
	;;
build_server_health_check)
  build_server_health_check
  ;;
setup_worker_output_dir)
	setup_worker_output_dir ${@:2}
	;;
*)
	echo "Invalid command"
	exit 1
	;;
esac
