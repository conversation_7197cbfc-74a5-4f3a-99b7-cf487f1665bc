package main

import (
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/kyc/vkyc"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

type ReRegisterUserForVKYC struct {
	vkycClient vkyc.VKYCClient
	kycConn    *grpc.ClientConn
}

var (
	vkycRecordNotFound []string
)

func (r *ReRegisterUserForVKYC) DoJob(ctx context.Context, req *JobRequest) error {
	// TODO (Rishu Sahu): Replace actor id list with pkg list
	var (
		startInd, _ = strconv.Atoi(req.Args1)
	)

	for i := startInd; i < len(vkycPkg.ReAttemptVKYC); i++ {
		if i%2000 == 0 || r.vkycClient == nil {
			r.createNewVKYCClient()
		}
		actorId := vkycPkg.ReAttemptVKYC[i]
		newCtx := epificontext.CtxWithActorId(ctx, actorId)
		logger.Info(ctx, fmt.Sprintf("calling ReRegisterVkycInfo for the actor id: %v", actorId))
		resp, err := r.vkycClient.ReRegisterVkycInfo(newCtx, &vkyc.ReRegisterVkycInfoRequest{
			Identifier: &vkyc.ReRegisterVkycInfoRequest_ActorId{
				ActorId: actorId,
			},
			CallInfoSubStatus: vkyc.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_RE_ATTEMPT,
			AttemptSubStatus:  vkyc.VKYCAttemptSubStatus_VKYC_ATTEMPT_SUB_STATUS_RE_ATTEMPT,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			if resp.GetStatus().IsRecordNotFound() {
				vkycRecordNotFound = append(vkycRecordNotFound, actorId)
				continue
			}
			logger.Error(ctx, "error in re registering user for vkyc", zap.Error(rpcErr))
			gotErrorForActors = append(gotErrorForActors, actorId)
			continue
		}
	}
	if len(vkycRecordNotFound) > 0 {
		fmt.Println("List of actor ids for vkyc record not found:")
		printUsers(vkycRecordNotFound)
	}
	if len(gotErrorForActors) > 0 {
		fmt.Println("List of actor ids for vkyc record not found:")
		printUsers(gotErrorForActors)
	}
	return nil
}

func (r *ReRegisterUserForVKYC) createNewVKYCClient() vkyc.VKYCClient {
	// Close the existing connection if it exists
	if r.kycConn != nil {
		epifigrpc.CloseConn(r.kycConn)
	}

	// Create a new gRPC connection
	r.kycConn = epifigrpc.NewConnByService(cfg.KYC_SERVICE)

	// Create a new client using the connection
	vkycClient := vkyc.NewVKYCClient(r.kycConn)
	r.vkycClient = vkycClient

	return vkycClient
}
