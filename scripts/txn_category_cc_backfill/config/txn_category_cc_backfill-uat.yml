Application:
  Environment: "uat"

Aws:
  Region: "ap-south-1"

CreditCardDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "categoriser-backfill"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CategoryCCTxnEventSqsPublisher:
  QueueName: "uat-search-cc-txn-event-consumer-queue"
