package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"flag"
	"fmt"

	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

func abflDealNumberBackfill() {
	flag.Parse()

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	palVgClient := palVgPb.NewAbflClient(vgConn)

	ctx := context.Background()

	accountIds := make(map[string]string)
	accountIds["AccntId95mJv8ScfxxB"] = "cb056178-0425-4e1a-a3af-adc0f95c78dd"
	accountIds["AccntId6qJdQgndZ4Zb"] = "639e92b6-669f-4900-a406-c14a46f99b6d"
	accountIds["AccntId5VBkjCsPABzF"] = "56db67c8-fce6-4b2e-af2e-2644773ffad7"
	accountIds["AccntId66bgaQYtf8fK"] = "1cb18a76-cf84-4c33-b374-146bdd146e0d"
	accountIds["AccntIdwEPRJ5vZFBa7"] = "6ceb4e3d-f9fa-4998-a9ed-e5351876221f"

	for accountId, loanUniqueId := range accountIds {
		res, err := palVgClient.LoanDisbursementStatus(ctx, &palVgPb.LoanDisbursementStatusRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
			AccountId: accountId,
			UniqueId:  loanUniqueId,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			fmt.Println("error in hitting verify pan", te, accountId)
			continue
		}

		fmt.Printf("deal number for accountId: %v, resp: %v :", accountId, res)
	}
}
