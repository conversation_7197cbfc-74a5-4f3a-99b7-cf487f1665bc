Application:
  Environment: "qa"
  Name: "ca_equity_backfill"

AWS:
  Region: "ap-south-1"

ConnectedAccountDb:
  AppName: "connectedaccount"
  DbType: "PGDB"
  StatementTimeout: 1m
  Name: "connected_account"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "qa/rds/epifimetis/connected_account_dev_user"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    ConnectedAccountDbUserNamePassword: "qa/rds/postgres14"
