package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/comms"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/scripts/presto_result_s3_uploader/config"

	"github.com/epifi/gamma/vendormapping/dao"
)

type DobDiscrepancyMailScheduler struct {
	conf             *config.Config
	usersClient      user.UsersClient
	actorClient      actor.ActorClient
	savingsClient    savingsPb.SavingsClient
	vendorMappingDao *dao.VendorMappingDao
	commsClient      comms.CommsClient
}

var (
	s3DobDiscrepnacyPath = "PRESTO/FIREHOSEID/DOB_UPDATE_DISCREPANCY_VKYC_FLOW/%v.csv"
)

func (v *DobDiscrepancyMailScheduler) GetQueryStorageInfo() []*QueryStorageInfo {
	args, query := dobDiscrepancyGetSnowflakeDetail()
	file := dobDiscrepancyGetS3File()
	var queryStorageInfos []*QueryStorageInfo
	queryStorageInfos = append(queryStorageInfos, &QueryStorageInfo{
		queryArgs:   args,
		prestoQuery: query,
		s3File:      file,
	})
	return queryStorageInfos
}

// return presto query and args
func dobDiscrepancyGetSnowflakeDetail() ([]interface{}, string) {
	// get all cases created 7 days before since job runs in every 7 days
	dt := time.Now().AddDate(0, 0, -7)
	dateTillFetch := dt.Format(inReviewDateFormat)
	// source should be vkyc
	args := []interface{}{"TRUE", "EKYC_SOURCE_VKYC", dateTillFetch}
	query := "select actor_id from kyc_attempts where CAST(JSON_EXTRACT(kyc_attempts.request_params, '$.dobDiscrepancyInfo.discrepancyExists') AS VARCHAR) = ? AND CAST(JSON_EXTRACT(kyc_attempts.request_params, '$.ekycSource') AS VARCHAR) = ? and CAST(created_at AS DATE) >= CAST(? AS DATE)"
	return args, query
}

// return s3 file name
func dobDiscrepancyGetS3File() string {
	s3OutputPathWithDate := fmt.Sprintf(s3DobDiscrepnacyPath, time.Now().Format("01-02-2006"))
	return s3OutputPathWithDate
}

type UserInfo struct {
	ActorId       string     `csv:"Actor Id"`
	AccountNumber string     `csv:"Account number"`
	EpifiDob      *date.Date `csv:"Epifi DOB"`
	FederalDob    *date.Date `csv:"Federal DOB"`
}

// fetch list of users having ekyc soruce as vkyc and have dob discrepancy as true
// we retrieve their dob from our dob
// we make call to fetchCustomerDetails for these users
// compare their DOB if mismatch we will add these users to our list and send that list to federal bank
//
//nolint:funlen
func (v *DobDiscrepancyMailScheduler) DoJob(actorIds []string, _ int) error {
	ctx := context.Background()
	var userDetails []*UserInfo
	var failedActorIds []string
	var sameDobActorIdList []string
	for _, actorId := range actorIds {
		ctx = epificontext.CtxWithActorId(ctx, actorId)
		userRes, userErr := v.usersClient.GetUser(ctx, &user.GetUserRequest{
			Identifier: &user.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if te := epifigrpc.RPCError(userRes, userErr); te != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch user detail for actor id: %v", actorId))
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		userProfileDob := userRes.GetUser().GetProfile().GetDateOfBirth()
		customerDetails, customerDetailsErr := v.usersClient.GetCustomerDetails(ctx, &user.GetCustomerDetailsRequest{
			ActorId:    actorId,
			UserId:     userRes.GetUser().GetId(),
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Provenance: user.Provenance_APP,
		})

		if te := epifigrpc.RPCError(customerDetails, customerDetailsErr); te != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch customer detail for actor id: %v", actorId))
			failedActorIds = append(failedActorIds, actorId)
			continue
		}

		if customerDetails.GetDateOfBirth() == nil || datetime.DateEquals(customerDetails.GetDateOfBirth(), userProfileDob) {
			logger.Info(ctx, fmt.Sprintf("Unexpected: Same dob on our side and fed side dobs profile %v, fed %v, actorId %v", userProfileDob, customerDetails.GetDateOfBirth(), actorId))
			sameDobActorIdList = append(sameDobActorIdList, actorId)
			time.Sleep(100 * time.Millisecond)
			continue
		}

		savingsResp, savingsErr := v.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
			Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
					ActorId:     actorId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})

		if rpcErr := epifigrpc.RPCError(savingsResp, savingsErr); rpcErr != nil {
			logger.Error(ctx, fmt.Sprintf("Error in fetch savings detail for actor id: %v", actorId))
			failedActorIds = append(failedActorIds, actorId)
			continue
		}

		userDetails = append(userDetails, &UserInfo{
			ActorId:       actorId,
			AccountNumber: savingsResp.GetAccount().GetAccountNo(),
			EpifiDob:      userProfileDob,
			FederalDob:    customerDetails.GetDateOfBirth(),
		})

		time.Sleep(100 * time.Millisecond)
	}

	if len(userDetails) == 0 {
		logger.Info(ctx, "Can't find any user with discrepancy")
		return nil
	}

	logger.Info(ctx, fmt.Sprintf("Alert! Failed to check discrepancy for these users %v", failedActorIds))
	logger.Info(ctx, fmt.Sprintf("Same dob found for these ids %v", sameDobActorIdList))

	logger.Info(ctx, "Please find below users for whom discrepancy exists")

	for _, uDetail := range userDetails {
		logger.Info(ctx, fmt.Sprintf("Actorid: %v, Account no: %v \n", uDetail.ActorId, uDetail.AccountNumber))
	}
	dateTillFetch := time.Now().Format("01-02-2006")
	fileName, bytes, err := createAndConvertToBytesCsvFile(ctx, userDetails, "DobDiscrepancy"+dateTillFetch)
	if err != nil {
		logger.Error(ctx, "error in converting file to bytes")
		return err
	}
	err = v.populateAndSendEmail(ctx, fileName, bytes)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Error in sending mail to fed to for till date: %v", dateTillFetch))
		return err
	}
	logger.Info(ctx, "Successfully send mail to users having discrepancy")
	return nil
}

func (v *DobDiscrepancyMailScheduler) GetS3FileName() []string {
	var fileNames []string
	s3OutputPathWithDate := fmt.Sprintf(s3DobDiscrepnacyPath, time.Now().Format("01-02-2006"))
	fileNames = append(fileNames, s3OutputPathWithDate)
	return fileNames
}

//nolint:dupl
func (v *DobDiscrepancyMailScheduler) populateAndSendEmail(ctx context.Context, fileName string, fileData []byte) error {
	attachments := []*comms.EmailMessage_Attachment{
		{
			FileContent:    fileData,
			FileName:       fileName,
			Disposition:    comms.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		},
	}
	// using exising created mail template since we want to send mail to ourselves once in a week
	// and we want attachment file only
	// TODO(PRANSHU): Create a seperate mail template
	resp, err := v.commsClient.SendMessage(ctx, &comms.SendMessageRequest{
		Type:   comms.QoS_GUARANTEED,
		Medium: comms.Medium_EMAIL,
		UserIdentifier: &comms.SendMessageRequest_EmailId{
			EmailId: v.conf.DobDiscrepancyEmailInfo.ToEmailId,
		},
		Message: &comms.SendMessageRequest_Email{
			Email: &comms.EmailMessage{
				FromEmailId:   v.conf.DobDiscrepancyEmailInfo.FromEmailId,
				FromEmailName: v.conf.DobDiscrepancyEmailInfo.FromEmailName,
				ToEmailName:   v.conf.DobDiscrepancyEmailInfo.ToEmailId,
				EmailOption: &comms.EmailOption{
					Option: &comms.EmailOption_UserStuckInVkycReviewStateFederalEmailOption{
						UserStuckInVkycReviewStateFederalEmailOption: &comms.UserStuckInVkycReviewStateFederalEmailOption{
							EmailType: comms.EmailType_USER_STUCK_IN_VKYC_REVIEW_STATE_FEDERAL_EMAIL,
							Option: &comms.UserStuckInVkycReviewStateFederalEmailOption_UserStuckInVkycReviewStateFederalEmailOptionV1{
								UserStuckInVkycReviewStateFederalEmailOptionV1: &comms.UserStuckInVkycReviewStateFederalEmailOptionV1{
									TemplateVersion: comms.TemplateVersion_VERSION_V1,
								},
							},
						},
					},
				},
				Attachment: attachments,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error while emailing bulk user details CSV", zap.Error(grpcErr))
		return grpcErr
	}
	return nil
}
