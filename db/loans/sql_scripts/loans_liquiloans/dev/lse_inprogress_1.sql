-- updating LSE status to in progress for rerunning the payment requests stuck in manual intervention
update loan_step_executions
set status='LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS',
	updated_at=current_timestamp
where step_name = 'LOAN_STEP_EXECUTION_STEP_NAME_RECON_PAYMENT'
  and ref_id in ('PALPREE8+A3VCTsW3LgtURslzwg240721==',
				 'PALPRAhUEl7fKRWO2SSyrnOQ3CA240723==',
				 'PALPRsBdFCbOQSpm8TUSsZ4lVJg240723==')
  and status = 'LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION';

update loan_step_executions
set status='LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS',
	updated_at=current_timestamp
where step_name = 'LOAN_STEP_EXECUTION_STEP_NAME_RECON_PAYMENT'
  and ref_id = 'PALPRT2zMTVLPQu2MPamNtvZWAQ240720=='
  and status = 'LOAN_STEP_EXECUTION_STATUS_FAILED';
