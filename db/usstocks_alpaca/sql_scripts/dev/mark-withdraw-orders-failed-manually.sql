-- Updating wallet order to FAILED --
UPDATE
	wallet_orders
set
	status = 'WALLET_ORDER_STATUS_FAILED',
	updated_at = now()
where
		id in ('USSWO3nNUcSjAyj240311', 'USSWO22mpidAER1240311');

-- updating account activity entry for the corresponding order --
UPDATE
	account_activities
set
	order_state = 'ORDER_FAILED',
	updated_at = now()
where
		order_id in ('USSWO3nNUcSjAyj240311', 'USSWO22mpidAER1240311');


-- updating workflow request for the order to FAILED --
UPDATE
	workflow_requests
set
	status = 'FAILED', updated_at = now()
where
		id in ('WFR6Dhr9ij2RKitcSa8fdkCbw240311==', 'WFRXwlwICbBTCmKz2wMyoejsA240311==');

-- updating workflow history for the order to FAILED --
UPDATE
	workflow_histories
set
	status = 'FAILED', updated_at = now()
where
		wf_req_id in ('WFR6Dhr9ij2RKitcSa8fdkCbw240311==', 'WFRXwlwICbBTCmKz2wMyoejsA240311==')
  and stage = 'INITIATE_WALLET_FUND_TRANSFER';
