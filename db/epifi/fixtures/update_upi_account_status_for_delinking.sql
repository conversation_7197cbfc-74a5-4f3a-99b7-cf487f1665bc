-- While testing delinking flow of tpap accounts, we found a bug where the last activity that marks the upi account as delinked
-- was not getting invoked due to which the accounts got delinked at vendor's end but was not updated on our end, updating the same
-- through fixture for affected users.

UPDATE upi_accounts SET status = 'UPI_ACCOUNT_STATUS_DELINKED' WHERE
id in ('41d874ab-1123-4f1e-a5da-a002f60e3a9d',
	   '705132d5-4759-409e-a009-528821caa7be',
	   '5a94d8a1-c435-4b41-8966-cd9765c77831'
      )
