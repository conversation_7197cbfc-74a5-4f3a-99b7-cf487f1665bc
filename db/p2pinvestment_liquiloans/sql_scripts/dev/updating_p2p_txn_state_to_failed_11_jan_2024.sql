-- moving below P2P  txns to failed state

UPDATE p2p_investment_transactions
set status = 'FAILED', updated_at = NOW(),
	details = jsonb_set(
		details::jsonb,
		array ['timeline'],
		(details -> 'timeline')::jsonb ||
			json_build_object('status', 'FAILED', 'createdAt',
			NOW())::jsonb)
where id = 'P2PIT3SMD/ZB5RhWBo/4YGadC/w231223=='  and status = 'UNKNOWN';

-- updating scheme id for investment where user claimed that they had invested in flexi but the txn was executed in lock-in (10%)
-- Liquiloans had executed this txn now in 8% flexi after our request

UPDATE p2p_investment_transactions
set scheme_id='invest-scheme-flexi-8-id', updated_at = NOW()
where id = 'P2PITZPbkVZPHQa6YW2GlUiU/yg231221==' and investor_id ='P2PIVR221226itHwiTFlR3Oe8WVGCyvzXQ==';
