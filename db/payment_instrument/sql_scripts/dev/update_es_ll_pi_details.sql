-- payment details to be used for SI execution is to be updated (add_ll_pi.sql)

UPDATE payment_instruments set verified_name = 'NDX P2P PRIVATE LIMITED',
							   identifier = '{"account": {"account_type": "CURRENT", "actual_account_number": "NLIQUI70029", "ifsc_code": "ICIC0000106", "secure_account_number": "xxxxxxx0029", "name":"NDX P2P PRIVATE LIMITED BORROWER REPAYMENT ESCROW ACCOUNT"}, "card": null, "upi": null}'
                           where id = 'paymentinstrument-liquiloans-early-salary';
