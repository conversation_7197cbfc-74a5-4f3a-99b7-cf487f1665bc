-- custom amount was enabled for round-up & invest, this ended up allowing users to create subscription with higher round-up value.
-- As part of subscription SI of amount 200 is created for the fund house, If the amount is greater that
-- 200, payment fails with error ST_FAILURE_MAXIMUM_AMOUNT_BREACHED
-- this fixture resets the round amount of such subscriptions to 100

UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set(
            rule_param_values, '{ruleParamValues,configuredRoundAmount,moneyVal,units}',
            '100'
        ),
    updated_at = now()
where
    rule_id = 'f49d1c45-708f-49c7-988e-131050dbfe88' AND
    -- version_state = 'CURRENT' AND -- not using version_state filter to replace even the expired versions
    (rule_param_values -> 'ruleParamValues' -> 'configuredRoundAmount' -> 'moneyVal' ->> 'units')::integer > 100;



UPDATE
    subscription_runtime_infos
SET
    rule_param_values = jsonb_set(
            rule_param_values, '{ruleParamValues,configuredRoundAmount,moneyVal,units}',
            '100'
        ),
    updated_at = now()
where
        rule_id = 'f49d1c45-708f-49c7-988e-131050dbfe88' AND
        (rule_param_values -> 'ruleParamValues' -> 'configuredRoundAmount' -> 'moneyVal' ->> 'units')::integer > 100;


